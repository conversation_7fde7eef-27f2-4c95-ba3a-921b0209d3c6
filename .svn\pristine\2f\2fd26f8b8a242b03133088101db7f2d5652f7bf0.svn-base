<template>
    <el-dialog :title="$t('GLOBAL._PCGDXZ')" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

          <el-col :lg="12">
            <el-form-item label="产线代码" prop="LineCode">
              <div>
                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="物料" prop="MaterialCode">
              <div>
                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="计划数量" prop="PlanQuantity">
              <el-input v-model="dialogForm.PlanQuantity" placeholder="请输入计划数量" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="单位" prop="Unit">
              <el-input v-model="dialogForm.Unit" placeholder="请输入单位" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="开始工作日" prop="StartWorkday">
              <el-input v-model="dialogForm.StartWorkday" placeholder="请输入开始工作日" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :lg="12">
            <el-form-item label="结束工作日" prop="FinishWorkday">
              <el-input v-model="dialogForm.FinishWorkday" placeholder="请输入结束工作日" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="首批时长" prop="FirstLotPeriod">
              <el-input v-model="dialogForm.FirstLotPeriod" placeholder="请输入首批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="中间批时长" prop="MiddleLotPeriod">
              <el-input v-model="dialogForm.MiddleLotPeriod" placeholder="请输入中间批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="末批时长" prop="LastLotPeriod">
              <el-input v-model="dialogForm.LastLotPeriod" placeholder="请输入末批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="每批数量" prop="LotQuantity">
              <el-input v-model="dialogForm.LotQuantity" placeholder="请输入每批数量" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="批次总数" prop="LotCount">
              <el-input v-model="dialogForm.LotCount" placeholder="请输入批次总数" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="配方版本" prop="BomVersion">
              <el-input v-model="dialogForm.BomVersion" placeholder="请输入配方版本" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="对应关系" prop="StandardLotType">
              <el-select v-model="dialogForm.StandardLotType" placeholder="请选择对应关系" clearable style="width:100%">
                <el-option v-for="item in standardPeriodTypeOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="从第几批返工" prop="ReworkLot">
              <el-input v-model="dialogForm.ReworkLot" placeholder="请输入从第几批返工" />
            </el-form-item>
          </el-col>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">拆分</el-button>
      </div>
    </el-dialog>
  </template>

<script>
  import {
    //getLineList,
    getAddBatchInfo,
    addBatch
  } from "@/api/planManagement/weekSchedule";
  export default {
    components:{
      
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        // factoryOptions: [],
        // workshopOptions: [],
        // lineOptions: [],        
        // categoryOptions: [],
        // shiftOptions: [],
        standardPeriodTypeOptions: [],
        currentRow: {},
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
      //this.getLineList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType')    
      },
      // async getLineList() {
      //   const { response } = await getLineList({
      //    //areaCode: 'PackingArea'
      //    areaCode: 'Formulation'
      //   })
      //   console.log(response)
      //   this.lineOptions = response
      // },
      submit() {
        addBatch(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            //console.log("show")
            this.getDialogInfo(data)
          }
        })
      },
      getDialogInfo(data){
        //console.log("getDialogInfo")
        getAddBatchInfo(data).then(res => {
          this.dialogForm = res.response
        })
      },
      // setFormLineName(EquipmentCode) {
      //   console.log(EquipmentCode);
      //   console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));
      //   this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID
      //   //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName
      //   this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode
      //   console.log(this.dialogForm);
      // },
      // setMaterial(val){
      //   // console.log("setMaterial")
      //   // console.log(val)        
      //   this.dialogForm.MaterialId = val.ID
      //   this.dialogForm.MaterialCode = val.Code
      //   this.dialogForm.MaterialName = val.NAME
      //   this.$forceUpdate()
      //   // this.matInfo = val        
      //   // console.log(this.dialogForm.MaterialCode)
      //   // console.log(this.dialogForm.MaterialName)
      // },
      // openMaterialTable(){
      //   this.$refs['materialTable'].show()
      // }
    }
  }
  </script>