{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue", "mtime": 1749177894365}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkFA;AAEA;AACA,SACAA,WADA,EACAC,eADA,QAEA,+BAFA;AAGA,wD,CAEA;;AAGA;EACAC,iBADA;EAEAC;IACA;IACAC;EAFA,CAFA;;EAMAC;IACA;MACAC;QACAC,YADA;QAEAC;MAFA,CADA;MAKAC,QALA;MAMAC,eANA;MAOAC,kCAPA;MAQAC,aARA;MASAC,cATA;MAUAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,CAVA;MAuBAC,QAvBA;MAwBAC;QACAjB,eADA;QAEAkB,sBAFA,CAGA;QACA;QACA;;MALA;IAxBA;EAgCA,CAvCA;;EAwCAC;IACA;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CAjDA;;EAkDAC;IACAC;MACA;QACA;MACA;IACA,CALA;;IAMAC;MACA;IACA,CARA;;IASAC;MACA;MACA;IACA,CAZA;;IAaAC;MACA;MACA;IACA,CAhBA;;IAiBAC;MACA;MACA;IACA,CApBA;;IAsBAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAlC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAmC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CApCA;;IAsCAC;MACArC;QACA;QACA;MACA,CAHA;IAIA;;EA3CA;AAlDA,E,CAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "names": ["delBatchDcs", "getBatchDcsList", "name", "components", "FormDialog", "data", "searchForm", "pageIndex", "pageSize", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "loading", "tableOption", "code", "width", "align", "mainH", "buttonOption", "serveIp", "mounted", "window", "methods", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "delRow", "title", "message", "confirmText", "cancelText", "then", "catch", "err", "console", "getTableData"], "sourceRoot": "src/views/planManagement/batchDcs", "sources": ["index.vue"], "sourcesContent": ["<!--\n * @Descripttion: (批次DCS下发/PPM_B_BATCH_DCS)\n * @version: (1.0)\n * @Author: (admin)\n * @Date: (2025-05-14)\n * @LastEditors: (admin)\n * @LastEditTime: (2025-05-14)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n      \n      <el-form-item label=\"产线\" prop=\"LineName\">\n        <el-input v-model=\"searchForm.LineName\" placeholder=\"请输入产线名称\" />\n      </el-form-item>\n\n      <el-form-item label=\"工单号\" prop=\"PoNo\">\n        <el-input v-model=\"searchForm.PoNo\" placeholder=\"请输入工单号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"批次号\" prop=\"BatchtNo\">\n        <el-input v-model=\"searchForm.BatchtNo\" placeholder=\"请输入批次号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"物料代码\" prop=\"MaterialCode\">\n        <el-input v-model=\"searchForm.MaterialCode\" placeholder=\"请输入物料代码\" />\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n        <!-- <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n          \n          </template>\n        </el-table-column> -->\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delBatchDcs, getBatchDcsList\n} from \"@/api/planManagement/batchDcs\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\n//import { batchDcsColumn } from '@/columns/planManagement/batchDcs.js';\n\n\nexport default {\n  name: 'index.vue',\n  components: {\n    //UploadButton,\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('BatchDcs.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n      {code: 'LineName', width: 130, align: 'center'},\n      {code: 'PoNo', width: 180, align: 'center'},\n      {code: 'BatchtNo', width: 130, align: 'center'},\n      {code: 'MaterialCode', width: 180, align: 'center'},\n      {code: 'MaterialName', width: 180, align: 'center'},\n      {code: 'Unit', width: 130, align: 'center'},\n      {code: 'StandardQuantity', width: 130, align: 'center'},\n      {code: 'PlanQuantity', width: 130, align: 'center'},\n      {code: 'Status', width: 130, align: 'center'},\n      {code: 'SendData', width: 200, align: 'center'},\n      {code: 'ResponseData', width: 200, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'批次DCS下发',\n        serveIp:'baseURL_PPM',\n        // uploadUrl:'/api/BatchDcs/ImportData', //导入\n        // exportUrl:'/api/BatchDcs/ExportData', //导出\n        // DownLoadUrl:'/api/BatchDcs/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      for (let key in this.hansObj) {\n        this.tableName = getTableHead(this.hansObj, this.tableOption)\n      }\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delBatchDcs([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getBatchDcsList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"BatchDcs\": {\n//    \"table\": {\n//        \"poNo\": \"poNo\",\n//        \"batchtNo\": \"batchtNo\",\n//        \"materialCode\": \"materialCode\",\n//        \"unit\": \"unit\",\n//        \"standardQuantity\": \"standardQuantity\",\n//        \"planQuantity\": \"planQuantity\",\n//        \"status\": \"status\",\n//        \"sendData\": \"sendData\",\n//        \"responseData\": \"responseData\",\n//        \"remark\": \"remark\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}