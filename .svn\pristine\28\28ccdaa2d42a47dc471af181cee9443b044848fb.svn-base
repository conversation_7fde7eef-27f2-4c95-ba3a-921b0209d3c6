﻿using Magicodes.ExporterAndImporter.Core;
namespace SEFA.PPM.Model.Models
{

    public class WeekScheduleExcelDto
    {
        [ImporterHeader(Name = "工厂")]
        [ExporterHeader(DisplayName = "工厂")]
        public string Factory { get; set; }

        [ImporterHeader(Name = "车间")]
        [ExporterHeader(DisplayName = "车间")]
        public string WorkShop { get; set; }

        [ImporterHeader(Name = "产线代码")]
        [ExporterHeader(DisplayName = "产线代码")]
        public string LineCode { get; set; }

    }

}