const path = require('path')
const webpack = require("webpack");
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

function resolve(dir) {
    return path.join(__dirname, dir)
}

module.exports = {
    productionSourceMap: false,
    outputDir: 'dist',

    chainWebpack: (config) => {
        config.resolve.alias.set('~$', resolve('./')).set('@$', resolve('src'));
        config.plugin('provide').use(webpack.ProvidePlugin, [{
            'window.Quill': 'quill/dist/quill.js',
            'Quill': 'quill/dist/quill.js'
        }])
    },
    css: {
        loaderOptions: {
            postcss: {
                plugins: [require('autoprefixer')()],
            },
            less: {
                lessOptions: {
                    modifyVars: {
                        'primary-color': '#3dcd58',
                        // 'link-color': '#0048FF',
                    },
                    javascriptEnabled: true,
                },
            },
        },
    },

    configureWebpack: config => {
        if (process.env.NODE_ENV === "production") {
            config.mode = 'production';
            config.optimization.minimizer.push(
                new UglifyJsPlugin({
                    uglifyOptions: {
                        // 删除注释
                        output: {
                            comments: false
                        },
                        compress: {
                            drop_console: true, //console
                            drop_debugger: true,
                            pure_funcs: ['console.log'] //移除console
                        }
                    }
                }),

            )
        } else {
            config.mode = "development";
        }
    },
    devServer: {
        proxy: {
            // '/api': {
            //     target: 'http://*************:30032',
            //     ws: false,
            //     changeOrigin: true,
            //     pathRewrite: {
            //         '^/api': '/api',
            //     },
            // },
            '/api': {
                target: 'http://127.0.0.1:30019',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/api': '/api',
                },
            },
            '/kpiapi': {
                target: 'http://*************:30024',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/kpiapi': '/kpiapi',
                },
            },
            '/sim': {
                target: 'http://*************:30018',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/sim': '/sim',
                },
            },
            '/trace': {
                target: 'http://*************:30015',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/trace': '/trace',
                },
            },
            '/order': {
                target: 'http://*************:30014',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/order': '/order',
                },
            },
            '/api2': {
                target: 'http://*************:30014',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/api2': '/api2',
                },
            },
            '/quality': {
                target: 'http://*************:30008',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/quality': '/quality',
                },
            },
            '/shift': {
                target: 'http://*************:30020',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/shift': '/shift',
                },
            },
            '/materail': {
                target: 'http://127.0.0.1:30018',
                // target: 'http://*************:30033',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/materail': '/materail',
                }
            },
            '/andon': {
                target: 'http://*************:30012',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/andon': '/andon',
                }
            },
            '/tpm': {
                target: 'http://*************:30034',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/tpm': '/tpm',
                }
            },
            '/backend-api': {
                target: 'https://qms.aac.tech',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/backend-api': '/backend-api',
                }
            },
            '/simapi': {
                target: 'https://*************:30018',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/simapi': '/simapi',
                }
            },
            '/simkpi': {
                target: 'https://*************:30018',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/simkpi': '/simkpi',
                }
            },
            '/ppm': {
                // target: 'https://*************:30033',
                target: 'http://127.0.0.1:30018',
                ws: false,
                changeOrigin: true,
                pathRewrite: {
                    '^/ppm': '/ppm',
                }
            }

        },
        hot: true
    },
    assetsDir: 'static',
    runtimeCompiler: true,
}