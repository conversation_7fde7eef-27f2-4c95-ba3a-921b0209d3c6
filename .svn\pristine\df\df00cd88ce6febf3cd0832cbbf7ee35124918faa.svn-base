<template>
  <div>
    <el-drawer class="drawer" :visible.sync="drawer" :direction="'rtl'" :before-close="handleClose"
      :append-to-body="false" size="80%">
      <div slot="title" class="title-box">
        <span>{{ `${currentRow.OrderNo} | ${currentRow.LineCode} | ${currentRow.Factory} | ${currentRow.MaterialCode}-${currentRow.MaterialName}` }}</span>
      </div>
      <div class="InventorySearchBox">
        <div class="searchbox pd5">
          <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            <el-form-item :label="$t('GLOBAL._SSL')">
              <el-input clearable v-model="searchForm.Key"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button icon="el-icon-search" @click="getTableData">{{ $t('GLOBAL._CX') }}</el-button>
            </el-form-item>
            <!-- <el-form-item>
              <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">{{
                $t('GLOBAL._XZ') }}
              </el-button>
            </el-form-item> -->
          </el-form>
        </div>
      </div>
      <div class="table-box">
        <el-table v-loading="loading" :data="tableData" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" style="width: 100%" height="83vh">
          <el-table-column prop="operation" width="180" :label="$t('GLOBAL._ACTIONS')" align="center">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-circle-plus-outline" @click="showDialog((scope.row))">{{$t('GLOBAL._XZ') }}</el-button>
              <el-button size="mini" type="text" @click="downloadDCS(scope.row)" :disabled="scope.row.Status == 'NotSplit' ? true : false">{{ $t('GLOBAL._XFDCS') }}</el-button>
            </template>
          </el-table-column>
          <el-table-column v-for="(item, index) in tableHeadDrawer" :key="index" :prop="item.field" :label="item.label" :width="item.width" :align="item.alignType">
            <template slot-scope="scope">
              <span v-if="['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)">
                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}
              </span>
              <span v-else-if="item.field === 'PoStatus'"> {{ status[scope.row[item.field]] }} </span>
              <span v-else> {{ scope.row[item.field] }} </span>
            </template>
          </el-table-column>

        </el-table>
      </div>
    </el-drawer>
    <DetailForm @saveForm="getTableData" ref="detailDialog" />
  </div>
</template>

<script>
import { getProductionOrderList, downloadDCS } from '@/api/planManagement/weekSchedule'
import DetailForm from './poListDetailForm'
import {getTableHead} from "@/util/dataDictionary.js";
export default {
  name: 'POListDetail',
  components: {
    DetailForm
  },
  data() {
    return {
      searchForm: {},
      drawer: false,
      tableData: [],
      tableHeadDrawer: [],      
      hansObjDrawer: this.$t('WeekFormulation.poListDetail'),
      tableOptionDrawer:[        
        {code: 'Sequence', width: 100, align: 'left'},
        {code: 'ProductionOrderNo', width: 200, align: 'left'},
        {code: 'SegmentCode', width: 150, align: 'left'},
        {code: 'LineCode', width: 100, align: 'left'},
        {code: 'MaterialCode', width: 180, align: 'left'},
        {code: 'MaterialName', width: 180, align: 'left'},
        {code: 'PlanQty', width: 100, align: 'left'},
        {code: 'PlanDate', width: 180, align: 'left'},        
        {code: 'PrepareShiftid', width: 100, align: 'left'},
        {code: 'PlanStartTime', width: 150, align: 'left'},
        {code: 'PlanEndTime', width: 150, align: 'left'},
        {code: 'StartTime', width: 150, align: 'left'},
        {code: 'EndTime', width: 150, align: 'left'},
        {code: 'PoStatus', width: 150, align: 'left'},
        {code: 'ReleaseStatus', width: 100, align: 'left'},
        {code: 'ProduceStatus', width: 100, align: 'left'},
        {code: 'BomVersion', width: 100, align: 'left'},
        {code: 'Type', width: 100, align: 'left'},
        {code: 'Sapordertype', width: 100, align: 'left'},
        {code: 'Remark', width: 200, align: 'left'},
      ],
      loading: false,
      sapSegmentMaterialId: 0,
      currentRow: {},
      status: {
        '1': 'Pending Release',
        '2': 'Released',
        '3': 'Complete',
        '4': 'Cancel',
        '5' : 'Stop',
        '6' : 'Running'
      }
    }
  },
  mounted() {
    this.initTableHead()
  },
  methods: {
    show(val) {
      this.currentRow = val
      this.sapSegmentMaterialId = val.ID
      this.drawer = true
      this.getTableData()
    },
    handleClose() {
      this.drawer = false
    },
    async getTableData() {
      const { response } = await getProductionOrderList({
        ParentId: this.currentRow.ID,
        ...this.searchForm
      })
      this.tableData = response
    },
    initTableHead() {
      this.tableHeadDrawer = []
      // for (let key in this.hansObjDrawer) {
      //   this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })
      // }
      this.tableHeadDrawer = getTableHead(this.hansObjDrawer, this.tableOptionDrawer)
      this.$forceUpdate()
      console.log(this.tableHeadDrawer);
    },
    showDialog(row) {
      this.$refs.detailDialog.show(row)
    },
    downloadDCS(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM_XFDCS'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        downloadDCS([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  :deep(.el-drawer__body) {
    padding-top: 10px;
    background-color: #FFFFFF;
    overflow-y: hidden
  }

  :deep(.el-form--inline) {
    height: 32px;
  }

  .title-box {
    font-size: 18px;
    color: #909399;
  }

  .pd5 {
    padding: 5px;
  }

  .table-box {
    padding: 0 10px;

    :deep(.el-button.is-disabled) {
      background-color: transparent !important;
      border: 0 !important;
    }

    i {
      margin-right: 5px;
      font-size: 15px !important;
      color: #67c23a;
    }
  }
}
</style>