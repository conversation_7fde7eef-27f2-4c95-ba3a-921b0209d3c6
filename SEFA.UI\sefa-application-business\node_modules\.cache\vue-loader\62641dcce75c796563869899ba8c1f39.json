{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetailForm.vue", "mtime": 1749177894469}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["poListDetailForm.vue"], "names": [], "mappings": ";AAwGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "poListDetailForm.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\n    <el-dialog :title=\"$t('GLOBAL._PCGDXZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线代码\" prop=\"LineCode\">\n              <div>\n                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料\" prop=\"MaterialCode\">\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入计划数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-input v-model=\"dialogForm.StartWorkday\" placeholder=\"请输入开始工作日\" disabled />\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-input v-model=\"dialogForm.FinishWorkday\" placeholder=\"请输入结束工作日\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"首批时长\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入首批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"中间批时长\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"末批时长\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入末批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"每批数量\" prop=\"LotQuantity\">\n              <el-input v-model=\"dialogForm.LotQuantity\" placeholder=\"请输入每批数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"批次总数\" prop=\"LotCount\">\n              <el-input v-model=\"dialogForm.LotCount\" placeholder=\"请输入批次总数\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"配方版本\" prop=\"BomVersion\">\n              <el-input v-model=\"dialogForm.BomVersion\" placeholder=\"请输入配方版本\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"对应关系\" prop=\"StandardLotType\">\n              <el-select v-model=\"dialogForm.StandardLotType\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in standardPeriodTypeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"从第几批返工\" prop=\"ReworkLot\">\n              <el-input v-model=\"dialogForm.ReworkLot\" placeholder=\"请输入从第几批返工\" />\n            </el-form-item>\n          </el-col>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">拆分</el-button>\n      </div>\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    //getLineList,\n    getAddBatchInfo,\n    addBatch\n  } from \"@/api/planManagement/weekSchedule\";\n  export default {\n    components:{\n      \n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        // factoryOptions: [],\n        // workshopOptions: [],\n        // lineOptions: [],        \n        // categoryOptions: [],\n        // shiftOptions: [],\n        standardPeriodTypeOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      //this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType')    \n      },\n      // async getLineList() {\n      //   const { response } = await getLineList({\n      //    //areaCode: 'PackingArea'\n      //    areaCode: 'Formulation'\n      //   })\n      //   console.log(response)\n      //   this.lineOptions = response\n      // },\n      submit() {\n        addBatch(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            //console.log(\"show\")\n            this.getDialogInfo(data)\n          }\n        })\n      },\n      getDialogInfo(data){\n        //console.log(\"getDialogInfo\")\n        getAddBatchInfo(data).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      // setFormLineName(EquipmentCode) {\n      //   console.log(EquipmentCode);\n      //   console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n      //   this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n      //   //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n      //   this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n      //   console.log(this.dialogForm);\n      // },\n      // setMaterial(val){\n      //   // console.log(\"setMaterial\")\n      //   // console.log(val)        \n      //   this.dialogForm.MaterialId = val.ID\n      //   this.dialogForm.MaterialCode = val.Code\n      //   this.dialogForm.MaterialName = val.NAME\n      //   this.$forceUpdate()\n      //   // this.matInfo = val        \n      //   // console.log(this.dialogForm.MaterialCode)\n      //   // console.log(this.dialogForm.MaterialName)\n      // },\n      // openMaterialTable(){\n      //   this.$refs['materialTable'].show()\n      // }\n    }\n  }\n  </script>"]}]}