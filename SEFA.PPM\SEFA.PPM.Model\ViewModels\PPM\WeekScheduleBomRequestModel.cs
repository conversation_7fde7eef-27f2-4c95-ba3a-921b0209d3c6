﻿using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class WeekScheduleBomRequestModel : RequestPageModelBase
    {
        public WeekScheduleBomRequestModel()
        {
        }

        /// <summary>
        /// Desc:
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:周计划ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string WeekScheduleId { get; set; }
        /// <summary>
        /// Desc:SegmentID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SegmentId { get; set; }
        /// <summary>
        /// Desc:Segment名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SegmentName { get; set; }
        /// <summary>
        /// Desc:Segment物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SegmentMaterialId { get; set; }
        /// <summary>
        /// Desc:Segment物料步骤ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string SegmentMaterialStepId { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialVer { get; set; }
        /// <summary>
        /// Desc:物料类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string MaterialType { get; set; }
        /// <summary>
        /// Desc:使用顺序
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Sort { get; set; }
        /// <summary>
        /// Desc:上级ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:替代物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InsteadMaterialId { get; set; }
        /// <summary>
        /// Desc:替代物料代码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InsteadMaterialCode { get; set; }
        /// <summary>
        /// Desc:替代物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InsteadMaterialName { get; set; }
        /// <summary>
        /// Desc:替代物料版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InsteadMaterialVer { get; set; }
        /// <summary>
        /// Desc:替代物料分组
        /// Default:
        /// Nullable:True
        /// </summary>
        public string InsteadMaterialGroup { get; set; }
        /// <summary>
        /// Desc:替代比例
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? InsteadRate { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:标准需求数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal StandardQuantity { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQuantity { get; set; }
        /// <summary>
        /// Desc:实际数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ActualQuantity { get; set; }
        /// <summary>
        /// Desc:SAP工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapOrderNo { get; set; }
        /// <summary>
        /// Desc:SAP报工数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? SapFeedbackQuantity { get; set; }
        /// <summary>
        /// Desc:SAP状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapStatus { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

    }
}