{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetailForm.vue", "mtime": 1749177894469}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAwGA,SACA;AACAA,eAFA,EAGAC,QAHA,QAIA,mCAJA;AAKA;EACAC,cADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIA;MACA;MACA;MACA;MACA;MACAC,6BATA;MAUAC,cAVA;MAWAC;IAXA;EAaA,CAlBA;;EAmBAC;IACA,oBADA,CAEA;EACA,CAtBA;;EAuBAC,WACA,CAxBA;;EAyBAC;IACA;MACA;IACA,CAHA;;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAZ;QACA;QACA;QACA;MACA,CAJA;IAKA,CAlBA;;IAmBAa;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA,CALA;IAMA,CA7BA;;IA8BAC;MACA;MACAf;QACA;MACA,CAFA;IAGA,CAnCA,CAoCA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EAzDA;AAzBA", "names": ["getAddBatchInfo", "addBatch", "components", "data", "dialogForm", "dialogVisible", "formLoading", "standardPeriodTypeOptions", "currentRow", "matInfo", "created", "mounted", "methods", "submit", "show", "getDialogInfo"], "sourceRoot": "src/views/planManagement/weekFormulation", "sources": ["poListDetailForm.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"$t('GLOBAL._PCGDXZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线代码\" prop=\"LineCode\">\n              <div>\n                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料\" prop=\"MaterialCode\">\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入计划数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-input v-model=\"dialogForm.StartWorkday\" placeholder=\"请输入开始工作日\" disabled />\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-input v-model=\"dialogForm.FinishWorkday\" placeholder=\"请输入结束工作日\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"首批时长\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入首批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"中间批时长\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"末批时长\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入末批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"每批数量\" prop=\"LotQuantity\">\n              <el-input v-model=\"dialogForm.LotQuantity\" placeholder=\"请输入每批数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"批次总数\" prop=\"LotCount\">\n              <el-input v-model=\"dialogForm.LotCount\" placeholder=\"请输入批次总数\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"配方版本\" prop=\"BomVersion\">\n              <el-input v-model=\"dialogForm.BomVersion\" placeholder=\"请输入配方版本\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"对应关系\" prop=\"StandardLotType\">\n              <el-select v-model=\"dialogForm.StandardLotType\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in standardPeriodTypeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"从第几批返工\" prop=\"ReworkLot\">\n              <el-input v-model=\"dialogForm.ReworkLot\" placeholder=\"请输入从第几批返工\" />\n            </el-form-item>\n          </el-col>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">拆分</el-button>\n      </div>\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    //getLineList,\n    getAddBatchInfo,\n    addBatch\n  } from \"@/api/planManagement/weekSchedule\";\n  export default {\n    components:{\n      \n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        // factoryOptions: [],\n        // workshopOptions: [],\n        // lineOptions: [],        \n        // categoryOptions: [],\n        // shiftOptions: [],\n        standardPeriodTypeOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      //this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType')    \n      },\n      // async getLineList() {\n      //   const { response } = await getLineList({\n      //    //areaCode: 'PackingArea'\n      //    areaCode: 'Formulation'\n      //   })\n      //   console.log(response)\n      //   this.lineOptions = response\n      // },\n      submit() {\n        addBatch(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            //console.log(\"show\")\n            this.getDialogInfo(data)\n          }\n        })\n      },\n      getDialogInfo(data){\n        //console.log(\"getDialogInfo\")\n        getAddBatchInfo(data).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      // setFormLineName(EquipmentCode) {\n      //   console.log(EquipmentCode);\n      //   console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n      //   this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n      //   //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n      //   this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n      //   console.log(this.dialogForm);\n      // },\n      // setMaterial(val){\n      //   // console.log(\"setMaterial\")\n      //   // console.log(val)        \n      //   this.dialogForm.MaterialId = val.ID\n      //   this.dialogForm.MaterialCode = val.Code\n      //   this.dialogForm.MaterialName = val.NAME\n      //   this.$forceUpdate()\n      //   // this.matInfo = val        \n      //   // console.log(this.dialogForm.MaterialCode)\n      //   // console.log(this.dialogForm.MaterialName)\n      // },\n      // openMaterialTable(){\n      //   this.$refs['materialTable'].show()\n      // }\n    }\n  }\n  </script>"]}]}