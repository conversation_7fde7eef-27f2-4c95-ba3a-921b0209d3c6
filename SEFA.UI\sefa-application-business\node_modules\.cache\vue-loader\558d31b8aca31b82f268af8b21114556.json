{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\index.vue", "mtime": 1749177894465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<!--\n * @Descripttion: (配置工单批次管理)\n * @version: (1.0)\n * @Author: (SECI)\n * @Date: (2025-05-22)\n * @LastEditors: (SECI)\n * @LastEditTime: (2025-05-22)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <el-form-item label=\"计划编号\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.OrderNo\" placeholder=\"请输入计划编号\" />\n        </el-form-item>\n        <el-form-item label=\"产品名称\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.MaterialName\" placeholder=\"请输入产品名称\" />\n        </el-form-item>\n        <el-form-item label=\"产线\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.lineCode\" placeholder=\"请输入产线\" />\n        </el-form-item>\n        <el-form-item label=\"计划时间\" class=\"mb-2\">\n          <el-date-picker v-model=\"searchForm.StartWorkday\" type=\"datetime\" placeholder=\"选择开始时间\"></el-date-picker>\n          ~\n          <el-date-picker v-model=\"searchForm.FinishWorkday\" type=\"datetime\" placeholder=\"选择结束时间\"></el-date-picker>\n        </el-form-item>\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        \n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"combination\">\n              <i class=\"el-icon-document\" @click=\"poListDialog(scope.row)\"></i>&nbsp;&nbsp;\n              <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._PCGDCF') }}</el-button>\n            </div>                        \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n    <POList ref=\"poList\" />\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport FormDialog from './form-dialog'\nimport POList from './poListDetail.vue'\nimport {delWeekSchedule, getWeekScheduleList} from \"@/api/planManagement/weekSchedule\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\nexport default {\n  name: 'index',\n  components: {\n    FormDialog,\n    POList\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('WeekSchedule.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n        {code: 'Factory', width: 100, align: 'center'},\n        {code: 'Workshop', width: 150, align: 'center'},\n        {code: 'Category', width: 150, align: 'center'},\n        {code: 'DesignCode', width: 150, align: 'center'},       \n        {code: 'LineCode', width: 150, align: 'center'},       \n        {code: 'MaterialCode', width: 100, align: 'center'},\n        {code: 'MaterialName', width: 180, align: 'center'},\n        {code: 'OrderNo', width: 180, align: 'center'},\n        {code: 'Output', width: 100, align: 'center'},\n        {code: 'PackSize', width: 150, align: 'center'},\n        {code: 'PlanQuantity', width: 100, align: 'center'},\n        {code: 'Remark', width: 150, align: 'center'},\n        {code: 'SapOrderNo', width: 150, align: 'center'},\n        {code: 'StartWorkday', width: 150, align: 'center', format: 'yyyy-MM-dd'},\n        {code: 'StartShift', width: 100, align: 'center'},\n        {code: 'FinishWorkday', width: 180, align: 'center'},\n        {code: 'FinishShift', width: 100, align: 'center'},\n        {code: 'Status', width: 100, align: 'center'},\n        {code: 'Type', width: 100, align: 'center'},\n        {code: 'Unit', width: 100, align: 'center'},\n        {code: 'WorkCenter', width: 100, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'配置周计划',\n        serveIp:'baseURL_PPM',\n        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入\n        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出\n        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      this.tableName = getTableHead(this.hansObj, this.tableOption)\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n    poListDialog(row) {\n      this.$refs.poList.show(row)\n    },\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delWeekSchedule([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getWeekScheduleList(this.searchForm).then(res => {\n        //console.log(res);\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}