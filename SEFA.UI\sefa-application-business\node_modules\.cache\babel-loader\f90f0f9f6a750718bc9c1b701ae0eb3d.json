{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekPacking\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekPacking\\form-dialog.vue", "mtime": 1749177894525}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA0IA,SACAA,qBADA,EAEAC,oBAFA,EAGAC,WAHA,QAIA,mCAJA;AAKA;AACA;EACAC;IACAC;EADA,CADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,kBAJA;MAKAC,mBALA;MAMAC,eANA;MAOAC,mBAPA;MAQAC,gBARA;MASAC,eATA;MAUAC,cAVA;MAWAC;IAXA;EAaA,CAlBA;;EAmBAC;IACA;IACA;EACA,CAtBA;;EAuBAC,WACA,CAxBA;;EAyBAC;IACA;MACA;MACA;MACA;MACA;MACA;IACA,CAPA;;IAQA;MACA;QAAAC;MAAA;QACA;QACAC;MAFA;MAIAC;MACA;IACA,CAfA;;IAgBAC;MACAtB;QACA;QACA;QACA;MACA,CAJA;IAKA,CAtBA;;IAuBAuB;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAhCA;;IAiCAC;MACAzB;QACA;MACA,CAFA;IAGA,CArCA;;IAsCA0B;MACAJ;MACAA;MACA,0FAHA,CAIA;;MACA;MACAA;IACA,CA7CA;;IA8CAK;MACA;MACA;MACA;MACA;MACA;MACA,oBANA,CAOA;MACA;MACA;IACA,CAxDA;;IAyDAC;MACA;IACA;;EA3DA;AAzBA", "names": ["getWeekScheduleDetail", "saveWeekScheduleForm", "getLineList", "components", "MaterialTable", "data", "dialogForm", "dialogVisible", "formLoading", "factoryOptions", "workshopOptions", "lineOptions", "categoryOptions", "shiftOptions", "typeOptions", "currentRow", "matInfo", "created", "mounted", "methods", "response", "areaCode", "console", "submit", "show", "getDialogDetail", "setFormLineName", "setMaterial", "openMaterialTable"], "sourceRoot": "src/views/planManagement/weekPacking", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"工厂\" prop=\"factory\">\n              <el-select v-model=\"dialogForm.Factory\" placeholder=\"请选择工厂\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in factoryOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"车间\" prop=\"Workshop\">\n              <el-select v-model=\"dialogForm.Workshop\" placeholder=\"请选择车间\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in workshopOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线代码\" prop=\"LineCode\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.LindCode\" placeholder=\"请选择产线\" @change=\"setFormLineName\">\n                    <el-option v-for=\"(item, index) in lineOptions\" :key=\"index\" :label=\"item.EquipmentName\" :value=\"item.EquipmentCode\">\n                    </el-option>\n                </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划分类\" prop=\"Category\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.Category\" placeholder=\"请选择计划分类\" >\n                <el-option v-for=\"item in categoryOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\" prop=\"MaterialCode\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"包装规格\" prop=\"PackSize\">\n              <el-input v-model=\"dialogForm.PackSize\" placeholder=\"请输入包装规格\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"设计代码\" prop=\"DesignCode\">\n              <el-input v-model=\"dialogForm.DesignCode\" placeholder=\"请输入设计代码\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"输出\" prop=\"Output\">\n              <el-input v-model=\"dialogForm.Output\" placeholder=\"请输入输出\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-date-picker v-model=\"dialogForm.StartWorkday\" type=\"datetime\" placeholder=\"选择日期时间\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始班次\" prop=\"StartShift\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.StartShift\" placeholder=\"请选择开始班次\">\n                <el-option v-for=\"item in shiftOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-date-picker v-model=\"dialogForm.FinishWorkday\" type=\"datetime\" placeholder=\"选择日期时间\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束班次\" prop=\"FinishShift\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.FinishShift\" placeholder=\"请选择结束班次\">\n                <el-option v-for=\"item in shiftOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入计划数量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划编号\" prop=\"OrderNo\">\n              <el-input v-model=\"dialogForm.OrderNo\" placeholder=\"请输入计划编号\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划类型\" prop=\"Type\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.Type\" placeholder=\"请选择计划类型\">\n                <el-option v-for=\"item in typeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"dialogForm.Remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getWeekScheduleDetail,\n    saveWeekScheduleForm,\n    getLineList\n  } from \"@/api/planManagement/weekSchedule\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        factoryOptions: [],\n        workshopOptions: [],\n        lineOptions: [],        \n        categoryOptions: [],\n        shiftOptions: [],\n        typeOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.factoryOptions = await this.$getDataDictionary('WeekScheduleFactory');\n        this.workshopOptions = await this.$getDataDictionary('WeekScheduleWorkshop');\n        this.categoryOptions = await this.$getDataDictionary('WeekScheduleCategory');\n        this.shiftOptions = await this.$getDataDictionary('WeekScheduleShift');\n        this.typeOptions = await this.$getDataDictionary('WeekScheduleType');\n      },\n      async getLineList() {\n        const { response } = await getLineList({\n         //areaCode: 'PackingArea'\n         areaCode: 'Formulation'\n        })\n        console.log(response)\n        this.lineOptions = response\n      },\n      submit() {\n        saveWeekScheduleForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getWeekScheduleDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      setFormLineName(EquipmentCode) {\n        console.log(EquipmentCode);\n        console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n        console.log(this.dialogForm);\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)        \n        this.dialogForm.MaterialId = val.ID\n        this.dialogForm.MaterialCode = val.Code\n        this.dialogForm.MaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}