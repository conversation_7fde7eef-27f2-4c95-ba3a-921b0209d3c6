{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749630426874}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICdAL3ZpZXdzL0ludmVudG9yeS9teXN0eWxlLnNjc3MnOwppbXBvcnQgeyBNZXNzYWdlLCBNZXNzYWdlQm94IH0gZnJvbSAnZWxlbWVudC11aSc7CmltcG9ydCB7IEF2YWxsYWJsZUludmVudG9yeUNvbHVtbiwgUE9JbnZlbnRvcnlQYWxsZXRzQ29sdW1uIH0gZnJvbSAnQC9jb2x1bW5zL2ZhY3RvcnlQbGFudC90YWJsZUhlYWRlcnMnOwppbXBvcnQgeyBHZXRQYWdlTGlzdE5ld01hdGVyaWFsUHJlRG93biwgR2V0UGFnZUxpc3RCeU1hdGVyaWFsLCBHZXRQYWdlTGlzdEJ5QmF0Y2hJRFNCeUlELCBHZXRQYWdlTGlzdEJ5TWF0ZXJpYWxJSSwgR2V0UGFnZUxpc3RCeUJhdGNoSURTSUksIEdldFBhZ2VMaXN0TWF0ZXJpYWxQcmVUb3AsIEdldFBhZ2VMaXN0TmV3TWF0ZXJpYWxQcmVUb3AsIEdldENvblNlbGVjdExpc3QsIEZpcnN0QWRkUGFsbGV0LCBHZXRQYWdlTGlzdEJ5QmF0Y2hJRFMsIE15Z2V0U1NDQyB9IGZyb20gJ0AvYXBpL0ludmVudG9yeS9NYXRlcmlhbFByZXBhcmF0aW9uLmpzJzsKaW1wb3J0IHsgR2V0UHJpbml0MiwgR2V0UHJpbml0MywgR2V0UHJpbml0OCwgUHJpbnRQcmVwYXJhTGFiZWxLWSB9IGZyb20gJ0AvYXBpL0ludmVudG9yeS9jb21tb24uanMnOwppbXBvcnQgeyBFbXB0eSB9IGZyb20gJ2FudC1kZXNpZ24tdnVlJzsKZXhwb3J0IGRlZmF1bHQgewogIGNvbXBvbmVudHM6IHsKICAgIFBhcnRpYWxCYWc6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL1BhcnRpYWxCYWcnKSwKICAgIEZ1bGxBbW91bnQ6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL0Z1bGxBbW91bnQnKSwKICAgIEZ1bGxCYWc6ICgpID0+IGltcG9ydCgnLi9jb21wb25lbnRzL0Z1bGxCYWcnKSwKICAgIFBPSW52ZW50b3J5OiAoKSA9PiBpbXBvcnQoJy4vY29tcG9uZW50cy9QT0ludmVudG9yeScpLAogICAgQmF0Y2hQYWxsZXRzOiAoKSA9PiBpbXBvcnQoJy4vY29tcG9uZW50cy9CYXRjaFBhbGxldHMnKQogIH0sCgogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBQcmludElkOiAnJywKICAgICAgcHJpbnRlcm9wdGlvbjogW10sCiAgICAgIFByaW50TW9kZWw6IGZhbHNlLAogICAgICBpc0V4cGlyYXRpb25EYXRlOiBmYWxzZSwKICAgICAgdGFibGVJZDogJ0lOVl9DTFpCJywKICAgICAgYWN0aXZlTmFtZTogJ1BhcnRpYWxCYWcnLAogICAgICBPbmx5RnVsbEFtb3VudDogZmFsc2UsCiAgICAgIEhpZGVjb21wbGV0ZWQ6IGZhbHNlLAogICAgICBkZXRhaWxvYmo6IHt9LAogICAgICByb29tOiAnJywKICAgICAgdGFibGVMaXN0OiBbXSwKICAgICAgdGFibGVMaXN0QmF0Y2hQTzogW10sCiAgICAgIFNlbGVjdExpc3Q6IFtdLAogICAgICBoZWFkZXJCYXRjaFBPOiBQT0ludmVudG9yeVBhbGxldHNDb2x1bW4sCiAgICAgIGhlYWRlcjogQXZhbGxhYmxlSW52ZW50b3J5Q29sdW1uLAogICAgICB3YXk6ICcnLAogICAgICBsaXN0SWQ6ICcnLAogICAgICBub3dDaG9vc2VSb3c6IHt9LAogICAgICBNYXRlcmlhbExpc3Q6IFtdLAogICAgICBNYXRlcmlhbE5vdzogbnVsbCwKICAgICAgVXNlVHlwZTogJycsCiAgICAgIGNsYmxGbGFnOiBmYWxzZSwKICAgICAga2VlcEtleURvd246IGZhbHNlLAogICAgICBtaW5UYWJsZUhlaWdodDogMTUwLAogICAgICAvLyDmnIDlsI/ooajmoLzpq5jluqYKICAgICAgbWF4VGFibGVIZWlnaHQ6IDQwMCwKICAgICAgLy8g5pyA5aSn6KGo5qC86auY5bqmCiAgICAgIHJvd0hlaWdodDogNDgsCiAgICAgIC8vIOavj+ihjOeahOmrmOW6pu+8iOiwg+aVtOS4uuabtOWHhuehrueahOWAvO+8iQogICAgICB3aW5kb3dIZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodCwKICAgICAgLy8g56qX5Y+j6auY5bqmCiAgICAgIHVzZVZpZXdwb3J0SGVpZ2h0OiBmYWxzZSwKICAgICAgLy8g5piv5ZCm5L2/55So6KeG5Y+j6auY5bqm5qih5byPCiAgICAgIHRhYmxlSGVpZ2h0OiAyMDAsCiAgICAgIC8vIOW9k+WJjeihqOagvOmrmOW6pgogICAgICB0YWJsZUhlaWdodEtleTogMCwKICAgICAgLy8g5by65Yi26YeN5paw5riy5p+T55qEa2V5CiAgICAgIGlzUmVzaXppbmc6IGZhbHNlLAogICAgICAvLyDpmLLmraJyZXNpemXmrbvlvqrnjq8KICAgICAgLy8g5rWL6K+V55u45YWz5pWw5o2uIC0g5rWL6K+V5a6M5oiQ5ZCO5Yig6ZmkCiAgICAgIHRlc3REYXRhQ291bnRlcjogMCwKICAgICAgb3JpZ2luYWxUYWJsZUxpc3Q6IFtdIC8vIOS/neWtmOWOn+Wni+aVsOaNrgoKICAgIH07CiAgfSwKCiAgbW91bnRlZCgpIHsKICAgIGNvbnNvbGUubG9nKHRoaXMuJHJvdXRlKTsKICAgIGNvbnNvbGUubG9nKHRoaXMuJHJvdXRlLnBhdGgpOwogICAgbGV0IG15a2V5ID0gd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ01hdGVyaWFsUHJlcGFyYXRpb24nKTsKCiAgICBpZiAobXlrZXkgPT0gJ2NsYmwnKSB7CiAgICAgIHRoaXMud2F5ID0gJ01hdGVyaWFsJzsKICAgIH0gZWxzZSB7CiAgICAgIHRoaXMud2F5ID0gJ0JhdGNoJzsKICAgIH0KCiAgICB0aGlzLmRldGFpbG9iaiA9IEpTT04ucGFyc2UodGhpcy4kcm91dGUucXVlcnkucXVlcnkpOwogICAgY29uc29sZS5sb2codGhpcy5kZXRhaWxvYmosIDEyMyk7CiAgICB0aGlzLmNsYmxGbGFnID0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLlRhZ3BTKSA+PSBOdW1iZXIodGhpcy5kZXRhaWxvYmouTWluUHZhbHVlKSAmJiBOdW1iZXIodGhpcy5kZXRhaWxvYmouVGFncFMpIDw9IE51bWJlcih0aGlzLmRldGFpbG9iai5NYXhQdmFsdWUpOwogICAgY29uc29sZS5sb2codGhpcy5jbGJsRmxhZyk7CiAgICB0aGlzLlVzZVR5cGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS5Vc2VUeXBlOwogICAgdGhpcy5saXN0SWQgPSBKU09OLnBhcnNlKHRoaXMuJHJvdXRlLnF1ZXJ5Lkxpc3QpOwogICAgdGhpcy5yb29tID0gd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3Jvb20nKTsKICAgIHRoaXMuZ2V0cHJpbnRMaXN0KCk7IC8vIHRoaXMud2F5ID0gdGhpcy4kcm91dGUucXVlcnkud2F5OwoKICAgIHRoaXMuR2V0TWF0ZXJpYWxMaXN0KCk7CiAgICB0aGlzLmdldFNlbGVjdExpc3QoKTsKICAgIHRoaXMuZ2V0VG9wRGF0YSgpOwoKICAgIGlmICh0aGlzLndheSA9PSAnQmF0Y2gnKSB7CiAgICAgIHRoaXMuZ2V0UE9UYWJlbERhdGEoKTsKICAgICAgdGhpcy5hY3RpdmVOYW1lID0gJ0Z1bGxCYWcnOwogICAgfSBlbHNlIHsKICAgICAgdGhpcy5hY3RpdmVOYW1lID0gJ1BhcnRpYWxCYWcnOwogICAgfSAvLyDmt7vliqDnqpflj6PlpKflsI/lj5jljJbnm5HlkKzlmagKCgogICAgdGhpcy5pc1Jlc2l6aW5nID0gZmFsc2U7IC8vIOmYsuatouatu+W+queOr+agh+W/lwoKICAgIHRoaXMuaGFuZGxlUmVzaXplID0gKCkgPT4gewogICAgICBpZiAodGhpcy5pc1Jlc2l6aW5nKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+i3s+i/h3Jlc2l6ZeS6i+S7tu+8jOmYsuatouatu+W+queOrycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgdGhpcy53aW5kb3dIZWlnaHQgPSB3aW5kb3cuaW5uZXJIZWlnaHQ7CiAgICAgIGNvbnNvbGUubG9nKCfnqpflj6PlpKflsI/lj5jljJY6JywgdGhpcy53aW5kb3dIZWlnaHQpOwoKICAgICAgaWYgKHRoaXMudXNlVmlld3BvcnRIZWlnaHQpIHsKICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlSGVpZ2h0KCk7CiAgICAgIH0KICAgIH07CgogICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsgLy8g5Yid5aeL5YyW5pel5b+XCgogICAgY29uc29sZS5sb2coJz09PSDooajmoLzoh6rpgILlupTpq5jluqbliJ3lp4vljJYgPT09Jyk7CiAgICBjb25zb2xlLmxvZygn5Yid5aeL5qih5byPOicsIHRoaXMudXNlVmlld3BvcnRIZWlnaHQgPyAn6KeG5Y+j5qih5byPJyA6ICfooYzmlbDmqKHlvI8nKTsKICAgIGNvbnNvbGUubG9nKCfliJ3lp4vnqpflj6Ppq5jluqY6JywgdGhpcy53aW5kb3dIZWlnaHQpOwogICAgY29uc29sZS5sb2coJ3dheeWPmOmHj+WAvDonLCB0aGlzLndheSk7CiAgICBjb25zb2xlLmxvZygndGFibGVMaXN06ZW/5bqmOicsIHRoaXMudGFibGVMaXN0ID8gdGhpcy50YWJsZUxpc3QubGVuZ3RoIDogJ3VuZGVmaW5lZCcpOyAvLyDliJ3lp4vljJbooajmoLzpq5jluqYKCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgIHRoaXMudXBkYXRlVGFibGVIZWlnaHQoKTsKICAgICAgY29uc29sZS5sb2coJz09PSDpobXpnaLliJ3lp4vljJblrozmiJAgPT09Jyk7CiAgICB9KTsKICB9LAoKICBiZWZvcmVNb3VudCgpIHsKICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXl1cCcsIHRoaXMuZ2V0S2V5RG93bik7CiAgfSwKCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIC8vIOa4heeQhuS6i+S7tuebkeWQrOWZqAogICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleXVwJywgdGhpcy5nZXRLZXlEb3duKTsKCiAgICBpZiAodGhpcy5oYW5kbGVSZXNpemUpIHsKICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIHRoaXMuaGFuZGxlUmVzaXplKTsKICAgIH0KICB9LAoKICBtZXRob2RzOiB7CiAgICBvcGVuS2V5RG93bigpIHsKICAgICAgY29uc29sZS5sb2coMik7CgogICAgICBpZiAodGhpcy5rZWVwS2V5RG93biA9PSBmYWxzZSkgewogICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdrZXl1cCcsIHRoaXMuZ2V0S2V5RG93bik7CiAgICAgICAgdGhpcy5rZWVwS2V5RG93biA9IHRydWU7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleXVwJywgdGhpcy5nZXRLZXlEb3duKTsKICAgICAgICB0aGlzLmtlZXBLZXlEb3duID0gZmFsc2U7CiAgICAgIH0KICAgIH0sCgogICAgZ2V0S2V5RG93bihldmVudCkgewogICAgICBpZiAodGhpcy5rZWVwS2V5RG93bikgewogICAgICAgIGxldCBjb2RlID0gZXZlbnQua2V5Q29kZTsKICAgICAgICBjb25zb2xlLmxvZyhjb2RlKTsKCiAgICAgICAgc3dpdGNoIChjb2RlKSB7CiAgICAgICAgICBjYXNlIDM3OgogICAgICAgICAgICBpZiAodGhpcy5NYXRlcmlhbExpc3RbdGhpcy5NYXRlcmlhbE5vdyAtIDFdKSB7CiAgICAgICAgICAgICAgdGhpcy5DaGFuZ2VNYXRlcmlhbCgtMSk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgTWVzc2FnZSh7CiAgICAgICAgICAgICAgICBtZXNzYWdlOiBgJHt0aGlzLiR0KCdNYXRlcmlhbFByZXBhcmF0aW9uQnVpbGQuTm9NYXRlcmlhbCcpfWAsCiAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgfQoKICAgICAgICAgICAgYnJlYWs7CgogICAgICAgICAgY2FzZSAzOToKICAgICAgICAgICAgaWYgKHRoaXMuTWF0ZXJpYWxMaXN0W3RoaXMuTWF0ZXJpYWxOb3cgKyAxXSkgewogICAgICAgICAgICAgIHRoaXMuQ2hhbmdlTWF0ZXJpYWwoKzEpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIE1lc3NhZ2UoewogICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7dGhpcy4kdCgnTWF0ZXJpYWxQcmVwYXJhdGlvbkJ1aWxkLk5vTWF0ZXJpYWwnKX1gLAogICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGJyZWFrOwoKICAgICAgICAgIGNhc2UgMzI6CiAgICAgICAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT0gJ0Z1bGxCYWcnKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ0Z1bGxCYWcnKTsKCiAgICAgICAgICAgICAgaWYgKHRoaXMuJHJlZnMuRnVsbEJhZy5nZXRidG5TdGF0dXMoKSkgewogICAgICAgICAgICAgICAgdGhpcy4kcmVmcy5GdWxsQmFnLlRyYW5zZmVyKCk7CiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIE1lc3NhZ2UoewogICAgICAgICAgICAgICAgICBtZXNzYWdlOiBgJHt0aGlzLiR0KCdNYXRlcmlhbFByZXBhcmF0aW9uQnVpbGQuTm9UcmFuc2ZlcicpfWAsCiAgICAgICAgICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CgogICAgICAgICAgICBpZiAodGhpcy5hY3RpdmVOYW1lID09ICdQYXJ0aWFsQmFnJykgewogICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQYXJ0aWFsQmFnJyk7CgogICAgICAgICAgICAgIGlmICh0aGlzLiRyZWZzLlBhcnRpYWxCYWcuZ2V0YnRuU3RhdHVzKCkpIHsKICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMuUGFydGlhbEJhZy5UcmFuc2ZlcigpOwogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBNZXNzYWdlKHsKICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7dGhpcy4kdCgnTWF0ZXJpYWxQcmVwYXJhdGlvbkJ1aWxkLk5vVHJhbnNmZXInKX1gLAogICAgICAgICAgICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQoKICAgICAgICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PSAnRnVsbEFtb3VudCcpIHsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnRnVsbEFtb3VudCcpOwoKICAgICAgICAgICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQW1vdW50LmdldGJ0blN0YXR1cygpKSB7CiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuVHJhbnNmZXIoKTsKICAgICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgTWVzc2FnZSh7CiAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGAke3RoaXMuJHQoJ01hdGVyaWFsUHJlcGFyYXRpb25CdWlsZC5Ob1RyYW5zZmVyJyl9YCwKICAgICAgICAgICAgICAgICAgdHlwZTogJ3dhcm5pbmcnCiAgICAgICAgICAgICAgICB9KTsKICAgICAgICAgICAgICB9CiAgICAgICAgICAgIH0KCiAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KCiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldFByaW50KCkgewogICAgICBsZXQgaWRzID0gdGhpcy50YWJsZUxpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIHJldHVybiBpdGVtLklEOwogICAgICB9KTsgLy8gbGV0IHBhcmFtcyA9IHsKICAgICAgLy8gICAgIElkczogaWRzLAogICAgICAvLyAgICAgZXF1bWVudGlkOiB0aGlzLnJvb20sCiAgICAgIC8vICAgICBQcmludElkOiB0aGlzLlByaW50SWQKICAgICAgLy8gfTsKCiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgcHJpbnRJZDogdGhpcy5QcmludElkLAogICAgICAgIEVxdWlwbWVudElkOiB0aGlzLnJvb20sCiAgICAgICAgQmFnU2laZTogdGhpcy5kZXRhaWxvYmouQmFnU2l6ZSwKICAgICAgICBNQ29kZTogdGhpcy5kZXRhaWxvYmouTUNvZGUsCiAgICAgICAgaWRzOiBpZHMKICAgICAgfTsKICAgICAgbGV0IHJlcyA9IGF3YWl0IFByaW50UHJlcGFyYUxhYmVsS1kocGFyYW1zKTsKICAgICAgTWVzc2FnZSh7CiAgICAgICAgbWVzc2FnZTogcmVzLm1zZywKICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgfSk7CiAgICAgIHRoaXMuUHJpbnRNb2RlbCA9IGZhbHNlOwogICAgfSwKCiAgICBhc3luYyBnZXRwcmludExpc3QoKSB7CiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgZXF1aXBtZW50SWQ6IHRoaXMucm9vbQogICAgICB9OwogICAgICBsZXQgcmVzMiA9IGF3YWl0IEdldFByaW5pdDgocGFyYW1zKTsKICAgICAgcmVzMi5yZXNwb25zZS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgIGl0ZW0udmFsdWUgPSBpdGVtLklEOwogICAgICAgIGl0ZW0ubGFiZWwgPSBpdGVtLkNvZGU7CiAgICAgICAgaXRlbS5JdGVtTmFtZSA9IGl0ZW0uQ29kZTsKICAgICAgICBpdGVtLkl0ZW1WYWx1ZSA9IGl0ZW0uSUQ7CiAgICAgIH0pOwogICAgICB0aGlzLnByaW50ZXJvcHRpb24gPSByZXMyLnJlc3BvbnNlOwoKICAgICAgaWYgKHRoaXMuJHJlZnMuUE9JbnZlbnRvcnkpIHsKICAgICAgICB0aGlzLiRyZWZzLlBPSW52ZW50b3J5LnByaW50ZXJvcHRpb24gPSB0aGlzLnByaW50ZXJvcHRpb247CiAgICAgIH0KICAgIH0sCgogICAgUHJpbnRBdmFsbGFibGUoKSB7CiAgICAgIHRoaXMuUHJpbnRJZCA9IHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdQcmludElkJyk7CiAgICAgIHRoaXMuUHJpbnRNb2RlbCA9IHRydWU7CiAgICB9LAoKICAgIGFzeW5jIGdldFBPVGFiZWxEYXRhKCkgewogICAgICBsZXQgcGFyYW1zID0gewogICAgICAgIEJhdGNoSWQ6IHRoaXMuZGV0YWlsb2JqLkJhdGNoSWQsCiAgICAgICAgUHJvT3JkZXJpZDogdGhpcy5kZXRhaWxvYmouUHJvZHVjdGlvbk9yZGVySWQsCiAgICAgICAgTWF0ZXJpYWxJZDogdGhpcy5kZXRhaWxvYmouTWF0ZXJpYWxJZCwKICAgICAgICBFcXVpcG1lbnRJZDogdGhpcy5yb29tLAogICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICBwYWdlU2l6ZTogMTAwMAogICAgICB9OwogICAgICBsZXQgcmVzID0gYXdhaXQgR2V0UGFnZUxpc3ROZXdNYXRlcmlhbFByZURvd24ocGFyYW1zKTsKICAgICAgdGhpcy50YWJsZUxpc3RCYXRjaFBPID0gcmVzLnJlc3BvbnNlLmRhdGE7CiAgICB9LAoKICAgIEdldFNTQ0Moa2V5KSB7CiAgICAgIGxldCBmbGFnID0gdGhpcy50YWJsZUxpc3Quc29tZShpdGVtID0+IHsKICAgICAgICByZXR1cm4gaXRlbS5TYlNzY2MgPT0ga2V5OwogICAgICB9KTsKCiAgICAgIGlmIChmbGFnID09IHRydWUpIHsKICAgICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQmFnKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLkZ1bGxCYWcuc3NjY0ZsYWcgPSB0cnVlOwogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuUGFydGlhbEJhZykgewogICAgICAgICAgdGhpcy4kcmVmcy5QYXJ0aWFsQmFnLnNzY2NGbGFnID0gdHJ1ZTsKICAgICAgICB9CgogICAgICAgIGlmICh0aGlzLiRyZWZzLkZ1bGxBbW91bnQpIHsKICAgICAgICAgIHRoaXMuJHJlZnMuRnVsbEFtb3VudC5zc2NjRmxhZyA9IHRydWU7CiAgICAgICAgfQogICAgICB9IGVsc2UgewogICAgICAgIGlmICh0aGlzLiRyZWZzLkZ1bGxCYWcpIHsKICAgICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5zc2NjRmxhZyA9IGZhbHNlOwogICAgICAgIH0KCiAgICAgICAgaWYgKHRoaXMuJHJlZnMuUGFydGlhbEJhZykgewogICAgICAgICAgdGhpcy4kcmVmcy5QYXJ0aWFsQmFnLnNzY2NGbGFnID0gZmFsc2U7CiAgICAgICAgfQoKICAgICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQW1vdW50KSB7CiAgICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuc3NjY0ZsYWcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCgogICAgRW1wdHlTc2NjKCkgewogICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQW1vdW50KSB7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQW1vdW50LnNzY2MgPSAnJzsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuJHJlZnMuRnVsbEJhZykgewogICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5zc2NjID0gJyc7CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLiRyZWZzLlBhcnRpYWxCYWcpIHsKICAgICAgICB0aGlzLiRyZWZzLlBhcnRpYWxCYWcuc3NjYyA9ICcnOwogICAgICB9CiAgICB9LAoKICAgIENoYW5nZU1hdGVyaWFsKG51bSkgewogICAgICBpZiAodGhpcy53YXkgPT0gJ0JhdGNoJyAmJiAhdGhpcy5Pbmx5RnVsbEFtb3VudCkgewogICAgICAgIHRoaXMuYWN0aXZlTmFtZSA9ICdGdWxsQmFnJzsKICAgICAgfQoKICAgICAgaWYgKHRoaXMud2F5ID09ICdNYXRlcmlhbCcpIHsKICAgICAgICB0aGlzLmFjdGl2ZU5hbWUgPSAnUGFydGlhbEJhZyc7CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLk9ubHlGdWxsQW1vdW50KSB7CiAgICAgICAgdGhpcy5hY3RpdmVOYW1lID0gJ0Z1bGxBbW91bnQnOwogICAgICB9CgogICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQW1vdW50KSB7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQW1vdW50LnNzY2MgPSAnJzsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuJHJlZnMuRnVsbEJhZykgewogICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5zc2NjID0gJyc7CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLiRyZWZzLlBhcnRpYWxCYWcpIHsKICAgICAgICB0aGlzLiRyZWZzLlBhcnRpYWxCYWcuc3NjYyA9ICcnOwogICAgICB9CgogICAgICBsZXQgbm93ID0gdGhpcy5NYXRlcmlhbE5vdyArIG51bTsKICAgICAgdGhpcy5kZXRhaWxvYmogPSB0aGlzLk1hdGVyaWFsTGlzdFtub3ddOwogICAgICBjb25zb2xlLmxvZyh0aGlzLmRldGFpbG9iaik7CiAgICAgIHRoaXMuTWF0ZXJpYWxOb3cgPSBub3c7CiAgICAgIHRoaXMucmVmcmVzaCgpOwogICAgfSwKCiAgICBhc3luYyBHZXRNYXRlcmlhbExpc3QoKSB7CiAgICAgIGxldCByZXM7CgogICAgICBpZiAod2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ01hdGVyaWFsUHJlcGFyYXRpb24nKSA9PSAnY2xibCcpIHsKICAgICAgICBpZiAodGhpcy5Vc2VUeXBlID09ICdCYXRjaCcpIHsKICAgICAgICAgIGxldCBkYXRhID0gewogICAgICAgICAgICBJRDogdGhpcy5saXN0SWQsCiAgICAgICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICAgICAgRXF1aXBtZW50SWQ6IHRoaXMucm9vbSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgIH07IC8vIGFsZXJ0KCdhYScpOwoKICAgICAgICAgIHJlcyA9IGF3YWl0IEdldFBhZ2VMaXN0QnlCYXRjaElEUyhkYXRhKTsgLy8gR2V0UGFnZUxpc3RCeUJhdGNoSURTKGRhdGEpOyAvL2lk5q+P5Liq6YO95piv5LiA5qC355qEIOS9huaYr01hdGVyaWFsSWTkuI3kuIDmoLcKICAgICAgICB9IGVsc2UgewogICAgICAgICAgbGV0IFByb0lkcyA9IHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5nZXRJdGVtKCdQcm9JZHMnKTsKICAgICAgICAgIGxldCBkYXRhID0gewogICAgICAgICAgICBFcXVtZW50SWQ6IHRoaXMucm9vbSwKICAgICAgICAgICAgUHJvSWQ6IEpTT04ucGFyc2UoUHJvSWRzKSwKICAgICAgICAgICAgTWF0ZXJpYWxJZDogdGhpcy5saXN0SWQsCiAgICAgICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgIH07CiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRQYWdlTGlzdEJ5TWF0ZXJpYWwoZGF0YSk7IC8vTWF0ZXJpYWxJZOavj+S4qumDveaYr+S4gOagt+eahCDkvYbmmK9JZOS4jeS4gOagtwogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy5Vc2VUeXBlID09ICdCYXRjaCcpIHsKICAgICAgICAgIGxldCBkYXRhID0gewogICAgICAgICAgICBJRDogdGhpcy5saXN0SWQsCiAgICAgICAgICAgIEVxdWlwbWVudElkOiB0aGlzLnJvb20sCiAgICAgICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICAgICAgcGFnZVNpemU6IDEwMDAKICAgICAgICAgIH07CiAgICAgICAgICByZXMgPSBhd2FpdCBHZXRQYWdlTGlzdEJ5QmF0Y2hJRFNJSShkYXRhKTsgLy9pZOavj+S4qumDveaYr+S4gOagt+eahCDkvYbmmK9NYXRlcmlhbElk5LiN5LiA5qC3CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGxldCBQcm9JZHMgPSB3aW5kb3cuc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgnUHJvSWRzJyk7CiAgICAgICAgICBsZXQgZGF0YSA9IHsKICAgICAgICAgICAgTWF0ZXJpYWxJZDogdGhpcy5saXN0SWQsCiAgICAgICAgICAgIEVxdW1lbnRJZDogdGhpcy5yb29tLAogICAgICAgICAgICBQcm9JZDogSlNPTi5wYXJzZShQcm9JZHMpLAogICAgICAgICAgICBwYWdlSW5kZXg6IDEsCiAgICAgICAgICAgIHBhZ2VTaXplOiAxMDAwCiAgICAgICAgICB9OwogICAgICAgICAgcmVzID0gYXdhaXQgR2V0UGFnZUxpc3RCeU1hdGVyaWFsSUkoZGF0YSk7IC8vTWF0ZXJpYWxJZOavj+S4qumDveaYr+S4gOagt+eahCDkvYbmmK9JZOS4jeS4gOagtwogICAgICAgIH0KICAgICAgfQoKICAgICAgbGV0IHJlc3BvbnNlID0gcmVzLnJlc3BvbnNlOwogICAgICB0aGlzLk1hdGVyaWFsTGlzdCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgIHRoaXMuTWF0ZXJpYWxMaXN0LmZvckVhY2goKGl0ZW0sIGluZGV4KSA9PiB7CiAgICAgICAgaWYgKGl0ZW0uT25seUlkID09IHRoaXMuZGV0YWlsb2JqLk9ubHlJZCkgewogICAgICAgICAgdGhpcy5NYXRlcmlhbE5vdyA9IGluZGV4OwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHRoaXMuZGV0YWlsb2JqID0gdGhpcy5NYXRlcmlhbExpc3RbdGhpcy5NYXRlcmlhbE5vd107CiAgICAgIHRoaXMuZGV0YWlsb2JqLmlzR1VuaXQgPSBmYWxzZTsKCiAgICAgIGlmICh0aGlzLmRldGFpbG9iai5DaGFuZ2VVbml0KSB7CiAgICAgICAgaWYgKHRoaXMuZGV0YWlsb2JqLkNoYW5nZVVuaXQgPT0gJ2cnKSB7CiAgICAgICAgICAvLyB0aGlzLmRldGFpbG9iai5UYWdwUyA9IE51bWJlcih0aGlzLmRldGFpbG9iai5UYWdwUykgKiAxMDAwOwogICAgICAgICAgLy8gdGhpcy5kZXRhaWxvYmouUGFyaXRpYWxQYWdlID0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLlBhcml0aWFsUGFnZSkgKiAxMDAwOwogICAgICAgICAgLy8gdGhpcy5kZXRhaWxvYmouTWluUHZhbHVlID0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLk1pblB2YWx1ZSkgKiAxMDAwOwogICAgICAgICAgLy8gdGhpcy5kZXRhaWxvYmouTWF4UHZhbHVlID0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLk1heFB2YWx1ZSkgKiAxMDAwOwogICAgICAgICAgLy8gdGhpcy5kZXRhaWxvYmouTVF1YW50aXR5ID0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLk1RdWFudGl0eSkgKiAxMDAwOwogICAgICAgICAgLy8gdGhpcy5kZXRhaWxvYmouTVF1YW50aXR5VG90YWwgPSBOdW1iZXIodGhpcy5kZXRhaWxvYmouTVF1YW50aXR5VG90YWwpICogMTAwMDsKICAgICAgICAgIHRoaXMuZGV0YWlsb2JqLmlzR1VuaXQgPSB0cnVlOwogICAgICAgIH0KICAgICAgfQoKICAgICAgdGhpcy5jbGJsRmxhZyA9IE51bWJlcih0aGlzLmRldGFpbG9iai5UYWdwUykgPj0gTnVtYmVyKHRoaXMuZGV0YWlsb2JqLk1pblB2YWx1ZSkgJiYgTnVtYmVyKHRoaXMuZGV0YWlsb2JqLlRhZ3BTKSA8PSBOdW1iZXIodGhpcy5kZXRhaWxvYmouTWF4UHZhbHVlKTsKCiAgICAgIGlmICh0aGlzLiRyZWZzLlBhcnRpYWxCYWcpIHsKICAgICAgICB0aGlzLiRyZWZzLlBhcnRpYWxCYWcuZ2V0RGV0YWlsb2JqKCk7CiAgICAgIH0KCiAgICAgIGlmICh0aGlzLiRyZWZzLkZ1bGxBbW91bnQpIHsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuZ2V0SW5RdWFudGl0eSgpOwogICAgICB9CgogICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQmFnKSB7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQmFnLkJhZ1dlaWdodCA9IHRoaXMuZGV0YWlsb2JqLkJhZ1NpemU7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQmFnLkJhZ3MgPSAwOwogICAgICB9CgogICAgICBpZiAodGhpcy4kcmVmcy5CYXRjaFBhbGxldHMpIHsKICAgICAgICB0aGlzLiRyZWZzLkJhdGNoUGFsbGV0cy5nZXRUYWJlbERhdGEoKTsKICAgICAgICB0aGlzLiRyZWZzLkJhdGNoUGFsbGV0cy5nZXRCYXRjaFBhbGxldHNTdGF0dXMoKTsKICAgICAgfQogICAgfSwKCiAgICBhc3luYyBnZXRSb3dCeVNzY2ModmFsKSB7CiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgTUNvZGU6IHRoaXMuZGV0YWlsb2JqLk1Db2RlLAogICAgICAgIFNTQ0M6IHZhbAogICAgICB9OwogICAgICBsZXQgcmVzID0gYXdhaXQgTXlnZXRTU0NDKHBhcmFtcyk7CgogICAgICBpZiAocmVzLnJlc3BvbnNlLmRhdGEgPT0gbnVsbCkgewogICAgICAgIE1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogYOivpei/vea6r+eggeS4jeWtmOWcqGAsCiAgICAgICAgICB0eXBlOiAnZXJyb3InCiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgbGV0IGRhdGEgPSByZXMucmVzcG9uc2UuZGF0YVswXTsKCiAgICAgICAgaWYgKGRhdGEuUmVtYXJrID09ICdreScpIHsKICAgICAgICAgIHRoaXMuJHJlZnMuVG9wVGFiZWwudGFibGVEYXRhLmZvckVhY2goaXRlbSA9PiB7CiAgICAgICAgICAgIGlmIChpdGVtLklEID09IGRhdGEuSUQpIHsKICAgICAgICAgICAgICB0aGlzLiRyZWZzLlRvcFRhYmVsLnNldEN1cnJlbnRSb3coaXRlbSk7CiAgICAgICAgICAgICAgdGhpcy5HZXRDdXJyZW50Um93KGl0ZW0pOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy4kcmVmcy5Ub3BCYXRjaFRhYmVsLnRhYmxlRGF0YS5mb3JFYWNoKGl0ZW0gPT4gewogICAgICAgICAgICBpZiAoaXRlbS5JRCA9PSBkYXRhLklEKSB7CiAgICAgICAgICAgICAgdGhpcy4kcmVmcy5Ub3BCYXRjaFRhYmVsLnNldEN1cnJlbnRSb3coaXRlbSk7CiAgICAgICAgICAgICAgdGhpcy5HZXRDdXJyZW50Um93MihpdGVtKTsKICAgICAgICAgICAgfQogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIEdldEN1cnJlbnRSb3codmFsKSB7CiAgICAgIGNvbnNvbGUubG9nKHZhbCwgMik7CiAgICAgIHRoaXMuaXNFeHBpcmF0aW9uRGF0ZSA9IHRoaXMuaXNEYXRlSW5UaGVQYXN0KHZhbC5FeHBpcmF0aW9uRGF0ZSk7IC8vIHRoaXMuJHJlZnMuVG9wVGFiZWwuc2V0Q3VycmVudFJvdygpOwogICAgICAvLyB0aGlzLiRyZWZzLlRvcEJhdGNoVGFiZWwuc2V0Q3VycmVudFJvdygpOwoKICAgICAgaWYgKHRoaXMuJHJlZnMuVG9wQmF0Y2hUYWJlbCkgewogICAgICAgIHRoaXMuJHJlZnMuVG9wQmF0Y2hUYWJlbC5zZXRDdXJyZW50Um93KCk7CiAgICAgIH0KCiAgICAgIHRoaXMuT25seUZ1bGxBbW91bnQgPSBmYWxzZTsKICAgICAgdGhpcy5ub3dDaG9vc2VSb3cgPSB2YWw7CgogICAgICBpZiAodGhpcy4kcmVmcy5CYXRjaFBhbGxldHMpIHsKICAgICAgICB0aGlzLiRyZWZzLkJhdGNoUGFsbGV0cy5JblF1YW50aXR5ID0gdGhpcy5ub3dDaG9vc2VSb3cuSW5RdWFudGl0eTsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuJHJlZnMuUGFydGlhbEJhZykgewogICAgICAgIHRoaXMuJHJlZnMuUGFydGlhbEJhZy5zc2NjRmxhZyA9IHRydWU7CiAgICAgICAgdGhpcy4kcmVmcy5QYXJ0aWFsQmFnLkluUXVhbnRpdHkgPSB0aGlzLm5vd0Nob29zZVJvdy5JblF1YW50aXR5OwogICAgICAgIHRoaXMuJHJlZnMuUGFydGlhbEJhZy5TdWJJZCA9IHRoaXMubm93Q2hvb3NlUm93LlN1YklkOwogICAgICAgIHRoaXMuJHJlZnMuUGFydGlhbEJhZy5zc2NjID0gdGhpcy5ub3dDaG9vc2VSb3cuU2JTc2NjOwogICAgICB9IC8v5Yik5pat5pW06KKL6L2s56e7CgoKICAgICAgaWYgKHRoaXMuJHJlZnMuRnVsbEJhZykgewogICAgICAgIGxldCBJblF1YW50aXR5ID0gdGhpcy5ub3dDaG9vc2VSb3cuSW5RdWFudGl0eTsKCiAgICAgICAgaWYgKHRoaXMuZGV0YWlsb2JqLkNoYW5nZVVuaXQpIHsKICAgICAgICAgIGlmICh0aGlzLmRldGFpbG9iai5DaGFuZ2VVbml0ID09ICdnJykgewogICAgICAgICAgICBJblF1YW50aXR5ID0gSW5RdWFudGl0eSAqIDEwMDA7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxCYWcuQmFnV2VpZ2h0ID0gdGhpcy5kZXRhaWxvYmouQmFnU2l6ZTsKICAgICAgICBsZXQgbnVtID0gdGhpcy5kZXRhaWxvYmouRnVsbFBhZ2Uuc3BsaXQoJy8nKVsxXSAtIHRoaXMuZGV0YWlsb2JqLkZ1bGxQYWdlLnNwbGl0KCcvJylbMF07CiAgICAgICAgbGV0IGtleSA9IG51bSAqIHRoaXMuZGV0YWlsb2JqLkJhZ1NpemU7CgogICAgICAgIGlmIChudW0gPT0gMCkgewogICAgICAgICAgLy/lt6blj7Pnm7jnrYnlsLHnpoHmraLovaznp7vvvIzlubbkuJTljIXmlbDkuLowCiAgICAgICAgICB0aGlzLiRyZWZzLkZ1bGxCYWcuc3NjY0ZsYWcgPSBmYWxzZTsKICAgICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5CYWdzID0gMDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgLy/kuI3nm7jnrYnlsLHliKTmlq3pgInkuK3mlbDph4/ot5/lt67lgLzkuZjku6XljZXljIXmlbDph48KICAgICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5zc2NjRmxhZyA9IHRydWU7CgogICAgICAgICAgaWYgKEluUXVhbnRpdHkgPj0ga2V5KSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5CYWdzID0gTWF0aC5mbG9vcihudW0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgdGhpcy4kcmVmcy5GdWxsQmFnLkJhZ3MgPSBNYXRoLmZsb29yKEluUXVhbnRpdHkgLyB0aGlzLmRldGFpbG9iai5CYWdTaXplKTsKICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIHRoaXMuJHJlZnMuRnVsbEJhZy5JblF1YW50aXR5ID0gSW5RdWFudGl0eTsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxCYWcuU3ViSWQgPSB0aGlzLm5vd0Nob29zZVJvdy5TdWJJZDsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxCYWcuc3NjYyA9IHRoaXMubm93Q2hvb3NlUm93LlNiU3NjYzsKICAgICAgfQoKICAgICAgaWYgKHRoaXMuJHJlZnMuRnVsbEFtb3VudCkgewogICAgICAgIHRoaXMuJHJlZnMuRnVsbEFtb3VudC5zc2NjRmxhZyA9IHRydWU7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQW1vdW50LlN1YklkID0gdGhpcy5ub3dDaG9vc2VSb3cuU3ViSWQ7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQW1vdW50LkluUXVhbnRpdHkgPSB0aGlzLm5vd0Nob29zZVJvdy5JblF1YW50aXR5OwogICAgICAgIHRoaXMuJHJlZnMuRnVsbEFtb3VudC5zc2NjID0gdGhpcy5ub3dDaG9vc2VSb3cuU2JTc2NjOwogICAgICAgIHRoaXMuJHJlZnMuRnVsbEFtb3VudC5nZXRJblF1YW50aXR5KCk7CiAgICAgIH0KICAgIH0sCgogICAgR2V0Q3VycmVudFJvdzIodmFsKSB7CiAgICAgIHRoaXMuaXNFeHBpcmF0aW9uRGF0ZSA9IHRoaXMuaXNEYXRlSW5UaGVQYXN0KHZhbC5FeHBpcmF0aW9uRGF0ZSk7CiAgICAgIHRoaXMuJHJlZnMuVG9wVGFiZWwuc2V0Q3VycmVudFJvdygpOwogICAgICB0aGlzLmFjdGl2ZU5hbWUgPSAnRnVsbEFtb3VudCc7CiAgICAgIHRoaXMuT25seUZ1bGxBbW91bnQgPSB0cnVlOyAvLyB0aGlzLiRyZWZzLlRvcEJhdGNoVGFiZWwuc2V0Q3VycmVudFJvdygpOwoKICAgICAgdGhpcy5ub3dDaG9vc2VSb3cgPSB2YWw7CgogICAgICBpZiAodGhpcy4kcmVmcy5GdWxsQW1vdW50KSB7CiAgICAgICAgdGhpcy4kcmVmcy5GdWxsQW1vdW50LnNzY2NGbGFnID0gdHJ1ZTsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuU3ViSWQgPSB0aGlzLm5vd0Nob29zZVJvdy5TdWJJZDsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuc3NjYyA9IHRoaXMubm93Q2hvb3NlUm93LlNiU3NjYzsKICAgICAgICB0aGlzLiRyZWZzLkZ1bGxBbW91bnQuZ2V0SW5RdWFudGl0eSh0cnVlLCB0aGlzLm5vd0Nob29zZVJvdyk7CiAgICAgIH0KICAgIH0sCgogICAgYXN5bmMgR2V0QWRkUGFsbGV0KCkgewogICAgICBsZXQgcGFyYW1zID0gewogICAgICAgIFRhcmVXZWlnaHQ6IHRoaXMuZGV0YWlsb2JqLk1RdWFudGl0eVRvdGFsLAogICAgICAgIFVvbUlEOiB0aGlzLmRldGFpbG9iai5UVWludGlkLAogICAgICAgIFByb0JhdGNoSUQ6IHRoaXMuZGV0YWlsb2JqLkJhdGNoSWQsCiAgICAgICAgRXF1aXBNZW50SUQ6IHRoaXMucm9vbSwKICAgICAgICBNYXRlcmlhbElkOiB0aGlzLmRldGFpbG9iai5NYXRlcmlhbElkLAogICAgICAgIFByb1JlcXVlc3RJRDogdGhpcy5kZXRhaWxvYmouUHJvZHVjdGlvbk9yZGVySWQKICAgICAgfTsKICAgICAgbGV0IHJlcyA9IGF3YWl0IEZpcnN0QWRkUGFsbGV0KHBhcmFtcyk7CiAgICAgIE1lc3NhZ2UoewogICAgICAgIG1lc3NhZ2U6IHJlcy5tc2csCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgIH0pOwogICAgICB0aGlzLnJlZnJlc2goKTsKICAgIH0sCgogICAgcmVmcmVzaCgpIHsKICAgICAgdGhpcy5nZXRUb3BEYXRhKCk7CiAgICAgIHRoaXMuR2V0TWF0ZXJpYWxMaXN0KCk7CgogICAgICBpZiAodGhpcy53YXkgPT0gJ0JhdGNoJykgewogICAgICAgIHRoaXMuZ2V0UE9UYWJlbERhdGEoKTsKICAgICAgICB0aGlzLmdldFNlbGVjdExpc3QoKTsKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRyZWZzLlBPSW52ZW50b3J5LmdldFRhYmVsRGF0YSgpOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldFNlbGVjdExpc3QoKSB7CiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgcHJvT3JkZXJJRDogdGhpcy5kZXRhaWxvYmouUHJvZHVjdGlvbk9yZGVySWQsCiAgICAgICAgYmF0Y2hJRDogdGhpcy5kZXRhaWxvYmouQmF0Y2hJZAogICAgICB9OwogICAgICBsZXQgcmVzID0gYXdhaXQgR2V0Q29uU2VsZWN0TGlzdChwYXJhbXMpOwogICAgICBjb25zb2xlLmxvZyhyZXMsIDEyMzEyMyk7CiAgICAgIHRoaXMuU2VsZWN0TGlzdCA9IHJlcy5yZXNwb25zZTsKCiAgICAgIGlmICh0aGlzLndheSA9PSAnQmF0Y2gnKSB7CiAgICAgICAgaWYgKHRoaXMuU2VsZWN0TGlzdC5sZW5ndGggIT0gMCkgewogICAgICAgICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oJ0JhdGNoUGFsbGV0cycsIHJlcy5yZXNwb25zZVswXS5JRCk7CgogICAgICAgICAgaWYgKHRoaXMuJHJlZnMuQmF0Y2hQYWxsZXRzKSB7CiAgICAgICAgICAgIHRoaXMuJHJlZnMuQmF0Y2hQYWxsZXRzLkJhdGNoUGFsbGV0c09wdGlvbiA9IHJlcy5yZXNwb25zZTsKICAgICAgICAgICAgdGhpcy4kcmVmcy5CYXRjaFBhbGxldHMuQmF0Y2hQYWxsZXRzID0gcmVzLnJlc3BvbnNlWzBdLklEOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLiRyZWZzLlBPSW52ZW50b3J5LmdldFRhYmVsRGF0YSgpOwogICAgICB9CiAgICB9LAoKICAgIGFzeW5jIGdldFRvcERhdGEoKSB7CiAgICAgIGxldCByZXM7CiAgICAgIGxldCBwYXJhbXMgPSB7CiAgICAgICAgTWF0ZXJpYWxJZDogdGhpcy5kZXRhaWxvYmouTWF0ZXJpYWxJZCwKICAgICAgICBFcXVpcG1lbnRJZDogdGhpcy5yb29tLAogICAgICAgIHBhZ2VJbmRleDogMSwKICAgICAgICBwYWdlU2l6ZTogMTAwMAogICAgICB9OwoKICAgICAgaWYgKHRoaXMud2F5ID09ICdCYXRjaCcpIHsKICAgICAgICByZXMgPSBhd2FpdCBHZXRQYWdlTGlzdE1hdGVyaWFsUHJlVG9wKHBhcmFtcyk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgcmVzID0gYXdhaXQgR2V0UGFnZUxpc3ROZXdNYXRlcmlhbFByZVRvcChwYXJhbXMpOwogICAgICB9CgogICAgICB0aGlzLnRhYmxlTGlzdCA9IHJlcy5yZXNwb25zZS5kYXRhOyAvLyDmlbDmja7liqDovb3lrozmiJDlkI7mm7TmlrDooajmoLzpq5jluqYKCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlSGVpZ2h0KCk7CiAgICAgIH0pOwogICAgfSwKCiAgICBiYWNrKHZhbCkgewogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOwogICAgfSwKCiAgICBpc0RhdGVJblRoZVBhc3QoZGF0ZVN0cmluZykgewogICAgICBjb25zdCBnaXZlbkRhdGUgPSBuZXcgRGF0ZShkYXRlU3RyaW5nKTsKICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKTsKICAgICAgcmV0dXJuIGdpdmVuRGF0ZSA8IG5vdzsKICAgIH0sCgogICAgLy8g5YiH5o2i6KGo5qC86auY5bqm5qih5byPCiAgICB0b2dnbGVUYWJsZUhlaWdodE1vZGUoKSB7CiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5YiH5o2i6KGo5qC86auY5bqm5qih5byPID09PScpOwogICAgICBjb25zb2xlLmxvZygn5YiH5o2i5YmNOicsIHRoaXMudXNlVmlld3BvcnRIZWlnaHQgPyAn6KeG5Y+j5qih5byPJyA6ICfooYzmlbDmqKHlvI8nKTsKICAgICAgY29uc29sZS5sb2coJ+W9k+WJjeihqOagvOmrmOW6pjonLCB0aGlzLnRhYmxlSGVpZ2h0KTsKICAgICAgdGhpcy51c2VWaWV3cG9ydEhlaWdodCA9ICF0aGlzLnVzZVZpZXdwb3J0SGVpZ2h0OwogICAgICBjb25zb2xlLmxvZygn5YiH5o2i5ZCOOicsIHRoaXMudXNlVmlld3BvcnRIZWlnaHQgPyAn6KeG5Y+j5qih5byPJyA6ICfooYzmlbDmqKHlvI8nKTsgLy8g56uL5Y2z6YeN5paw6K6h566X6KGo5qC86auY5bqmCgogICAgICB0aGlzLnVwZGF0ZVRhYmxlSGVpZ2h0KCk7IC8vIOaYvuekuuWIh+aNouaIkOWKn+a2iOaBrwoKICAgICAgTWVzc2FnZSh7CiAgICAgICAgbWVzc2FnZTogYOW3suWIh+aNouWIsCR7dGhpcy51c2VWaWV3cG9ydEhlaWdodCA/ICfop4blj6Ppq5jluqbmqKHlvI8nIDogJ+aVsOaNruihjOaVsOaooeW8jyd9YCwKICAgICAgICB0eXBlOiAnc3VjY2VzcycsCiAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOabtOaWsOihqOagvOmrmOW6pgogICAgdXBkYXRlVGFibGVIZWlnaHQoKSB7CiAgICAgIGlmICh0aGlzLmlzUmVzaXppbmcpIHsKICAgICAgICBjb25zb2xlLmxvZygn6Lez6L+HdXBkYXRlVGFibGVIZWlnaHTvvIzmraPlnKhyZXNpemXkuK0nKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCc9PT0gdXBkYXRlVGFibGVIZWlnaHQg6KKr6LCD55SoID09PScpOwogICAgICBjb25zb2xlLmxvZygn5b2T5YmN5qih5byPOicsIHRoaXMudXNlVmlld3BvcnRIZWlnaHQgPyAn6KeG5Y+j5qih5byPJyA6ICfooYzmlbDmqKHlvI8nKTsKICAgICAgY29uc29sZS5sb2coJ3RhYmxlTGlzdC5sZW5ndGg6JywgdGhpcy50YWJsZUxpc3QgPyB0aGlzLnRhYmxlTGlzdC5sZW5ndGggOiAndW5kZWZpbmVkJyk7CiAgICAgIGxldCBuZXdIZWlnaHQ7CgogICAgICBpZiAoIXRoaXMudGFibGVMaXN0IHx8IHRoaXMudGFibGVMaXN0Lmxlbmd0aCA9PT0gMCkgewogICAgICAgIG5ld0hlaWdodCA9IHRoaXMubWluVGFibGVIZWlnaHQ7CiAgICAgICAgY29uc29sZS5sb2coJ+S9v+eUqOacgOWwj+mrmOW6pjonLCBuZXdIZWlnaHQpOwogICAgICB9IGVsc2UgaWYgKHRoaXMudXNlVmlld3BvcnRIZWlnaHQpIHsKICAgICAgICAvLyDln7rkuo7op4blj6Ppq5jluqbnmoTlk43lupTlvI/orqHnrpcKICAgICAgICBjb25zdCBhdmFpbGFibGVIZWlnaHQgPSB0aGlzLndpbmRvd0hlaWdodCAqIDAuMjU7CiAgICAgICAgbmV3SGVpZ2h0ID0gTWF0aC5taW4oTWF0aC5tYXgoYXZhaWxhYmxlSGVpZ2h0LCB0aGlzLm1pblRhYmxlSGVpZ2h0KSwgdGhpcy5tYXhUYWJsZUhlaWdodCk7CiAgICAgICAgY29uc29sZS5sb2coJ+inhuWPo+aooeW8j+iuoeeulzonLCB7CiAgICAgICAgICB3aW5kb3dIZWlnaHQ6IHRoaXMud2luZG93SGVpZ2h0LAogICAgICAgICAgYXZhaWxhYmxlSGVpZ2h0LAogICAgICAgICAgbmV3SGVpZ2h0CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgLy8g5Z+65LqO5pWw5o2u6KGM5pWw6K6h566X6auY5bqmIC0g5pm66IO96K6h566XCiAgICAgICAgbmV3SGVpZ2h0ID0gdGhpcy5jYWxjdWxhdGVTbWFydFRhYmxlSGVpZ2h0KCk7CiAgICAgICAgY29uc29sZS5sb2coJ+ihjOaVsOaooeW8j+iuoeeul+e7k+aenDonLCB7CiAgICAgICAgICBkYXRhUm93czogdGhpcy50YWJsZUxpc3QubGVuZ3RoLAogICAgICAgICAgcm93SGVpZ2h0OiB0aGlzLnJvd0hlaWdodCwKICAgICAgICAgIGNhbGN1bGF0ZWRIZWlnaHQ6IG5ld0hlaWdodAogICAgICAgIH0pOwogICAgICB9CgogICAgICBjb25zb2xlLmxvZygn5pen6auY5bqmOicsIHRoaXMudGFibGVIZWlnaHQsICfmlrDpq5jluqY6JywgbmV3SGVpZ2h0KTsgLy8g5Y+q5pyJ6auY5bqm55yf55qE5pS55Y+Y5pe25omN5pu05pawCgogICAgICBpZiAoTWF0aC5hYnModGhpcy50YWJsZUhlaWdodCAtIG5ld0hlaWdodCkgPiAxKSB7CiAgICAgICAgdGhpcy50YWJsZUhlaWdodCA9IG5ld0hlaWdodDsKICAgICAgICB0aGlzLnRhYmxlSGVpZ2h0S2V5Kys7IC8vIOW8uuWItumHjeaWsOa4suafkwoKICAgICAgICBjb25zb2xlLmxvZygn6auY5bqm5bey5pu05paw5YiwOicsIHRoaXMudGFibGVIZWlnaHQsICdLZXk6JywgdGhpcy50YWJsZUhlaWdodEtleSk7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy5mb3JjZVRhYmxlSGVpZ2h0RE9NKG5ld0hlaWdodCk7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coJ+mrmOW6puaXoOWPmOWMlu+8jOi3s+i/h+abtOaWsCcpOwogICAgICB9CiAgICB9LAoKICAgIC8vIOaZuuiDveiuoeeul+ihqOagvOmrmOW6pgogICAgY2FsY3VsYXRlU21hcnRUYWJsZUhlaWdodCgpIHsKICAgICAgLy8g5Z+656GA6K6h566XCiAgICAgIGNvbnN0IGhlYWRlckhlaWdodCA9IDQwOyAvLyDooajlpLTpq5jluqYKCiAgICAgIGNvbnN0IGJvcmRlclBhZGRpbmcgPSA0OyAvLyDovrnmoYblkozlhoXovrnot50KCiAgICAgIGNvbnN0IGV4dHJhU3BhY2UgPSAxMjsgLy8g6aKd5aSW56m66Ze077yM56Gu5L+d5a6M5pW05pi+56S6CiAgICAgIC8vIOWmguaenOihqOagvOW3sue7j+a4suafk++8jOWwneivleajgOa1i+WunumZheWwuuWvuAoKICAgICAgaWYgKHRoaXMuJHJlZnMuVG9wVGFiZWwgJiYgdGhpcy50YWJsZUxpc3QubGVuZ3RoID4gMCkgewogICAgICAgIGNvbnN0IHRhYmxlRWwgPSB0aGlzLiRyZWZzLlRvcFRhYmVsLiRlbDsKICAgICAgICBjb25zdCBoZWFkZXJFbCA9IHRhYmxlRWwucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19oZWFkZXInKTsKICAgICAgICBjb25zdCBmaXJzdFJvdyA9IHRhYmxlRWwucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5IHRyJyk7CiAgICAgICAgbGV0IGFjdHVhbEhlYWRlckhlaWdodCA9IGhlYWRlckhlaWdodDsKICAgICAgICBsZXQgYWN0dWFsUm93SGVpZ2h0ID0gdGhpcy5yb3dIZWlnaHQ7CgogICAgICAgIGlmIChoZWFkZXJFbCkgewogICAgICAgICAgYWN0dWFsSGVhZGVySGVpZ2h0ID0gaGVhZGVyRWwub2Zmc2V0SGVpZ2h0OwogICAgICAgICAgY29uc29sZS5sb2coJ+ajgOa1i+WIsOeahOWunumZheihqOWktOmrmOW6pjonLCBhY3R1YWxIZWFkZXJIZWlnaHQpOwogICAgICAgIH0KCiAgICAgICAgaWYgKGZpcnN0Um93KSB7CiAgICAgICAgICBhY3R1YWxSb3dIZWlnaHQgPSBmaXJzdFJvdy5vZmZzZXRIZWlnaHQ7CiAgICAgICAgICBjb25zb2xlLmxvZygn5qOA5rWL5Yiw55qE5a6e6ZmF6KGM6auYOicsIGFjdHVhbFJvd0hlaWdodCk7IC8vIOabtOaWsOihjOmrmOmFjee9rgoKICAgICAgICAgIGlmIChhY3R1YWxSb3dIZWlnaHQgPiAwICYmIE1hdGguYWJzKGFjdHVhbFJvd0hlaWdodCAtIHRoaXMucm93SGVpZ2h0KSA+IDIpIHsKICAgICAgICAgICAgdGhpcy5yb3dIZWlnaHQgPSBhY3R1YWxSb3dIZWlnaHQ7CiAgICAgICAgICB9CiAgICAgICAgfQoKICAgICAgICBjb25zdCBjYWxjdWxhdGVkSGVpZ2h0ID0gYWN0dWFsSGVhZGVySGVpZ2h0ICsgdGhpcy50YWJsZUxpc3QubGVuZ3RoICogYWN0dWFsUm93SGVpZ2h0ICsgYm9yZGVyUGFkZGluZyArIGV4dHJhU3BhY2U7CiAgICAgICAgY29uc29sZS5sb2coJ+aZuuiDveiuoeeul+ivpuaDhTonLCB7CiAgICAgICAgICBhY3R1YWxIZWFkZXJIZWlnaHQsCiAgICAgICAgICBhY3R1YWxSb3dIZWlnaHQsCiAgICAgICAgICBkYXRhUm93czogdGhpcy50YWJsZUxpc3QubGVuZ3RoLAogICAgICAgICAgYm9yZGVyUGFkZGluZywKICAgICAgICAgIGV4dHJhU3BhY2UsCiAgICAgICAgICBjYWxjdWxhdGVkSGVpZ2h0CiAgICAgICAgfSk7CiAgICAgICAgcmV0dXJuIE1hdGgubWluKE1hdGgubWF4KGNhbGN1bGF0ZWRIZWlnaHQsIHRoaXMubWluVGFibGVIZWlnaHQpLCB0aGlzLm1heFRhYmxlSGVpZ2h0KTsKICAgICAgfSAvLyDlm57pgIDliLDln7rnoYDorqHnrpcKCgogICAgICBjb25zdCBiYXNpY0hlaWdodCA9IGhlYWRlckhlaWdodCArIHRoaXMudGFibGVMaXN0Lmxlbmd0aCAqIHRoaXMucm93SGVpZ2h0ICsgYm9yZGVyUGFkZGluZyArIGV4dHJhU3BhY2U7CiAgICAgIGNvbnNvbGUubG9nKCfkvb/nlKjln7rnoYDorqHnrpc6JywgYmFzaWNIZWlnaHQpOwogICAgICByZXR1cm4gTWF0aC5taW4oTWF0aC5tYXgoYmFzaWNIZWlnaHQsIHRoaXMubWluVGFibGVIZWlnaHQpLCB0aGlzLm1heFRhYmxlSGVpZ2h0KTsKICAgIH0sCgogICAgLy8g5qOA5rWL5a6e6ZmF6KGM6auYCiAgICBkZXRlY3RBY3R1YWxSb3dIZWlnaHQoKSB7CiAgICAgIGlmICh0aGlzLiRyZWZzLlRvcFRhYmVsICYmIHRoaXMudGFibGVMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICBjb25zdCB0YWJsZUVsID0gdGhpcy4kcmVmcy5Ub3BUYWJlbC4kZWw7CiAgICAgICAgY29uc3QgZmlyc3RSb3cgPSB0YWJsZUVsLnF1ZXJ5U2VsZWN0b3IoJy5lbC10YWJsZV9fYm9keSB0cicpOwoKICAgICAgICBpZiAoZmlyc3RSb3cpIHsKICAgICAgICAgIGNvbnN0IGFjdHVhbFJvd0hlaWdodCA9IGZpcnN0Um93Lm9mZnNldEhlaWdodDsKICAgICAgICAgIGNvbnNvbGUubG9nKCfmo4DmtYvliLDnmoTlrp7pmYXooYzpq5g6JywgYWN0dWFsUm93SGVpZ2h0KTsKCiAgICAgICAgICBpZiAoYWN0dWFsUm93SGVpZ2h0ID4gMCAmJiBNYXRoLmFicyhhY3R1YWxSb3dIZWlnaHQgLSB0aGlzLnJvd0hlaWdodCkgPiA1KSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfmm7TmlrDooYzpq5jku44nLCB0aGlzLnJvd0hlaWdodCwgJ+WIsCcsIGFjdHVhbFJvd0hlaWdodCk7CiAgICAgICAgICAgIHRoaXMucm93SGVpZ2h0ID0gYWN0dWFsUm93SGVpZ2h0OwogICAgICAgICAgICByZXR1cm4gYWN0dWFsUm93SGVpZ2h0OwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfQoKICAgICAgcmV0dXJuIHRoaXMucm93SGVpZ2h0OwogICAgfSwKCiAgICAvLyDlvLrliLborr7nva5ET03pq5jluqYKICAgIGZvcmNlVGFibGVIZWlnaHRET00oaGVpZ2h0KSB7CiAgICAgIGNvbnNvbGUubG9nKCc9PT0g5by65Yi26K6+572uRE9N6auY5bqmID09PScsIGhlaWdodCk7CgogICAgICBpZiAodGhpcy4kcmVmcy5Ub3BUYWJlbCkgewogICAgICAgIGNvbnN0IHRhYmxlQ29tcG9uZW50ID0gdGhpcy4kcmVmcy5Ub3BUYWJlbDsKICAgICAgICBjb25zdCB0YWJsZUVsID0gdGFibGVDb21wb25lbnQuJGVsOwogICAgICAgIGNvbnNvbGUubG9nKCfooajmoLznu4Tku7Y6JywgdGFibGVDb21wb25lbnQpOwogICAgICAgIGNvbnNvbGUubG9nKCfooajmoLxET03lhYPntKA6JywgdGFibGVFbCk7IC8vIOajgOa1i+WunumZheihjOmrmAoKICAgICAgICB0aGlzLmRldGVjdEFjdHVhbFJvd0hlaWdodCgpOyAvLyDmlrnms5UxOiDnm7TmjqXorr7nva7ooajmoLzlhYPntKDmoLflvI8KCiAgICAgICAgdGFibGVFbC5zdHlsZS5oZWlnaHQgPSBoZWlnaHQgKyAncHgnOwogICAgICAgIHRhYmxlRWwuc3R5bGUubWF4SGVpZ2h0ID0gaGVpZ2h0ICsgJ3B4JzsKICAgICAgICB0YWJsZUVsLnN0eWxlLm1pbkhlaWdodCA9IGhlaWdodCArICdweCc7IC8vIOaWueazlTI6IOiuvue9ruihqOagvOWGhemDqOWuueWZqAoKICAgICAgICBjb25zdCBib2R5V3JhcHBlciA9IHRhYmxlRWwucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5LXdyYXBwZXInKTsKCiAgICAgICAgaWYgKGJvZHlXcmFwcGVyKSB7CiAgICAgICAgICBib2R5V3JhcHBlci5zdHlsZS5tYXhIZWlnaHQgPSBoZWlnaHQgLSA0MCArICdweCc7CiAgICAgICAgICBjb25zb2xlLmxvZygn6K6+572uYm9keSB3cmFwcGVy6auY5bqmOicsIGhlaWdodCAtIDQwICsgJ3B4Jyk7CiAgICAgICAgfSAvLyDmlrnms5UzOiDosIPnlKhFbGVtZW50IFVJ55qE5YaF6YOo5pa55rOVCgoKICAgICAgICBpZiAodGFibGVDb21wb25lbnQuZG9MYXlvdXQpIHsKICAgICAgICAgIHRhYmxlQ29tcG9uZW50LmRvTGF5b3V0KCk7CiAgICAgICAgfSAvLyDpqozor4Horr7nva7nu5PmnpwKCgogICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgY29uc29sZS5sb2coJ0RPTemqjOivgSAtIOihqOagvOmrmOW6pjonLCB0YWJsZUVsLm9mZnNldEhlaWdodCk7CiAgICAgICAgICBjb25zb2xlLmxvZygnRE9N6aqM6K+BIC0g5qC35byP6auY5bqmOicsIHRhYmxlRWwuc3R5bGUuaGVpZ2h0KTsKICAgICAgICAgIGNvbnNvbGUubG9nKCdET03pqozor4EgLSDorqHnrpfmoLflvI86Jywgd2luZG93LmdldENvbXB1dGVkU3R5bGUodGFibGVFbCkuaGVpZ2h0KTsKCiAgICAgICAgICBpZiAoYm9keVdyYXBwZXIpIHsKICAgICAgICAgICAgY29uc29sZS5sb2coJ0RPTemqjOivgSAtIGJvZHkgd3JhcHBlcumrmOW6pjonLCBib2R5V3JhcHBlci5vZmZzZXRIZWlnaHQpOwogICAgICAgICAgICBjb25zb2xlLmxvZygnRE9N6aqM6K+BIC0gYm9keSB3cmFwcGVy5qC35byPOicsIGJvZHlXcmFwcGVyLnN0eWxlLm1heEhlaWdodCk7CiAgICAgICAgICB9IC8vIOajgOafpeaYr+WQpuaciea7muWKqOadoQoKCiAgICAgICAgICBjb25zdCBoYXNWZXJ0aWNhbFNjcm9sbGJhciA9IGJvZHlXcmFwcGVyICYmIGJvZHlXcmFwcGVyLnNjcm9sbEhlaWdodCA+IGJvZHlXcmFwcGVyLmNsaWVudEhlaWdodDsKICAgICAgICAgIGNvbnNvbGUubG9nKCfmmK/lkKbmnInlnoLnm7Tmu5rliqjmnaE6JywgaGFzVmVydGljYWxTY3JvbGxiYXIpOwoKICAgICAgICAgIGlmIChoYXNWZXJ0aWNhbFNjcm9sbGJhcikgewogICAgICAgICAgICBjb25zb2xlLmxvZygn5YaF5a656auY5bqmOicsIGJvZHlXcmFwcGVyLnNjcm9sbEhlaWdodCwgJ+WPr+ingemrmOW6pjonLCBib2R5V3JhcHBlci5jbGllbnRIZWlnaHQpOwogICAgICAgICAgfQogICAgICAgIH0sIDEwMCk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgY29uc29sZS5sb2coJ+ihqOagvOW8leeUqOS4jeWtmOWcqCcpOwogICAgICB9CiAgICB9LAoKICAgIC8vIOaJi+WKqOiuvue9ruihqOagvOmrmOW6puWPguaVsAogICAgc2V0VGFibGVIZWlnaHRQYXJhbXMobWluSGVpZ2h0LCBtYXhIZWlnaHQsIHJvd0hlaWdodCkgewogICAgICB0aGlzLm1pblRhYmxlSGVpZ2h0ID0gbWluSGVpZ2h0IHx8IDE1MDsKICAgICAgdGhpcy5tYXhUYWJsZUhlaWdodCA9IG1heEhlaWdodCB8fCA0MDA7CiAgICAgIHRoaXMucm93SGVpZ2h0ID0gcm93SGVpZ2h0IHx8IDQwOwogICAgfSwKCiAgICAvLyDmtYvor5Xmlrnms5UgLSDlj6/ku6XlnKjmtY/op4jlmajmjqfliLblj7DosIPnlKgKICAgIHRlc3RUYWJsZUhlaWdodCgpIHsKICAgICAgY29uc29sZS5sb2coJz09PSDooajmoLzpq5jluqbmtYvor5UgPT09Jyk7CiAgICAgIGNvbnNvbGUubG9nKCflvZPliY3mqKHlvI86JywgdGhpcy51c2VWaWV3cG9ydEhlaWdodCA/ICfop4blj6PmqKHlvI8nIDogJ+ihjOaVsOaooeW8jycpOwogICAgICBjb25zb2xlLmxvZygn6KGo5qC85pWw5o2u6KGM5pWwOicsIHRoaXMudGFibGVMaXN0ID8gdGhpcy50YWJsZUxpc3QubGVuZ3RoIDogMCk7CiAgICAgIGNvbnNvbGUubG9nKCfnqpflj6Ppq5jluqY6JywgdGhpcy53aW5kb3dIZWlnaHQpOwogICAgICBjb25zb2xlLmxvZygn5b2T5YmN6KGo5qC86auY5bqmOicsIHRoaXMudGFibGVIZWlnaHQpOwogICAgICBjb25zb2xlLmxvZygn5pyA5bCP6auY5bqmOicsIHRoaXMubWluVGFibGVIZWlnaHQpOwogICAgICBjb25zb2xlLmxvZygn5pyA5aSn6auY5bqmOicsIHRoaXMubWF4VGFibGVIZWlnaHQpOwogICAgICBjb25zb2xlLmxvZygn6KGM6auYOicsIHRoaXMucm93SGVpZ2h0KTsKICAgICAgY29uc29sZS5sb2coJ+ihqOagvEtleTonLCB0aGlzLnRhYmxlSGVpZ2h0S2V5KTsgLy8g5qOA5p+l6KGo5qC8RE9N5YWD57SgCgogICAgICBpZiAodGhpcy4kcmVmcy5Ub3BUYWJlbCkgewogICAgICAgIGNvbnN0IHRhYmxlRWwgPSB0aGlzLiRyZWZzLlRvcFRhYmVsLiRlbDsKICAgICAgICBjb25zb2xlLmxvZygn6KGo5qC8RE9N6auY5bqmOicsIHRhYmxlRWwuc3R5bGUuaGVpZ2h0KTsKICAgICAgICBjb25zb2xlLmxvZygn6KGo5qC85a6e6ZmF6auY5bqmOicsIHRhYmxlRWwub2Zmc2V0SGVpZ2h0KTsKICAgICAgICBjb25zb2xlLmxvZygn6KGo5qC86K6h566X5qC35byPOicsIHdpbmRvdy5nZXRDb21wdXRlZFN0eWxlKHRhYmxlRWwpLmhlaWdodCk7CiAgICAgIH0KCiAgICAgIHJldHVybiB0aGlzLnRhYmxlSGVpZ2h0OwogICAgfSwKCiAgICAvLyDph43mlrDorqHnrpfpq5jluqYKICAgIHJlY2FsY3VsYXRlSGVpZ2h0KCkgewogICAgICBjb25zb2xlLmxvZygnPT09IOmHjeaWsOiuoeeul+mrmOW6piA9PT0nKTsgLy8g5by65Yi26YeN5paw5qOA5rWL6KGM6auYCgogICAgICB0aGlzLmRldGVjdEFjdHVhbFJvd0hlaWdodCgpOyAvLyDph43mlrDorqHnrpflubblupTnlKjpq5jluqYKCiAgICAgIHRoaXMudXBkYXRlVGFibGVIZWlnaHQoKTsKICAgIH0sCgogICAgLy8g5by65Yi26K6+572u6KGo5qC86auY5bqmIC0g5rWL6K+V55SoCiAgICBmb3JjZVNldFRhYmxlSGVpZ2h0KGhlaWdodCkgewogICAgICBjb25zb2xlLmxvZygnPT09IOW8uuWItuiuvue9ruihqOagvOmrmOW6piA9PT0nLCBoZWlnaHQpOwogICAgICB0aGlzLnRhYmxlSGVpZ2h0ID0gaGVpZ2h0OwogICAgICB0aGlzLnRhYmxlSGVpZ2h0S2V5Kys7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLmZvcmNlVGFibGVIZWlnaHRET00oaGVpZ2h0KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOe7iOaegURPTeaTjeS9nOaWueazlQogICAgdWx0aW1hdGVGb3JjZUhlaWdodChoZWlnaHQpIHsKICAgICAgY29uc29sZS5sb2coJz09PSDnu4jmnoHlvLrliLbpq5jluqborr7nva4gPT09JywgaGVpZ2h0KTsgLy8gMS4g5pu05pawVnVl5pWw5o2uCgogICAgICB0aGlzLnRhYmxlSGVpZ2h0ID0gaGVpZ2h0OwogICAgICB0aGlzLnRhYmxlSGVpZ2h0S2V5Kys7IC8vIDIuIOW8uuWItuabtOaWsOe7hOS7tgoKICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsgLy8gMy4g562J5b6FRE9N5pu05paw5ZCO5pON5L2cCgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgaWYgKHRoaXMuJHJlZnMuVG9wVGFiZWwpIHsKICAgICAgICAgIGNvbnN0IHRhYmxlQ29tcG9uZW50ID0gdGhpcy4kcmVmcy5Ub3BUYWJlbDsKICAgICAgICAgIGNvbnN0IHRhYmxlRWwgPSB0YWJsZUNvbXBvbmVudC4kZWw7IC8vIDQuIOiuvue9ruaJgOacieWPr+iDveeahOmrmOW6puWxnuaApwoKICAgICAgICAgIHRhYmxlRWwuc3R5bGUuc2V0UHJvcGVydHkoJ2hlaWdodCcsIGhlaWdodCArICdweCcsICdpbXBvcnRhbnQnKTsKICAgICAgICAgIHRhYmxlRWwuc3R5bGUuc2V0UHJvcGVydHkoJ21heC1oZWlnaHQnLCBoZWlnaHQgKyAncHgnLCAnaW1wb3J0YW50Jyk7CiAgICAgICAgICB0YWJsZUVsLnN0eWxlLnNldFByb3BlcnR5KCdtaW4taGVpZ2h0JywgaGVpZ2h0ICsgJ3B4JywgJ2ltcG9ydGFudCcpOyAvLyA1LiDorr7nva5DU1Plj5jph48KCiAgICAgICAgICB0YWJsZUVsLnN0eWxlLnNldFByb3BlcnR5KCctLXRhYmxlLWhlaWdodCcsIGhlaWdodCArICdweCcpOwogICAgICAgICAgdGFibGVFbC5zdHlsZS5zZXRQcm9wZXJ0eSgnLS1keW5hbWljLXRhYmxlLWhlaWdodCcsIGhlaWdodCArICdweCcpOyAvLyA2LiDorr7nva7niLblrrnlmahDU1Plj5jph48KCiAgICAgICAgICBjb25zdCBjb250YWluZXIgPSB0YWJsZUVsLmNsb3Nlc3QoJy5hdmFpbGFibGUtaW52ZW50b3J5LWNvbnRhaW5lcicpOwoKICAgICAgICAgIGlmIChjb250YWluZXIpIHsKICAgICAgICAgICAgY29udGFpbmVyLnN0eWxlLnNldFByb3BlcnR5KCctLXRhYmxlLWhlaWdodCcsIGhlaWdodCArICdweCcpOwogICAgICAgICAgICBjb250YWluZXIuc3R5bGUuc2V0UHJvcGVydHkoJy0tZHluYW1pYy10YWJsZS1oZWlnaHQnLCBoZWlnaHQgKyAncHgnKTsKICAgICAgICAgIH0gLy8gNy4g6K6+572u5YaF6YOo5YWD57SgCgoKICAgICAgICAgIGNvbnN0IGJvZHlXcmFwcGVyID0gdGFibGVFbC5xdWVyeVNlbGVjdG9yKCcuZWwtdGFibGVfX2JvZHktd3JhcHBlcicpOwoKICAgICAgICAgIGlmIChib2R5V3JhcHBlcikgewogICAgICAgICAgICBib2R5V3JhcHBlci5zdHlsZS5zZXRQcm9wZXJ0eSgnbWF4LWhlaWdodCcsIGhlaWdodCAtIDQwICsgJ3B4JywgJ2ltcG9ydGFudCcpOwogICAgICAgICAgfSAvLyA4LiDosIPnlKhFbGVtZW50IFVJ5pa55rOVCgoKICAgICAgICAgIGlmICh0YWJsZUNvbXBvbmVudC5kb0xheW91dCkgewogICAgICAgICAgICB0YWJsZUNvbXBvbmVudC5kb0xheW91dCgpOwogICAgICAgICAgfSAvLyA5LiDop6blj5FyZXNpemXkuovku7bvvIjpmLLmraLmrbvlvqrnjq/vvIkKCgogICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7CiAgICAgICAgICAgIHRoaXMuaXNSZXNpemluZyA9IHRydWU7CiAgICAgICAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBFdmVudCgncmVzaXplJykpOwogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsKICAgICAgICAgICAgICB0aGlzLmlzUmVzaXppbmcgPSBmYWxzZTsKICAgICAgICAgICAgfSwgMTAwKTsgLy8gMTAuIOacgOe7iOmqjOivgQoKICAgICAgICAgICAgY29uc29sZS5sb2coJz09PSDmnIDnu4jpqozor4Hnu5PmnpwgPT09Jyk7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfooajmoLxvZmZzZXRIZWlnaHQ6JywgdGFibGVFbC5vZmZzZXRIZWlnaHQpOwogICAgICAgICAgICBjb25zb2xlLmxvZygn6KGo5qC8c3R5bGUuaGVpZ2h0OicsIHRhYmxlRWwuc3R5bGUuaGVpZ2h0KTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ+ihqOagvOiuoeeul+agt+W8jzonLCB3aW5kb3cuZ2V0Q29tcHV0ZWRTdHlsZSh0YWJsZUVsKS5oZWlnaHQpOwoKICAgICAgICAgICAgaWYgKGJvZHlXcmFwcGVyKSB7CiAgICAgICAgICAgICAgY29uc29sZS5sb2coJ2JvZHkgd3JhcHBlciBvZmZzZXRIZWlnaHQ6JywgYm9keVdyYXBwZXIub2Zmc2V0SGVpZ2h0KTsKICAgICAgICAgICAgICBjb25zb2xlLmxvZygnYm9keSB3cmFwcGVyIHN0eWxlOicsIGJvZHlXcmFwcGVyLnN0eWxlLm1heEhlaWdodCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIDIwMCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgLy8gPT09IOS7peS4i+S4uua1i+ivleaWueazle+8jOa1i+ivleWujOaIkOWQjuWIoOmZpCA9PT0KICAgIC8vIOeUn+aIkOa1i+ivleaVsOaNrgogICAgZ2VuZXJhdGVUZXN0RGF0YSgpIHsKICAgICAgY29uc3QgdGVzdERhdGEgPSB7CiAgICAgICAgSUQ6IGBURVNUXyR7Kyt0aGlzLnRlc3REYXRhQ291bnRlcn1gLAogICAgICAgIFNiU3NjYzogYFNTQ0Mke1N0cmluZyh0aGlzLnRlc3REYXRhQ291bnRlcikucGFkU3RhcnQoNCwgJzAnKX1gLAogICAgICAgIExCYXRjaDogYEJBVENIJHtTdHJpbmcodGhpcy50ZXN0RGF0YUNvdW50ZXIpLnBhZFN0YXJ0KDMsICcwJyl9YCwKICAgICAgICBNYXRlcmlhbENvZGU6IGBNQVQke1N0cmluZyh0aGlzLnRlc3REYXRhQ291bnRlciAlIDUgKyAxKS5wYWRTdGFydCgzLCAnMCcpfWAsCiAgICAgICAgTWF0ZXJpYWxOYW1lOiBg5rWL6K+V54mp5paZJHt0aGlzLnRlc3REYXRhQ291bnRlcn1gLAogICAgICAgIEluUXVhbnRpdHk6IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwMDApICsgMTAwLAogICAgICAgIE1hdGVyaWFsVW5pdDE6ICdrZycsCiAgICAgICAgTFN0YXR1czogTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMykgKyAxLAogICAgICAgIFNiU3RhdHVzOiBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAzKSArIDEsCiAgICAgICAgRXhwaXJhdGlvbkRhdGU6IG5ldyBEYXRlKERhdGUubm93KCkgKyBNYXRoLnJhbmRvbSgpICogMzY1ICogMjQgKiA2MCAqIDYwICogMTAwMCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLAogICAgICAgIExvY2F0aW9uOiBgQSR7U3RyaW5nKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDEwKSArIDEpLnBhZFN0YXJ0KDIsICcwJyl9LSR7U3RyaW5nKE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIDIwKSArIDEpLnBhZFN0YXJ0KDIsICcwJyl9YAogICAgICB9OwogICAgICByZXR1cm4gdGVzdERhdGE7CiAgICB9LAoKICAgIC8vIOa3u+WKoOa1i+ivleaVsOaNrgogICAgYWRkVGVzdERhdGEoKSB7CiAgICAgIC8vIOesrOS4gOasoea3u+WKoOaXtuS/neWtmOWOn+Wni+aVsOaNrgogICAgICBpZiAodGhpcy5vcmlnaW5hbFRhYmxlTGlzdC5sZW5ndGggPT09IDAgJiYgdGhpcy50YWJsZUxpc3QubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMub3JpZ2luYWxUYWJsZUxpc3QgPSBbLi4udGhpcy50YWJsZUxpc3RdOwogICAgICB9CgogICAgICBjb25zdCBuZXdEYXRhID0gdGhpcy5nZW5lcmF0ZVRlc3REYXRhKCk7CiAgICAgIHRoaXMudGFibGVMaXN0LnB1c2gobmV3RGF0YSk7CiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlSGVpZ2h0KCk7CiAgICAgIH0pOwogICAgICBNZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiBg5bey5re75Yqg5rWL6K+V5pWw5o2u77yM5b2T5YmN5YWxICR7dGhpcy50YWJsZUxpc3QubGVuZ3RofSDooYxgLAogICAgICAgIHR5cGU6ICdzdWNjZXNzJywKICAgICAgICBkdXJhdGlvbjogMjAwMAogICAgICB9KTsKICAgIH0sCgogICAgLy8g5Yig6Zmk5rWL6K+V5pWw5o2uCiAgICByZW1vdmVUZXN0RGF0YSgpIHsKICAgICAgaWYgKHRoaXMudGFibGVMaXN0Lmxlbmd0aCA+IDApIHsKICAgICAgICAvLyDkvJjlhYjliKDpmaTmtYvor5XmlbDmja4KICAgICAgICBjb25zdCB0ZXN0SW5kZXggPSB0aGlzLnRhYmxlTGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtLklEICYmIGl0ZW0uSUQuc3RhcnRzV2l0aCgnVEVTVF8nKSk7CgogICAgICAgIGlmICh0ZXN0SW5kZXggIT09IC0xKSB7CiAgICAgICAgICB0aGlzLnRhYmxlTGlzdC5zcGxpY2UodGVzdEluZGV4LCAxKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy50YWJsZUxpc3QucG9wKCk7CiAgICAgICAgfQoKICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgICB0aGlzLnVwZGF0ZVRhYmxlSGVpZ2h0KCk7CiAgICAgICAgfSk7CiAgICAgICAgTWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiBg5bey5Yig6Zmk5pWw5o2u77yM5b2T5YmN5YWxICR7dGhpcy50YWJsZUxpc3QubGVuZ3RofSDooYxgLAogICAgICAgICAgdHlwZTogJ3dhcm5pbmcnLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KTsKICAgICAgfSBlbHNlIHsKICAgICAgICBNZXNzYWdlKHsKICAgICAgICAgIG1lc3NhZ2U6ICfmsqHmnInmlbDmja7lj6/liKDpmaQnLAogICAgICAgICAgdHlwZTogJ2luZm8nLAogICAgICAgICAgZHVyYXRpb246IDIwMDAKICAgICAgICB9KTsKICAgICAgfQogICAgfSwKCiAgICAvLyDmmL7npLrmtYvor5Xkv6Hmga8KICAgIHNob3dUZXN0SW5mbygpIHsKICAgICAgY29uc3QgaW5mbyA9IGAKPT09IOihqOagvOiHqumAguW6lOmrmOW6pua1i+ivleS/oeaBryA9PT0K5b2T5YmN5qih5byPOiAke3RoaXMudXNlVmlld3BvcnRIZWlnaHQgPyAn6KeG5Y+j6auY5bqm5qih5byPJyA6ICfmlbDmja7ooYzmlbDmqKHlvI8nfQrmlbDmja7ooYzmlbA6ICR7dGhpcy50YWJsZUxpc3QubGVuZ3RofQrnqpflj6Ppq5jluqY6ICR7dGhpcy53aW5kb3dIZWlnaHR9cHgK6KGo5qC86auY5bqmOiAke3RoaXMudGFibGVIZWlnaHR9cHgK5pyA5bCP6auY5bqmOiAke3RoaXMubWluVGFibGVIZWlnaHR9cHgK5pyA5aSn6auY5bqmOiAke3RoaXMubWF4VGFibGVIZWlnaHR9cHgK6KGM6auY6K6+572uOiAke3RoaXMucm93SGVpZ2h0fXB4CgrmtYvor5Xor7TmmI46CjEuIOeCueWHuyLop4blj6PmqKHlvI8v6KGM5pWw5qih5byPIuaMiemSruWIh+aNouiuoeeul+aWueW8jwoyLiDngrnlh7siK+aVsOaNriLmjInpkq7mt7vliqDmtYvor5XmlbDmja7op4Llr5/pq5jluqblj5jljJYKMy4g54K55Ye7Ii3mlbDmja4i5oyJ6ZKu5Yig6Zmk5pWw5o2u6KeC5a+f6auY5bqm5Y+Y5YyWCjQuIOiwg+aVtOa1j+iniOWZqOeql+WPo+Wkp+Wwj+a1i+ivleWTjeW6lOW8j+aViOaenAo1LiDlnKjop4blj6PmqKHlvI/kuIvvvIzooajmoLzpq5jluqbkuLrnqpflj6Ppq5jluqbnmoQyNSUKNi4g5Zyo6KGM5pWw5qih5byP5LiL77yM6KGo5qC86auY5bqm5qC55o2u5pWw5o2u6KGM5pWw6K6h566XCiAgICAgICAgICAgIGA7CiAgICAgIGNvbnNvbGUubG9nKGluZm8pOwogICAgICB0aGlzLiRhbGVydChpbmZvLCAn5rWL6K+V5L+h5oGvJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICB0eXBlOiAnaW5mbycKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOaBouWkjeWOn+Wni+aVsOaNriAo5Y+v5Zyo5o6n5Yi25Y+w6LCD55SoKQogICAgcmVzdG9yZU9yaWdpbmFsRGF0YSgpIHsKICAgICAgaWYgKHRoaXMub3JpZ2luYWxUYWJsZUxpc3QubGVuZ3RoID4gMCkgewogICAgICAgIHRoaXMudGFibGVMaXN0ID0gWy4uLnRoaXMub3JpZ2luYWxUYWJsZUxpc3RdOwogICAgICAgIHRoaXMub3JpZ2luYWxUYWJsZUxpc3QgPSBbXTsKICAgICAgICB0aGlzLnRlc3REYXRhQ291bnRlciA9IDA7CiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgICAgdGhpcy51cGRhdGVUYWJsZUhlaWdodCgpOwogICAgICAgIH0pOwogICAgICAgIE1lc3NhZ2UoewogICAgICAgICAgbWVzc2FnZTogJ+W3suaBouWkjeWOn+Wni+aVsOaNricsCiAgICAgICAgICB0eXBlOiAnc3VjY2VzcycKICAgICAgICB9KTsKICAgICAgfQogICAgfSAvLyA9PT0g5rWL6K+V5pa55rOV57uT5p2fID09PQoKCiAgfQp9Ow=="}, {"version": 3, "mappings": "AA+QA;AACA;AACA;AACA,SACAA,6BADA,EAEAC,qBAFA,EAGAC,yBAHA,EAIAC,uBAJA,EAKAC,uBALA,EAMAC,yBANA,EAOAC,4BAPA,EAQAC,gBARA,EASAC,cATA,EAUAC,qBAVA,EAWAC,SAXA,QAYA,wCAZA;AAaA;AACA;AAEA;EACAC;IACAC,mDADA;IAEAC,mDAFA;IAGAC,6CAHA;IAIAC,qDAJA;IAKAC;EALA,CADA;;EAQAC;IACA;MACAC,WADA;MAEAC,iBAFA;MAGAC,iBAHA;MAIAC,uBAJA;MAKAC,mBALA;MAMAC,wBANA;MAOAC,qBAPA;MAQAC,oBARA;MASAC,aATA;MAUAC,QAVA;MAWAC,aAXA;MAYAC,oBAZA;MAaAC,cAbA;MAcAC,uCAdA;MAeAC,gCAfA;MAgBAC,OAhBA;MAiBAC,UAjBA;MAkBAC,gBAlBA;MAmBAC,gBAnBA;MAoBAC,iBApBA;MAqBAC,WArBA;MAsBAC,eAtBA;MAuBAC,kBAvBA;MAwBAC,mBAxBA;MAwBA;MACAC,mBAzBA;MAyBA;MACAC,aA1BA;MA0BA;MACAC,gCA3BA;MA2BA;MACAC,wBA5BA;MA4BA;MACAC,gBA7BA;MA6BA;MACAC,iBA9BA;MA8BA;MACAC,iBA/BA;MA+BA;MACA;MACAC,kBAjCA;MAkCAC,qBAlCA,CAkCA;;IAlCA;EAoCA,CA7CA;;EA8CAC;IACAC;IACAA;IACA;;IACA;MACA;IACA,CAFA,MAEA;MACA;IACA;;IACA;IACAA;IACA;IACAA;IACA;IACA;IACA;IACA,oBAhBA,CAiBA;;IACA;IACA;IACA;;IACA;MACA;MACA;IACA,CAHA,MAGA;MACA;IACA,CA1BA,CA4BA;;;IACA,wBA7BA,CA6BA;;IACA;MACA;QACAA;QACA;MACA;;MACA;MACAA;;MACA;QACA;MACA;IACA,CAVA;;IAWAC,qDAzCA,CA2CA;;IACAD;IACAA;IACAA;IACAA;IACAA,kFAhDA,CAkDA;;IACA;MACA;MACAA;IACA,CAHA;EAIA,CArGA;;EAsGAE;IACAD;EACA,CAxGA;;EAyGAE;IACA;IACAF;;IACA;MACAA;IACA;EACA,CA/GA;;EAgHAG;IACAC;MACAL;;MACA;QACAC;QACA;MACA,CAHA,MAGA;QACAA;QACA;MACA;IACA,CAVA;;IAWAK;MACA;QACA;QACAN;;QACA;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAO;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAF;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;QAvDA;;QAyDA;MACA;IACA,CA1EA;;IA2EA;MACA;QACA;MACA,CAFA,EADA,CAKA;MACA;MACA;MACA;MACA;;MAEA;QACAC,qBADA;QAEAC,sBAFA;QAGAC,+BAHA;QAIAC,2BAJA;QAKAC;MALA;MAQA;MACAP;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CApGA;;IAqGA;MACA;QACAM;MADA;MAGA;MACAC;QACAC;QACAA;QACAA;QACAA;MACA,CALA;MAMA;;MACA;QACA;MACA;IACA,CApHA;;IAqHAC;MACA;MACA;IACA,CAxHA;;IAyHA;MACA;QACAC,+BADA;QAEAC,4CAFA;QAGAC,qCAHA;QAIAV,sBAJA;QAKAW,YALA;QAMAC;MANA;MAQA;MACA;IACA,CApIA;;IAqIAC;MACA;QACA;MACA,CAFA;;MAGA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA,CAVA,MAUA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA;IACA,CA9JA;;IA+JAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;IACA,CAzKA;;IA0KAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;MACA;MACA1B;MACA;MACA;IACA,CAlMA;;IAmMA;MACA;;MACA;QACA;UACA;YACA2B,eADA;YAEAL,YAFA;YAGAX,sBAHA;YAIAY;UAJA,EADA,CAOA;;UACAK,wCARA,CAQA;QACA,CATA,MASA;UACA;UACA;YACAC,oBADA;YAEAC,yBAFA;YAGAT,uBAHA;YAIAC,YAJA;YAKAC;UALA;UAOAK,wCATA,CASA;QACA;MACA,CArBA,MAqBA;QACA;UACA;YACAD,eADA;YAEAhB,sBAFA;YAGAW,YAHA;YAIAC;UAJA;UAMAK,0CAPA,CAOA;QACA,CARA,MAQA;UACA;UACA;YACAP,uBADA;YAEAQ,oBAFA;YAGAC,yBAHA;YAIAR,YAJA;YAKAC;UALA;UAOAK,0CATA,CASA;QACA;MACA;;MAEA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;MAKA;MACA;;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;MACA;;MACA;QACA;QACA;MACA;IACA,CAnRA;;IAoRA;MACA;QACAf,2BADA;QAEAkB;MAFA;MAIA;;MACA;QACAxB;UACAC,kBADA;UAEAC;QAFA;MAIA,CALA,MAKA;QACA;;QACA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA,CAPA,MAOA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA;MACA;IACA,CAjTA;;IAkTAuB;MACAhC;MACA,iEAFA,CAGA;MACA;;MACA;QACA;MACA;;MACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;MACA,CAlBA,CAmBA;;;MACA;QACA;;QACA;UACA;YACAiC;UACA;QACA;;QACA;QACA;QACA;;QACA;UACA;UACA;UACA;QACA,CAJA,MAIA;UACA;UACA;;UACA;YACA;UACA,CAFA,MAEA;YACA;UACA;QACA;;QAEA;QACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA,CAzWA;;IA0WAC;MACA;MACA;MACA;MACA,2BAJA,CAKA;;MACA;;MACA;QACA;QACA;QACA;QACA;MACA;IACA,CAvXA;;IAwXA;MACA;QACAC,yCADA;QAEAC,6BAFA;QAGAC,kCAHA;QAIAC,sBAJA;QAKAjB,qCALA;QAMAkB;MANA;MAQA;MACAhC;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CAvYA;;IAwYA+B;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA;IACA,CAjZA;;IAkZA;MACA;QACAC,4CADA;QAEAC;MAFA;MAIA;MACA1C;MACA;;MACA;QACA;UACAC;;UACA;YACA;YACA;UACA;QACA;MACA,CARA,MAQA;QACA;MACA;IACA,CAraA;;IAsaA;MACA;MACA;QACAoB,qCADA;QAEAV,sBAFA;QAGAW,YAHA;QAIAC;MAJA;;MAMA;QACAK;MACA,CAFA,MAEA;QACAA;MACA;;MACA,mCAbA,CAcA;;MACA;QACA;MACA,CAFA;IAGA,CAxbA;;IAybAe;MACA;IACA,CA3bA;;IA4bAC;MACA;MACA;MACA;IACA,CAhcA;;IAicA;IACAC;MACA7C;MACAA;MACAA;MAEA;MAEAA,8DAPA,CASA;;MACA,yBAVA,CAYA;;MACAO;QACAC,8DADA;QAEAC,eAFA;QAGAqC;MAHA;IAKA,CApdA;;IAqdA;IACAC;MACA;QACA/C;QACA;MACA;;MAEAA;MACAA;MACAA;MAEA;;MAEA;QACAgD;QACAhD;MACA,CAHA,MAGA;QACA;QACA;QACAgD;QACAhD;UACAR,+BADA;UAEAyD,eAFA;UAGAD;QAHA;MAKA,CATA,MASA;QACA;QACAA;QAEAhD;UACAkD,+BADA;UAEA3D,yBAFA;UAGA4D;QAHA;MAKA;;MAEAnD,yDAnCA,CAqCA;;MACA;QACA;QACA,sBAFA,CAEA;;QACAA;QAEA;UACA;QACA,CAFA;MAGA,CARA,MAQA;QACAA;MACA;IACA,CAvgBA;;IAygBA;IACAoD;MACA;MACA,wBAFA,CAEA;;MACA,wBAHA,CAGA;;MACA,sBAJA,CAIA;MAEA;;MACA;QACA;QACA;QACA;QAEA;QACA;;QAEA;UACAC;UACArD;QACA;;QAEA;UACAsD;UACAtD,0CAFA,CAGA;;UACA;YACA;UACA;QACA;;QAEA;QAEAA;UACAqD,kBADA;UAEAC,eAFA;UAGAJ,+BAHA;UAIAK,aAJA;UAKAC,UALA;UAMAL;QANA;QASA;MACA,CAzCA,CA2CA;;;MACA;MACAnD;MACA;IACA,CAzjBA;;IA2jBA;IACAyD;MACA;QACA;QACA;;QACA;UACA;UACAzD;;UACA;YACAA;YACA;YACA;UACA;QACA;MACA;;MACA;IACA,CA3kBA;;IA6kBA;IACA0D;MACA1D;;MAEA;QACA;QACA;QAEAA;QACAA,iCALA,CAOA;;QACA,6BARA,CAUA;;QACA2D;QACAA;QACAA,wCAbA,CAeA;;QACA;;QACA;UACAC;UACA5D;QACA,CApBA,CAsBA;;;QACA;UACA6D;QACA,CAzBA,CA2BA;;;QACAC;UACA9D;UACAA;UACAA;;UAEA;YACAA;YACAA;UACA,CARA,CAUA;;;UACA;UACAA;;UACA;YACAA;UACA;QACA,CAhBA,EAgBA,GAhBA;MAkBA,CA9CA,MA8CA;QACAA;MACA;IACA,CAloBA;;IAmoBA;IACA+D;MACA;MACA;MACA;IACA,CAxoBA;;IAyoBA;IACAC;MACAhE;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA,2CATA,CAWA;;MACA;QACA;QACAA;QACAA;QACAA;MACA;;MAEA;IACA,CA9pBA;;IAgqBA;IACAiE;MACAjE,8BADA,CAEA;;MACA,6BAHA,CAIA;;MACA;IACA,CAvqBA;;IAyqBA;IACAkE;MACAlE;MACA;MACA;MAEA;QACA;MACA,CAFA;IAGA,CAlrBA;;IAorBA;IACAmE;MACAnE,wCADA,CAGA;;MACA;MACA,sBALA,CAOA;;MACA,oBARA,CAUA;;MACA;QACA;UACA;UACA,mCAFA,CAIA;;UACA2D;UACAA;UACAA,oEAPA,CASA;;UACAA;UACAA,mEAXA,CAaA;;UACA;;UACA;YACAS;YACAA;UACA,CAlBA,CAoBA;;;UACA;;UACA;YACAR;UACA,CAxBA,CA0BA;;;UACA;YACAC;UACA,CA7BA,CA+BA;;;UACAC;YACA;YACA7D;YACA6D;cACA;YACA,CAFA,EAEA,GAFA,EAHA,CAOA;;YACA9D;YACAA;YACAA;YACAA;;YAEA;cACAA;cACAA;YACA;UACA,CAjBA,EAiBA,GAjBA;QAkBA;MACA,CApDA;IAqDA,CArvBA;;IAuvBA;IAEA;IACAqE;MACA;QACA1C,oCADA;QAEA2C,8DAFA;QAGAC,+DAHA;QAIAC,2EAJA;QAKAC,2CALA;QAMAxC,kDANA;QAOAyC,mBAPA;QAQAC,0CARA;QASAC,2CATA;QAUAC,4GAVA;QAWAC;MAXA;MAaA;IACA,CAzwBA;;IA2wBA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;MAEA;QACA;MACA,CAFA;MAIAxE;QACAC,iDADA;QAEAC,eAFA;QAGAqC;MAHA;IAKA,CA9xBA;;IAgyBA;IACAkC;MACA;QACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;;QAEA;UACA;QACA,CAFA;QAIAzE;UACAC,+CADA;UAEAC,eAFA;UAGAqC;QAHA;MAKA,CAlBA,MAkBA;QACAvC;UACAC,kBADA;UAEAC,YAFA;UAGAqC;QAHA;MAKA;IACA,CA3zBA;;IA6zBA;IACAmC;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAjBA;MAmBAjF;MAEA;QACAkF,uBADA;QAEAzE;MAFA;IAIA,CAx1BA;;IA01BA;IACA0E;MACA;QACA;QACA;QACA;QAEA;UACA;QACA,CAFA;QAIA5E;UACAC,kBADA;UAEAC;QAFA;MAIA;IACA,CA12BA,CA42BA;;;EA52BA;AAhHA", "names": ["GetPageListNewMaterialPreDown", "GetPageListByMaterial", "GetPageListByBatchIDSByID", "GetPageListByMaterialII", "GetPageListByBatchIDSII", "GetPageListMaterialPreTop", "GetPageListNewMaterialPreTop", "GetConSelectList", "FirstAddPallet", "GetPageListByBatchIDS", "MygetSSCC", "components", "PartialBag", "FullAmount", "FullBag", "POInventory", "BatchPallets", "data", "PrintId", "printeroption", "PrintModel", "isExpirationDate", "tableId", "activeName", "OnlyFullAmount", "Hidecompleted", "<PERSON><PERSON><PERSON>", "room", "tableList", "tableListBatchPO", "SelectList", "headerBatchPO", "header", "way", "listId", "nowChooseRow", "MaterialList", "MaterialNow", "UseType", "clblFlag", "keepKeyDown", "minTableHeight", "maxTableHeight", "rowHeight", "windowHeight", "useViewportHeight", "tableHeight", "tableHeightKey", "isResizing", "testDataCounter", "originalTableList", "mounted", "console", "window", "beforeMount", "<PERSON><PERSON><PERSON><PERSON>", "methods", "openKeyDown", "getKeyDown", "Message", "message", "type", "printId", "EquipmentId", "BagSiZe", "MCode", "ids", "equipmentId", "res2", "item", "PrintAvallable", "BatchId", "ProOrderid", "MaterialId", "pageIndex", "pageSize", "GetSSCC", "EmptySscc", "ChangeMaterial", "ID", "res", "EqumentId", "ProId", "SSCC", "GetCurrentRow", "InQuantity", "GetCurrentRow2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UomID", "ProBatchID", "EquipMentID", "ProRequestID", "refresh", "proOrderID", "batchID", "back", "isDateInThePast", "toggleTableHeightMode", "duration", "updateTableHeight", "newHeight", "availableHeight", "dataRows", "calculatedHeight", "calculateSmartTableHeight", "actualHeaderHeight", "actualRowHeight", "borderPadding", "extraSpace", "detectActualRowHeight", "forceTableHeightDOM", "tableEl", "bodyWrapper", "tableComponent", "setTimeout", "setTableHeightParams", "testTableHeight", "recalculateHeight", "forceSetTableHeight", "ultimateForceHeight", "container", "generateTestData", "SbSscc", "LBatch", "MaterialCode", "MaterialName", "MaterialUnit1", "LStatus", "SbStatus", "ExpirationDate", "Location", "addTestData", "removeTestData", "showTestInfo", "confirmButtonText", "restoreOriginalData"], "sourceRoot": "src/views/Inventory/buildpalletsStart", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox available-inventory-container\" :style=\"{\r\n                '--table-height': tableHeight + 'px',\r\n                '--dynamic-table-height': tableHeight + 'px'\r\n            }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- 测试状态显示 - 测试完成后删除 -->\r\n                            <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                [{{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                                数据: {{ tableList.length }}行 |\r\n                                高度: {{ tableHeight }}px]\r\n                            </span>\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; flex-wrap: wrap; gap: 5px; align-items: center; z-index: 10;\">\r\n                            <!-- 打印按钮 -->\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n\r\n                            <!-- 模式切换按钮 - 始终显示 -->\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                size=\"small\"\r\n                                @click=\"toggleTableHeightMode()\"\r\n                                :title=\"useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\"\r\n                                style=\"min-width: 80px; background: #fff; border: 1px solid #ddd;\">\r\n                                {{ useViewportHeight ? '视口模式' : '行数模式' }}\r\n                            </el-button>\r\n\r\n                            <!-- 测试按钮组 - 测试完成后删除 -->\r\n                            <el-button size=\"mini\" type=\"success\" @click=\"addTestData()\" title=\"添加测试数据\" style=\"background: #67c23a; color: white; border: none;\">+数据</el-button>\r\n                            <el-button size=\"mini\" type=\"warning\" @click=\"removeTestData()\" title=\"删除测试数据\" style=\"background: #e6a23c; color: white; border: none;\">-数据</el-button>\r\n                            <el-button size=\"mini\" type=\"info\" @click=\"showTestInfo()\" title=\"显示测试信息\" style=\"background: #909399; color: white; border: none;\">信息</el-button>\r\n                            <el-button size=\"mini\" type=\"danger\" @click=\"forceSetTableHeight(300)\" title=\"强制设置300px\" style=\"background: #f56c6c; color: white; border: none;\">300px</el-button>\r\n                            <el-button size=\"mini\" type=\"danger\" @click=\"forceSetTableHeight(500)\" title=\"强制设置500px\" style=\"background: #f56c6c; color: white; border: none;\">500px</el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 测试控制面板 - 测试完成后删除 -->\r\n                <div style=\"background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px; padding: 10px; margin-bottom: 10px;\">\r\n                    <div style=\"display: flex; align-items: center; gap: 10px; flex-wrap: wrap;\">\r\n                        <span style=\"font-weight: bold; color: #0369a1;\">🧪 表格高度测试:</span>\r\n\r\n                        <el-button\r\n                            size=\"small\"\r\n                            @click=\"toggleTableHeightMode()\"\r\n                            :type=\"useViewportHeight ? 'primary' : 'default'\">\r\n                            {{ useViewportHeight ? '🖥️ 视口模式' : '📏 行数模式' }}\r\n                        </el-button>\r\n\r\n                        <el-button size=\"small\" type=\"success\" @click=\"addTestData()\">➕ 添加数据</el-button>\r\n                        <el-button size=\"small\" type=\"warning\" @click=\"removeTestData()\">➖ 删除数据</el-button>\r\n                        <el-button size=\"small\" type=\"info\" @click=\"showTestInfo()\">详细信息</el-button>\r\n                        <el-button size=\"small\" type=\"primary\" @click=\"recalculateHeight()\">重新计算</el-button>\r\n                        <el-button size=\"small\" type=\"danger\" @click=\"ultimateForceHeight(200)\">� 终极200px</el-button>\r\n                        <el-button size=\"small\" type=\"danger\" @click=\"ultimateForceHeight(400)\">� 终极400px</el-button>\r\n\r\n                        <span style=\"color: #64748b; font-size: 12px;\">\r\n                            当前: {{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                            数据: {{ tableList.length }}行 |\r\n                            高度: {{ tableHeight }}px\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :class=\"['dynamic-table', `height-${Math.floor(tableHeight)}`]\"\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px !important',\r\n                        maxHeight: tableHeight + 'px !important',\r\n                        minHeight: tableHeight + 'px !important'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableHeightKey\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n            minTableHeight: 150, // 最小表格高度\r\n            maxTableHeight: 400, // 最大表格高度\r\n            rowHeight: 48, // 每行的高度（调整为更准确的值）\r\n            windowHeight: window.innerHeight, // 窗口高度\r\n            useViewportHeight: false, // 是否使用视口高度模式\r\n            tableHeight: 200, // 当前表格高度\r\n            tableHeightKey: 0, // 强制重新渲染的key\r\n            isResizing: false, // 防止resize死循环\r\n            // 测试相关数据 - 测试完成后删除\r\n            testDataCounter: 0,\r\n            originalTableList: [] // 保存原始数据\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 添加窗口大小变化监听器\r\n        this.isResizing = false; // 防止死循环标志\r\n        this.handleResize = () => {\r\n            if (this.isResizing) {\r\n                console.log('跳过resize事件，防止死循环');\r\n                return;\r\n            }\r\n            this.windowHeight = window.innerHeight;\r\n            console.log('窗口大小变化:', this.windowHeight);\r\n            if (this.useViewportHeight) {\r\n                this.updateTableHeight();\r\n            }\r\n        };\r\n        window.addEventListener('resize', this.handleResize);\r\n\r\n        // 初始化日志\r\n        console.log('=== 表格自适应高度初始化 ===');\r\n        console.log('初始模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n        console.log('初始窗口高度:', this.windowHeight);\r\n        console.log('way变量值:', this.way);\r\n        console.log('tableList长度:', this.tableList ? this.tableList.length : 'undefined');\r\n\r\n        // 初始化表格高度\r\n        this.$nextTick(() => {\r\n            this.updateTableHeight();\r\n            console.log('=== 页面初始化完成 ===');\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n            // 数据加载完成后更新表格高度\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        },\r\n        // 切换表格高度模式\r\n        toggleTableHeightMode() {\r\n            console.log('=== 切换表格高度模式 ===');\r\n            console.log('切换前:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('当前表格高度:', this.tableHeight);\r\n\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n\r\n            console.log('切换后:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            // 立即重新计算表格高度\r\n            this.updateTableHeight();\r\n\r\n            // 显示切换成功消息\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n        // 更新表格高度\r\n        updateTableHeight() {\r\n            if (this.isResizing) {\r\n                console.log('跳过updateTableHeight，正在resize中');\r\n                return;\r\n            }\r\n\r\n            console.log('=== updateTableHeight 被调用 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('tableList.length:', this.tableList ? this.tableList.length : 'undefined');\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 基于视口高度的响应式计算\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 基于数据行数计算高度 - 智能计算\r\n                newHeight = this.calculateSmartTableHeight();\r\n\r\n                console.log('行数模式计算结果:', {\r\n                    dataRows: this.tableList.length,\r\n                    rowHeight: this.rowHeight,\r\n                    calculatedHeight: newHeight\r\n                });\r\n            }\r\n\r\n            console.log('旧高度:', this.tableHeight, '新高度:', newHeight);\r\n\r\n            // 只有高度真的改变时才更新\r\n            if (Math.abs(this.tableHeight - newHeight) > 1) {\r\n                this.tableHeight = newHeight;\r\n                this.tableHeightKey++; // 强制重新渲染\r\n                console.log('高度已更新到:', this.tableHeight, 'Key:', this.tableHeightKey);\r\n\r\n                this.$nextTick(() => {\r\n                    this.forceTableHeightDOM(newHeight);\r\n                });\r\n            } else {\r\n                console.log('高度无变化，跳过更新');\r\n            }\r\n        },\r\n\r\n        // 智能计算表格高度\r\n        calculateSmartTableHeight() {\r\n            // 基础计算\r\n            const headerHeight = 40; // 表头高度\r\n            const borderPadding = 4; // 边框和内边距\r\n            const extraSpace = 12; // 额外空间，确保完整显示\r\n\r\n            // 如果表格已经渲染，尝试检测实际尺寸\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                const headerEl = tableEl.querySelector('.el-table__header');\r\n                const firstRow = tableEl.querySelector('.el-table__body tr');\r\n\r\n                let actualHeaderHeight = headerHeight;\r\n                let actualRowHeight = this.rowHeight;\r\n\r\n                if (headerEl) {\r\n                    actualHeaderHeight = headerEl.offsetHeight;\r\n                    console.log('检测到的实际表头高度:', actualHeaderHeight);\r\n                }\r\n\r\n                if (firstRow) {\r\n                    actualRowHeight = firstRow.offsetHeight;\r\n                    console.log('检测到的实际行高:', actualRowHeight);\r\n                    // 更新行高配置\r\n                    if (actualRowHeight > 0 && Math.abs(actualRowHeight - this.rowHeight) > 2) {\r\n                        this.rowHeight = actualRowHeight;\r\n                    }\r\n                }\r\n\r\n                const calculatedHeight = actualHeaderHeight + (this.tableList.length * actualRowHeight) + borderPadding + extraSpace;\r\n\r\n                console.log('智能计算详情:', {\r\n                    actualHeaderHeight,\r\n                    actualRowHeight,\r\n                    dataRows: this.tableList.length,\r\n                    borderPadding,\r\n                    extraSpace,\r\n                    calculatedHeight\r\n                });\r\n\r\n                return Math.min(Math.max(calculatedHeight, this.minTableHeight), this.maxTableHeight);\r\n            }\r\n\r\n            // 回退到基础计算\r\n            const basicHeight = headerHeight + (this.tableList.length * this.rowHeight) + borderPadding + extraSpace;\r\n            console.log('使用基础计算:', basicHeight);\r\n            return Math.min(Math.max(basicHeight, this.minTableHeight), this.maxTableHeight);\r\n        },\r\n\r\n        // 检测实际行高\r\n        detectActualRowHeight() {\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                const firstRow = tableEl.querySelector('.el-table__body tr');\r\n                if (firstRow) {\r\n                    const actualRowHeight = firstRow.offsetHeight;\r\n                    console.log('检测到的实际行高:', actualRowHeight);\r\n                    if (actualRowHeight > 0 && Math.abs(actualRowHeight - this.rowHeight) > 5) {\r\n                        console.log('更新行高从', this.rowHeight, '到', actualRowHeight);\r\n                        this.rowHeight = actualRowHeight;\r\n                        return actualRowHeight;\r\n                    }\r\n                }\r\n            }\r\n            return this.rowHeight;\r\n        },\r\n\r\n        // 强制设置DOM高度\r\n        forceTableHeightDOM(height) {\r\n            console.log('=== 强制设置DOM高度 ===', height);\r\n\r\n            if (this.$refs.TopTabel) {\r\n                const tableComponent = this.$refs.TopTabel;\r\n                const tableEl = tableComponent.$el;\r\n\r\n                console.log('表格组件:', tableComponent);\r\n                console.log('表格DOM元素:', tableEl);\r\n\r\n                // 检测实际行高\r\n                this.detectActualRowHeight();\r\n\r\n                // 方法1: 直接设置表格元素样式\r\n                tableEl.style.height = height + 'px';\r\n                tableEl.style.maxHeight = height + 'px';\r\n                tableEl.style.minHeight = height + 'px';\r\n\r\n                // 方法2: 设置表格内部容器\r\n                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                if (bodyWrapper) {\r\n                    bodyWrapper.style.maxHeight = (height - 40) + 'px';\r\n                    console.log('设置body wrapper高度:', (height - 40) + 'px');\r\n                }\r\n\r\n                // 方法3: 调用Element UI的内部方法\r\n                if (tableComponent.doLayout) {\r\n                    tableComponent.doLayout();\r\n                }\r\n\r\n                // 验证设置结果\r\n                setTimeout(() => {\r\n                    console.log('DOM验证 - 表格高度:', tableEl.offsetHeight);\r\n                    console.log('DOM验证 - 样式高度:', tableEl.style.height);\r\n                    console.log('DOM验证 - 计算样式:', window.getComputedStyle(tableEl).height);\r\n\r\n                    if (bodyWrapper) {\r\n                        console.log('DOM验证 - body wrapper高度:', bodyWrapper.offsetHeight);\r\n                        console.log('DOM验证 - body wrapper样式:', bodyWrapper.style.maxHeight);\r\n                    }\r\n\r\n                    // 检查是否有滚动条\r\n                    const hasVerticalScrollbar = bodyWrapper && bodyWrapper.scrollHeight > bodyWrapper.clientHeight;\r\n                    console.log('是否有垂直滚动条:', hasVerticalScrollbar);\r\n                    if (hasVerticalScrollbar) {\r\n                        console.log('内容高度:', bodyWrapper.scrollHeight, '可见高度:', bodyWrapper.clientHeight);\r\n                    }\r\n                }, 100);\r\n\r\n            } else {\r\n                console.log('表格引用不存在');\r\n            }\r\n        },\r\n        // 手动设置表格高度参数\r\n        setTableHeightParams(minHeight, maxHeight, rowHeight) {\r\n            this.minTableHeight = minHeight || 150;\r\n            this.maxTableHeight = maxHeight || 400;\r\n            this.rowHeight = rowHeight || 40;\r\n        },\r\n        // 测试方法 - 可以在浏览器控制台调用\r\n        testTableHeight() {\r\n            console.log('=== 表格高度测试 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('表格数据行数:', this.tableList ? this.tableList.length : 0);\r\n            console.log('窗口高度:', this.windowHeight);\r\n            console.log('当前表格高度:', this.tableHeight);\r\n            console.log('最小高度:', this.minTableHeight);\r\n            console.log('最大高度:', this.maxTableHeight);\r\n            console.log('行高:', this.rowHeight);\r\n            console.log('表格Key:', this.tableHeightKey);\r\n\r\n            // 检查表格DOM元素\r\n            if (this.$refs.TopTabel) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                console.log('表格DOM高度:', tableEl.style.height);\r\n                console.log('表格实际高度:', tableEl.offsetHeight);\r\n                console.log('表格计算样式:', window.getComputedStyle(tableEl).height);\r\n            }\r\n\r\n            return this.tableHeight;\r\n        },\r\n\r\n        // 重新计算高度\r\n        recalculateHeight() {\r\n            console.log('=== 重新计算高度 ===');\r\n            // 强制重新检测行高\r\n            this.detectActualRowHeight();\r\n            // 重新计算并应用高度\r\n            this.updateTableHeight();\r\n        },\r\n\r\n        // 强制设置表格高度 - 测试用\r\n        forceSetTableHeight(height) {\r\n            console.log('=== 强制设置表格高度 ===', height);\r\n            this.tableHeight = height;\r\n            this.tableHeightKey++;\r\n\r\n            this.$nextTick(() => {\r\n                this.forceTableHeightDOM(height);\r\n            });\r\n        },\r\n\r\n        // 终极DOM操作方法\r\n        ultimateForceHeight(height) {\r\n            console.log('=== 终极强制高度设置 ===', height);\r\n\r\n            // 1. 更新Vue数据\r\n            this.tableHeight = height;\r\n            this.tableHeightKey++;\r\n\r\n            // 2. 强制更新组件\r\n            this.$forceUpdate();\r\n\r\n            // 3. 等待DOM更新后操作\r\n            this.$nextTick(() => {\r\n                if (this.$refs.TopTabel) {\r\n                    const tableComponent = this.$refs.TopTabel;\r\n                    const tableEl = tableComponent.$el;\r\n\r\n                    // 4. 设置所有可能的高度属性\r\n                    tableEl.style.setProperty('height', height + 'px', 'important');\r\n                    tableEl.style.setProperty('max-height', height + 'px', 'important');\r\n                    tableEl.style.setProperty('min-height', height + 'px', 'important');\r\n\r\n                    // 5. 设置CSS变量\r\n                    tableEl.style.setProperty('--table-height', height + 'px');\r\n                    tableEl.style.setProperty('--dynamic-table-height', height + 'px');\r\n\r\n                    // 6. 设置父容器CSS变量\r\n                    const container = tableEl.closest('.available-inventory-container');\r\n                    if (container) {\r\n                        container.style.setProperty('--table-height', height + 'px');\r\n                        container.style.setProperty('--dynamic-table-height', height + 'px');\r\n                    }\r\n\r\n                    // 7. 设置内部元素\r\n                    const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                    if (bodyWrapper) {\r\n                        bodyWrapper.style.setProperty('max-height', (height - 40) + 'px', 'important');\r\n                    }\r\n\r\n                    // 8. 调用Element UI方法\r\n                    if (tableComponent.doLayout) {\r\n                        tableComponent.doLayout();\r\n                    }\r\n\r\n                    // 9. 触发resize事件（防止死循环）\r\n                    setTimeout(() => {\r\n                        this.isResizing = true;\r\n                        window.dispatchEvent(new Event('resize'));\r\n                        setTimeout(() => {\r\n                            this.isResizing = false;\r\n                        }, 100);\r\n\r\n                        // 10. 最终验证\r\n                        console.log('=== 最终验证结果 ===');\r\n                        console.log('表格offsetHeight:', tableEl.offsetHeight);\r\n                        console.log('表格style.height:', tableEl.style.height);\r\n                        console.log('表格计算样式:', window.getComputedStyle(tableEl).height);\r\n\r\n                        if (bodyWrapper) {\r\n                            console.log('body wrapper offsetHeight:', bodyWrapper.offsetHeight);\r\n                            console.log('body wrapper style:', bodyWrapper.style.maxHeight);\r\n                        }\r\n                    }, 200);\r\n                }\r\n            });\r\n        },\r\n\r\n        // === 以下为测试方法，测试完成后删除 ===\r\n\r\n        // 生成测试数据\r\n        generateTestData() {\r\n            const testData = {\r\n                ID: `TEST_${++this.testDataCounter}`,\r\n                SbSscc: `SSCC${String(this.testDataCounter).padStart(4, '0')}`,\r\n                LBatch: `BATCH${String(this.testDataCounter).padStart(3, '0')}`,\r\n                MaterialCode: `MAT${String(this.testDataCounter % 5 + 1).padStart(3, '0')}`,\r\n                MaterialName: `测试物料${this.testDataCounter}`,\r\n                InQuantity: Math.floor(Math.random() * 1000) + 100,\r\n                MaterialUnit1: 'kg',\r\n                LStatus: Math.floor(Math.random() * 3) + 1,\r\n                SbStatus: Math.floor(Math.random() * 3) + 1,\r\n                ExpirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\r\n                Location: `A${String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`\r\n            };\r\n            return testData;\r\n        },\r\n\r\n        // 添加测试数据\r\n        addTestData() {\r\n            // 第一次添加时保存原始数据\r\n            if (this.originalTableList.length === 0 && this.tableList.length > 0) {\r\n                this.originalTableList = [...this.tableList];\r\n            }\r\n\r\n            const newData = this.generateTestData();\r\n            this.tableList.push(newData);\r\n\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n\r\n            Message({\r\n                message: `已添加测试数据，当前共 ${this.tableList.length} 行`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n\r\n        // 删除测试数据\r\n        removeTestData() {\r\n            if (this.tableList.length > 0) {\r\n                // 优先删除测试数据\r\n                const testIndex = this.tableList.findIndex(item => item.ID && item.ID.startsWith('TEST_'));\r\n                if (testIndex !== -1) {\r\n                    this.tableList.splice(testIndex, 1);\r\n                } else {\r\n                    this.tableList.pop();\r\n                }\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: `已删除数据，当前共 ${this.tableList.length} 行`,\r\n                    type: 'warning',\r\n                    duration: 2000\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: '没有数据可删除',\r\n                    type: 'info',\r\n                    duration: 2000\r\n                });\r\n            }\r\n        },\r\n\r\n        // 显示测试信息\r\n        showTestInfo() {\r\n            const info = `\r\n=== 表格自适应高度测试信息 ===\r\n当前模式: ${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}\r\n数据行数: ${this.tableList.length}\r\n窗口高度: ${this.windowHeight}px\r\n表格高度: ${this.tableHeight}px\r\n最小高度: ${this.minTableHeight}px\r\n最大高度: ${this.maxTableHeight}px\r\n行高设置: ${this.rowHeight}px\r\n\r\n测试说明:\r\n1. 点击\"视口模式/行数模式\"按钮切换计算方式\r\n2. 点击\"+数据\"按钮添加测试数据观察高度变化\r\n3. 点击\"-数据\"按钮删除数据观察高度变化\r\n4. 调整浏览器窗口大小测试响应式效果\r\n5. 在视口模式下，表格高度为窗口高度的25%\r\n6. 在行数模式下，表格高度根据数据行数计算\r\n            `;\r\n\r\n            console.log(info);\r\n\r\n            this.$alert(info, '测试信息', {\r\n                confirmButtonText: '确定',\r\n                type: 'info'\r\n            });\r\n        },\r\n\r\n        // 恢复原始数据 (可在控制台调用)\r\n        restoreOriginalData() {\r\n            if (this.originalTableList.length > 0) {\r\n                this.tableList = [...this.originalTableList];\r\n                this.originalTableList = [];\r\n                this.testDataCounter = 0;\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: '已恢复原始数据',\r\n                    type: 'success'\r\n                });\r\n            }\r\n        }\r\n\r\n        // === 测试方法结束 ===\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 可用库存表格容器自适应样式\r\n    .available-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 450px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 动态表格样式 - 强制覆盖Element UI默认样式\r\n        .dynamic-table {\r\n            transition: height 0.3s ease !important;\r\n            width: 100% !important;\r\n\r\n            // 强制设置表格高度\r\n            &.el-table {\r\n                height: var(--table-height, 200px) !important;\r\n                max-height: var(--table-height, 200px) !important;\r\n                min-height: var(--table-height, 200px) !important;\r\n            }\r\n\r\n            // 确保表格内容区域也使用动态高度\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 200px) - 40px) !important;\r\n                overflow-y: auto !important;\r\n\r\n                // 自定义滚动条样式\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px !important;\r\n                min-height: 40px !important;\r\n                max-height: 40px !important;\r\n            }\r\n\r\n            // 固定列样式\r\n            .el-table__fixed,\r\n            .el-table__fixed-right {\r\n                height: var(--table-height, 200px) !important;\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 300px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 250px;\r\n        }\r\n    }\r\n\r\n    // 全局强制样式 - 确保表格高度生效\r\n    .el-table {\r\n        &.dynamic-table {\r\n            height: var(--dynamic-table-height, 200px) !important;\r\n            max-height: var(--dynamic-table-height, 200px) !important;\r\n\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--dynamic-table-height, 200px) - 40px) !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    /* 测试按钮样式 - 测试完成后删除 */\r\n    .test-buttons {\r\n        .el-button {\r\n            margin-left: 3px !important;\r\n            font-size: 11px;\r\n            padding: 5px 8px;\r\n        }\r\n    }\r\n\r\n    /* 确保按钮容器可见 */\r\n    .searchbox {\r\n        position: relative;\r\n\r\n        > div:last-child {\r\n            z-index: 100 !important;\r\n            background: rgba(255, 255, 255, 0.95);\r\n            border-radius: 4px;\r\n            padding: 5px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}