{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749628599700}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoOA;AACA;AACA;AACA,SACAA,6BADA,EAEAC,qBAFA,EAGAC,yBAHA,EAIAC,uBAJA,EAKAC,uBALA,EAMAC,yBANA,EAOAC,4BAPA,EAQAC,gBARA,EASAC,cATA,EAUAC,qBAVA,EAWAC,SAXA,QAYA,wCAZA;AAaA;AACA;AAEA;EACAC;IACAC,mDADA;IAEAC,mDAFA;IAGAC,6CAHA;IAIAC,qDAJA;IAKAC;EALA,CADA;;EAQAC;IACA;MACAC,WADA;MAEAC,iBAFA;MAGAC,iBAHA;MAIAC,uBAJA;MAKAC,mBALA;MAMAC,wBANA;MAOAC,qBAPA;MAQAC,oBARA;MASAC,aATA;MAUAC,QAVA;MAWAC,aAXA;MAYAC,oBAZA;MAaAC,cAbA;MAcAC,uCAdA;MAeAC,gCAfA;MAgBAC,OAhBA;MAiBAC,UAjBA;MAkBAC,gBAlBA;MAmBAC,gBAnBA;MAoBAC,iBApBA;MAqBAC,WArBA;MAsBAC,eAtBA;MAuBAC,kBAvBA;MAwBAC,mBAxBA;MAwBA;MACAC,mBAzBA;MAyBA;MACAC,aA1BA;MA0BA;MACAC,gCA3BA;MA2BA;MACAC,wBA5BA;MA4BA;MACAC,gBA7BA;MA6BA;MACAC,iBA9BA;MA8BA;MACA;MACAC,kBAhCA;MAiCAC,qBAjCA,CAiCA;;IAjCA;EAmCA,CA5CA;;EA6CAC;IACAC;IACAA;IACA;;IACA;MACA;IACA,CAFA,MAEA;MACA;IACA;;IACA;IACAA;IACA;IACAA;IACA;IACA;IACA;IACA,oBAhBA,CAiBA;;IACA;IACA;IACA;;IACA;MACA;MACA;IACA,CAHA,MAGA;MACA;IACA,CA1BA,CA4BA;;;IACA;MACA;MACAA;;MACA;QACA;MACA;IACA,CANA;;IAOAC,qDApCA,CAsCA;;IACAD;IACAA;IACAA,0CAzCA,CA2CA;;IACA;MACA;IACA,CAFA;EAGA,CA5FA;;EA6FAE;IACAD;EACA,CA/FA;;EAgGAE;IACA;IACAF;;IACA;MACAA;IACA;EACA,CAtGA;;EAuGAG;IACAC;MACAL;;MACA;QACAC;QACA;MACA,CAHA,MAGA;QACAA;QACA;MACA;IACA,CAVA;;IAWAK;MACA;QACA;QACAN;;QACA;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAO;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACA;YACA,CAFA,MAEA;cACAF;gBACAC,4DADA;gBAEAC;cAFA;YAIA;;YACA;;UACA;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;cACAT;;cACA;gBACA;cACA,CAFA,MAEA;gBACAO;kBACAC,4DADA;kBAEAC;gBAFA;cAIA;YACA;;YACA;QAvDA;;QAyDA;MACA;IACA,CA1EA;;IA2EA;MACA;QACA;MACA,CAFA,EADA,CAKA;MACA;MACA;MACA;MACA;;MAEA;QACAC,qBADA;QAEAC,sBAFA;QAGAC,+BAHA;QAIAC,2BAJA;QAKAC;MALA;MAQA;MACAP;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CApGA;;IAqGA;MACA;QACAM;MADA;MAGA;MACAC;QACAC;QACAA;QACAA;QACAA;MACA,CALA;MAMA;;MACA;QACA;MACA;IACA,CApHA;;IAqHAC;MACA;MACA;IACA,CAxHA;;IAyHA;MACA;QACAC,+BADA;QAEAC,4CAFA;QAGAC,qCAHA;QAIAV,sBAJA;QAKAW,YALA;QAMAC;MANA;MAQA;MACA;IACA,CApIA;;IAqIAC;MACA;QACA;MACA,CAFA;;MAGA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA,CAVA,MAUA;QACA;UACA;QACA;;QACA;UACA;QACA;;QACA;UACA;QACA;MACA;IACA,CA9JA;;IA+JAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;IACA,CAzKA;;IA0KAC;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;MACA;MACA1B;MACA;MACA;IACA,CAlMA;;IAmMA;MACA;;MACA;QACA;UACA;YACA2B,eADA;YAEAL,YAFA;YAGAX,sBAHA;YAIAY;UAJA,EADA,CAOA;;UACAK,wCARA,CAQA;QACA,CATA,MASA;UACA;UACA;YACAC,oBADA;YAEAC,yBAFA;YAGAT,uBAHA;YAIAC,YAJA;YAKAC;UALA;UAOAK,wCATA,CASA;QACA;MACA,CArBA,MAqBA;QACA;UACA;YACAD,eADA;YAEAhB,sBAFA;YAGAW,YAHA;YAIAC;UAJA;UAMAK,0CAPA,CAOA;QACA,CARA,MAQA;UACA;UACA;YACAP,uBADA;YAEAQ,oBAFA;YAGAC,yBAHA;YAIAR,YAJA;YAKAC;UALA;UAOAK,0CATA,CASA;QACA;MACA;;MAEA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;MAKA;MACA;;MACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;;MACA;;MACA;QACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;MACA;;MACA;QACA;QACA;MACA;IACA,CAnRA;;IAoRA;MACA;QACAf,2BADA;QAEAkB;MAFA;MAIA;;MACA;QACAxB;UACAC,kBADA;UAEAC;QAFA;MAIA,CALA,MAKA;QACA;;QACA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA,CAPA,MAOA;UACA;YACA;cACA;cACA;YACA;UACA,CALA;QAMA;MACA;IACA,CAjTA;;IAkTAuB;MACAhC;MACA,iEAFA,CAGA;MACA;;MACA;QACA;MACA;;MACA;MACA;;MACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;MACA,CAlBA,CAmBA;;;MACA;QACA;;QACA;UACA;YACAiC;UACA;QACA;;QACA;QACA;QACA;;QACA;UACA;UACA;UACA;QACA,CAJA,MAIA;UACA;UACA;;UACA;YACA;UACA,CAFA,MAEA;YACA;UACA;QACA;;QAEA;QACA;QACA;MACA;;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA,CAzWA;;IA0WAC;MACA;MACA;MACA;MACA,2BAJA,CAKA;;MACA;;MACA;QACA;QACA;QACA;QACA;MACA;IACA,CAvXA;;IAwXA;MACA;QACAC,yCADA;QAEAC,6BAFA;QAGAC,kCAHA;QAIAC,sBAJA;QAKAjB,qCALA;QAMAkB;MANA;MAQA;MACAhC;QACAC,gBADA;QAEAC;MAFA;MAIA;IACA,CAvYA;;IAwYA+B;MACA;MACA;;MACA;QACA;QACA;MACA,CAHA,MAGA;QACA;MACA;IACA,CAjZA;;IAkZA;MACA;QACAC,4CADA;QAEAC;MAFA;MAIA;MACA1C;MACA;;MACA;QACA;UACAC;;UACA;YACA;YACA;UACA;QACA;MACA,CARA,MAQA;QACA;MACA;IACA,CAraA;;IAsaA;MACA;MACA;QACAoB,qCADA;QAEAV,sBAFA;QAGAW,YAHA;QAIAC;MAJA;;MAMA;QACAK;MACA,CAFA,MAEA;QACAA;MACA;;MACA,mCAbA,CAcA;;MACA;QACA;MACA,CAFA;IAGA,CAxbA;;IAybAe;MACA;IACA,CA3bA;;IA4bAC;MACA;MACA;MACA;IACA,CAhcA;;IAicA;IACAC;MACA7C;MACA;MACAA,4CAHA,CAKA;;MACA;IACA,CAzcA;;IA0cA;IACA8C;MACA;;MAEA;QACAC;MACA,CAFA,MAEA;QACA;QACA;QACAA;MACA,CAJA,MAIA;QACA;QACA;QACA;QACAA;MACA;;MAEA/C;MACA;MACA,sBAlBA,CAkBA;;MAEA;QACA;UACA;QACA;MACA,CAJA;IAKA,CApeA;;IAqeA;IACAgD;MACA;MACA;MACA;IACA,CA1eA;;IA2eA;IACAC;MACAjD;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;IACA,CAvfA;;IAyfA;IAEA;IACAkD;MACA;QACAvB,oCADA;QAEAwB,8DAFA;QAGAC,+DAHA;QAIAC,2EAJA;QAKAC,2CALA;QAMArB,kDANA;QAOAsB,mBAPA;QAQAC,0CARA;QASAC,2CATA;QAUAC,4GAVA;QAWAC;MAXA;MAaA;IACA,CA3gBA;;IA6gBA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;MAEA;QACA;MACA,CAFA;MAIArD;QACAC,iDADA;QAEAC,eAFA;QAGAoD;MAHA;IAKA,CAhiBA;;IAkiBA;IACAC;MACA;QACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;;QAEA;UACA;QACA,CAFA;QAIAvD;UACAC,+CADA;UAEAC,eAFA;UAGAoD;QAHA;MAKA,CAlBA,MAkBA;QACAtD;UACAC,kBADA;UAEAC,YAFA;UAGAoD;QAHA;MAKA;IACA,CA7jBA;;IA+jBA;IACAE;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAjBA;MAmBA/D;MAEA;QACAgE,uBADA;QAEAvD;MAFA;IAIA,CA1lBA;;IA4lBA;IACAwD;MACA;QACA;QACA;QACA;QAEA;UACA;QACA,CAFA;QAIA1D;UACAC,kBADA;UAEAC;QAFA;MAIA;IACA,CA5mBA,CA8mBA;;;EA9mBA;AAvGA", "names": ["GetPageListNewMaterialPreDown", "GetPageListByMaterial", "GetPageListByBatchIDSByID", "GetPageListByMaterialII", "GetPageListByBatchIDSII", "GetPageListMaterialPreTop", "GetPageListNewMaterialPreTop", "GetConSelectList", "FirstAddPallet", "GetPageListByBatchIDS", "MygetSSCC", "components", "PartialBag", "FullAmount", "FullBag", "POInventory", "BatchPallets", "data", "PrintId", "printeroption", "PrintModel", "isExpirationDate", "tableId", "activeName", "OnlyFullAmount", "Hidecompleted", "<PERSON><PERSON><PERSON>", "room", "tableList", "tableListBatchPO", "SelectList", "headerBatchPO", "header", "way", "listId", "nowChooseRow", "MaterialList", "MaterialNow", "UseType", "clblFlag", "keepKeyDown", "minTableHeight", "maxTableHeight", "rowHeight", "windowHeight", "useViewportHeight", "tableHeight", "tableHeightKey", "testDataCounter", "originalTableList", "mounted", "console", "window", "beforeMount", "<PERSON><PERSON><PERSON><PERSON>", "methods", "openKeyDown", "getKeyDown", "Message", "message", "type", "printId", "EquipmentId", "BagSiZe", "MCode", "ids", "equipmentId", "res2", "item", "PrintAvallable", "BatchId", "ProOrderid", "MaterialId", "pageIndex", "pageSize", "GetSSCC", "EmptySscc", "ChangeMaterial", "ID", "res", "EqumentId", "ProId", "SSCC", "GetCurrentRow", "InQuantity", "GetCurrentRow2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UomID", "ProBatchID", "EquipMentID", "ProRequestID", "refresh", "proOrderID", "batchID", "back", "isDateInThePast", "toggleTableHeightMode", "updateTableHeight", "newHeight", "setTableHeightParams", "testTableHeight", "generateTestData", "SbSscc", "LBatch", "MaterialCode", "MaterialName", "MaterialUnit1", "LStatus", "SbStatus", "ExpirationDate", "Location", "addTestData", "duration", "removeTestData", "showTestInfo", "confirmButtonText", "restoreOriginalData"], "sourceRoot": "src/views/Inventory/buildpalletsStart", "sources": ["index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox available-inventory-container\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- 测试状态显示 - 测试完成后删除 -->\r\n                            <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                [{{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                                数据: {{ tableList.length }}行 |\r\n                                高度: {{ tableHeight }}px]\r\n                            </span>\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px;\">\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                size=\"small\"\r\n                                @click=\"toggleTableHeightMode()\"\r\n                                :title=\"useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\">\r\n                                {{ useViewportHeight ? '视口模式' : '行数模式' }}\r\n                            </el-button>\r\n                            <!-- 测试按钮组 - 测试完成后删除 -->\r\n                            <div class=\"test-buttons\">\r\n                                <el-button size=\"small\" type=\"success\" @click=\"addTestData()\" title=\"添加测试数据\">+数据</el-button>\r\n                                <el-button size=\"small\" type=\"warning\" @click=\"removeTestData()\" title=\"删除测试数据\">-数据</el-button>\r\n                                <el-button size=\"small\" type=\"info\" @click=\"showTestInfo()\" title=\"显示测试信息\">测试信息</el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    style=\"width: 100%\"\r\n                    :height=\"tableHeight\"\r\n                    :key=\"tableHeightKey\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n            minTableHeight: 150, // 最小表格高度\r\n            maxTableHeight: 400, // 最大表格高度\r\n            rowHeight: 40, // 每行的高度\r\n            windowHeight: window.innerHeight, // 窗口高度\r\n            useViewportHeight: false, // 是否使用视口高度模式\r\n            tableHeight: 200, // 当前表格高度\r\n            tableHeightKey: 0, // 强制重新渲染的key\r\n            // 测试相关数据 - 测试完成后删除\r\n            testDataCounter: 0,\r\n            originalTableList: [] // 保存原始数据\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 添加窗口大小变化监听器\r\n        this.handleResize = () => {\r\n            this.windowHeight = window.innerHeight;\r\n            console.log('窗口大小变化:', this.windowHeight);\r\n            if (this.useViewportHeight) {\r\n                this.updateTableHeight();\r\n            }\r\n        };\r\n        window.addEventListener('resize', this.handleResize);\r\n\r\n        // 初始化日志\r\n        console.log('=== 表格自适应高度初始化 ===');\r\n        console.log('初始模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n        console.log('初始窗口高度:', this.windowHeight);\r\n\r\n        // 初始化表格高度\r\n        this.$nextTick(() => {\r\n            this.updateTableHeight();\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n            // 数据加载完成后更新表格高度\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        },\r\n        // 切换表格高度模式\r\n        toggleTableHeightMode() {\r\n            console.log('切换前:', this.useViewportHeight);\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n            console.log('切换后:', this.useViewportHeight);\r\n\r\n            // 重新计算表格高度\r\n            this.updateTableHeight();\r\n        },\r\n        // 更新表格高度\r\n        updateTableHeight() {\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n            } else if (this.useViewportHeight) {\r\n                // 基于视口高度的响应式计算\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n            } else {\r\n                // 基于数据行数计算高度\r\n                const headerHeight = 40;\r\n                const dataHeight = headerHeight + (this.tableList.length * this.rowHeight);\r\n                newHeight = Math.min(Math.max(dataHeight, this.minTableHeight), this.maxTableHeight);\r\n            }\r\n\r\n            console.log('新计算的高度:', newHeight);\r\n            this.tableHeight = newHeight;\r\n            this.tableHeightKey++; // 强制重新渲染\r\n\r\n            this.$nextTick(() => {\r\n                if (this.$refs.TopTabel) {\r\n                    this.$refs.TopTabel.doLayout();\r\n                }\r\n            });\r\n        },\r\n        // 手动设置表格高度参数\r\n        setTableHeightParams(minHeight, maxHeight, rowHeight) {\r\n            this.minTableHeight = minHeight || 150;\r\n            this.maxTableHeight = maxHeight || 400;\r\n            this.rowHeight = rowHeight || 40;\r\n        },\r\n        // 测试方法 - 可以在浏览器控制台调用\r\n        testTableHeight() {\r\n            console.log('=== 表格高度测试 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('表格数据行数:', this.tableList ? this.tableList.length : 0);\r\n            console.log('窗口高度:', this.windowHeight);\r\n            console.log('当前表格高度:', this.tableHeight);\r\n            console.log('最小高度:', this.minTableHeight);\r\n            console.log('最大高度:', this.maxTableHeight);\r\n            console.log('行高:', this.rowHeight);\r\n            console.log('表格Key:', this.tableHeightKey);\r\n            return this.tableHeight;\r\n        },\r\n\r\n        // === 以下为测试方法，测试完成后删除 ===\r\n\r\n        // 生成测试数据\r\n        generateTestData() {\r\n            const testData = {\r\n                ID: `TEST_${++this.testDataCounter}`,\r\n                SbSscc: `SSCC${String(this.testDataCounter).padStart(4, '0')}`,\r\n                LBatch: `BATCH${String(this.testDataCounter).padStart(3, '0')}`,\r\n                MaterialCode: `MAT${String(this.testDataCounter % 5 + 1).padStart(3, '0')}`,\r\n                MaterialName: `测试物料${this.testDataCounter}`,\r\n                InQuantity: Math.floor(Math.random() * 1000) + 100,\r\n                MaterialUnit1: 'kg',\r\n                LStatus: Math.floor(Math.random() * 3) + 1,\r\n                SbStatus: Math.floor(Math.random() * 3) + 1,\r\n                ExpirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\r\n                Location: `A${String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`\r\n            };\r\n            return testData;\r\n        },\r\n\r\n        // 添加测试数据\r\n        addTestData() {\r\n            // 第一次添加时保存原始数据\r\n            if (this.originalTableList.length === 0 && this.tableList.length > 0) {\r\n                this.originalTableList = [...this.tableList];\r\n            }\r\n\r\n            const newData = this.generateTestData();\r\n            this.tableList.push(newData);\r\n\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n\r\n            Message({\r\n                message: `已添加测试数据，当前共 ${this.tableList.length} 行`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n\r\n        // 删除测试数据\r\n        removeTestData() {\r\n            if (this.tableList.length > 0) {\r\n                // 优先删除测试数据\r\n                const testIndex = this.tableList.findIndex(item => item.ID && item.ID.startsWith('TEST_'));\r\n                if (testIndex !== -1) {\r\n                    this.tableList.splice(testIndex, 1);\r\n                } else {\r\n                    this.tableList.pop();\r\n                }\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: `已删除数据，当前共 ${this.tableList.length} 行`,\r\n                    type: 'warning',\r\n                    duration: 2000\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: '没有数据可删除',\r\n                    type: 'info',\r\n                    duration: 2000\r\n                });\r\n            }\r\n        },\r\n\r\n        // 显示测试信息\r\n        showTestInfo() {\r\n            const info = `\r\n=== 表格自适应高度测试信息 ===\r\n当前模式: ${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}\r\n数据行数: ${this.tableList.length}\r\n窗口高度: ${this.windowHeight}px\r\n表格高度: ${this.tableHeight}px\r\n最小高度: ${this.minTableHeight}px\r\n最大高度: ${this.maxTableHeight}px\r\n行高设置: ${this.rowHeight}px\r\n\r\n测试说明:\r\n1. 点击\"视口模式/行数模式\"按钮切换计算方式\r\n2. 点击\"+数据\"按钮添加测试数据观察高度变化\r\n3. 点击\"-数据\"按钮删除数据观察高度变化\r\n4. 调整浏览器窗口大小测试响应式效果\r\n5. 在视口模式下，表格高度为窗口高度的25%\r\n6. 在行数模式下，表格高度根据数据行数计算\r\n            `;\r\n\r\n            console.log(info);\r\n\r\n            this.$alert(info, '测试信息', {\r\n                confirmButtonText: '确定',\r\n                type: 'info'\r\n            });\r\n        },\r\n\r\n        // 恢复原始数据 (可在控制台调用)\r\n        restoreOriginalData() {\r\n            if (this.originalTableList.length > 0) {\r\n                this.tableList = [...this.originalTableList];\r\n                this.originalTableList = [];\r\n                this.testDataCounter = 0;\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: '已恢复原始数据',\r\n                    type: 'success'\r\n                });\r\n            }\r\n        }\r\n\r\n        // === 测试方法结束 ===\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 可用库存表格容器自适应样式\r\n    .available-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 450px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        .el-table {\r\n            transition: height 0.3s ease;\r\n\r\n            .el-table__body-wrapper {\r\n                overflow-y: auto;\r\n\r\n                // 自定义滚动条样式\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 300px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 250px;\r\n        }\r\n    }\r\n\r\n    /* 测试按钮样式 - 测试完成后删除 */\r\n    .test-buttons {\r\n        .el-button {\r\n            margin-left: 3px !important;\r\n            font-size: 11px;\r\n            padding: 5px 8px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}