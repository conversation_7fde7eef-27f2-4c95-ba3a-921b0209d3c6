﻿using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IBatchDcsServices
	/// </summary>	
    public interface IBatchDcsServices :IBaseServices<BatchDcsEntity>
	{
		Task<PageModel<BatchDcsEntity>> GetPageList(BatchDcsRequestModel reqModel);

        Task<List<BatchDcsEntity>> GetList(BatchDcsRequestModel reqModel);

		Task<bool> SaveForm(BatchDcsEntity entity);
    }
}