{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue", "mtime": 1749180160948}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA8CA,SACAA,sBADA,EAEAC,wBAFA,EAGAC,cAHA,QAIA,mCAJA,C,CAKA;;AACA;EACAC,aACA;EADA,CADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,cAJA;MAKAC,uBALA;MAMAC;IANA;EAQA,CAbA;;EAcAC;IACA;EACA,CAhBA;;EAiBAC,WACA,CAlBA;;EAmBAC;IACA,sBACA,CAFA;;IAGA;MACA;QACAC;QACAA;QACA;MACA,CAJA;IAKA,CATA;;IAUAC;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA,CALA;IAMA,CApBA;;IAqBAC;MACAF;MACA;QACAG;;QACA;UACA;UACA;UACA;QACA;MACA,CAPA;IAQA,CA/BA;;IAgCAC;MACAJ;MACAA;MACA;MACA;IACA,CArCA,CAsCA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;EAnDA;AAnBA", "names": ["getInsteadMaterialList", "getWeekScheduleBomDetail", "changeMaterial", "components", "data", "dialogForm", "dialogVisible", "formLoading", "currentRow", "insteadMaterialList", "matInfo", "created", "mounted", "methods", "console", "show", "materialChange", "item", "submit"], "sourceRoot": "src/views/planManagement/weekFormulation", "sources": ["bomDetailForm.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n        <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\">\n              <!-- {{ dialogForm.MaterialCode }} - {{ dialogForm.MaterialName }} -->\n              <el-select placeholder=\"请选择新替代物料\" v-model=\"dialogForm.MaterialCode\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"替代物料代码\">\n              {{ dialogForm.InsteadMaterialCode }} - {{ dialogForm.InsteadMaterialName }}\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"12\">\n            <el-form-item label=\"新替代物料\">\n              <el-select placeholder=\"请选择新替代物料\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"\">\n              \n            </el-form-item>\n          </el-col> -->\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <!-- <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table> -->\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getInsteadMaterialList,\n    getWeekScheduleBomDetail,\n    changeMaterial\n  } from \"@/api/planManagement/weekSchedule\";\n  //import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      // MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        currentRow: {},\n        insteadMaterialList:[],\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n      },\n      async initInsteadMaterialList(data) {\n        await getInsteadMaterialList(data).then(res => {\n              console.log(\"initInsteadMaterialList\")\n              console.log(res.response)\n              this.insteadMaterialList = res.response\n            });\n      },      \n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.dialogForm = data\n            this.initInsteadMaterialList(data)\n          }\n        })\n      },\n      materialChange(val) {\n            console.log(val);\n            this.insteadMaterialList.forEach(item => {\n                item.value = item.ID;\n                if(item.MaterialCode == val) {\n                    this.dialogForm.MaterialId  = \"\";\n                    this.dialogForm.MaterialCode  = item.MaterialCode;\n                    this.dialogForm.MaterialName  = item.MaterialName;\n                }\n            });\n          },\n      submit() {\n        console.log(\"bomDetailForm.submit\")\n        console.log(this.dialogForm)\n        this.$emit('saveForm',this.dialogForm)\n        this.dialogVisible = false\n      },\n      // setMaterial(val){\n      //   // console.log(\"setMaterial\")\n      //   // console.log(val)        \n      //   this.dialogForm.MaterialId = val.ID\n      //   this.dialogForm.MaterialCode = val.Code\n      //   this.dialogForm.MaterialName = val.NAME\n      //   this.$forceUpdate()\n      //   // this.matInfo = val        \n      //   // console.log(this.dialogForm.MaterialCode)\n      //   // console.log(this.dialogForm.MaterialName)\n      // },\n      // openMaterialTable(){\n      //   this.$refs['materialTable'].show()\n      // },\n    }\n  }\n  </script>"]}]}