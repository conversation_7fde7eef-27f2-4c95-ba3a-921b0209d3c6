{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetail.vue?vue&type=style&index=0&id=20b55e30&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetail.vue", "mtime": 1749177894467}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZHJhd2VyIHsNCiAgOmRlZXAoLmVsLWRyYXdlcl9fYm9keSkgew0KICAgIHBhZGRpbmctdG9wOiAxMHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogICAgb3ZlcmZsb3cteTogaGlkZGVuDQogIH0NCg0KICA6ZGVlcCguZWwtZm9ybS0taW5saW5lKSB7DQogICAgaGVpZ2h0OiAzMnB4Ow0KICB9DQoNCiAgLnRpdGxlLWJveCB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICB9DQoNCiAgLnBkNSB7DQogICAgcGFkZGluZzogNXB4Ow0KICB9DQoNCiAgLnRhYmxlLWJveCB7DQogICAgcGFkZGluZzogMCAxMHB4Ow0KDQogICAgOmRlZXAoLmVsLWJ1dHRvbi5pcy1kaXNhYmxlZCkgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsNCiAgICAgIGJvcmRlcjogMCAhaW1wb3J0YW50Ow0KICAgIH0NCg0KICAgIGkgew0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgICBmb250LXNpemU6IDE1cHggIWltcG9ydGFudDsNCiAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["poListDetail.vue"], "names": [], "mappings": ";AAyJA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "poListDetail.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\"\r\n      :append-to-body=\"false\" size=\"80%\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.OrderNo} | ${currentRow.LineCode} | ${currentRow.Factory} | ${currentRow.MaterialCode}-${currentRow.MaterialName}` }}</span>\r\n      </div>\r\n      <div class=\"InventorySearchBox\">\r\n        <div class=\"searchbox pd5\">\r\n          <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n            <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n              <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button icon=\"el-icon-search\" @click=\"getTableData\">{{ $t('GLOBAL._CX') }}</el-button>\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                $t('GLOBAL._XZ') }}\r\n              </el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"table-box\">\r\n        <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n          element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n          <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog((scope.row))\">{{$t('GLOBAL._XZ') }}</el-button>\r\n              <el-button size=\"mini\" type=\"text\" @click=\"downloadDCS(scope.row)\" :disabled=\"scope.row.Status == 'NotSplit' ? true : false\">{{ $t('GLOBAL._XFDCS') }}</el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column v-for=\"(item, index) in tableHeadDrawer\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\" :width=\"item.width\" :align=\"item.alignType\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)\">\r\n                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.field === 'PoStatus'\"> {{ status[scope.row[item.field]] }} </span>\r\n              <span v-else> {{ scope.row[item.field] }} </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n        </el-table>\r\n      </div>\r\n    </el-drawer>\r\n    <DetailForm @saveForm=\"getTableData\" ref=\"detailDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionOrderList, downloadDCS } from '@/api/planManagement/weekSchedule'\r\nimport DetailForm from './poListDetailForm'\r\nimport {getTableHead} from \"@/util/dataDictionary.js\";\r\nexport default {\r\n  name: 'POListDetail',\r\n  components: {\r\n    DetailForm\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      drawer: false,\r\n      tableData: [],\r\n      tableHeadDrawer: [],      \r\n      hansObjDrawer: this.$t('WeekFormulation.poListDetail'),\r\n      tableOptionDrawer:[        \r\n        {code: 'Sequence', width: 100, align: 'left'},\r\n        {code: 'ProductionOrderNo', width: 200, align: 'left'},\r\n        {code: 'SegmentCode', width: 150, align: 'left'},\r\n        {code: 'LineCode', width: 100, align: 'left'},\r\n        {code: 'MaterialCode', width: 180, align: 'left'},\r\n        {code: 'MaterialName', width: 180, align: 'left'},\r\n        {code: 'PlanQty', width: 100, align: 'left'},\r\n        {code: 'PlanDate', width: 180, align: 'left'},        \r\n        {code: 'PrepareShiftid', width: 100, align: 'left'},\r\n        {code: 'PlanStartTime', width: 150, align: 'left'},\r\n        {code: 'PlanEndTime', width: 150, align: 'left'},\r\n        {code: 'StartTime', width: 150, align: 'left'},\r\n        {code: 'EndTime', width: 150, align: 'left'},\r\n        {code: 'PoStatus', width: 150, align: 'left'},\r\n        {code: 'ReleaseStatus', width: 100, align: 'left'},\r\n        {code: 'ProduceStatus', width: 100, align: 'left'},\r\n        {code: 'BomVersion', width: 100, align: 'left'},\r\n        {code: 'Type', width: 100, align: 'left'},\r\n        {code: 'Sapordertype', width: 100, align: 'left'},\r\n        {code: 'Remark', width: 200, align: 'left'},\r\n      ],\r\n      loading: false,\r\n      sapSegmentMaterialId: 0,\r\n      currentRow: {},\r\n      status: {\r\n        '1': 'Pending Release',\r\n        '2': 'Released',\r\n        '3': 'Complete',\r\n        '4': 'Cancel',\r\n        '5' : 'Stop',\r\n        '6' : 'Running'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTableHead()\r\n  },\r\n  methods: {\r\n    show(val) {\r\n      this.currentRow = val\r\n      this.sapSegmentMaterialId = val.ID\r\n      this.drawer = true\r\n      this.getTableData()\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    async getTableData() {\r\n      const { response } = await getProductionOrderList({\r\n        ParentId: this.currentRow.ID,\r\n        ...this.searchForm\r\n      })\r\n      this.tableData = response\r\n    },\r\n    initTableHead() {\r\n      this.tableHeadDrawer = []\r\n      // for (let key in this.hansObjDrawer) {\r\n      //   this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })\r\n      // }\r\n      this.tableHeadDrawer = getTableHead(this.hansObjDrawer, this.tableOptionDrawer)\r\n      this.$forceUpdate()\r\n      console.log(this.tableHeadDrawer);\r\n    },\r\n    showDialog(row) {\r\n      this.$refs.detailDialog.show(row)\r\n    },\r\n    downloadDCS(row) {\r\n      this.$confirms({\r\n        title: this.$t('GLOBAL._TS'),\r\n        message: this.$t('GLOBAL._COMFIRM_XFDCS'),\r\n        confirmText: this.$t('GLOBAL._QD'),\r\n        cancelText: this.$t('GLOBAL._QX')\r\n      }).then(async () => {\r\n        downloadDCS([row.ID]).then(res => {\r\n          this.$message.success(res.msg)\r\n          this.getTableData()\r\n        })\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    padding-top: 10px;\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    :deep(.el-button.is-disabled) {\r\n      background-color: transparent !important;\r\n      border: 0 !important;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}