﻿using SEFA.PPM.IServices;
using SEFA.Base.Model;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;
using SEFA.Base;

namespace SEFA.PPMApi.Controllers
{
	[Route("api/[controller]/[action]")]
	[ApiController]
    [AllowAnonymous]
    public class WeekScheduleBomController : BaseApiController
    {
        /// <summary>
        /// WeekScheduleBom
        /// </summary>
        private readonly IWeekScheduleBomServices _weekScheduleBomServices;
    
        public WeekScheduleBomController(IWeekScheduleBomServices WeekScheduleBomServices)
        {
            _weekScheduleBomServices = WeekScheduleBomServices;
        }
    
        [HttpPost]
        public async Task<MessageModel<List<WeekScheduleBomEntity>>> GetList([FromBody] WeekScheduleBomRequestModel reqModel)
        {
            var data = await _weekScheduleBomServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<WeekScheduleBomEntity>>> GetPageList([FromBody] WeekScheduleBomRequestModel reqModel)
        {
            var data = await _weekScheduleBomServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<WeekScheduleBomEntity>> GetEntity(string id)
        {
            var data = await _weekScheduleBomServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] WeekScheduleBomEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _weekScheduleBomServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _weekScheduleBomServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] WeekScheduleBomEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _weekScheduleBomServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _weekScheduleBomServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed( "删除失败");
            }
        }
    }
    //public class WeekScheduleBomRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}