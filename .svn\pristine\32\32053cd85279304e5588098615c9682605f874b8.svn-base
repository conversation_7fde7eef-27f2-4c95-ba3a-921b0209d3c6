import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ORDER'


/**
 * 批次DCS下发分页查询
 * @param {查询条件} data
 */
export function getBatchDcsList(data) {
    const api = '/ppm/BatchDcs/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}


/**
 * 保存批次DCS下发
 * @param data
 */
export function saveBatchDcsForm(data) {
    const api = '/ppm/BatchDcs/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取批次DCS下发详情
 * @param {Id}
 */
export function getBatchDcsDetail(id) {
    const api = '/ppm/BatchDcs/GetEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 删除批次DCS下发
 * @param {主键} data
 */
export function delBatchDcs(data) {
    const api = '/ppm/BatchDcs/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}



