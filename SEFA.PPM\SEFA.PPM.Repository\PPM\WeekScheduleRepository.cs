﻿using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// WeekScheduleRepository
	/// </summary>
    public class WeekScheduleRepository : BaseRepository<WeekScheduleEntity>, IWeekScheduleRepository
    {
        public WeekScheduleRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}