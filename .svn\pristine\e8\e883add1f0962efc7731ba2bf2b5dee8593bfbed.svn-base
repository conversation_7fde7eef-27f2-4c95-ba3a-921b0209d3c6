<!--
 * @Descripttion: (标准时间批量/PPM_M_STANDARD_PERIOD_LOT)
 * @version: (1.0)
 * @Author: (admin)
 * @Date: (2025-05-14)
 * @LastEditors: (admin)
 * @LastEditTime: (2025-05-14)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            				    
      <el-form-item label="产线代码" prop="lineCode">
        <el-input v-model="searchForm.lineCode" placeholder="请输入产线代码" />
      </el-form-item>
    				    
      <el-form-item label="物料代码" prop="materialCode">
        <el-input v-model="searchForm.materialCode" placeholder="请输入物料代码" />
      </el-form-item>

        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
            {{ $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
              </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                        <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          
          </template>
        </el-table-column>
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20, 50, 100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import FormDialog from './form-dialog'
import {
    delStandardPeriodLot, getStandardPeriodLotList
} from "@/api/planManagement/standardPeriodLot";
import {getTableHead} from "@/util/dataDictionary.js";

//import { standardPeriodLotColumn } from '@/columns/pPM/standardPeriodLot.js';
//import UploadButton from "@/components/UploadButton.vue";

export default {
  name: 'index.vue',
  components: {
    //UploadButton,
    FormDialog,
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('StandardPeriodLot.table'),
      tableName: [],
      loading: false,
      tableOption: [
      {code: 'LineCode', width: 130, align: 'center'},
      {code: 'MaterialCode', width: 130, align: 'center'},
      {code: 'MaterialName', width: 130, align: 'center'},
      {code: 'Type', width: 130, align: 'center'},
      {code: 'FirstLotPeriod', width: 130, align: 'center'},
      {code: 'MiddleLotPeriod', width: 130, align: 'center'},
      {code: 'LastLotPeriod', width: 130, align: 'center'},
      {code: 'PlanQuantity', width: 130, align: 'center'},
      {code: 'MinLotQuantity', width: 130, align: 'center'},
      {code: 'MaxLotQuantity', width: 130, align: 'center'},
      {code: 'Remark', width: 130, align: 'center'},
      {code: 'CreateDate', width: 130, align: 'center'},
      {code: 'CreateUserId', width: 130, align: 'center'},
      {code: 'ModifyDate', width: 130, align: 'center'},
      {code: 'ModifyUserId', width: 130, align: 'center'},
      ],
      mainH: 0,
      buttonOption:{
        name:'标准时间批量',
        serveIp:'baseURL_PPM',
        // uploadUrl:'/api/StandardPeriodLot/ImportData', //导入
        // exportUrl:'/api/StandardPeriodLot/ExportData', //导出
        // DownLoadUrl:'/api/StandardPeriodLot/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName = getTableHead(this.hansObj, this.tableOption)
      }
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delStandardPeriodLot([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData(data) {
      getStandardPeriodLotList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}

//<!-- 移到到src/local/en.json和zh-Hans.json -->
//"StandardPeriodLot": {
//    "table": {
//        "lineCode": "lineCode",
//        "materialCode": "materialCode",
//        "materialName": "materialName",
//        "type": "type",
//        "firstLotPeriod": "firstLotPeriod",
//        "middleLotPeriod": "middleLotPeriod",
//        "lastLotPeriod": "lastLotPeriod",
//        "planQuantity": "planQuantity",
//        "minLotQuantity": "minLotQuantity",
//        "maxLotQuantity": "maxLotQuantity",
//        "remark": "remark",
//        "createdate": "createdate",
//        "createuserid": "createuserid",
//        "modifydate": "modifydate",
//        "modifyuserid": "modifyuserid",
//    }
//},
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>