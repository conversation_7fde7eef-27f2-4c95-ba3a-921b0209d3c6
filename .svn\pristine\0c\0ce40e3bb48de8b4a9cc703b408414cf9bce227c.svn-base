﻿using AutoMapper;
using SEFA.MKM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.Models.Interface;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.InterfaceView;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.PPM;
using SEFA.PPM.Model.ViewModels.SIM.View;


namespace SEFA.PPM.Model.AutoMapperProfile
{
    public class CommonProfile : Profile
    {
        public CommonProfile()
        {
            CreateMap<InfluxOpcTagModel, InfluxOpcTagModelETO>(MemberList.None).ReverseMap();
            CreateMap<ProduceModel, PoProducedActualEntity>(MemberList.None).ReverseMap();
            CreateMap<DowntimeEntity, DowntimeHistoryEntity>(MemberList.None).ReverseMap();
            CreateMap<LogsheetDetailRequestModel, LogsheetDetailEntity>(MemberList.None).ReverseMap();
            CreateMap<LogsheetReturnEntity, LogsheetEntity>(MemberList.None).ReverseMap();
            CreateMap<LogsheetEntity, LogsheetReturnEntity>(MemberList.None).ReverseMap();
            CreateMap<LogsheetAdnDetailViewEntity, LogsheetReturnEntity>(MemberList.None).ReverseMap();
            CreateMap<SafetytgtEntity, SafetytgtRequestModel>(MemberList.None).ReverseMap();
            CreateMap<ProductionOrderPropertyEntity, ProductionOrderProperty>(MemberList.None).ReverseMap();
            CreateMap<DowntimeReasonMappingSaveListEntity, DowntimeReasonMappingEntity>(MemberList.None).ReverseMap();
            CreateMap<MMI_ProductionFeedback, ParaExecutionLogEntity>(MemberList.None).ReverseMap();
            CreateMap<EquipmentSalesContainerModel, EquipmentSalesContainerExcelDto>(MemberList.None).ReverseMap();
            CreateMap<InventoryModel, VerifiyExport>(MemberList.None).ReverseMap();
            CreateMap<ConfirmationEntity, ConfirmationEntityModel>(MemberList.None).ReverseMap();
            CreateMap<KpiTgtViewModel, KpitgtExcelDto>(MemberList.None).ReverseMap();
            CreateMap<LosstgtModel, LosstgtExcelDto>(MemberList.None).ReverseMap();
            CreateMap<LosstgtGroupModel, LosstgtExcelDtoYear>(MemberList.None).ReverseMap();
            CreateMap<ProdtgtContainerViewModel, ProdtgtContainerExcelDto>(MemberList.None).ReverseMap();
            CreateMap<ProdtgtSaucetypeModel, ProdtgtSaucetypeEecelDto>(MemberList.None).ReverseMap();
            CreateMap<DowntimetgtViewEntity, DowntimetgtExcelDto>(MemberList.None).ReverseMap();
            CreateMap<PoProducedExecutionEntity, PoProducedExecutionModel>(MemberList.None).ReverseMap();
            CreateMap<SampleListVEntity, SampleListVModel>(MemberList.None).ReverseMap();

            CreateMap<ProdWeightExcelDto, ProdWeightExcelDto>(MemberList.None).ReverseMap();

            CreateMap<SafetyModel, SafetyExcelDto>(MemberList.None).ReverseMap();
            CreateMap<LosstgtLineModel, LosstgtExcelDtoLine>(MemberList.None).ReverseMap();

            CreateMap<ThroatadditionEntity, ThroatadditionExcelDto>(MemberList.None).ReverseMap();
            CreateMap<PoConsumeMaterialListViewEntity, EveryDayMaterialRequirement>(MemberList.None).ReverseMap();
            CreateMap<ProductionOrderExcelDto, ProductionOrderViewModel>(MemberList.None).ReverseMap();

            CreateMap<SpecialformulacapacityModel, SpecialformulacapacityExcelDto>(MemberList.None).ReverseMap();
            CreateMap<FormulascheduleEntity, FormulaScheduleEexcelDto>(MemberList.None).ReverseMap();
            CreateMap<FormulaTankCapacityEntity, FormulaTankCapacityExcelDto>(MemberList.None).ReverseMap();
            CreateMap<FormulaTankCapacityModel, FormulaTankCapacityExcelDto>(MemberList.None).ReverseMap();
            CreateMap<SappackorderEntity, SappackorderrecordEntity>(MemberList.None).ReverseMap();
            CreateMap<PP_SAP_PackOrder, SappackorderrecordEntity>()
                .ForMember(record => record.AuartFill, pp => { pp.MapFrom(s => s.AUART_FILL); })
                .ForMember(record => record.MatnrComp, pp => { pp.MapFrom(s => s.MATNR_COMP); })
                .ForMember(record => record.MaktxComp, pp => { pp.MapFrom(s => s.MAKTX_COMP); })
                .ForMember(record => record.PsmngComp, pp => { pp.MapFrom(s => s.PSMNG_COMP); })
                .ForMember(record => record.AmeinComp, pp => { pp.MapFrom(s => s.AMEIN_COMP); })
                .ForMember(record => record.MngPu, pp => { pp.MapFrom(s => s.MNG_PU); })
                .ForMember(record => record.MngPuo, pp => { pp.MapFrom(s => s.MNG_PUO); })
                .ForMember(record => record.BatchFw, pp => { pp.MapFrom(s => s.BATCH_FW); })
                .ForMember(record => record.ShelfFw, pp => { pp.MapFrom(s => s.SHELF_FW); })
                .ForMember(record => record.VeranFw, pp => { pp.MapFrom(s => s.VERAN_FW); })
                .ForMember(record => record.MaktxCFw, pp => { pp.MapFrom(s => s.MAKTX_C_FW); })
                .ForMember(record => record.Lhmg1Fw, pp => { pp.MapFrom(s => s.LHMG1_FW); })
                .ForMember(record => record.MengeCFw, pp => { pp.MapFrom(s => s.MENGE_C_FW); })
                .ForMember(record => record.FlagMes, pp => { pp.MapFrom(s => s.Flag_MES); })
                .ForMember(record => record.Fgprie, pp => { pp.MapFrom(s => s.FGPRI); })
                .ReverseMap();

            CreateMap<CookinglossEntity, CookinglossExcelDto>(MemberList.None).ReverseMap();
            CreateMap<CipTimeEntity, CipTimeExcelDto>(MemberList.None).ReverseMap();
            CreateMap<CipSwitchtypeEntity, CipSwitchtypeExcelDto>(MemberList.None).ReverseMap();

            CreateMap<PlanOrderEntity, PlanOrderExcelDto>(MemberList.None).ReverseMap();
            CreateMap<WorkorderthroatEntity, WorkorderthroatExcelDto>(MemberList.None).ReverseMap();
            CreateMap<PoExecutionHistroyViewEntity, ProduceOpenModel>(MemberList.None).ReverseMap();

            CreateMap<OilFormulaEntity, OilFormulaExcelDto>(MemberList.None).ReverseMap();
            CreateMap<PackspeedEntity, PackspeedExcelDto>(MemberList.None).ReverseMap();

            CreateMap<StandardPeriodLotEntity, StandardPeriodLotRequestModel>(MemberList.None).ReverseMap();
            CreateMap<WeekScheduleEntity, WeekScheduleRequestModel>(MemberList.None).ReverseMap();
            CreateMap<WeekScheduleBomEntity, WeekScheduleBomRequestModel>(MemberList.None).ReverseMap();
        }
    }
}
