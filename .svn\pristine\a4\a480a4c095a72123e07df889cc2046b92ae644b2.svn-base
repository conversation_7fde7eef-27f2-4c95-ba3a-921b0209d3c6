﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Abp.Extensions;
using SEFA.Base;
using SEFA.Base.Common.Extensions;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.Base.Common.LogHelper;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.PrintView;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.View;
using SqlSugar;
using BaseUniqueNumberEntity = SEFA.DFM.Model.Models.BaseUniqueNumberEntity;
using BatchEntity = SEFA.PPM.Model.Models.BatchEntity;
using EquipmentEntity = SEFA.MKM.Model.Models.EquipmentEntity;
using EquipmentMaterialEntity = SEFA.DFM.Model.Models.EquipmentMaterialEntity;
using MaterialEntity = SEFA.DFM.Model.Models.MaterialEntity;
using MaterialPropertyValueEntity = SEFA.DFM.Model.Models.MaterialPropertyValueEntity;
using UnitmanageEntity = SEFA.DFM.Model.Models.UnitmanageEntity;

namespace SEFA.MKM.Services
{
    public class MaterialPreparationViewServices : BaseServices<MaterialPreparationViewEntity>,
        IMaterialPreparationViewServices
    {
        /// <summary>
        /// 注册打印
        /// </summary>
        private readonly IPrintSelectViewServices _IPrintSelectViewServices;

        private readonly IBaseRepository<LabelPrinterEntity> _LabelPrinterEntityDal;

        private readonly IBaseRepository<EquipmentEntity> _EquipmentEntitydal;

        #region MyRegion

        private readonly IBaseRepository<PoProducedRequirementEntity> _PoProducedRequirementEntitydal;
        private readonly IBaseRepository<BatchProducedRequirementEntity> _BatchProducedRequirementEntitydal;
        private readonly IBaseRepository<PPM.Model.Models.UnitmanageEntity> _UnitmanageEntitydal;

        #endregion

        #region 工单

        private readonly IBaseRepository<ProductionOrderEntity> _ProductionOrderEntitDal;
        private readonly IBaseRepository<MlabelCreateViewEntity> _MlabelCreateViewEntityDal;
        private readonly IBaseRepository<PoConsumeRequirementEntity> _PPoConsumeRequirementEntitDal;
        private readonly IBaseRepository<BatchConsumeRequirementEntity> _BatchConsumeRequirementEntityDal;
        private readonly IBaseRepository<PPM.Model.Models.MaterialPropertyValueEntity> _MaterialPropertyValueEntityDal;

        #endregion

        #region 物料属性编码值常量

        public const string PREWEIGH_TOLERANCE_MAX_PERCENT = "PreweighToleranceMaxPercent";
        public const string PREWEIGH_TOLERANCE_MIN_PERCENT = "PreweighToleranceMinPercent";
        public const string FULL_BAG_WEIGHT = "FullBagWeight";
        public const string NEED_WEIGHING_CHECK = "NeedWeighingCheck";
        public const string REQUIRES_PRE_WEIGH = "RequiresPreWeigh";

        #endregion


        private readonly IBaseRepository<InventKqtyViewEntity> _InventKqtyViewEntityDal;
        private readonly IBaseRepository<UserinfoEntity> _dalUserinfoEntity;
        private readonly IBaseRepository<MaterialPreparationViewEntity> _dal;
        private readonly IBaseRepository<MaterialLabelViewEntity> _MaterialLabelViewEntityDal;
        private readonly IBaseRepository<BBatchDetailViewEntity> _dalBBatchDetailViewEntity;
        private readonly IBaseRepository<BBatchDetailIiViewEntity> _dalBBatchDetailIiViewEntity;
        private readonly IBaseRepository<BBatchDetailMaterialViewEntity> _dalBBatchDetailMaterialViewEntity;
        private readonly IBaseRepository<BaseUniqueNumberEntity> _dalUniqueNumberEntity;
        private readonly IBaseRepository<ContainerEntity> _dalContainerEntity;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBaseRepository<ContainerHistoryEntity> _ContainerHistoryEntityDal;
        private readonly IUser _uIser;
        private readonly IBaseRepository<MaterialSubLotEntity> _materialSubLotServicesDal;
        private readonly IBaseRepository<MaterialLotEntity> _materialLotEntityDal;
        private readonly IBaseRepository<MMaterialPropertyViewEntity> _materialPropertyDal;
        private readonly IBaseRepository<ClassEntity> _classDal;
        private readonly IBaseRepository<MaterialInventoryEntity> _materialInventoryEntityDal;

        private readonly IBaseRepository<MaterialTransferEntity> _materialTransferEntityDal;
        private readonly IBaseRepository<DicMaterialPreselectViewEntity> _dicMaterialPreselectViewEntityDal;
        private readonly IBaseRepository<MPreparationViewEntity> _MPreparationViewEntity;
        private readonly IBaseRepository<BatchEntity> _BatchEntity;


        //private readonly IInventorylistingViewServices _inventorylistingViewServices;
        //容器下拉框
        private readonly IBaseRepository<DicBatchPalletselectViewEntity> _dicBatchPalletselectViewEntitydal;

        //批次最下面(table表)
        private readonly IBaseRepository<BBatchDetailMcontainerViewEntity> _bBatchDetailMcontainerViewEntitydal;

        //首页ROOM下拉框
        private readonly IBaseRepository<MEquipmentroomViewEntity> _mEquipmentroomViewEntityDal;

        private readonly IBaseRepository<BBdetailIiViewEntity> _bBdetailIiViewEntityDal;

        private readonly IBaseRepository<BclblDetailViewEntity> _bclblDetailViewEntityDal;

        #region 按照物料备料

        private readonly IBaseRepository<BBatchDetailbymaterialViewEntity> _bBatchDetailbymaterialViewEntitydal;
        private readonly IBaseRepository<MaterialEntity> _MaterialEntitydal;

        //private readonly IBaseRepository<SegmentlistEntity> _SegmentlistEntitydal;
        private readonly IBaseRepository<MBatchriiViewEntity> _SegmentlistEntitydal;

        //
        private readonly IBaseRepository<ContainerBatchViewEntity> _ContainerBatchViewEntitydal;
        private readonly IBaseRepository<ContainerEntity> _ContainerEntity;

        private readonly IBaseRepository<MContainerViewEntity> _MContainerEntity;

        #endregion


        private readonly IBaseRepository<InventorylistingViewEntity> _InventorylistingViewEntitydal;
        private readonly IBaseRepository<ClblDiiViewEntity> _dalClblDiiViewEntity;
        private readonly IBaseRepository<InvnetqtyKyViewEntity> _dalInventKcViewEntity;

        #region 筛选数据源

        private readonly IBaseRepository<EquipmentMaterialEntity> _dalEquipmentMaterialEntity;
        private readonly IBaseRepository<MaterialGroupMappingEntity> _dalMaterialGroupMappingEntity;

        private readonly IInventorylistingViewServices _inventorylistingViewServices;

        #endregion

        private readonly IBaseRepository<ViewEntity> _ViewEntityDal;
        private readonly IBaseRepository<PoProducedExecutionEntity> _PoProducedExecutionEntityDal;
        private readonly IBaseRepository<PoConsumeActualEntity> _PoConsumeActualEntityDal;

        //     private readonly IBaseRepository<PoConsumeRequirementEntity> _BatchConsumeRequirementEntityDal;

        public MaterialPreparationViewServices(IBaseRepository<MaterialPreparationViewEntity> dal,
            IBaseRepository<BBatchDetailViewEntity> dalBBatchDetailViewEntity,
            IBaseRepository<BBatchDetailIiViewEntity> dalBBatchDetailIiViewEntity,
            IBaseRepository<BBatchDetailMaterialViewEntity> dalBBatchDetailMaterialViewEntity,
            IBaseRepository<BaseUniqueNumberEntity> dalUniqueNumberEntity,
            IBaseRepository<ContainerEntity> dalContainerEntity,
            IBaseRepository<ContainerHistoryEntity> containerHistoryEntityDal,
            IBaseRepository<MaterialSubLotEntity> materialSubLotServicesDal,
            IBaseRepository<MaterialLotEntity> materialLotEntityDal,
            IBaseRepository<MMaterialPropertyViewEntity> materialPropertyDal,
            IBaseRepository<MaterialInventoryEntity> materialInventoryEntity,
            IBaseRepository<MaterialTransferEntity> materialTransferEntityDal,
            IBaseRepository<DicMaterialPreselectViewEntity> dicMaterialPreselectViewEntityDal,
            IBaseRepository<DicBatchPalletselectViewEntity> dicBatchPalletselectViewEntitydal,
            IBaseRepository<BBatchDetailMcontainerViewEntity> bBatchDetailMcontainerViewEntitydal,
            IBaseRepository<BBatchDetailbymaterialViewEntity> bBatchDetailbymaterialViewEntitydal, IUser uIser,
            IUnitOfWork unitOfWork, IBaseRepository<MEquipmentroomViewEntity> mEquipmentroomViewEntityDal,
            IBaseRepository<MBatchriiViewEntity> segmentlistEntitydal,
            IBaseRepository<ContainerBatchViewEntity> containerBatchViewEntitydal,
            IBaseRepository<MPreparationViewEntity> MPreparationViewEntity,
            IBaseRepository<ContainerEntity> ContainerEntity,
            IBaseRepository<BatchEntity> BatchEntity,
            IBaseRepository<InventorylistingViewEntity> InventorylistingViewEntitydal,
            IBaseRepository<MContainerViewEntity> MContainerEntityDal,
            IBaseRepository<BBdetailIiViewEntity> bBdetailIiViewEntityDal,
            IBaseRepository<ClblDiiViewEntity> dalClblDiiViewEntity,
            IBaseRepository<BclblDetailViewEntity> bclblDetailViewEntityDal,
            IBaseRepository<InvnetqtyKyViewEntity> dalInventKcViewEntity,
            IBaseRepository<EquipmentMaterialEntity> dalEquipmentMaterialEntity,
            IBaseRepository<MaterialGroupMappingEntity> dalMaterialGroupMappingEntity
            , IInventorylistingViewServices inventorylistingViewServices
            , IBaseRepository<ViewEntity> viewEntityDal
            , IBaseRepository<MaterialLabelViewEntity> materialLabelViewEntityDal
            , IBaseRepository<ProductionOrderEntity> productionOrderEntitDal
            , IBaseRepository<PoConsumeRequirementEntity> pPoConsumeRequirementEntitDal
            , IBaseRepository<BatchConsumeRequirementEntity> batchConsumeRequirementEntityDal
            , IBaseRepository<PPM.Model.Models.MaterialPropertyValueEntity> materialPropertyValueEntityDal
            , IBaseRepository<MaterialEntity> materialEntitydal
            , IBaseRepository<MlabelCreateViewEntity> mlabelCreateViewEntityDal
            , IBaseRepository<PoProducedRequirementEntity> poProducedRequirementEntitydal
            , IBaseRepository<BatchProducedRequirementEntity> batchProducedRequirementEntitydal
            , IBaseRepository<PPM.Model.Models.UnitmanageEntity> unitmanageEntitydal
            , IPrintSelectViewServices iPrintSelectViewServices
            , IBaseRepository<PoProducedExecutionEntity> poProducedExecutionEntityDal
            , IBaseRepository<PoConsumeActualEntity> poConsumeActualEntityDal
            , IBaseRepository<UserinfoEntity> dalUserinfoEntity
            , IBaseRepository<InventKqtyViewEntity> inventKqtyViewEntityDal
            , IBaseRepository<MMaterialPropertyViewEntity> mMaterialPropertyViewNewDal
            , IBaseRepository<EquipmentEntity> equipmentEntitydal
            , IBaseRepository<LabelPrinterEntity> labelPrinterEntityDal
            , IBaseRepository<ClassEntity> classDal
            //IInventorylistingViewServices inventorylistingViewServices
        )

        {
            this._dal = dal;
            _MPreparationViewEntity = MPreparationViewEntity;
            _dalBBatchDetailViewEntity = dalBBatchDetailViewEntity;
            _dalBBatchDetailIiViewEntity = dalBBatchDetailIiViewEntity;
            _dalBBatchDetailMaterialViewEntity = dalBBatchDetailMaterialViewEntity;
            base.BaseDal = dal;
            _dalUniqueNumberEntity = dalUniqueNumberEntity;
            _dalContainerEntity = dalContainerEntity;
            _ContainerHistoryEntityDal = containerHistoryEntityDal;
            _materialSubLotServicesDal = materialSubLotServicesDal;
            _materialLotEntityDal = materialLotEntityDal;
            _materialPropertyDal = materialPropertyDal;
            _materialInventoryEntityDal = materialInventoryEntity;
            _materialTransferEntityDal = materialTransferEntityDal;
            _dicMaterialPreselectViewEntityDal = dicMaterialPreselectViewEntityDal;
            _dicBatchPalletselectViewEntitydal = dicBatchPalletselectViewEntitydal;
            _bBatchDetailMcontainerViewEntitydal = bBatchDetailMcontainerViewEntitydal;
            _bBatchDetailbymaterialViewEntitydal = bBatchDetailbymaterialViewEntitydal;
            _classDal = classDal;
            _uIser = uIser;
            _unitOfWork = unitOfWork;
            _mEquipmentroomViewEntityDal = mEquipmentroomViewEntityDal;
            _SegmentlistEntitydal = segmentlistEntitydal;
            _ContainerBatchViewEntitydal = containerBatchViewEntitydal;
            _ContainerEntity = ContainerEntity;
            _BatchEntity = BatchEntity;
            _InventorylistingViewEntitydal = InventorylistingViewEntitydal;
            _MContainerEntity = MContainerEntityDal;
            _bBdetailIiViewEntityDal = bBdetailIiViewEntityDal;
            _dalClblDiiViewEntity = dalClblDiiViewEntity;
            _bclblDetailViewEntityDal = bclblDetailViewEntityDal;
            _dalInventKcViewEntity = dalInventKcViewEntity;
            //_inventorylistingViewServices = inventorylistingViewServices;
            _dalEquipmentMaterialEntity = dalEquipmentMaterialEntity;
            _dalMaterialGroupMappingEntity = dalMaterialGroupMappingEntity;
            _inventorylistingViewServices = inventorylistingViewServices;
            _ViewEntityDal = viewEntityDal;
            _MaterialLabelViewEntityDal = materialLabelViewEntityDal;
            _ProductionOrderEntitDal = productionOrderEntitDal;
            _PPoConsumeRequirementEntitDal = pPoConsumeRequirementEntitDal;
            _BatchConsumeRequirementEntityDal = batchConsumeRequirementEntityDal;
            _MaterialPropertyValueEntityDal = materialPropertyValueEntityDal;
            _MaterialEntitydal = materialEntitydal;
            _MlabelCreateViewEntityDal = mlabelCreateViewEntityDal;
            _PoProducedRequirementEntitydal = poProducedRequirementEntitydal;
            _BatchProducedRequirementEntitydal = batchProducedRequirementEntitydal;
            _UnitmanageEntitydal = unitmanageEntitydal;
            _IPrintSelectViewServices = iPrintSelectViewServices;
            _PoProducedExecutionEntityDal = poProducedExecutionEntityDal;
            _PoConsumeActualEntityDal = poConsumeActualEntityDal;
            _dalUserinfoEntity = dalUserinfoEntity;
            _InventKqtyViewEntityDal = inventKqtyViewEntityDal;
            _EquipmentEntitydal = equipmentEntitydal;
            _LabelPrinterEntityDal = labelPrinterEntityDal;
        }

        #region 拼锅PDA

        /// <summary>
        /// 根据条码查询对应的容器编码是否存在
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetContainerPDA(string sscc)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                var conModel = await _dalContainerEntity.FindEntity(p => p.Name == sscc.Trim());
                if (conModel != null)
                {
                    if (conModel.Status == "3")
                    {
                        result.msg = "当前条码" + sscc + "已完成拼锅";
                        return result;
                    }

                    string proID = conModel.ProductionRequestId;
                    string batchID = conModel.ProductionBatchId;
                    string epqumentID = conModel.EquipmentId;
                    if (string.IsNullOrEmpty(proID) || string.IsNullOrEmpty(batchID) ||
                        string.IsNullOrEmpty(epqumentID))
                    {
                        result.msg = "当前条码" + sscc + "缺少批次或工单为空";
                        return result;
                    }
                    else
                    {
                        var batchData = await _BatchEntity.FindEntity(p => p.ID == batchID);

                        if (batchData == null)
                        {
                            result.msg = "容器" + sscc + "没有找到工单批次";
                            return result;
                        }

                        //这里返回重要参数以分号区分
                        //batchID;proID;equpmentID;batchData.Number;当前批次号;括号内当前第几批;+容器id
                        string returnMsg = batchID + ";" + proID + ";" + epqumentID + ";" + batchData.Number + ";" +
                                           conModel.ID;
                        result.msg = returnMsg;
                        result.success = true;
                        return result;
                    }
                }

                result.msg = "当前条码“" + sscc + "”非托盘码请确认";
                return result;
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 根据工单号查询表头数据(获取第一数据)（缸号，返回的数据取第1行数据 缸号：Sequence/Sequencetotal ）
        /// 工单号:ProOrder+(GetContainerPDA最后个数据)+配方 FormulaNo +缸号：Sequence 批次产量：来自于接口GetListByBatchIDPDA 汇总
        /// 生产线：EquipmentCode 生产日期：PlanStartTime
        /// </summary>
        /// <param name="ProID">工单号</param>
        /// <param name="RoomID">拼锅room的ID</param>
        /// <returns></returns>
        public async Task<PageModel<MaterialPreparationViewEntity>> GetPGTopPDA(
            MaterialPreparationViewRequestModel reqModel)
        {
            //第一步筛选所有数据
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();

            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProID), a => a.ID.Contains(reqModel.ProID))
                .And(p => p.Sapordertype == "ZXH2")
                .ToExpression();

            var dataF = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime).ToListAsync();

            #region 筛选数据

            //      List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);

            #endregion

            //筛选所有的ID   
            //string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            //if (mIDs == null || mIDs.Length <= 0)
            //{
            //    result.dataCount = 0;
            //    result.data = new List<MaterialPreparationViewEntity>();
            //    return result;
            //}


            //    mIDs = _MaterialEntitydal.Db.Queryable<DFM.Model.Models.MaterialEntity>().In(p => p.Code, mIDs).Select(mp => mp.ID).ToArray();

            //if (mIDs == null || mIDs.Length <= 0)
            //{
            //    result.dataCount = 0;
            //    result.data = new List<MaterialPreparationViewEntity>();
            //    return result;
            //}

            //筛选订单
            string[] proIDs = dataF.Select(p => p.ID).ToArray();
            var poM = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                .In(p => p.ProductionOrderId, proIDs).ToListAsync();
            if (poM == null || poM.Count <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            string[] mIDs = poM.Select(mp => mp.MaterialId).ToArray();

            //筛选
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();

            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            poM = poM.Where(p => mIDs.Contains(p.MaterialId)).ToList();


            if (proIDs == null || proIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            #region 这里筛选工单

            string[] poList = poM.GroupBy(p => p.ID).Select(p => p.Key).ToArray();

            var batchQTY = await _BatchConsumeRequirementEntityDal.Db.Queryable<BatchConsumeRequirementEntity>()
                .In(p => p.PoConsumeRequirementId, poList).Where(P => P.Quantity > 0).ToListAsync();

            string[] proIDsSearch = (from a in poM
                join b in batchQTY on a.ID equals b.PoConsumeRequirementId
                select new
                {
                    ID = a.ProductionOrderId,
                }).Distinct().ToList().Select(p => p.ID).ToArray();

            #endregion


            if (proIDsSearch == null || proIDsSearch.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }


            List<MaterialPreparationViewEntity> list = new List<MaterialPreparationViewEntity>();
            //筛选批次
            var batchListNew = await _BatchEntity.Db.Queryable<BatchEntity>()
                .In(p => p.ProductionOrderId, proIDs).ToListAsync();
            for (int i = 0; i < dataF.Count; i++)
            {
                string id = dataF[i].ID;
                int count = proIDsSearch.Where(p => p == id).Count();
                if (count > 0)
                {
                    var batch = batchListNew.Where(p => p.ProductionOrderId == id).OrderBy(p => p.Number).ToList()
                        .FirstOrDefault();
                    if (batch != null)
                    {
                        //重新计算需求总量
                        decimal qty = batchQTY.Where(p => p.BatchId == batch.ID).Sum(p => p.Quantity);
                        dataF[i].PlanQty = qty;

                        string pre_State = string.Empty;
                        pre_State = batch.PrepStatus;
                        if (!string.IsNullOrEmpty(pre_State))
                        {
                            string state = GetPre_State(pre_State);
                            dataF[i].NowState = state;
                        }
                    }

                    list.Add(dataF[i]);
                }
            }

            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          

            // 获取指定页的数据
            //  var rDat1 = list.OrderByDescending(p => p.PlanStartTime).ThenBy(p => p.Sequence).Skip(startIndex).Take(reqModel.pageSize).ToList();
            var rDat = list.OrderBy(p => p.LineCode).ThenBy(p => p.PlanStartTime).ThenBy(p => p.Psequence)
                .ThenBy(p => p.ProOrder).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = list.Count;
            result.data = rDat;
            return result;
        }

        /// <summary>
        /// 根据批次ID和eqpmentID获取数据源（底部数据源）（物料 ：MCode +MName  数量：MQuantity  /MQuantityTotal MQuantityunit 批次产量： returnData[0].Total）
        /// </summary>
        /// <param name="batchID">批次id</param>
        /// <param name="eqpmentID">拼锅返回数据</param>
        /// <returns></returns>
        public async Task<List<BBatchDetailIiViewEntityModel>> GetListByBatchIDPDA(string batchID, string eqpmentID)
        {
            //List<MpModel> mpList = await Get_DisMaterial(eqpmentID);
            //string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            //if (mIDs == null || mIDs.Length <= 0)

            //{
            //    return new List<BBatchDetailIiViewEntityModel>();
            //}
            BBatchDetailIiViewEntityModel returnData = new BBatchDetailIiViewEntityModel();
            List<BBatchDetailIiViewEntity> data = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            data = await _dalBBatchDetailIiViewEntity.Db
                .Queryable<BBatchDetailIiViewEntity>() //.In(p => p.MaterialId, mIDs)
                .Where(whereExpression).ToListAsync();

            decimal max = 0;
            decimal min = 0;
            decimal finish = 0;
            decimal maxR = 0;
            decimal minR = 0;
            string unitName = data[0].MaterialUnit1.Trim();

            for (int i = 0; i < data.Count; i++)
            {
                finish = data[i].MQuantity == null ? 0 : data[i].MQuantity.Value;
                min = data[i].MinPvalue == null ? 0 : data[i].MinPvalue.Value;
                max = data[i].MaxPvalue == null ? 0 : data[i].MaxPvalue.Value;

                if (finish >= min && finish <= max)
                {
                    data[i].MaterialUnit1 = "green";
                }
                else if (finish < min)
                {
                    data[i].MaterialUnit1 = "orange";
                }
                else if (finish > max)
                {
                    data[i].MaterialUnit1 = "red";
                }
                else
                {
                    data[i].MaterialUnit1 = "orange";
                }

                //再次判断橙色
                if (data[i].MaterialUnit1 == "orange")
                {
                    minR = data[i].MinPvalue == null ? 0 : Math.Round(data[i].MinPvalue.Value, 3);
                    maxR = data[i].MaxPvalue == null ? 0 : Math.Round(data[i].MaxPvalue.Value, 3);
                    if (finish >= minR && finish <= maxR)
                    {
                        data[i].MaterialUnit1 = "green";
                    }
                }
            }

            //排序
            returnData.listData = data.OrderByDescending(P => P.MQuantityTotal).ThenBy(P => P.MQuantity).ToList();
            if (data.Count > 0)
            {
                PPM.Model.Models.UnitmanageEntity modelUnit =
                    await _UnitmanageEntitydal.FindEntity(p => p.Name == unitName);
                returnData.UnitID = modelUnit.ID;
            }

            returnData.Total = data.Sum(p => p.MQuantity).ToString(); // + data[0].MQuantityunit;//批次产量


            List<BBatchDetailIiViewEntityModel> list = new List<BBatchDetailIiViewEntityModel>();
            list.Add(returnData);
            return list;
        }

        /// <summary>
        /// 判断当前数据所处位置（托盘/备料小标签+ invent.SubLotId;"/可用库存++ invent.SubLotId;）分号
        /// </summary>
        /// <param name="cName">容器号</param>
        /// <param name="sscc">扫码的二维码</param>
        /// <param name="batchID">批次ID</param>
        /// <param name="proID">工单ID</param>
        /// <param name="equpmentID">ROOMID</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> GetCodeTypeByPDA(string cName, string sscc, string batchID,
            string proID, string equpmentID)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //判断当前是否是容器
                var conModel = await _dalContainerEntity.FindEntity(p => p.Name == sscc.Trim());
                if (conModel != null)
                {
                    if (cName != conModel.Name.Trim() && cName != conModel.ID.Trim())
                    {
                        result.msg = "当前托盘条码" + sscc + "不属于该工单批次";
                        return result;
                    }

                    string pID = conModel.ProductionRequestId;
                    string bID = conModel.ProductionBatchId;

                    if (string.IsNullOrEmpty(pID) || string.IsNullOrEmpty(bID))
                    {
                        result.msg = "当前托盘条码" + sscc + "缺少批次或工单为空";
                        return result;
                    }
                    else
                    {
                        result.msg = "托盘";
                        result.success = true;
                        return result;
                    }
                }
                else
                {
                    var invent = await _InventorylistingViewEntitydal.FindEntity(p => p.Sscc == sscc);
                    if (invent != null)
                    {
                        string pID = invent.ProductionRequestId;
                        string bID = invent.BatchId2;
                        //该工单的物料
                        if (!string.IsNullOrEmpty(bID) && !string.IsNullOrEmpty(pID))
                        {
                            if (batchID == bID && pID == proID)
                            {
                                result.success = true;
                                result.msg = "备料小标签;" + invent.SubLotId;
                                return result;
                            }
                            else
                            {
                                result.msg = "当前标签“" + sscc + "”不属于该工单批次";
                                return result;
                            }
                        }
                        else
                        {
                            //默认认为是可用库存
                            result.success = true;
                            result.msg = "可用库存;" + invent.SubLotId;
                            return result;
                        }
                    }

                    result.msg = "当前条码“" + sscc + "”非托盘码和追溯码请确认";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result.msg = ex.Message;
                return result;
            }
        }


        /// <summary>  
        /// 用来查询可用库存的数据(实体)：sscc:  SSCC 追溯码 Bag 包数  Size 单包重量  MUnit 单位 TargetWeight 目标重量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        public async Task<InVentPDAModel> GetInventPDA(BBatchDetailIIModel model)
        {
            InVentPDAModel models = new InVentPDAModel();

            var data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()
                .Where(p => p.BatchId == model.batchID) //.In(p => p.MaterialId, mIDs)
                .ToListAsync();

            //查询当前库存
            var invents = await _InventorylistingViewEntitydal.FindList(p => p.Sscc == model.SSCC.Trim());

            //计算汇总库存数据，按照物料和设备
            var groupByInventList = from a in invents
                group a by new
                {
                    a.MaterialCode
                }
                into g
                select new
                {
                    g.Key.MaterialCode,
                    Quantity = g.Sum(p => p.Quantity)
                };


            List<BBdetailIiViewEntity> RData = (from a in data
                join r in groupByInventList on a.MCode equals r.MaterialCode //into groupJoin
                //           from r in groupJoin.DefaultIfEmpty()
                select new BBdetailIiViewEntity
                {
                    ChangeUnit = a.ChangeUnit,
                    ID = a.ID,
                    Mymin = a.Mymin,
                    Mymax = a.Mymax,
                    EquipmentId = a.EquipmentId,
                    OnlyId = model.SSCC.Trim(),
                    ProductionOrderNo = a.ProductionOrderNo,
                    FormulaNo = a.FormulaNo,
                    MBatchNumber = a.MBatchNumber,
                    MName = a.MName,
                    MCode = a.MCode,
                    MQuantity = a.MQuantity,
                    MQuantityunit = a.MQuantityunit,
                    MQuantityTotal = a.MQuantityTotal,
                    QuantityTotalUnit = a.QuantityTotalUnit,
                    TUintid = a.TUintid,
                    InQuantity = r.Quantity.Value,
                    MaterialUnit1 = a.MaterialUnit1,
                    BagSize = a.BagSize,
                    FullPage = a.FullPage,
                    TagpS = a.TagpS,
                    ParitialPage = a.ParitialPage,
                    TagpSUnit = a.TagpSUnit,
                    FullFinish = a.FullFinish,
                    BagS = a.BagS,
                    CompleteStates = a.CompleteStates,
                    ConsumedStates = a.ConsumedStates,
                    MaterialId = a.MaterialId,
                    MinPvalue = a.MinPvalue,
                    SetMin = a.SetMin,
                    MaxPvalue = a.MaxPvalue,
                    SetMax = a.SetMax,
                    ConSumed = a.ConSumed,
                    BatchId = a.BatchId,
                    ProductionOrderId = a.ProductionOrderId,
                    LineCode = a.LineCode,
                    Sequencetotal = a.Sequencetotal
                }).ToList();

            if (RData != null && RData.Count > 0)
            {
                models.SSCC = model.SSCC.Trim();
                models.Size = RData[0].BagSize; //单包重量
                models.MUnit = RData[0].MQuantityunit;
                models.TargetWeight = RData[0].InQuantity; //目标重量
                models.SSCCID = invents[0].SlotId;
                models.MaterialId = invents[0].SlotId;
                //计算差值
                string fullBag = RData[0].FullPage;
                string[] fulls = fullBag.Split('/');
                if (fulls != null && fulls.Length >= 2)
                {
                    //还差的整袋数量
                    int nFinish = Convert.ToInt32(fulls[1]) - Convert.ToInt32(fulls[0]);

                    if (nFinish == 0)
                    {
                        models.Bag = "0";
                    }
                    else
                    {
                        // models.Bag= nFinish.ToString();
                        decimal nFinishDecimal = Convert.ToDecimal(nFinish * Convert.ToInt32(RData[0].BagSize));
                        // 总需求量 <= 库存数量（默认为需求袋数）
                        if (nFinishDecimal <= RData[0].MQuantityTotal)
                        {
                            //这里做一个防错
                            decimal minValue = RData[0].MinPvalue.Value - RData[0].MQuantity.Value;
                            decimal maxValue = RData[0].MaxPvalue.Value - RData[0].MQuantity.Value;
                            models.Bag = nFinish.ToString();

                            //如果多加一包不超过范围（就多加一包）
                            decimal maxQty = Convert.ToDecimal(models.Bag) * Convert.ToDecimal(models.Size);
                            if (maxValue > maxQty)
                            {
                                //多加一包
                                decimal lastValue = maxQty + Convert.ToDecimal(models.Size);
                                if (lastValue <= maxValue)
                                {
                                    models.Bag = (Convert.ToInt32(models.Bag) + 1).ToString();
                                }
                            }
                        }
                        else
                        {
                            int bag = Convert.ToInt32(RData[0].MQuantityTotal) / Convert.ToInt32(RData[0].BagSize);
                            models.Bag = bag.ToString();
                        }
                    }
                }
                // models.Bag=
            }

            //if (RData[0].ChangeUnit == "g") 
            //{
            //    models.Size = "";
            //    models.MUnit = "g";
            //}
            return models;
        }

        /// <summary>
        /// 工单库存转移
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_FullAmountPDA(FullAmountModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行转移", state);
                        return result;
                    }
                }

                #endregion

                string userId = _uIser.Name.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SbSscc == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();
                if (dataModel == null)
                {
                    result.msg = "没有找到追溯码信息";
                    return result;
                }

                reqModel.subID = dataModel.SubId;
                decimal inQty = dataModel.InQuantity;

                ///查询当前批次库存信息
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);
                MaterialInventoryEntity upModelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "该批次不存在";
                    return result;
                }

                #region 限制批次

                string pID = inventMaterialList[0].ProductionRequestId;
                string mID = dataModel.MaterialId;
                string lotCode = dataModel.LBatch;
                if (!string.IsNullOrEmpty(pID) && !string.IsNullOrEmpty(mID))
                {
                    var ppList =
                        await _PPoConsumeRequirementEntitDal.FindList(p =>
                            p.ProductionOrderId == pID && p.MaterialId == mID);
                    if (ppList != null && ppList.Count > 0)
                    {
                        string mLot = ppList[0].MaterialLotNo;

                        if (!string.IsNullOrEmpty(mLot))
                        {
                            if (lotCode != mLot)
                            {
                                result.msg = "所选库存非指定批次，不允许使用!";
                                return result;
                            }
                        }
                    }
                }

                #endregion

                if (!string.IsNullOrEmpty(inventMaterialList[0].ContainerId))
                {
                    result.msg = "当前追溯码已经绑定容器";
                }

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                if (string.IsNullOrEmpty(reqModel.containerID))
                {
                    result.msg = "请选择容器";
                    return result;
                }

                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);
                //判断当前容器是否是完成状态
                if (model != null)
                {
                    string conState = model.Status;
                    if (conState == "3")
                    {
                        result.msg = "当前容器已完成，请选择其他容器";
                        return result;
                    }
                }

                //
                upModelMaterialInvent = inventMaterialList[0];
                decimal inventValue = inventMaterialList[0].Quantity;

                if (inventMaterialList[0].Quantity < 0)
                {
                    result.msg = "转移数量为负数";
                    return result;
                }

                //进行绑定容器
                upModelMaterialInvent.Modify(upModelMaterialInvent.ID, _uIser.ID.ToString());
                upModelMaterialInvent.ContainerId = reqModel.containerID == null ? "" : reqModel.containerID;

                if (string.IsNullOrEmpty(upModelMaterialInvent.BatchId))
                {
                    upModelMaterialInvent.InventoryType = "Full";
                    upModelMaterialInvent.ProductionRequestId = reqModel.proOrderID;
                    upModelMaterialInvent.BatchId = reqModel.batchID;
                }
                else
                {
                    upModelMaterialInvent.InventoryType = "Full";
                }


                try
                {
                    //这里需要根据单包重量来重新判断类型
                    if (upModelMaterialInvent != null)
                    {
                        //查询库存信息
                        var inventModel =
                            await _InventorylistingViewEntitydal.FindEntity(p => p.ID == upModelMaterialInvent.ID);
                        if (inventModel != null)
                        {
                            decimal bageSize = Convert.ToDecimal(inventModel.PropertyValue);

                            decimal zc = inventValue % bageSize;
                            if (zc == 0m || zc == 0)
                            {
                                upModelMaterialInvent.InventoryType = "Full";
                            }
                            else
                            {
                                upModelMaterialInvent.InventoryType = "Partial";
                            }
                        }
                    }
                }
                catch
                {
                }


                upModelMaterialInvent.EquipmentId = reqModel.equpmentID;

                //写入历史

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;


                #region 走业务流程

                _unitOfWork.BeginTran();


                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                //string inventGUID = Guid.NewGuid().ToString();
                //string transer1GUID = Guid.NewGuid().ToString();
                //string transer2GUID = Guid.NewGuid().ToString();


                //库存表(添加)               

                #region 写入容器记录 1

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userId);
                //hisModel1.ID = transer1GUID;
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = eqpmentID;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = inventValue.ToString();
                hisModel1.QuantityUomId = unitID;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = expirationDate;

                listTranHis.Add(hisModel1);

                #endregion

                #region 写入容器记录2

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(userId);
                //hisModel.ID = transer2GUID.ToString();
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ContainerCode = model.Name;
                hisModel.Type = "Transfer";
                hisModel.EquipmentId = eqpmentID;
                hisModel.State = "4";
                hisModel.Comment = "Full Bag Transfer";
                hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                hisModel.BatchId = reqModel.batchID; //工单批次ID
                hisModel.Status = "Full";
                hisModel.Quantity = inventValue.ToString();
                hisModel.QuantityUomId = unitID;
                hisModel.SublotId = reqModel.subID;
                hisModel.LotId = lotID;
                hisModel.MaterialId = dataModel.MaterialId;
                //   hisModel
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ExpirationDate = expirationDate;
                listTranHis.Add(hisModel);

                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userId);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = reqModel.subID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(inventValue), 3);
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "拼锅-整袋转移";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = reqModel.equpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);

                #endregion


                //批量提交

                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                //扣库存（更新原来的数据）
                bool upMaterialInvent = await _materialInventoryEntityDal.Update(upModelMaterialInvent);


                //这里执行新增数据
                if (!hisResult || !tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();

                    result.msg = "转移失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                #region 打印标签

                if (upModelMaterialInvent != null)
                {
                    //打印对应的备料标签                   
                    var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(upModelMaterialInvent.ID);

                    #region 打印新逻辑（根据打印机名字来拿对应的模板）

                    List<PrintSelectViewEntity> printIDData = new List<PrintSelectViewEntity>();

                    //  根据打印机ID查询打印code
                    if (!string.IsNullOrEmpty(reqModel.PrintId))
                    {
                        var entityMode = await _LabelPrinterEntityDal.FindEntity(reqModel.PrintId);

                        if (entityMode != null)
                        {
                            string pCode = entityMode.Code;
                            string pName = entityMode.Description;

                            if (pCode.Contains("移动"))
                            {
                                //移动的打印
                                printIDData =
                                    await _IPrintSelectViewServices.GetPrinit_MoveBagByEqumentID(reqModel.equpmentID);
                            }
                            else
                            {
                                printIDData =
                                    await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);
                            }
                        }
                        else
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "转移成功，请配置打印机";
                        return result;
                    }

                    #endregion

                    //这里拿打印机，默认拿第一个
                    //        var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);//_IPrintSelectViewServices.GetSelectPrinit_Move();

                    if (printIDData != null && printIDData.Count >= 0)
                    {
                        try
                        {
                            //这里筛选对应的打印机
                            if (!string.IsNullOrEmpty(reqModel.PrintId))
                            {
                                printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                            }

                            if (printIDData == null || printIDData.Count <= 0)
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }

                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;

                            //判断当前数据是否包含工单id
                            string pro = inventMaterialList[0].ProductionRequestId;

                            if (!string.IsNullOrEmpty(pro))
                            {
                            }
                            else
                            {
                                //执行打印                                                                        
                                await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID,
                                    teampClassID, inventViewData, inventViewData[0], 1);
                            }

                            // await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                        }
                        catch (Exception ex)
                        {
                            result.success = true;
                            result.msg = "转移成功，打印失败：" + ex.Message;
                            return result;
                        }
                        //执行打印
                    }
                }

                #endregion

                result.success = true;
                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();

                result.msg = "转移失败";
                return result;
            }
        }


        /// <summary>
        /// 拼锅-转移-选可用库存()
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_FullBagPDA(FullBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            var resultno = new MessageModel<string>();
            try
            {
                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行转移", state);
                        return result;
                    }
                }

                #endregion

                decimal oldQty = 0;
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                //绑定数据
                //这里需要扣子批次库存并转移 1首先查询库存数据
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);

                MaterialInventoryEntity modelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "库存中未查询到该批次数据";
                    return result;
                }

                oldQty = inventMaterialList[0].Quantity; //当前库存

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                modelMaterialInvent = inventMaterialList[0];

                if (!string.IsNullOrEmpty(modelMaterialInvent.ContainerId))
                {
                    result.msg = "当前追溯码已经绑定容器";
                    return result;
                }

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;
                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);
                if (model == null)
                {
                    result.msg = "容器不存在";
                    return result;
                }

                if (model.Status == "3")
                {
                    result.msg = "当前托盘已完成拼锅，请重新选择托盘";
                    return result;
                }

                //计算可以拆分的整包数据(这里需要对包数数量进行一次校验，大于则用最大袋数) 
                int maxbags = reqModel.bags * Convert.ToInt32(reqModel.bagWeight);


                if (maxbags == 0)
                {
                    result.msg = "请确认该物料是否需要整包数量";
                    return result;
                }

                int tagerW = Convert.ToInt32(Convert.ToDecimal(reqModel.targetWeight.Trim()));

                if (tagerW < maxbags)
                {
                    result.msg = "当前数量超过需求最大库存，请重新选择库存进行操作";
                    return result;

                    reqModel.bags = maxbags / Convert.ToInt32(reqModel.bagWeight);
                }
                //根据袋数创建库存信息（子批次码需要重新生成不同的数据）批次信息用改数据下的批次信息


                #region 走业务流程

                //MKM_B_MATERIAL_INVENTORY、MKM_B_MATERIAL_LOT、MKM_B_MATERIAL_SUB_LOT中创建库存
                _unitOfWork.BeginTran();

                List<MaterialSubLotEntity> listSubLot = new List<MaterialSubLotEntity>();
                List<MaterialInventoryEntity> listVent = new List<MaterialInventoryEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialInventoryEntity> listVent2 = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                int kjNumber = 0;
                string userID = _uIser.Name.ToString();
                //循环添加的包数

                #region 这里计算需要扣减的数量

                kjNumber = reqModel.bags * Convert.ToInt32(reqModel.bagWeight);

                #endregion

                if (oldQty < kjNumber)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "当前库存小于转移数量";
                    return result;
                }

                string subGUID = string.Empty; //= Guid.NewGuid().ToString();                    

                //子批次表(添加)
                MaterialSubLotEntity modelSubLot = new MaterialSubLotEntity();
                modelSubLot.Create(userID);
                subGUID = modelSubLot.ID;
                //modelSubLot.ID = subGUID;
                modelSubLot.Type = "0";


                #region 构造实体

                SSCCModel models = new SSCCModel();
                models.Type = "";
                models.NextCode = "";
                models.MaxCode = "";
                models.MinCode = "";
                models.Prefix = "";
                models.TableName = "";
                models.TableId = "";
                models.SequenceType = "";
                models.ResetType = "";
                models.FeatureId = "";
                models.pageIndex = 1;
                models.pageSize = 10;
                models.orderByFileds = "";
                string token = _uIser.GetToken();
                var ssccString =
                    await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                if (ssccString.success == true)
                {
                    //ssccString = ssccString.response;
                    modelSubLot.SubLotId = ssccString.response.ToString();
                }

                #endregion

                modelSubLot.ExternalStatus = "3";
                listSubLot.Add(modelSubLot);

                //库存表(添加)//这里需要加下（full bage的状态）
                MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                //modelInventory.ID = inventGUID;
                modelInventory.Create(userID);
                modelInventory.LotId = lotID;
                modelInventory.SublotId = subGUID;
                modelInventory.Quantity =
                    Math.Round(Convert.ToDecimal(kjNumber), 3); //kjNumber;//Convert.ToInt32(reqModel.bagWeight);
                modelInventory.StorageLocation = storage_location;
                modelInventory.EquipmentId = reqModel.equpmentID;
                modelInventory.InventoryType = "Full";
                modelInventory.ContainerId = reqModel.containerID;
                modelInventory.QuantityUomId = unitID;
                modelInventory.BatchId = reqModel.batchID;
                modelInventory.ProductionRequestId = reqModel.proOrderID;
                listVent.Add(modelInventory);

                #region MKM_L_CONTAINER_HISTORY 1

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userID);
                //hisModel1.ID = transer1GUID;
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = eqpmentID;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                //hisModel1.ProductOrderId = reqModel.proOrderID;// 工单ID
                //hisModel1.BatchId = reqModel.batchID;//工单批次ID
                //hisModel1.MaterialId = reqModel.MaterialId;
                //hisModel1.SublotId = reqModel.subID;
                hisModel1.Quantity = reqModel.actualWeight == null ? "0" : reqModel.actualWeight.ToString();
                hisModel1.QuantityUomId = unitID;
                // hisModel1.LotId = lotID;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = expirationDate;

                listTranHis.Add(hisModel1);

                #endregion

                #region MKM_L_CONTAINER_HISTORY

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(userID);
                //hisModel.ID = transer2GUID.ToString();
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ContainerCode = model.Name;
                hisModel.Type = "Transfer";
                hisModel.EquipmentId = eqpmentID;
                hisModel.State = "4";
                hisModel.Comment = "Full Bag Transfer";
                hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                hisModel.BatchId = reqModel.batchID; //工单批次ID
                hisModel.Status = "Full";
                hisModel.Quantity = reqModel.bagWeight.ToString();
                hisModel.QuantityUomId = unitID;
                hisModel.SublotId = subGUID;
                hisModel.LotId = lotID;
                hisModel.MaterialId = dataModel.MaterialId;
                //   hisModel
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ExpirationDate = expirationDate;
                listTranHis.Add(hisModel);

                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userID);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = subGUID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(kjNumber), 3); //Convert.ToDecimal(reqModel.bagWeight);
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "拼锅-转移";
                trans.ProductionExecutionId = "";
                trans.NewProductionExecutionId = reqModel.proOrderID;
                trans.OldBatchId = "";
                trans.NewBatchId = reqModel.batchID;
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = eqpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);

                #endregion


                //当库存为0时删除库存
                modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity), 3) -
                                               Math.Round(Convert.ToDecimal(kjNumber),
                                                   3); // modelMaterialInvent.Quantity - Convert.ToDecimal(kjNumber);
                if (modelMaterialInvent.Quantity < 0)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "当前库存小于转移数量";
                    return result;
                }

                if (modelMaterialInvent.Quantity == 0)
                {
                    var inventoryModel = await _InventorylistingViewEntitydal.QueryById(modelMaterialInvent.ID);

                    if (inventoryModel != null)
                    {
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans1 = new MaterialTransferEntity();
                        trans1.Create(_uIser.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans1.OldStorageLocation = inventoryModel.LocationF;
                        trans1.NewStorageLocation = inventoryModel.LocationF;
                        trans1.OldLotId = inventoryModel.LotId;
                        trans1.NewLotId = inventoryModel.LotId;
                        trans1.OldSublotId = inventoryModel.SlotId;
                        trans1.NewSublotId = inventoryModel.SlotId;
                        trans1.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans1.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans1.Quantity =
                            Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity),
                                3); // Convert.ToInt32(modelMaterialInvent.Quantity);
                        trans1.QuantityUomId = inventoryModel.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans1.Type = "Delete";
                        trans1.Comment = "删除库存";
                        trans1.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans1.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans1.OldEquipmentId = inventoryModel.EquipmentId;
                        trans1.NewEquipmentId = inventoryModel.EquipmentId;
                        trans1.OldContainerId = inventoryModel.ContainerId;
                        trans1.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans1.OldMaterialId = inventoryModel.MaterialId;
                        trans1.NewMaterialId = inventoryModel.MaterialId;
                        trans1.OldLotExternalStatus = inventoryModel.StatusF;
                        trans1.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans1.NewLotExternalStatus = inventoryModel.StatusF;
                        trans1.NewSublotExternalStatus = inventoryModel.StatusS;

                        trans1.PhysicalQuantity =
                            inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans1.TareQuantity =
                            inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value; //皮数量
                        listTran.Add(trans1);

                        #endregion
                    }

                    //删除库存
                    await _materialInventoryEntityDal.DeleteById(modelMaterialInvent.ID);
                }
                else
                {
                    //扣库存
                    modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                    listVent2.Add(modelMaterialInvent);
                }

                //批量提交
                bool subLotResult = await _materialSubLotServicesDal.Add(listSubLot) > 0;
                bool inventoryResult = await _materialInventoryEntityDal.Add(listVent) > 0;
                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                bool upMaterialInvent = true;
                if (listVent2.Count > 0)
                {
                    upMaterialInvent = await _materialInventoryEntityDal.Update(listVent2);
                }

                if (!subLotResult || !inventoryResult || !hisResult || !tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "添加失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;

                #region 打印标签不考虑是否成功（打印最小标签）

                string equpmentID = reqModel.equpmentID;

                //这里打印标签，upVent打印更新数据的标签，inven

                if (listVent.Count > 0)
                {
                    //打印对应的备料标签
                    for (int i = 0; i < listVent.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(listVent[i].ID);
                        //List<object> objs = new List<object>();
                        //objs.Add(inventViewData);

                        //这里拿打印机，（默认当前节点）
                        //var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);

                        //if (printIDData != null && printIDData.Count >= 0)
                        //{
                        //    //执行打印
                        //    await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                        //}


                        #region 打印新逻辑（根据打印机名字来拿对应的模板）

                        List<PrintSelectViewEntity> printIDData = new List<PrintSelectViewEntity>();

                        //  根据打印机ID查询打印code
                        if (!string.IsNullOrEmpty(reqModel.PrintId))
                        {
                            var entityMode = await _LabelPrinterEntityDal.FindEntity(reqModel.PrintId);

                            if (entityMode != null)
                            {
                                string pCode = entityMode.Code;
                                string pName = entityMode.Description;

                                if (pCode.Contains("移动"))
                                {
                                    //移动的打印
                                    printIDData =
                                        await _IPrintSelectViewServices.GetPrinit_MoveBagByEqumentID(
                                            reqModel.equpmentID);
                                }
                                else
                                {
                                    printIDData =
                                        await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);
                                }
                            }
                            else
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }
                        }
                        else
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }

                        #endregion

                        // 这里拿打印机，（默认当前节点）
                        //     var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);//_IPrintSelectViewServices.GetSelectPrinit_Move();
                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            if (!string.IsNullOrEmpty(reqModel.PrintId))
                            {
                                printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                            }

                            if (printIDData == null || printIDData.Count <= 0)
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }

                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;
                            //执行打印                                                                        
                            await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID,
                                teampClassID, inventViewData, inventViewData[0], 1);
                        }
                    }
                }

                if (listVent2.Count > 0)
                {
                    //打印库存标签
                    for (int i = 0; i < listVent2.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(listVent2[i].ID, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //这里拿打印机，默认拿第一个
                        var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_Bag();

                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            //执行打印
                            //  await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        }
                    }
                }
                //   string

                #endregion

                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "添加失败";
                return result;
            }
        }

        #endregion

        public async Task<List<MEquipmentroomViewEntity>> GetRoomSelectList()
        {
            List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var data = await _mEquipmentroomViewEntityDal.Db.Queryable<MEquipmentroomViewEntity>().ToListAsync();
            return data;
        }

        public async Task<List<MEquipmentroomViewEntity>> GetRoomSelectListCLBL()
        {
            string name = "ProcessPlant";
            List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var data = await _mEquipmentroomViewEntityDal.Db.Queryable<MEquipmentroomViewEntity>()
                .Where(p => !p.EquipmentCode.Contains(name)).ToListAsync();
            return data;
        }


        public async Task<List<MEquipmentroomViewEntity>> GetRoomSelectListByName(string name)
        {
            List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
            RefAsync<int> dataCount = 0;
            var data = await _mEquipmentroomViewEntityDal.Db.Queryable<MEquipmentroomViewEntity>()
                .Where(p => p.EquipmentCode.Contains(name)).ToListAsync();
            return data;
        }

        //public async Task<List<MEquipmentroomViewEntity>> GetRoomSelectListBySC(string name)
        //{
        //    List<MEquipmentroomViewEntity> result = new List<MEquipmentroomViewEntity>();
        //    List<string> roomS = new List<string>();
        //    RefAsync<int> dataCount = 0;
        //    var data = await _mEquipmentroomViewEntityDal.Db.Queryable<MEquipmentroomViewEntity>().
        //        Where(p => p.EquipmentCode != "ProcessPlant" && p.EquipmentCode != "SaucePlant").ToListAsync();
        //    return data;
        //}

        public async Task<List<MBatchriiViewEntity>> GetSegmentList(MBatchriiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MBatchriiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderId), a => a.ID.Equals(reqModel.ProductionOrderId))
                .ToExpression();
            var data = await _SegmentlistEntitydal.Db.Queryable<MBatchriiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        /// <summary>
        /// 查询容器下拉框
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<List<MContainerViewEntity>> GetConSelectList(string proOrderID, string batchID)
        {
            List<MContainerViewEntity> result = new List<MContainerViewEntity>();

            var whereExpression = Expressionable.Create<MContainerViewEntity>().AndIF(!string.IsNullOrEmpty(proOrderID),
                    a => a.ProductionRequestId.Equals(proOrderID))
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.ProductionBatchId.Equals(batchID))
                .ToExpression();
            var data = await _MContainerEntity.Db.Queryable<MContainerViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        public async Task<List<ContainerEntity>> GetContainerSelectList(string proOrderID, string batchID)
        {
            var whereExpression = Expressionable.Create<ContainerEntity>()
                .AndIF(!string.IsNullOrEmpty(proOrderID), a => a.ProductionRequestId.Equals(proOrderID))
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.ProductionBatchId.Equals(batchID))
                .ToExpression();
            var data = await _ContainerBatchViewEntitydal.Db.Queryable<ContainerEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<MaterialPreparationViewEntity>> GetList(MaterialPreparationViewRequestModel reqModel)
        {
            List<MaterialPreparationViewEntity> result = new List<MaterialPreparationViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .ToExpression();
            var data = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 工单工序批次备料查询 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<MaterialPreparationViewEntity>> GetPageList(
            MaterialPreparationViewRequestModel reqModel)
        {
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProLine), a => a.ProLine == reqModel.ProLine)
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductFamily), a => a.ProductFamily == reqModel.ProductFamily)
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode == reqModel.EquipmentCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName)
                .AndIF(reqModel.StarTime != null, a => a.PlanStartTime >= reqModel.StarTime)
                .AndIF(reqModel.EndTime != null, a => a.PlanStartTime <= reqModel.EndTime)
                .ToExpression();
            var data = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        /// <summary>
        /// 备料跳转第一界面根据proOrderID查询批次
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<List<BBatchDetailViewEntity>> GetByProOrderID(string proOrderId)
        {
            List<BBatchDetailViewEntity> result = new List<BBatchDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailViewEntity>()
                .AndIF(!string.IsNullOrEmpty(proOrderId), a => a.ProductionOrderId == proOrderId)
                .ToExpression();
            var data = await _dalBBatchDetailViewEntity.Db.Queryable<BBatchDetailViewEntity>()
                .Where(whereExpression).OrderBy(p => p.BatchNumber).ToListAsync();
            return data;
        }


        /// <summary>
        /// 备料跳转第一界面根据proOrderID、EquipmentId查询批次
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<List<BBatchDetailViewEntity>> GetByList(string proOrderId, string eqpmentid)
        {
            List<BBatchDetailViewEntity> result = new List<BBatchDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailViewEntity>()
                .AndIF(!string.IsNullOrEmpty(proOrderId), a => a.ProductionOrderId == proOrderId)
                .AndIF(!string.IsNullOrEmpty(eqpmentid), a => a.EquipmentId == eqpmentid)
                .ToExpression();
            var data = await _dalBBatchDetailViewEntity.Db.Queryable<BBatchDetailViewEntity>()
                .Where(whereExpression).OrderBy(p => p.BatchNumber).ToListAsync();
            return data;
        }


        /// <summary>
        /// 根据批次ID获取对应的物料信息
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        public async Task<List<BBatchDetailIiViewEntity>> GetListByBatchID(string batchID, string eqpmentID)
        {
            //List<MpModel> mpList = await Get_DisMaterial(eqpmentID);

            //string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            //if (mIDs == null || mIDs.Length <= 0)

            //{
            //    return new List<BBatchDetailIiViewEntity>();
            //}

            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db
                .Queryable<BBatchDetailIiViewEntity>() //.In(p => p.MaterialId, mIDs)
                .Where(whereExpression).ToListAsync();

            for (int i = 0; i < data.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = data[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    decimal valueMin = Math.Round(Convert.ToDecimal(data[i].MinPvalue), 3);
                    decimal valueMax = Math.Round(Convert.ToDecimal(data[i].MaxPvalue), 3);

                    if (data[i].MQuantity >= valueMin && data[i].MQuantity <= valueMax)
                    {
                        data[i].CompleteStates = "OK";
                    }
                }

                string unitName = data[i].QuantityTotalUnit;
                if (unitName == "g" || unitName == "G")
                {
                    data[i].MQuantity = data[i].MQuantity * 1000;
                    data[i].MQuantityTotal = data[i].MQuantityTotal * 1000;
                }
            }

            //  await Get_DisMaterial("02406241-3593-4874-163e-0370f6000000");
            return data;
        }


        public async Task<bool> IsFinish(string batchID, string eqpmentID)
        {
            List<MpModel> mpList = await Get_DisMaterial(eqpmentID);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return false;
            }

            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db
                .Queryable<BBatchDetailIiViewEntity>() //.In(p => p.MaterialId, mIDs)
                .Where(whereExpression).ToListAsync();

            //  await Get_DisMaterial("02406241-3593-4874-163e-0370f6000000");

            int finishCount = 0;
            if (data == null)
            {
                return false;
            }

            //重新绑定数据
            for (int i = 0; i < data.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = data[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    decimal valueMin = Math.Round(Convert.ToDecimal(data[i].MinPvalue), 3);
                    decimal valueMax = Math.Round(Convert.ToDecimal(data[i].MaxPvalue), 3);

                    if (data[i].MQuantity >= valueMin && data[i].MQuantity <= valueMax)
                    {
                        data[i].CompleteStates = "OK";
                    }
                }
            }

            int countOK = data.Where(p => p.CompleteStates == "OK" && p.IsPg == "0").Count();
            int countNum = data.Count;

            int isPG = data.Where(P => P.IsPg == "1").Count();

            if (isPG > 0)
            {
                //这里判断当前的零头袋是否拼锅完成
                var noPGData = data.Where(P => P.IsPg == "1").ToList();

                for (int i = 0; i < noPGData.Count; i++)
                {
                    try
                    {
                        //拿到当前数据的最大值和最小值、目标值、目标袋子重量
                        decimal max = noPGData[i].MaxPvalue.Value;
                        decimal min = noPGData[i].MinPvalue.Value;
                        //单包重量
                        decimal bageSize = Convert.ToDecimal(noPGData[i].BagSize);
                        string pValueStr = noPGData[i].FullPage;
                        //获取目标值
                        string subStr = pValueStr.Split('/')[1];
                        decimal pMin = bageSize * Convert.ToDecimal(subStr);

                        if (pMin != 0)
                        {
                            max = max - pMin;
                            min = min - pMin;
                        }

                        //查询库存信息(这里匹配的是容器不能为空)
                        var reuslt = await _InventorylistingViewEntitydal.FindList(p =>
                            p.MaterialId == noPGData[i].MaterialId &&
                            p.BatchId2 == noPGData[i].BatchId &&
                            p.ProductionRequestId == noPGData[i].ProductionOrderId
                            && !string.IsNullOrEmpty(p.ContainerId)
                            && p.Quantity.Value > 0 && p.InventoryType == "Partial"
                        );
                        decimal mTotal = reuslt.Sum(p => p.Quantity.Value);
                        if (mTotal >= min && mTotal <= max)
                        {
                        }
                        else
                        {
                            return false;
                        }
                    }
                    catch
                    {
                    }
                }
            }

            if (countNum == countOK)
            {
                return true;
            }
            else if (countNum - countOK == isPG)
            {
                return true;
            }

            return false;
        }


        /// <summary>
        /// 根据批次ID获取对应的物料信息
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        public async Task<List<BBdetailIiViewEntity>> GetListByBatchIDNew(string batchID)
        {
            List<BBdetailIiViewEntity> result = new List<BBdetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBdetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="batchIDS">批次ID组</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByBatchIDS(BBatchDetailIIModel model)
        {
            PageModel<BBatchDetailIiViewEntity> result = new PageModel<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            //.And(p => p.BatchId.IsIn(batchIDS)).ToExpression();
            //var data = await _dal.Db.Queryable<MContainerViewEntity>().In(p => p.ClassId, reqModel.ClassId).In(p => p.StatusId, reqModel.StatusId)
            //     .Where(Expressionable.Create<MContainerViewEntity>().ToExpression())
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>().ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db.Queryable<BBatchDetailIiViewEntity>()
                .In(p => p.BatchId, model.ID)
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(model.pageIndex, model.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="batchIDS">批次ID组</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        public async Task<PageModel<BBdetailIiViewEntity>> GetPageListByBatchIDSII(BBatchDetailIIModel model)
        {
            //List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);
            //string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            //if (mIDs == null || mIDs.Length <= 0)
            //{
            //    return new PageModel<BBdetailIiViewEntity>();
            //}
            PageModel<BBdetailIiViewEntity> result = new PageModel<BBdetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            //  var whereExpression = Expressionable.Create<BBdetailIiViewEntity>().ToExpression();


            var data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()
                .In(p => p.BatchId, model.ID) //.In(p => p.MaterialId, mIDs)
                .ToListAsync();

            string[] ids = data.GroupBy(p => p.MaterialId).Select(p => p.Key).ToArray();
            var listInvent = await _dalInventKcViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId).In(p => p.MaterialId, ids).ToListAsync();

            //计算汇总库存数据，按照物料和设备
            var groupByInventList = from a in listInvent
                group a by new
                {
                    a.MaterialCode
                }
                into g
                select new
                {
                    g.Key.MaterialCode,
                    Quantity = g.Sum(p => p.Quantity)
                };


            List<BBdetailIiViewEntity> RData = (from a in data
                join b in groupByInventList on a.MCode equals b.MaterialCode into groupJoin
                from r in groupJoin.DefaultIfEmpty()
                select new BBdetailIiViewEntity
                {
                    Remark = a.Remark,
                    ChangeUnit = a.ChangeUnit,
                    ID = a.ID,
                    Mymin = a.Mymin,
                    Mymax = a.Mymax,
                    EquipmentId = a.EquipmentId,
                    OnlyId = a.OnlyId,
                    ProductionOrderNo = a.ProductionOrderNo,
                    FormulaNo = a.FormulaNo,
                    MBatchNumber = a.MBatchNumber,
                    MName = a.MName,
                    MCode = a.MCode,
                    MQuantity = a.MQuantity,
                    MQuantityunit = a.MQuantityunit,
                    MQuantityTotal = a.MQuantityTotal,
                    QuantityTotalUnit = a.QuantityTotalUnit,
                    TUintid = a.TUintid,
                    InQuantity = r == null ? 0 : r.Quantity,
                    MaterialUnit1 = a.MaterialUnit1,
                    BagSize = a.BagSize,
                    FullPage = a.FullPage,
                    TagpS = a.TagpS,
                    ParitialPage = a.ParitialPage,
                    TagpSUnit = a.TagpSUnit,
                    FullFinish = a.FullFinish,
                    BagS = a.BagS,
                    CompleteStates = a.CompleteStates,
                    ConsumedStates = a.ConsumedStates,
                    MaterialId = a.MaterialId,
                    MinPvalue = a.MinPvalue,
                    SetMin = a.SetMin,
                    MaxPvalue = a.MaxPvalue,
                    SetMax = a.SetMax,
                    ConSumed = a.ConSumed,
                    BatchId = a.BatchId,
                    ProductionOrderId = a.ProductionOrderId,
                    LineCode = a.LineCode,
                    Sequencetotal = a.Sequencetotal
                }).ToList();


            for (int i = 0; i < RData.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = RData[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    if (RData[i].MQuantity >= RData[i].MinPvalue && RData[i].MQuantity <= RData[i].MaxPvalue)
                    {
                        RData[i].CompleteStates = "OK";
                    }
                    else if (RData[i].Mymin != RData[i].Mymax && RData[i].MQuantity >= RData[i].Mymin &&
                             RData[i].MQuantity <= RData[i].Mymax)
                    {
                        RData[i].CompleteStates = "OK";
                    }
                }

                string changeUint = RData[i].ChangeUnit;
                if (changeUint == "g" || changeUint == "G")
                {
                    //      RData[i].MaterialUnit1 = "g";
                    RData[i].TagpSUnit = "g";
                    RData[i].QuantityTotalUnit = "g";
                    RData[i].MQuantityunit = "g";
                    RData[i].MQuantity = RData[i].MQuantity * 1000;

                    RData[i].TagpS = RData[i].TagpS * 1000;
                    RData[i].ParitialPage =
                        (Convert.ToDecimal(RData[i].ParitialPage) * Convert.ToDecimal(1000)).ToString();
                    RData[i].MinPvalue = RData[i].MinPvalue * 1000;
                    RData[i].MaxPvalue = RData[i].MaxPvalue * 1000;
                    //      RData[i].MQuantity = RData[i].MQuantity * 1000;
                    RData[i].MQuantityTotal = RData[i].MQuantityTotal * 1000;
                    RData[i].BagSize = (Convert.ToDecimal(RData[i].BagSize) * 1000).ToString();
                }
            }


            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          

            // 获取指定页的数据
            var rDat = RData.OrderBy(p => p.ProductionOrderNo).ThenBy(p => p.OnlyId).Skip(startIndex)
                .Take(model.pageSize).ToList();
            result.dataCount = RData.Count;
            result.data = rDat;
            return result;
        }

        /// <summary>
        /// 查询选中的物料（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="MaterialId">物料ID</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns
        public async Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByMaterial(BBatchDModel model)
        {
            PageModel<BBatchDetailIiViewEntity> result = new PageModel<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>().ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db
                .Queryable<BBatchDetailIiViewEntity>() //In(p => p.ProductionOrderId, model.ProId)
                .Where(whereExpression)
                .Where(p => p.MaterialId == model.MaterialId) //&&p.EquipmentId == model.EqumentId)
                .OrderByDescending(p => p.CreateDate)
                .ToListAsync();
            if (model.ProId != null && model.ProId.Length > 0)
            {
                data = await _dalBBatchDetailIiViewEntity.Db.Queryable<BBatchDetailIiViewEntity>()
                    .In(p => p.ProductionOrderId, model.ProId)
                    .Where(whereExpression)
                    .Where(p => p.MaterialId == model.MaterialId) //&&p.EquipmentId == model.EqumentId)
                    .OrderByDescending(p => p.CreateDate)
                    .ToListAsync();
            }

            #region linq

            //var resultList = (from a in data
            //                  group a by new
            //                  {
            //                      a.EquipmentId,
            //                      a.ProductionOrderNo,
            //                      a.ProductionOrderId,
            //                      a.MBatchNumber,
            //                      a.MCode,
            //                      a.MName,

            //                      // a.UnitId,
            //                      a.InQuantity,
            //                      a.MQuantityunit,
            //                      a.ID

            //                  }
            //                   into g
            //                  let x = g.Where(p => p.CompleteStates == "").Count() > 0 ? "" : "OK"
            //                  select new BBatchDetailIiViewEntity
            //                  {
            //                      EquipmentId = g.Key.EquipmentId,
            //                      //唯一标识列
            //                      OnlyId = "",
            //                      ProductionOrderNo = g.Key.ProductionOrderNo,
            //                      ProductionOrderId = g.Key.ProductionOrderId,
            //                      EquipmentName = string.Join(",", g.Select(d => d.EquipmentName)),
            //                      MBatchNumber = g.Key.MBatchNumber,
            //                      MCode = g.Key.MCode,
            //                      MName = g.Key.MName,

            //                      //SegmentCode = string.Join(",", g.Select(d => d.SegmentCode)),//+"-" +string.Join(",", g.Select(d => d.SegmentName)),//g.Key.SegmentCode,
            //                      //                                                             // SegmentName  = g.Key.SegmentName,
            //                      //EquipmentName = string.Join(",", g.Select(d => d.EquipmentName)),
            //                      //QSum = g.Sum(p => p.QSum.Value),
            //                      //CompleteStates = x,
            //                      //MQuantityunit = g.Key.MQuantityunit,
            //                      //InQuantity = g.Key.InQuantity,
            //                      //UnitId = g.Key.UnitId,
            //                      ID = g.Key.ID
            //                  }).ToList();

            #endregion

            var results1 = data.ToPageModel(model.pageIndex, model.pageSize, c => c.EquipmentId, OrderType.Asc);

            int pageIndex = model.pageIndex; // 当前页码，从1开始
            int pageSize = model.pageSize;
            return results1;
        }


        /// <summary>
        /// 查询选中的物料（函数用来选择多批次的物料信息并显示）(拼锅区域)
        /// </summary>
        /// <param name="MaterialId">物料ID</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        public async Task<PageModel<BBdetailIiViewEntity>> GetPageListByMaterialII(BBatchDModel model)
        {
            PageModel<BBdetailIiViewEntity> result = new PageModel<BBdetailIiViewEntity>();
            RefAsync<int> dataCount = 0
                ;
            var whereExpression = Expressionable.Create<BBdetailIiViewEntity>().And(p => p.MQuantityTotal > 0)
                .ToExpression();
            List<BBdetailIiViewEntity> data = new List<BBdetailIiViewEntity>();

            if (model.ProId != null && model.ProId.Length > 0)
            {
                //data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()//.In(p => p.ProductionOrderId, model.ProId)
                data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()
                    .In(p => p.ProductionOrderId, model.ProId)
                    .Where(whereExpression)
                    .Where(p => p.MaterialId == model.MaterialId) //&&p.EquipmentId == model.EqumentId)
                    .OrderByDescending(p => p.CreateDate)
                    .ToListAsync();
            }
            else
            {
                data = await _bBdetailIiViewEntityDal.Db
                    .Queryable<BBdetailIiViewEntity>() //.In(p => p.ProductionOrderId, model.ProId)
                    .Where(whereExpression)
                    .Where(p => p.MaterialId == model.MaterialId) //&&p.EquipmentId == model.EqumentId)
                    .OrderByDescending(p => p.CreateDate)
                    .ToListAsync();
            }

            var listInvent = await _dalInventKcViewEntity.FindList(p =>
                p.MaterialId == model.MaterialId && p.EquipmentId == model.EqumentId);
            //      var listInvent = await _InventKqtyViewEntityDal.FindList(p => p.EquipmentId );

            //var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
            //  .Where(whereExpression).Where(p => p.ContainerId == null || p.ContainerId == "").OrderByDescending(p => p.CreateDate)
            //  .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);


            //这里重新查询库存信息
            string mCode = string.Empty;

            string changeUint = string.Empty;
            for (int i = 0; i < data.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = data[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    if (data[i].MQuantity >= data[i].MinPvalue && data[i].MQuantity <= data[i].MaxPvalue)
                    {
                        data[i].CompleteStates = "OK";
                    }
                    else if (data[i].Mymin != data[i].Mymax && data[i].MQuantity >= data[i].Mymin &&
                             data[i].MQuantity <= data[i].Mymax)
                    {
                        data[i].CompleteStates = "OK";
                    }
                }

                changeUint = data[i].ChangeUnit;
                mCode = data[i].MCode;
                decimal max = data[i].Mymax.Value;
                decimal min = data[i].Mymin.Value;
                if (max < min)
                {
                    data[i].Mymax += Math.Round(Convert.ToDecimal(data[i].BagSize), 3);
                }

                var invnetModel = listInvent.Where(p => p.MaterialCode == mCode).ToList();
                if (invnetModel == null || invnetModel.Count == 0)
                {
                    data[i].InQuantity = 0;
                }
                else
                {
                    data[i].MaterialUnit1 = data[i].MaterialUnit1; //invnetModel[0].MaterialUnit1;
                    data[i].InQuantity = invnetModel.Sum(p => p.Quantity);
                }

                if (changeUint == "g" || changeUint == "G")
                {
                    //      RData[i].MaterialUnit1 = "g";
                    data[i].TagpSUnit = "g";
                    data[i].QuantityTotalUnit = "g";
                    data[i].MQuantityunit = "g";
                    data[i].MQuantity = data[i].MQuantity * 1000;

                    data[i].TagpS = data[i].TagpS * 1000;
                    data[i].ParitialPage =
                        (Convert.ToDecimal(data[i].ParitialPage) * Convert.ToDecimal(1000)).ToString();
                    data[i].MinPvalue = data[i].MinPvalue * 1000;
                    data[i].MaxPvalue = data[i].MaxPvalue * 1000;
                    //   data[i].MQuantity = data[i].MQuantity * 1000;
                    data[i].MQuantityTotal = data[i].MQuantityTotal * 1000;
                    data[i].BagSize = (Convert.ToDecimal(data[i].BagSize) * 1000).ToString();
                }
            }


            // var data2 = data.OrderByDescending(p => p.CreateDate).ToListAsync();

            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = data.OrderBy(p => p.MBatchNumber).ThenBy(p => p.ProductionOrderNo)
                .ThenByDescending(p => p.CreateDate).Skip(startIndex).Take(model.pageSize).ToList();
            result.dataCount = data.Count;
            result.data = rDat;
            return result;


            //var results1 = data.ToPageModel(model.pageIndex, model.pageSize, c => c.EquipmentId, OrderType.Asc);

            //int pageIndex = model.pageIndex; // 当前页码，从1开始
            //int pageSize = model.pageSize;
            //return results1;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <param name="isContainer"></param>
        /// <param name="isHType"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListByBatchDetailMaterialPG(
            BBatchDetailMaterialViewRequestModel reqModel, bool isContainer, bool isHType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
                .AndIF(!string.IsNullOrEmpty(reqModel.mId), a => a.MaterialId == reqModel.mId)
                .And(a => a.HType == "" || a.HType == null).ToExpression();

            if (isHType == true)
            {
                whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                    .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
                    .AndIF(!string.IsNullOrEmpty(reqModel.mId), a => a.MaterialId == reqModel.mId)
                    .And(a => a.HType != "" || a.HType != null)
                    .ToExpression();
            }


            if (isContainer == true)
            {
                if (string.IsNullOrEmpty(reqModel.ContainerId))
                {
                    result = new PageModel<BBatchDetailMaterialViewEntity>();
                    return result;
                }
                else
                {
                    var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                        .Where(whereExpression).Where(p => p.ContainerId == reqModel.ContainerId)
                        .OrderByDescending(p => p.CreateDate)
                        .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                    for (int i = 0; i < data.Count; i++)
                    {
                        string pID = data[i].ProductionRequestId;
                        string mID = data[i].MaterialId;
                        //根据物料和工单查询对应的需求表
                        var poList = await _PPoConsumeRequirementEntitDal.FindList(p =>
                            p.ProductionOrderId == pID && p.MaterialId == mID && p.ChangeUnit == "g");

                        if (poList != null && poList.Count > 0)
                        {
                            data[i].InQuantity = data[i].InQuantity * 1000;
                            data[i].MaterialUnit1 = "g";
                        }
                    }


                    result.dataCount = dataCount;
                    result.data = data;
                    return result;
                }
            }
            else
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.ContainerId == null || p.ContainerId == "")
                    .OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);


                result.dataCount = dataCount;
                result.data = data;
                return result;
            }
        }

        /// <summary>
        /// 根据物料ID查询最后界面数据源
        /// </summary>
        /// <param name="MaterialId"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListByBatchDetailMaterial(
            BBatchDetailMaterialViewRequestModel reqModel, bool isContainer, bool isHType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
                .And(a => a.HType == "" || a.HType == null).ToExpression();

            if (isHType == true)
            {
                whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                    .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
                    .And(a => a.HType != "" || a.HType != null)
                    .ToExpression();
            }

            var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                .Where(whereExpression).Where(p => p.ContainerId == null || p.ContainerId == "")
                .OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            if (isContainer == true)
            {
                if (string.IsNullOrEmpty(reqModel.ContainerId))
                {
                    result = new PageModel<BBatchDetailMaterialViewEntity>();
                    return result;
                }
                else
                {
                    data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                        .Where(whereExpression).Where(p => p.ContainerId == reqModel.ContainerId)
                        .OrderByDescending(p => p.CreateDate)
                        .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                    result.dataCount = dataCount;
                    result.data = data;
                    return result;
                }
            }
            else
            {
                data = data.Where(p => string.IsNullOrEmpty(p.ProductionRequestId)).ToList(); //筛选掉绑定了工单的库存
                result.dataCount = dataCount;
                result.data = data;
                return result;
            }
        }


        /// <summary>
        /// 根据物料ID查询最后界面数据源(库存清单)
        /// </summary>
        /// <param name="MaterialId"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewMaterial(
            BBatchDetailMaterialViewRequestModel reqModel, bool ProOrderid, bool Batchid, bool isType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                //location
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
                .And(p => p.IsWeighingCheck == "已复秤"). //.And(p => p.IsWeighingCheck == "已复称").
                And(p => p.ContainerId == null || p.ContainerId == "").ToExpression();

            if (ProOrderid == true && Batchid == true)
            {
                whereExpression = whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    //加入查询条件(时间)
                    //batch pallet id
                    .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderid), a => a.PId == reqModel.ProOrderid)
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    //pro
                    .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId == reqModel.BatchId)
                    .And(p => p.ContainerId == null || p.ContainerId == "")
                    .ToExpression(); //.And(p => p.HType == "Partial").ToExpression();
            }

            if (isType == true)
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression)
                    .Where(p => (p.IsWeighingCheck == "已复秤" && p.PropertyValue == "1") ||
                                (p.PropertyValue == "0")) //(p => p.IsWeighingCheck == "已复秤" || p.PropertyValue == null)
                    .OrderByDescending(p => p.CreateDate).ToListAsync();


                List<BBatchDetailMaterialViewEntity> resultList = (from x in data
                    group x by new
                    {
                        x.LotId,
                        x.SubId,
                        x.LBatch,
                        x.UnitId,
                        x.LStatus,
                        x.SbSscc,
                        x.SbStatus,
                        x.InQuantity,
                        x.MaterialUnit1,
                        x.HType,
                        x.ProductionOrderNo,
                        x.BatchCode,
                        x.InventBin,
                        x.ExpirationDate,
                        x.ID,
                        x.CreateDate,
                        x.CreateUserId,
                        x.ModifyDate,
                        x.ModifyUserId,
                        //x.UpdateTimeStamp
                    }
                    into g
                    select new BBatchDetailMaterialViewEntity
                    {
                        LotId = g.Key.LotId,
                        SubId = g.Key.SubId,
                        LBatch = g.Key.LBatch,
                        UnitId = g.Key.UnitId,
                        LStatus = g.Key.LStatus,
                        SbSscc = g.Key.SbSscc,
                        SbStatus = g.Key.SbStatus,
                        InQuantity = g.Key.InQuantity,
                        MaterialUnit1 = g.Key.MaterialUnit1,
                        HType = g.Key.HType,
                        ProductionOrderNo = g.Key.ProductionOrderNo,
                        BatchCode = g.Key.BatchCode,
                        InventBin = g.Key.InventBin,
                        ExpirationDate = g.Key.ExpirationDate,
                        ID = g.Key.ID,
                        CreateDate = g.Key.CreateDate,
                        CreateUserId = g.Key.CreateUserId,
                        ModifyDate = g.Key.ModifyDate,
                        ModifyUserId = g.Key.ModifyUserId,
                        //UpdateTimeStamp = g.Key.UpdateTimeStamp
                    }).ToList();

                //根据物料和工单查询对应的需求表
                var poList = await _PPoConsumeRequirementEntitDal.FindList(p =>
                    p.ProductionOrderId == reqModel.ProOrderid && p.MaterialId == reqModel.MaterialId &&
                    p.ChangeUnit == "g");

                if (poList != null && poList.Count > 0)
                {
                    for (int i = 0; i < resultList.Count; i++)
                    {
                        resultList[i].InQuantity = resultList[i].InQuantity * 1000;
                        resultList[i].MaterialUnit1 = "g";
                    }
                }

                // PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
                var results1 = resultList.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.ModifyDate,
                    OrderType.Asc);
                int pageIndex = reqModel.pageIndex; // 当前页码，从1开始
                int pageSize = reqModel.pageSize; // 每页的数据数量
                // resultList = resultList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

                return results1;
            }
            else
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.HType != null)
                    .OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                result.dataCount = dataCount;
                result.data = data;
            }

            return result;
        }


        public async Task<PageModel<ViewEntity>> GetMateriaConsumlListPG(BBatchDetailMaterialViewRequestModel reqModel)
        {
            PageModel<ViewEntity> result = new PageModel<ViewEntity>();
            RefAsync<int> dataCount = 0;

            //
            if (reqModel.Pids == null || reqModel.Pids.Length < 0)
            {
                return result;
            }

            //物料
            var mData = await _MaterialEntitydal.FindEntity(p => p.Code == reqModel.MCode);
            //
            var poData = await _PPoConsumeRequirementEntitDal.FindList(P =>
                P.MaterialId == mData.ID && reqModel.Pids.Contains(P.ProductionOrderId));
            List<string> str = poData.GroupBy(p => p.ID).Select(p => p.Key).ToList();
            //批次
            // var batchData = await _BatchEntity.FindEntity(p => p.ID == reqModel.BatchId);
            //最终结果
            var batchCData = await _BatchConsumeRequirementEntityDal.Db.Queryable<BatchConsumeRequirementEntity>()
                .In(p => p.PoConsumeRequirementId, str).ToListAsync();

            var whereExpression = Expressionable.Create<ViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MId), a => a.MaterialId == reqModel.MId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.MCode), a => a.MCode == reqModel.MCode)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.PID), a => a.ID == reqModel.PID).ToExpression();

            var data = await _ViewEntityDal.Db.Queryable<ViewEntity>().Where(whereExpression)
                .OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);


            List<BatchConsumeRequirementEntity> serachList = new List<BatchConsumeRequirementEntity>();
            if (batchCData != null && batchCData.Count > 0)
            {
                serachList.Add(batchCData[0]);
            }

            for (int i = 0; i < data.Count; i++)
            {
                data[i].Quantity = Math.Round(serachList.Sum(p => p.Quantity), 3);
            }

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        /// <summary>
        /// 根据物料获取需求量
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<ViewEntity>> GetMateriaConsumlList(BBatchDetailMaterialViewRequestModel reqModel)
        {
            PageModel<ViewEntity> result = new PageModel<ViewEntity>();
            RefAsync<int> dataCount = 0;

            if (string.IsNullOrEmpty(reqModel.BatchId))
            {
                return result;
            }

            //物料
            var mData = await _MaterialEntitydal.FindEntity(p => p.Code == reqModel.MCode);
            //
            var poData = await _PPoConsumeRequirementEntitDal.FindList(P =>
                P.MaterialId == mData.ID && P.ProductionOrderId == reqModel.PID);
            List<string> str = poData.GroupBy(p => p.ID).Select(p => p.Key).ToList();
            //批次
            var batchData = await _BatchEntity.FindEntity(p => p.ID == reqModel.BatchId);
            //最终结果
            var batchCData = await _BatchConsumeRequirementEntityDal.Db.Queryable<BatchConsumeRequirementEntity>()
                .In(p => p.PoConsumeRequirementId, str).Where(P => P.BatchId == reqModel.BatchId).ToListAsync();

            var whereExpression = Expressionable.Create<ViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MId), a => a.MaterialId == reqModel.MId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.MCode), a => a.MCode == reqModel.MCode)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.PID), a => a.ID == reqModel.PID).ToExpression();

            var data = await _ViewEntityDal.Db.Queryable<ViewEntity>().Where(whereExpression)
                .OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            for (int i = 0; i < data.Count; i++)
            {
                data[i].Quantity = Math.Round(batchCData.Sum(p => p.Quantity), 3);
            }

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }


        /// <summary>
        /// 根据物料ID查询最后界面数据源
        /// </summary>
        /// <param name="MaterialId"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial(
            BBatchDetailMaterialViewRequestModel reqModel, bool ProOrderid, bool Batchid, bool isType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                //location
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc).ToExpression();
            //Status
            //  .AndIF(!string.IsNullOrEmpty(reqModel.ConStatus), a => a.ContainerStatus == reqModel.ConStatus)
            // //bin
            // .AndIF(!string.IsNullOrEmpty(reqModel.LocationS), a => a.LocationS == reqModel.LocationS)
            //  //时间
            //  .AndIF(reqModel.StarTime != null, a => a.StatesTime >= reqModel.StarTime)
            //  .AndIF(reqModel.EndTime != null, a => a.StatesTime <= reqModel.EndTime)
            ////machine
            //.AndIF(!string.IsNullOrEmpty(reqModel.CMachine), a => a.CMachine == reqModel.CMachine).ToExpression();

            if (ProOrderid == true && Batchid == true)
            {
                whereExpression = whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    //加入查询条件(时间)
                    //batch pallet id
                    .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderid), a => a.PId == reqModel.ProOrderid)
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    //pro
                    .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId == reqModel.BatchId).ToExpression();
            }

            if (isType == true)
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.HType == "") //.Where(p=>p.HType==null|| p.HType == "")
                    .OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                result.dataCount = dataCount;
                result.data = data;
            }
            else
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.HType != null)
                    .OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                result.dataCount = dataCount;
                result.data = data;
            }

            return result;

            //PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            //RefAsync<int> dataCount = 0;
            //var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>().
            //    AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId).AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
            //     .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
            //                 .ToExpression();

            //var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
            //    .Where(whereExpression)
            //    .Where(p => p.ProductionOrderNo == null || p.ProductionOrderNo == "")
            //    .Where(p => p.BatchCode == null || p.BatchCode == "").OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            //if (ProOrderid == true && Batchid == true)
            //{
            //    data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
            //        .Where(whereExpression).Where(p => p.PId == reqModel.ProOrderid && p.BatchCode == reqModel.BatchId).OrderByDescending(p => p.CreateDate).OrderByDescending(p => p.CreateDate)
            //        .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //}
            //result.dataCount = dataCount;
            //result.data = data;
            //return result;
        }
        /// <summary>
        /// 创建托盘，部分数据源来自上一个跳转界面PRODUCTION_BATCH_ID、PRODUCTION_REQUEST_ID、EQUIPMENT_ID
        /// </summary>
        /// <param name="tareWeight">所有重量换算kg相加</param>
        /// <param name="uomID">单位</param>
        /// <param name="proBatchID">生产批次ID</param>
        /// <param name="proRequestID">工单ID</param>
        /// <param name="EquipMentID">存储区ID</param>
        /// <param name="MaterialId">物料ID</param>
        /// <param name="lotID">物料批次</param>
        /// <param name="subLotID">物料子批次</param>      
        /// <returns></returns>
        //public async Task<bool> AddPallet(int tareWeight, string uomID, string proBatchID, string proRequestID, string equipMentID, string materialId, string lotID, string subLotID)


        /// <summary>
        /// 
        /// </summary>
        /// <param name="tareWeight">所有重量换算kg相加</param>
        /// <param name="uomID">单位</param>
        /// <param name="proBatchID">生产批次ID</param>
        /// <param name="proRequestID">工单ID</param>
        /// <param name="EquipMentID">存储区ID</param>
        /// <param name="MaterialId">物料ID</param>
        public async Task<MessageModel<string>> AddPallet(MPPallentAddModel plateAddModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            //去DFM_M_BASE_UNIQUE_NUMBER中找到type=BATCH_PALLET的条目，获取NEXT_CODE
            BaseUniqueNumberEntity model =
                await _dalUniqueNumberEntity.FindEntity(p => p.Type == "BATCH_PALLET");
            //用该值在MKM_M_CONTAINER中创建一条记录
            Random rand = new Random();
            int value = rand.Next(100, 999);

            string nextContarnerCode = DateTime.Now.ToString("yyyyMMddHHmmss") + value;
            if (model != null)
            {
                nextContarnerCode = model.NextCode;
            }

            //这里查询是否存在重复数据
            var cList = await _dalContainerEntity.FindList(p => p.Name == nextContarnerCode);
            if (cList != null && cList.Count > 0)
            {
                Random rand1 = new Random();
                int value1 = rand1.Next(1, 9);
                nextContarnerCode = DateTime.Now.ToString("yyyyMMddHHmmss") + value1.ToString();
            }

            if (nextContarnerCode != null)
            {
                try
                {
                    List<ContainerEntity> containerList = new List<ContainerEntity>();
                    List<ContainerHistoryEntity> containerHisList = new List<ContainerHistoryEntity>();

                    string userID = _uIser.Name.ToString();
                    _unitOfWork.BeginTran();

                    #region 新增容器

                    string guid = string.Empty;
                    ContainerEntity cModel = new ContainerEntity();
                    //新增5字段
                    cModel.Create(userID);
                    //获取id
                    guid = cModel.ID;
                    //容器名称
                    cModel.Name = nextContarnerCode;
                    //comment：Batch Pallet created
                    cModel.Comment = "Batch Pallet created";
                    // type：数据字典中的ContainerActionType = 7

                    //STATE：ContainerStatus = 6
                    //pending
                    cModel.Status = "1";
                    //class：数据字典中的containerclass=1
                    cModel.Class = "BatchPallet";
                    //Name、tare_weight(所有重量换算kg相加)、weight_UOM、PRODUCTION_BATCH_ID、PRODUCTION_REQUEST_ID、EQUIPMENT_ID、时间相关需要填
                    cModel.TareWeight = Convert.ToInt32(plateAddModel.TareWeight);
                    //重量单位
                    cModel.WeightUomId = plateAddModel.UomID;
                    //批次
                    cModel.ProductionBatchId = plateAddModel.ProBatchID;
                    //工单
                    cModel.ProductionRequestId = plateAddModel.ProRequestID;
                    //设备
                    cModel.EquipmentId = plateAddModel.EquipMentID;

                    containerList.Add(cModel);

                    #endregion

                    //赋值                

                    #region 写入容器记录表

                    // CONTAINER_ID，CONTAINER_CODE（name），TYPE（ContainerActionType = CREATED），EQUIPMENT_ID，STATE，COMMENT（和container中创建的comment一致），PRODUCT_ORDER_ID，BATCH_ID
                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    hisModel.Create(userID);
                    //   hisModel
                    //   hisModel.ID = Guid.NewGuid().ToString();
                    hisModel.ContainerId = guid;
                    hisModel.ContainerCode = nextContarnerCode;
                    hisModel.Type = "Created";
                    hisModel.EquipmentId = plateAddModel.EquipMentID;
                    hisModel.EquipmentRequirementId = plateAddModel.ProRequestID;
                    hisModel.State = "1";
                    hisModel.Comment = "Batch Pallet created";
                    hisModel.ProductOrderId = hisModel.ProductOrderId; // 工单ID
                    hisModel.BatchId = hisModel.ProductOrderId; //工单批次ID
                    hisModel.MaterialId = plateAddModel.MaterialId;
                    //hisModel.SublotId = subLotID;
                    //hisModel.Quantity = tareWeight.ToString();
                    //hisModel.QuantityUomId = uomID;
                    //hisModel.BatchConsumedRequirementId = cHisModel.BatchConsumedRequirementId;//批次用量需求ID
                    //hisModel.ConsumedRequirementId = cHisModel.ConsumedRequirementId;//工单用量需求ID
                    //hisModel.ProductionExecutionId = cHisModel.ProductionExecutionId;//工单执行ID        

                    //hisModel.MaterialProducedActualId = cHisModel.MaterialProducedActualId; //物料产出记录ID
                    //hisModel.MaterialConsumedActualId = cHisModel.MaterialConsumedActualId;  //物料消耗记录ID
                    //hisModel.LotId = lotID;
                    //hisModel.ExpirationDate = DateTime.Now;
                    containerHisList.Add(hisModel);

                    #endregion

                    bool rqResult = await _ContainerHistoryEntityDal.Add(containerHisList) > 0;
                    bool insertContainer = await _dalContainerEntity.Add(containerList) > 0;
                    if (!insertContainer || !rqResult)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "新增失败";
                        return result;
                    }


                    _unitOfWork.CommitTran();

                    result.success = true;
                    return result;
                }
                catch (Exception)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "新增失败";
                    return result;
                }
            }

            result.msg = "下一条容器不能为空";
            return result;
        }


        /// <summary>
        /// 根据批次号获取对应的容器（批次）
        /// </summary>
        /// <param name="batchID"></param>
        /// <param name="stateClass"(默认传入"BatchPallet")></param>
        /// <returns></returns>
        public async Task<List<ContainerEntity>> GetContainerBatchPalletList(string batchID, string stateClass)
        {
            List<ContainerEntity> result = new List<ContainerEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ContainerEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.ProductionBatchId == batchID)
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.Class == stateClass)
                .ToExpression();
            var data = await _dalContainerEntity.Db.Queryable<ContainerEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 获取备料主页右上角下拉框里面的EquipmentId需要传入到后面的界面使用
        /// </summary>
        /// <returns></returns>
        public async Task<List<DicMaterialPreselectViewEntity>> GetMarialPreSelect()
        {
            List<DicMaterialPreselectViewEntity> result = new List<DicMaterialPreselectViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<DicMaterialPreselectViewEntity>().ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db.Queryable<DicMaterialPreselectViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 备料转移
        /// </summary>
        /// <param name="lotID">批次ID</param>
        ///// <param name="subID">子批次</param>
        /// <param name="bags">包数</param>
        /// <param name="bagWeight">单包重量</param>
        /// <param name="targetWeight">目标重量在视图最上面</param>
        /// <param name="actualWeight">目标重量在视图最上面之前容器量</param>
        /// <param name="storage_location">库存表的storage_location</param>
        /// <param name="eqpmentID">库存表的eqpmentID</param>
        /// <param name="unitID">选中的单位ID</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <returns></returns>
        //public async Task<bool> MPreparationTransfer(string lotID, string subID, int bags, int bagWeight, int targetWeight, int actualWeight, string storage_location, string eqpmentID, string unitID, string containerID)


        #region 备料界面最下面的容器下拉框

        /// <summary>
        /// 备料界面最下面的容器下拉框
        /// </summary>
        /// <param name="batchID">根据批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_II_VIEW）</param>
        /// <returns></returns>
        public async Task<List<DicBatchPalletselectViewEntity>> GetContainerSelect(string batchID)
        {
            List<DicBatchPalletselectViewEntity> result = new List<DicBatchPalletselectViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<DicBatchPalletselectViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _dicBatchPalletselectViewEntitydal.Db.Queryable<DicBatchPalletselectViewEntity>()
                .Where(whereExpression).OrderBy(p => p.CreateDate).ToListAsync();
            return data;
        }


        /// <summary>
        /// 查询实际耗用量（所有容器的）
        /// </summary>
        /// <param name="batchID">根据生产批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_II_VIEW）</param>
        /// <returns></returns>
        public async Task<decimal> GetContainerSelectQuantity(string batchID)
        {
            Decimal actureCount = 0;
            List<DicBatchPalletselectViewEntity> result = new List<DicBatchPalletselectViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<DicBatchPalletselectViewEntity>()
                .AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
                .ToExpression();
            var data = await _dicBatchPalletselectViewEntitydal.FindList(p => p.BatchId == batchID);

            for (int i = 0; i < data.Count; i++)
            {
                var materialID = data[i].MaterialId;
                var dataValue = await _bBatchDetailMcontainerViewEntitydal.FindList(p => p.MaterialId == materialID);
                if (dataValue != null && dataValue.Count > 0)
                {
                    actureCount += dataValue.Sum(P => P.IQuantity);
                }
            }

            return actureCount;
        }

        #endregion

        #region 最下面查询batch pallets

        /// <summary>
        /// 查询最下面batch pallets
        /// </summary>
        /// <param name="reqModel">只需要传入批次表的id lotid 和分页参数</param>
        /// <returns></returns> 
        public async Task<PageModel<BBatchDetailMcontainerViewEntity>> GetPageBatchDetailList(
            BBatchDetailMcontainerViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBatchDetailMcontainerViewEntity>()
                .ToExpression();
            var data = await _bBatchDetailMcontainerViewEntitydal.QueryPage(whereExpression, reqModel.pageIndex,
                reqModel.pageSize);

            return data;
        }

        #endregion


        #region 最新逻辑，按照少标签模式

        /// <summary>
        /// 拼锅-转移-选可用库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_FullBag(FullBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            var resultno = new MessageModel<string>();
            try
            {
                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行转移", state);
                        return result;
                    }
                }

                #endregion

                if (reqModel.ChangeUnit == "g")
                {
                    reqModel.actualValue = reqModel.actualValue == "0"
                        ? "0"
                        : Math.Round(Convert.ToDecimal(reqModel.actualValue) / 1000, 3).ToString();
                    reqModel.actualWeight = reqModel.actualWeight == "0"
                        ? "0"
                        : Math.Round(Convert.ToDecimal(reqModel.actualWeight) / 1000, 3).ToString();
                    reqModel.bagWeight = reqModel.bagWeight == "0"
                        ? "0"
                        : Math.Round(Convert.ToDecimal(reqModel.bagWeight) / 1000, 3).ToString();
                    reqModel.targetWeight = reqModel.targetWeight == "0"
                        ? "0"
                        : Math.Round(Convert.ToDecimal(reqModel.targetWeight) / 1000, 3).ToString();
                }

                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                //绑定数据
                //这里需要扣子批次库存并转移 1首先查询库存数据
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);

                MaterialInventoryEntity modelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "库存中未查询到改批次数据";
                    return result;
                }

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                modelMaterialInvent = inventMaterialList[0];


                if (!string.IsNullOrEmpty(modelMaterialInvent.ContainerId))
                {
                    result.msg = "当前追溯码已经绑定容器";
                    return result;
                }

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;
                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);
                if (model == null)
                {
                    result.msg = "容器不存在";
                    return result;
                }

                if (model.Status == "3")
                {
                    result.msg = "当前托盘已完成拼锅，请重新选择托盘";
                    return result;
                }

                //计算可以拆分的整包数据(这里需要对包数数量进行一次校验，大于则用最大袋数) 
                int maxbags = reqModel.bags * Convert.ToInt32(reqModel.bagWeight);


                if (maxbags == 0)
                {
                    result.msg = "请确认该物料是否需要整包数量";
                    return result;
                }

                int tagerW = Convert.ToInt32(Convert.ToDecimal(reqModel.targetWeight.Trim()));

                if (tagerW < maxbags)
                {
                    result.msg = "当前数量超过需求最大库存，请重新选择库存进行操作";
                    return result;

                    reqModel.bags = maxbags / Convert.ToInt32(reqModel.bagWeight);
                }
                //根据袋数创建库存信息（子批次码需要重新生成不同的数据）批次信息用改数据下的批次信息


                #region 走业务流程

                //MKM_B_MATERIAL_INVENTORY、MKM_B_MATERIAL_LOT、MKM_B_MATERIAL_SUB_LOT中创建库存
                _unitOfWork.BeginTran();

                List<MaterialSubLotEntity> listSubLot = new List<MaterialSubLotEntity>();
                List<MaterialInventoryEntity> listVent = new List<MaterialInventoryEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialInventoryEntity> listVent2 = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                int kjNumber = 0;
                string userID = _uIser.Name.ToString();
                //循环添加的包数

                #region 这里计算需要扣减的数量

                kjNumber = reqModel.bags * Convert.ToInt32(reqModel.bagWeight);

                #endregion

                string subGUID = string.Empty; //= Guid.NewGuid().ToString();                    

                //子批次表(添加)
                MaterialSubLotEntity modelSubLot = new MaterialSubLotEntity();
                modelSubLot.Create(userID);
                subGUID = modelSubLot.ID;
                //modelSubLot.ID = subGUID;
                modelSubLot.Type = "0";


                #region 构造实体

                SSCCModel models = new SSCCModel();
                models.Type = "";
                models.NextCode = "";
                models.MaxCode = "";
                models.MinCode = "";
                models.Prefix = "";
                models.TableName = "";
                models.TableId = "";
                models.SequenceType = "";
                models.ResetType = "";
                models.FeatureId = "";
                models.pageIndex = 1;
                models.pageSize = 10;
                models.orderByFileds = "";
                string token = _uIser.GetToken();
                var ssccString =
                    await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                if (ssccString.success == true)
                {
                    //ssccString = ssccString.response;
                    modelSubLot.SubLotId = ssccString.response.ToString();
                }

                #endregion

                modelSubLot.ExternalStatus = "3";
                listSubLot.Add(modelSubLot);

                //库存表(添加)//这里需要加下（full bage的状态）
                MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                //modelInventory.ID = inventGUID;
                modelInventory.Create(userID);
                modelInventory.LotId = lotID;
                modelInventory.SublotId = subGUID;
                modelInventory.Quantity =
                    Math.Round(Convert.ToDecimal(kjNumber), 3); // kjNumber;//Convert.ToInt32(reqModel.bagWeight);
                modelInventory.StorageLocation = storage_location;
                modelInventory.EquipmentId = reqModel.equpmentID;
                modelInventory.InventoryType = "Full";
                modelInventory.ContainerId = reqModel.containerID;
                modelInventory.QuantityUomId = unitID;
                modelInventory.BatchId = reqModel.batchID;
                modelInventory.ProductionRequestId = reqModel.proOrderID;
                modelInventory.BatchConsumeRequirementId = reqModel.batchConsumeRequirementId;
                listVent.Add(modelInventory);

                #region MKM_L_CONTAINER_HISTORY 1

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userID);
                //hisModel1.ID = transer1GUID;
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = eqpmentID;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                //hisModel1.ProductOrderId = reqModel.proOrderID;// 工单ID
                //hisModel1.BatchId = reqModel.batchID;//工单批次ID
                //hisModel1.MaterialId = reqModel.MaterialId;
                //hisModel1.SublotId = reqModel.subID;
                hisModel1.Quantity = reqModel.actualWeight.ToString();
                hisModel1.QuantityUomId = unitID;
                // hisModel1.LotId = lotID;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = expirationDate;

                listTranHis.Add(hisModel1);

                #endregion

                #region MKM_L_CONTAINER_HISTORY

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(userID);
                //hisModel.ID = transer2GUID.ToString();
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ContainerCode = model.Name;
                hisModel.Type = "Transfer";
                hisModel.EquipmentId = eqpmentID;
                hisModel.State = "4";
                hisModel.Comment = "Full Bag Transfer";
                hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                hisModel.BatchId = reqModel.batchID; //工单批次ID
                hisModel.Status = "Full";
                hisModel.Quantity = reqModel.bagWeight.ToString();
                hisModel.QuantityUomId = unitID;
                hisModel.SublotId = subGUID;
                hisModel.LotId = lotID;
                hisModel.MaterialId = dataModel.MaterialId;
                //   hisModel
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ExpirationDate = expirationDate;
                listTranHis.Add(hisModel);

                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userID);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = subGUID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;

                trans.Quantity = Math.Round(Convert.ToDecimal(kjNumber), 3); //Convert.ToDecimal(reqModel.bagWeight);

                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "拼锅-转移";
                trans.ProductionExecutionId = "";
                trans.NewProductionExecutionId = reqModel.proOrderID;
                trans.OldBatchId = "";
                trans.NewBatchId = reqModel.batchID;
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = eqpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);

                #endregion


                //当库存为0时删除库存
                modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity), 3) -
                                               Math.Round(Convert.ToDecimal(kjNumber),
                                                   3); // modelMaterialInvent.Quantity - Convert.ToDecimal(kjNumber);
                if (modelMaterialInvent.Quantity < 0)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "当前库存小于转移数量";
                    return result;
                }

                if (modelMaterialInvent.Quantity == 0)
                {
                    var inventoryModel = await _InventorylistingViewEntitydal.QueryById(modelMaterialInvent.ID);

                    if (inventoryModel != null)
                    {
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans1 = new MaterialTransferEntity();
                        trans1.Create(_uIser.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans1.OldStorageLocation = inventoryModel.LocationF;
                        trans1.NewStorageLocation = inventoryModel.LocationF;
                        trans1.OldLotId = inventoryModel.LotId;
                        trans1.NewLotId = inventoryModel.LotId;
                        trans1.OldSublotId = inventoryModel.SlotId;
                        trans1.NewSublotId = inventoryModel.SlotId;
                        trans1.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans1.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans1.Quantity =
                            Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity),
                                3); //  Convert.ToInt32(modelMaterialInvent.Quantity);
                        trans1.QuantityUomId = inventoryModel.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans1.Type = "Delete";
                        trans1.Comment = "删除库存";
                        trans1.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans1.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans1.OldEquipmentId = inventoryModel.EquipmentId;
                        trans1.NewEquipmentId = inventoryModel.EquipmentId;
                        trans1.OldContainerId = inventoryModel.ContainerId;
                        trans1.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans1.OldMaterialId = inventoryModel.MaterialId;
                        trans1.NewMaterialId = inventoryModel.MaterialId;
                        trans1.OldLotExternalStatus = inventoryModel.StatusF;
                        trans1.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans1.NewLotExternalStatus = inventoryModel.StatusF;
                        trans1.NewSublotExternalStatus = inventoryModel.StatusS;

                        trans1.PhysicalQuantity =
                            inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans1.TareQuantity =
                            inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value; //皮数量
                        listTran.Add(trans1);

                        #endregion
                    }

                    //删除库存
                    await _materialInventoryEntityDal.DeleteById(modelMaterialInvent.ID);
                }
                else
                {
                    //扣库存
                    modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                    listVent2.Add(modelMaterialInvent);
                }

                //批量提交
                bool subLotResult = await _materialSubLotServicesDal.Add(listSubLot) > 0;
                bool inventoryResult = await _materialInventoryEntityDal.Add(listVent) > 0;
                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                bool upMaterialInvent = true;
                if (listVent2.Count > 0)
                {
                    upMaterialInvent = await _materialInventoryEntityDal.Update(listVent2);
                }

                if (!subLotResult || !inventoryResult || !hisResult || !tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "添加失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;

                #region 打印标签不考虑是否成功（打印最小标签）

                string equpmentID = reqModel.equpmentID;

                //这里打印标签，upVent打印更新数据的标签，inven

                if (listVent.Count > 0)
                {
                    //打印对应的备料标签
                    for (int i = 0; i < listVent.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(listVent[i].ID);
                        //List<object> objs = new List<object>();
                        //objs.Add(inventViewData);

                        //这里拿打印机，（默认当前节点）
                        //var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);

                        //if (printIDData != null && printIDData.Count >= 0)
                        //{
                        //    //执行打印
                        //    await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                        //}

                        #region 打印新逻辑（根据打印机名字来拿对应的模板）

                        List<PrintSelectViewEntity> printIDData = new List<PrintSelectViewEntity>();

                        //  根据打印机ID查询打印code
                        if (!string.IsNullOrEmpty(reqModel.PrintId))
                        {
                            var entityMode = await _LabelPrinterEntityDal.FindEntity(reqModel.PrintId);

                            if (entityMode != null)
                            {
                                string pCode = entityMode.Code;
                                string pName = entityMode.Description;

                                if (pCode.Contains("移动"))
                                {
                                    //移动的打印
                                    printIDData =
                                        await _IPrintSelectViewServices.GetPrinit_MoveBagByEqumentID(
                                            reqModel.equpmentID);
                                }
                                else
                                {
                                    printIDData =
                                        await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);
                                }
                            }
                            else
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }
                        }
                        else
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }

                        #endregion


                        //原逻辑
                        // 这里拿打印机，（默认当前节点）
                        //  var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);


                        //_IPrintSelectViewServices.GetSelectPrinit_Move();

                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            if (!string.IsNullOrEmpty(reqModel.PrintId))
                            {
                                printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                            }

                            if (printIDData == null || printIDData.Count <= 0)
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }

                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;
                            //执行打印                                                                        
                            await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID,
                                teampClassID, inventViewData, inventViewData[0], 1);
                        }
                    }
                }

                if (listVent2.Count > 0)
                {
                    //打印库存标签
                    for (int i = 0; i < listVent2.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(listVent2[i].ID, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //这里拿打印机，默认拿第一个
                        var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_Bag();

                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            //执行打印
                            //  await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        }
                    }
                }
                //   string

                #endregion

                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "添加失败";
                return result;
            }
        }

        #endregion


        /// <summary>
        /// 备料转移Full Bag(多标签)
        /// </summary>
        /// <param name="subID">子批次</param>
        /// <param name="bags">包数</param>
        /// <param name="actualValue">库房实际重量</param>
        /// <param name="MaterialId">物料ID</param>
        /// <param name="bagWeight">单包重量</param>
        /// <param name="targetWeight">目标重量在视图最上面</param>
        /// <param name="actualWeight">目标重量在视图最上面之前容器量</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_FullBagbydbq(FullBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            var resultno = new MessageModel<string>();
            try
            {
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                //绑定数据
                //这里需要扣子批次库存并转移 1首先查询库存数据
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);

                MaterialInventoryEntity modelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "库存中未查询到改批次数据";
                    return result;
                }

                modelMaterialInvent = inventMaterialList[0];

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;
                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);
                if (model == null)
                {
                    result.msg = "容器不存在";
                    return result;
                }

                if (model.Status == "3")
                {
                    result.msg = "当前托盘已完成拼锅，请重新选择托盘";
                    return result;
                }

                //计算可以拆分的整包数据(这里需要对包数数量进行一次校验，大于则用最大袋数) 
                int maxbags = reqModel.bags * Convert.ToInt32(reqModel.bagWeight);

                int tagerW = Convert.ToInt32(Convert.ToDecimal(reqModel.targetWeight.Trim()));

                if (tagerW < maxbags)
                {
                    reqModel.bags = maxbags / Convert.ToInt32(reqModel.bagWeight);
                }
                //根据袋数创建库存信息（子批次码需要重新生成不同的数据）批次信息用改数据下的批次信息


                #region 走业务流程

                //MKM_B_MATERIAL_INVENTORY、MKM_B_MATERIAL_LOT、MKM_B_MATERIAL_SUB_LOT中创建库存
                _unitOfWork.BeginTran();

                List<MaterialSubLotEntity> listSubLot = new List<MaterialSubLotEntity>();
                List<MaterialInventoryEntity> listVent = new List<MaterialInventoryEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialInventoryEntity> listVent2 = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                int kjNumber = 0;
                string userID = _uIser.Name.ToString();
                //循环添加的包数
                for (int i = 0; i < reqModel.bags; i++)
                {
                    #region 这里计算需要扣减的数量

                    kjNumber += Convert.ToInt32(reqModel.bagWeight);

                    #endregion

                    string subGUID = string.Empty; //= Guid.NewGuid().ToString();
                    //string inventGUID = string.Empty; ; //Guid.NewGuid().ToString();
                    //string transer1GUID = string.Empty;
                    //string transer2GUID = Guid.NewGuid().ToString();

                    //子批次表(添加)
                    MaterialSubLotEntity modelSubLot = new MaterialSubLotEntity();
                    modelSubLot.Create(userID);
                    subGUID = modelSubLot.ID;
                    //modelSubLot.ID = subGUID;
                    modelSubLot.Type = "0";

                    //获取SSCC子批次信息(这里需要确认数据源)
                    //  var ssccString = await Base.Common.HttpRestSharp.HttpHelper.GetApiAsync<MessageModel<string>>("http://apk.neters.club/", "api/BaseUniqueNumber/GetUniqueNumber", _uIser.GetToken(), null);

                    #region 构造实体

                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _uIser.GetToken();
                    var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber",
                        token, models);
                    if (ssccString.success == true)
                    {
                        //ssccString = ssccString.response;
                        modelSubLot.SubLotId = ssccString.response.ToString();
                    }

                    #endregion

                    modelSubLot.ExternalStatus = "3";
                    listSubLot.Add(modelSubLot);

                    //库存表(添加)//这里需要加下（full bage的状态）
                    MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                    //modelInventory.ID = inventGUID;
                    modelInventory.Create(userID);
                    modelInventory.LotId = lotID;
                    modelInventory.SublotId = subGUID;
                    modelInventory.Quantity =
                        Math.Round(Convert.ToDecimal(reqModel.bagWeight), 3); // Convert.ToInt32(reqModel.bagWeight);
                    modelInventory.StorageLocation = storage_location;
                    modelInventory.EquipmentId = reqModel.equpmentID;
                    modelInventory.InventoryType = "Full";
                    modelInventory.ContainerId = reqModel.containerID;
                    modelInventory.QuantityUomId = unitID;
                    modelInventory.BatchId = reqModel.batchID;
                    modelInventory.ProductionRequestId = reqModel.proOrderID;
                    modelInventory.BatchConsumeRequirementId = reqModel.batchConsumeRequirementId;
                    listVent.Add(modelInventory);

                    #region MKM_L_CONTAINER_HISTORY 1

                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                    hisModel1.Create(userID);
                    //hisModel1.ID = transer1GUID;
                    //   hisModel
                    hisModel1.ContainerId = reqModel.containerID;
                    hisModel1.ContainerCode = model.Name;
                    hisModel1.State = "4";
                    hisModel1.Type = "Change Status";
                    hisModel1.EquipmentId = eqpmentID;
                    //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                    //hisModel.State = containerModel.Status;
                    hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                    //hisModel1.ProductOrderId = reqModel.proOrderID;// 工单ID
                    //hisModel1.BatchId = reqModel.batchID;//工单批次ID
                    //hisModel1.MaterialId = reqModel.MaterialId;
                    //hisModel1.SublotId = reqModel.subID;
                    hisModel1.Quantity = reqModel.actualWeight.ToString();
                    hisModel1.QuantityUomId = unitID;
                    // hisModel1.LotId = lotID;
                    //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    // hisModel.Status 批次执行状态
                    //hisModel.ContainerCode= containerModel. 容器编号
                    //hisModel.MaterialProducedActualId 物料产出记录ID
                    //hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel1.LotId = inventModel.LotId;
                    hisModel1.ExpirationDate = expirationDate;

                    listTranHis.Add(hisModel1);

                    #endregion

                    #region MKM_L_CONTAINER_HISTORY

                    //写入容器记录表(Add)
                    ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                    hisModel.Create(userID);
                    //hisModel.ID = transer2GUID.ToString();
                    hisModel.ContainerId = reqModel.containerID;
                    hisModel.ContainerCode = model.Name;
                    hisModel.Type = "Transfer";
                    hisModel.EquipmentId = eqpmentID;
                    hisModel.State = "4";
                    hisModel.Comment = "Full Bag Transfer";
                    hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                    hisModel.BatchId = reqModel.batchID; //工单批次ID
                    hisModel.Status = "Full";
                    hisModel.Quantity = reqModel.bagWeight.ToString();
                    hisModel.QuantityUomId = unitID;
                    hisModel.SublotId = subGUID;
                    hisModel.LotId = lotID;
                    hisModel.MaterialId = dataModel.MaterialId;
                    //   hisModel
                    hisModel.ContainerId = reqModel.containerID;
                    hisModel.ExpirationDate = expirationDate;
                    listTranHis.Add(hisModel);

                    //hisModel.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                    ////hisModel.State = containerModel.Status;
                    //hisModel.Comment = containerHisType;
                    ////hisModel.ProductOrderId 工单ID
                    ////hisModel.BatchId 工单批次ID
                    //hisModel.MaterialId = inventModel.MaterialId;
                    //hisModel.SublotId = inventModel.SubLotId;
                    //hisModel.Quantity = inventModel.Quantity.ToString();
                    //hisModel.QuantityUomId = inventModel.QuantityUomId;
                    ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                    ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                    ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                    //// hisModel.Status 批次执行状态
                    ////hisModel.ContainerCode= containerModel. 容器编号
                    ////hisModel.MaterialProducedActualId 物料产出记录ID
                    ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                    //hisModel.LotId = inventModel.LotId;
                    //hisModel.ExpirationDate = expirationDate;

                    //bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

                    #endregion

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userID);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = storage_location;
                    trans.NewStorageLocation = storage_location;
                    trans.OldLotId = lotID;
                    trans.NewLotId = lotID;
                    trans.OldSublotId = reqModel.subID;
                    trans.NewSublotId = subGUID;
                    trans.OldExpirationDate = expirationDate;
                    trans.NewExpirationDate = expirationDate;
                    trans.Quantity =
                        Math.Round(Convert.ToDecimal(reqModel.bagWeight), 3); //Convert.ToDecimal(reqModel.bagWeight);
                    trans.QuantityUomId = unitID;
                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Transfer Inventory";
                    trans.Comment = "转移";
                    //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                    //trans.TransferGroupId
                    trans.OldEquipmentId = eqpmentID;
                    trans.NewEquipmentId = eqpmentID;
                    trans.OldContainerId = "";
                    trans.NewContainerId = reqModel.containerID;
                    //status
                    trans.OldMaterialId = dataModel.MaterialId;
                    trans.NewMaterialId = dataModel.MaterialId;
                    trans.OldLotExternalStatus = dataModel.LStatus;
                    trans.OldSublotExternalStatus = dataModel.SbStatus;
                    trans.NewLotExternalStatus = dataModel.LStatus;
                    trans.NewSublotExternalStatus = dataModel.SbStatus;

                    //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                    //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                    listTran.Add(trans);

                    #endregion
                }

                //当库存为0时删除库存
                modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity), 3) -
                                               Math.Round(Convert.ToDecimal(kjNumber),
                                                   3); //modelMaterialInvent.Quantity - Convert.ToDecimal(kjNumber);
                if (modelMaterialInvent.Quantity < 0)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "当前库存小于转移数量";
                    return result;
                }

                if (modelMaterialInvent.Quantity == 0)
                {
                    var inventoryModel = await _InventorylistingViewEntitydal.QueryById(modelMaterialInvent.ID);

                    if (inventoryModel != null)
                    {
                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(_uIser.Name.ToString());
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventoryModel.LocationF;
                        trans.NewStorageLocation = inventoryModel.LocationF;
                        trans.OldLotId = inventoryModel.LotId;
                        trans.NewLotId = inventoryModel.LotId;
                        trans.OldSublotId = inventoryModel.SlotId;
                        trans.NewSublotId = inventoryModel.SlotId;
                        trans.OldExpirationDate = inventoryModel.ExpirationDate;
                        trans.NewExpirationDate = inventoryModel.ExpirationDate;
                        trans.Quantity =
                            Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity),
                                3); // Convert.ToInt32(modelMaterialInvent.Quantity);
                        trans.QuantityUomId = inventoryModel.QuantityUomId;
                        // trans.ProductionExecutionId = model.ProductionRequestId;
                        trans.Type = "Delete";
                        trans.Comment = "删除库存";
                        trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventoryModel.EquipmentId;
                        trans.NewEquipmentId = inventoryModel.EquipmentId;
                        trans.OldContainerId = inventoryModel.ContainerId;
                        trans.NewContainerId = inventoryModel.ContainerId;
                        //status
                        trans.OldMaterialId = inventoryModel.MaterialId;
                        trans.NewMaterialId = inventoryModel.MaterialId;
                        trans.OldLotExternalStatus = inventoryModel.StatusF;
                        trans.OldSublotExternalStatus = inventoryModel.StatusS;
                        trans.NewLotExternalStatus = inventoryModel.StatusF;
                        trans.NewSublotExternalStatus = inventoryModel.StatusS;

                        trans.PhysicalQuantity =
                            inventoryModel.MaxVolume == null ? "" : inventoryModel.MaxVolume.ToString(); //物理数量
                        trans.TareQuantity =
                            inventoryModel.TareWeight == null ? 0 : inventoryModel.TareWeight.Value; //皮数量
                        listTran.Add(trans);

                        #endregion
                    }

                    //删除库存
                    await _materialInventoryEntityDal.DeleteById(modelMaterialInvent.ID);
                }
                else
                {
                    //扣库存
                    modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                    listVent2.Add(modelMaterialInvent);
                }

                //批量提交
                bool subLotResult = await _materialSubLotServicesDal.Add(listSubLot) > 0;
                bool inventoryResult = await _materialInventoryEntityDal.Add(listVent) > 0;
                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                bool upMaterialInvent = true;
                if (listVent2.Count > 0)
                {
                    upMaterialInvent = await _materialInventoryEntityDal.Update(listVent2);
                }

                if (!subLotResult || !inventoryResult || !hisResult || !tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "添加失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "添加失败";
                return result;
            }
        }

        #region 原MPreparationTransfer_PartialBag注释

        /*
                 /// <summary>
        /// 备料转移PartialBag（前端校验数量）
        /// </summary>
        /// <param name="subID">子批次</param>
        /// <param name="inputBagWeight">输入数量</param>
        /// <param name="bagsWeight">单包重量(从Full Bag里面拿设置的数量)</param>
        /// <param name="targetWeight">目标重量在视图最上面</param>
        /// <param name="actualWeight">目标重量在视图最上面之前容器量</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>
        /// <param name="equpmentID">这里来自于首页的下拉框数据源</param>
        /// <returns></returns>
        public async Task<bool> MPreparationTransfer_PartialBag(PartialBagModel reqModel)
        {
            try
            {
                string userId = _uIser.ID.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
               .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                            .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                //绑定数据
                //这里需要扣子批次库存并转移 1首先查询库存数据
                List<MaterialInventoryEntity> inventMaterialList = await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);

                MaterialInventoryEntity modelMaterialInvent = new MaterialInventoryEntity();
                // MaterialInventoryEntity upModelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    return false;

                }
                //计算数量
                // int ylNum = reqModel.bagsWeight - reqModel.inputBagWeight;

                //获取当前的数据(库存信息)
                modelMaterialInvent = inventMaterialList[0];
                modelMaterialInvent.Modify(modelMaterialInvent.ID, userId);
                //
                //upModelMaterialInvent = inventMaterialList[0];

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;
                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);


                //根据袋数创建库存信息（子批次码需要重新生成不同的数据）批次信息用改数据下的批次信息
                //调用API接口生成子批次码

                #region 走业务流程
                //MKM_B_MATERIAL_INVENTORY、MKM_B_MATERIAL_LOT、MKM_B_MATERIAL_SUB_LOT中创建库存
                _unitOfWork.BeginTran();

                List<MaterialSubLotEntity> listSubLot = new List<MaterialSubLotEntity>();
                List<MaterialInventoryEntity> listVent = new List<MaterialInventoryEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                List<MaterialInventoryEntity> upVent = new List<MaterialInventoryEntity>();

                int kjNumber = 0;
                //循环添加的包数

                #region 这里计算需要扣减的数量
                kjNumber += reqModel.inputBagWeight;

                #endregion
                string subGUID = string.Empty;
                //string subGUID = Guid.NewGuid().ToString();
                //string inventGUID = Guid.NewGuid().ToString();
                //string transer1GUID = Guid.NewGuid().ToString();
                //string transer2GUID = Guid.NewGuid().ToString();

                //子批次表(添加)
                MaterialSubLotEntity modelSubLot = new MaterialSubLotEntity();
                modelSubLot.Create(userId);
                subGUID = modelSubLot.ID;
                //modelSubLot.ID = subGUID;
                modelSubLot.Type = "0";

                //获取SSCC子批次信息(这里需要确认数据源)
                #region 构造实体

                SSCCModel models = new SSCCModel();
                models.Type = "";
                models.NextCode = "";
                models.MaxCode = "";
                models.MinCode = "";
                models.Prefix = "";
                models.TableName = "";
                models.TableId = "";
                models.SequenceType = "";
                models.ResetType = "";
                models.FeatureId = "";
                models.pageIndex = 1;
                models.pageSize = 10;
                models.orderByFileds = "";
                string token = _uIser.GetToken();
                var ssccString = await HttpHelper.PostAsync<string>("BASE", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                #endregion

                if (ssccString.success == true)
                {
                    modelSubLot.SubLotId = ssccString.response.ToString();
                }
                //  modelSubLot.SubLotId = ssccString.msg;
                listSubLot.Add(modelSubLot);
                //计算库存-输入的重量 大于0则添加新纪录进入库存否则更新库存数据添加容器
                decimal Inventorynum = modelMaterialInvent.Quantity - reqModel.inputBagWeight;
                if (Inventorynum == 0)
                {
                    //upModelMaterialInvent.Quantity = inputBagWeight;
                    modelMaterialInvent.ContainerId = reqModel.containerID;
                    modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                    upVent.Add(modelMaterialInvent);

                }
                else
                {
                    //库存表(添加)
                    MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                    modelInventory.Create(userId);
                    //modelInventory.ID = inventGUID;
                    modelInventory.LotId = lotID;
                    modelInventory.SublotId = subGUID;
                    modelInventory.Quantity = reqModel.inputBagWeight;
                    modelInventory.StorageLocation = storage_location;
                    modelInventory.InventoryType = "Partial";
                    modelInventory.EquipmentId = eqpmentID;
                    modelInventory.ContainerId = reqModel.containerID;
                    modelInventory.QuantityUomId = unitID;
                    listVent.Add(modelInventory);

                    //扣库存（更新原来的数据）
                    modelMaterialInvent.Quantity = modelMaterialInvent.Quantity - Convert.ToDecimal(kjNumber);
                    modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                    upVent.Add(modelMaterialInvent);
                }


                #region 写入容器记录 1

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userId);
                //hisModel1.ID = transer1GUID;
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = eqpmentID;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = reqModel.actualWeight.ToString();
                hisModel1.QuantityUomId = unitID;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = expirationDate;

                listTranHis.Add(hisModel1);


                #endregion

                #region 写入容器记录2

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(userId);
                //hisModel.ID = transer2GUID.ToString();
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ContainerCode = model.Name;
                hisModel.Type = "Transfer";
                hisModel.EquipmentId = eqpmentID;
                hisModel.State = "4";
                hisModel.Comment = "Partial Bag Transfer";
                hisModel.ProductOrderId = reqModel.proOrderID;// 工单ID
                hisModel.BatchId = reqModel.batchID;//工单批次ID
                hisModel.Status = "Partial Bag";
                hisModel.Quantity = reqModel.inputBagWeight.ToString();
                hisModel.QuantityUomId = unitID;
                hisModel.SublotId = subGUID;
                hisModel.LotId = lotID;
                hisModel.MaterialId = dataModel.MaterialId;
                //   hisModel
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ExpirationDate = expirationDate;
                listTranHis.Add(hisModel);

                //hisModel.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                ////hisModel.State = containerModel.Status;
                //hisModel.Comment = containerHisType;
                ////hisModel.ProductOrderId 工单ID
                ////hisModel.BatchId 工单批次ID
                //hisModel.MaterialId = inventModel.MaterialId;
                //hisModel.SublotId = inventModel.SubLotId;
                //hisModel.Quantity = inventModel.Quantity.ToString();
                //hisModel.QuantityUomId = inventModel.QuantityUomId;
                ////hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                ////hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                ////hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                //// hisModel.Status 批次执行状态
                ////hisModel.ContainerCode= containerModel. 容器编号
                ////hisModel.MaterialProducedActualId 物料产出记录ID
                ////hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel.LotId = inventModel.LotId;
                //hisModel.ExpirationDate = expirationDate;

                //bool hisUp = await _containerHistoryServices.Add(hisModel) > 0;

                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userId);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = subGUID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = reqModel.inputBagWeight;
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "转移";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = eqpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);


                #endregion


                #region 这里新增库存转移记录

                #region //子批次
                MaterialSubLotEntity modelSubLot2 = new MaterialSubLotEntity();
                modelSubLot2.Create(userId);
                //modelSubLot2.ID = Guid.NewGuid().ToString();
                modelSubLot2.Type = "0";

                //获取SSCC子批次信息(这里需要确认数据源)
                //  var ssccString1 = await Base.Common.HttpRestSharp.HttpHelper.GetApiAsync<MessageModel<string>>("http://apk.neters.club/", "api/BaseUniqueNumber/GetUniqueNumber", _uIser.GetToken(), null);

                var ssccString1 = await HttpHelper.PostAsync<string>("BASE", "api/BaseUniqueNumber/GetUniqueNumber", token, model);
                string ssccStr2 = string.Empty;
                if (ssccString1.success == true)
                {
                    //modelSubLot2.SubLotId = ssccStr2;
                    ssccStr2 = ssccString1.response.ToString();
                    modelSubLot2.SubLotId = ssccStr2;
                }



                listSubLot.Add(modelSubLot2);

                #endregion
                //批量提交
                bool subLotResult = await _materialSubLotServicesDal.Add(listSubLot) > 0;
                bool inventoryResult = await _materialInventoryEntityDal.Add(listVent) > 0;
                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;

                bool upResult = await _materialInventoryEntityDal.Update(upVent);

                //这里执行新增数据

                if (!subLotResult || !inventoryResult || !hisResult || !tranResult || !upResult)
                {
                    _unitOfWork.RollbackTran();

                    return false;
                }

                _unitOfWork.CommitTran();

                return true;

                #endregion
                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();

                return false;
            }

        }
         */

        #endregion

        /// <summary>
        /// 备料转移PartialBag（前端校验数量） 称量备料-转移按钮
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="inputBagWeight">输入数量</param>
        /// <param name="bagsWeight">单包重量(从Full Bag里面拿设置的数量)</param>
        /// <param name="targetWeight">目标重量在视图最上面</param>
        /// <param name="actualWeight">目标重量在视图最上面之前容器量</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>
        /// <param name="equpmentID">这里来自于首页的下拉框数据源</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_PartialBag(PartialBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行称量备料转移", state);
                        return result;
                    }
                }

                #endregion


                //单包数量
                decimal bagsSize = Convert.ToDecimal(reqModel.bagWeight);
                //处理g逻辑
                if (reqModel.ChangeUnit == "g")
                {
                    //bagsSize = bagsSize * 1000;
                }

                //输入数量
                decimal inputNumber = reqModel.inputBagWeight;

                if (inputNumber <= 0)
                {
                    result.msg = "称量数量为负数，请重新称量";
                    return result;
                }

                //称量备料转移
                //1.拿选中的库存数据，判断当前是否大于单包重量，如果大于则需要考虑拆整袋操作。例如90 单袋数量30，最终就会变成库存里面就变成一个 60 一个拆分后剩余的数据比如称的5那就是25和5，5 放进最终结果里面，库存里面剩余60和25
                string userId = _uIser.Name.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                //绑定数据
                //这里需要扣子批次库存并转移 1首先查询库存数据
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);
                MaterialInventoryEntity modelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "未找到子批次";
                    return result;
                }

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion


                //计算数量(差值)
                decimal ylNum = Math.Round(Convert.ToDecimal(bagsSize), 3) -
                                Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                                    3); //Convert.ToDecimal(bagsSize) - reqModel.inputBagWeight;

                if (ylNum < 0)
                {
                    result.msg = "转移数量过大，超过单包重量";
                    return result;
                }

                //获取当前的数据(库存信息)
                modelMaterialInvent = inventMaterialList[0];
                //选中值
                decimal selectQty =
                    Math.Round(Convert.ToDecimal(modelMaterialInvent.Quantity), 3); // modelMaterialInvent.Quantity;
                if (reqModel.ChangeUnit == "g")
                {
                    selectQty = selectQty * 1000;
                }

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;

                if (string.IsNullOrEmpty(eqpmentID))
                {
                    result.msg = "请确定是否选择房间，请尝试重新打开菜单页";
                    return result;
                }

                string unitID = dataModel.UnitId;

                DateTime expirationDate = dataModel.ExpirationDate;

                //创建的子批次
                List<MaterialSubLotEntity> listSubLot = new List<MaterialSubLotEntity>();
                List<MaterialInventoryEntity> listVent = new List<MaterialInventoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();
                List<MaterialInventoryEntity> upVent = new List<MaterialInventoryEntity>();
                string inventID = string.Empty;

                #region 单独处理整包数据操作

                decimal valuePage = 0;

                if (selectQty <= Convert.ToDecimal(bagsSize))
                {
                    valuePage = 0;
                }
                else
                {
                    valuePage = Math.Round(Convert.ToDecimal(selectQty), 3) /
                                Math.Round(Convert.ToDecimal(bagsSize), 3);
                    //Convert.ToDecimal(Convert.ToDecimal(selectQty) / Convert.ToDecimal(bagsSize));
                }

                //打印条数
                int printCount = 0;


                //获取物料属性(“IsSplitBag”，默认为0，值为0时：称量备料的转移按钮不会导致可用库存新增一条库存)
                var mPropertyModels = await _materialPropertyDal.FindList(p =>
                    p.MaterialId == reqModel.MaterialId && p.PropertyCode == "IsSplitBag");
                var mPropertyModel = mPropertyModels.FirstOrDefault();
                if (mPropertyModel != null)
                {
                    string propertyCode = mPropertyModel.PropertyCode;
                    if (!string.IsNullOrEmpty(propertyCode))
                    {
                        if (propertyCode == "IsSplitBag")
                        {
                            string value = mPropertyModel.PropertyValue;
                            if (!string.IsNullOrEmpty(value))
                            {
                                if (value == "0")
                                {
                                    valuePage = 0;
                                }
                            }
                        }
                    }
                }

                _unitOfWork.BeginTran();
                //只有一袋
                if (valuePage < 1)
                {
                    //打印原有标签信息，这里是更新了库存之后的（子批次条码不变）

                    //生成新的标签，并打印

                    #region 创建子批次生成标签(新的)

                    string subGUID = string.Empty;
                    MaterialSubLotEntity modelSubLotOne = new MaterialSubLotEntity();
                    modelSubLotOne.Create(userId);
                    subGUID = modelSubLotOne.ID;
                    modelSubLotOne.Type = "0";
                    modelSubLotOne.ExternalStatus = "3";

                    #region 获取SSCC

                    DateTime nowTime = DateTime.Now;
                    SSCCModel models = new SSCCModel();
                    models.Type = "";
                    models.NextCode = "";
                    models.MaxCode = "";
                    models.MinCode = "";
                    models.Prefix = "";
                    models.TableName = "";
                    models.TableId = "";
                    models.SequenceType = "";
                    models.ResetType = "";
                    models.FeatureId = "";
                    models.pageIndex = 1;
                    models.pageSize = 10;
                    models.orderByFileds = "";
                    string token = _uIser.GetToken();
                    var ssccString = await HttpHelper.PostAsync<string>("DFM", "api/BaseUniqueNumber/GetUniqueNumber",
                        token, models);

                    SerilogServer.LogDebug($"获取SSCC耗时:[{(DateTime.Now - nowTime).TotalSeconds}]", "获取SSCC耗时log");

                    if (ssccString.success == true)
                    {
                        modelSubLotOne.SubLotId = ssccString.response.ToString();
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "子批次生成失败";
                        return result;
                    }

                    listSubLot.Add(modelSubLotOne);

                    #endregion

                    #region 新增库存

                    MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                    modelInventory.Create(userId);
                    modelInventory.LotId = lotID;

                    decimal insertQTY = Math.Round(Convert.ToDecimal(reqModel.inputBagWeight), 6);
                    if (reqModel.ChangeUnit == "g")
                    {
                        if (insertQTY != 0)
                        {
                            insertQTY = Math.Round(Convert.ToDecimal(insertQTY / 1000), 6);
                        }
                    }

                    modelInventory.Quantity = insertQTY; // Convert.ToDecimal(reqModel.inputBagWeight);
                    modelInventory.SublotId = subGUID;
                    modelInventory.InventoryType = "Partial";
                    modelInventory.EquipmentId = reqModel.equpmentID;
                    ;
                    modelInventory.ProductionRequestId = reqModel.proOrderID;
                    modelInventory.BatchConsumeRequirementId = reqModel.batchConsumeRequirementId;
                    modelInventory.BatchId = reqModel.batchID;
                    modelInventory.QuantityUomId = modelMaterialInvent.QuantityUomId;
                    modelInventory.ContainerId = "";
                    listVent.Add(modelInventory);
                    inventID = modelInventory.ID;

                    #endregion

                    #endregion

                    #region 更新原库存信息

                    decimal Inventorynum = Math.Round(Convert.ToDecimal(selectQty), 3) -
                                           Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                                               3); //Convert.ToDecimal(selectQty) - Convert.ToDecimal(reqModel.inputBagWeight);
                    if (Inventorynum == 0)
                    {
                        await _materialInventoryEntityDal.DeleteById(modelMaterialInvent.ID);

                        //扣库存（更新原来的数据）
                        //modelMaterialInvent.Quantity = selectQty - Convert.ToDecimal(bagsSize);
                        //modelMaterialInvent.ContainerId = "";
                        //modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                        //upVent.Add(modelMaterialInvent);
                        Inventorynum = Convert.ToDecimal(reqModel.inputBagWeight);

                        #region 写入删除历史

                        //写入历史记录
                        MaterialTransferEntity transDelete = new MaterialTransferEntity();
                        transDelete.Create(userId);
                        //trans.ID = Guid.NewGuid().ToString();
                        transDelete.OldStorageLocation = storage_location;
                        transDelete.NewStorageLocation = storage_location;
                        transDelete.OldLotId = lotID;
                        transDelete.NewLotId = lotID;
                        transDelete.OldSublotId = reqModel.subID;
                        transDelete.NewSublotId = subGUID;
                        transDelete.OldExpirationDate = expirationDate;
                        transDelete.NewExpirationDate = expirationDate;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (selectQty != 0)
                            {
                                transDelete.Quantity = Math.Round(Convert.ToDecimal(selectQty / 1000), 3);
                            }
                        }
                        else
                        {
                            transDelete.Quantity = Math.Round(Convert.ToDecimal(selectQty), 3);
                        }

                        // Convert.ToDecimal(selectQty);
                        transDelete.QuantityUomId = unitID;
                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        transDelete.Type = "Delete";
                        transDelete.Comment = "称量-删除库存";

                        //新添加的字段（订单和子批次）
                        //transDelete.ProductionExecutionId = "";
                        //transDelete.NewProductionExecutionId = reqModel.proOrderID;
                        //transDelete.OldBatchId = "";
                        //transDelete.NewBatchId = reqModel.batchID;

                        //trans.TransferGroupId
                        transDelete.OldEquipmentId = eqpmentID;
                        transDelete.NewEquipmentId = eqpmentID;
                        transDelete.OldContainerId = "";
                        transDelete.NewContainerId = reqModel.containerID;
                        //status
                        transDelete.OldMaterialId = dataModel.MaterialId;
                        transDelete.NewMaterialId = dataModel.MaterialId;
                        transDelete.OldLotExternalStatus = dataModel.LStatus;
                        transDelete.OldSublotExternalStatus = dataModel.SbStatus;
                        transDelete.NewLotExternalStatus = dataModel.LStatus;
                        transDelete.NewSublotExternalStatus = dataModel.SbStatus;
                        transDelete.Mode = reqModel.IsType;
                        //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                        //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                        listTran.Add(transDelete);

                        #endregion
                    }
                    else
                    {
                        if (Inventorynum < 0)
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "库存不足";
                            return result;
                        }

                        //扣库存（更新原来的数据）

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (Inventorynum != 0)
                            {
                                modelMaterialInvent.Quantity =
                                    Math.Round(Convert.ToDecimal(Inventorynum / 1000), 3); // Inventorynum;
                            }
                        }
                        else
                        {
                            modelMaterialInvent.Quantity =
                                Math.Round(Convert.ToDecimal(Inventorynum), 3); // Inventorynum;
                        }

                        modelMaterialInvent.ContainerId = "";
                        modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                        upVent.Add(modelMaterialInvent);
                    }

                    #region 写入转移历史

                    //写入历史记录
                    MaterialTransferEntity trans = new MaterialTransferEntity();
                    trans.Create(userId);
                    //trans.ID = Guid.NewGuid().ToString();
                    trans.OldStorageLocation = storage_location;
                    trans.NewStorageLocation = storage_location;
                    trans.OldLotId = lotID;
                    trans.NewLotId = lotID;
                    trans.OldSublotId = reqModel.subID;
                    trans.NewSublotId = subGUID;
                    trans.OldExpirationDate = expirationDate;
                    trans.NewExpirationDate = expirationDate;

                    if (reqModel.ChangeUnit == "g")
                    {
                        if (inputNumber != 0)
                        {
                            trans.Quantity = Math.Round(Convert.ToDecimal(inputNumber / 1000), 3);
                        } // Math.Round(Convert.ToDecimal(Inventorynum), 3);  //Convert.ToDecimal(Inventorynum);
                    }
                    else
                    {
                        trans.Quantity =
                            Math.Round(Convert.ToDecimal(inputNumber),
                                3); // Math.Round(Convert.ToDecimal(Inventorynum), 3);  //Convert.ToDecimal(Inventorynum);
                    }

                    trans.QuantityUomId = unitID;
                    //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                    trans.Type = "Transfer Inventory";
                    trans.Comment = "称量-转移";
                    trans.Mode = reqModel.IsType;
                    //新添加的字段（订单和子批次）
                    trans.ProductionExecutionId = "";
                    trans.NewProductionExecutionId = reqModel.proOrderID;
                    trans.OldBatchId = "";
                    trans.NewBatchId = reqModel.batchID;

                    //trans.TransferGroupId
                    trans.OldEquipmentId = eqpmentID;
                    trans.NewEquipmentId = eqpmentID;
                    trans.OldContainerId = "";
                    trans.NewContainerId = reqModel.containerID;
                    //status
                    trans.OldMaterialId = dataModel.MaterialId;
                    trans.NewMaterialId = dataModel.MaterialId;
                    trans.OldLotExternalStatus = dataModel.LStatus;
                    trans.OldSublotExternalStatus = dataModel.SbStatus;
                    trans.NewLotExternalStatus = dataModel.LStatus;
                    trans.NewSublotExternalStatus = dataModel.SbStatus;
                    //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                    //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                    listTran.Add(trans);

                    #endregion

                    #endregion
                }
                else //多袋 （更新当前袋子数量）
                {
                    //打印原有标签信息，这里是更新了库存之后的（子批次条码不变）
                    //打印单包剩余的标签，生成新的标签
                    //生成新的标签，并打印

                    #region 转移的数据库存处理（单包）

                    decimal Inventorynum = Math.Round(Convert.ToDecimal(bagsSize), 3) -
                                           Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                                               3); // Convert.ToDecimal(bagsSize) - Convert.ToDecimal(reqModel.inputBagWeight);

                    #region 剩余的袋数

                    if (Inventorynum == 0)
                    {
                        #region 创建子批次生成标签

                        string subGUIDx = string.Empty;

                        MaterialSubLotEntity modelSubLotOnex = new MaterialSubLotEntity();
                        modelSubLotOnex.Create(userId);
                        subGUIDx = modelSubLotOnex.ID;
                        modelSubLotOnex.Type = "0";
                        modelSubLotOnex.ExternalStatus = "3";

                        #region 获取SSCC

                        SSCCModel modelsx = new SSCCModel();
                        modelsx.Type = "";
                        modelsx.NextCode = "";
                        modelsx.MaxCode = "";
                        modelsx.MinCode = "";
                        modelsx.Prefix = "";
                        modelsx.TableName = "";
                        modelsx.TableId = "";
                        modelsx.SequenceType = "";
                        modelsx.ResetType = "";
                        modelsx.FeatureId = "";
                        modelsx.pageIndex = 1;
                        modelsx.pageSize = 10;
                        modelsx.orderByFileds = "";
                        string token = _uIser.GetToken();
                        var ssccString = await HttpHelper.PostAsync<string>("DFM",
                            "api/BaseUniqueNumber/GetUniqueNumber", token, modelsx);

                        if (ssccString.success == true)
                        {
                            modelSubLotOnex.SubLotId = ssccString.response.ToString();
                        }
                        else
                        {
                            _unitOfWork.RollbackTran();
                            result.msg = "子批次生成失败";
                            return result;
                        }

                        listSubLot.Add(modelSubLotOnex);

                        #endregion

                        #endregion

                        //添加库存
                        MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                        modelInventory.Create(userId);
                        modelInventory.LotId = lotID;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (reqModel.inputBagWeight != 0)
                            {
                                modelInventory.Quantity =
                                    Math.Round(Convert.ToDecimal(reqModel.inputBagWeight / 1000),
                                        3); // Convert.ToDecimal(reqModel.inputBagWeight);
                            }
                        }
                        else
                        {
                            modelInventory.Quantity =
                                Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                                    3); // Convert.ToDecimal(reqModel.inputBagWeight);
                        }


                        modelInventory.SublotId = subGUIDx;
                        modelInventory.InventoryType = "Partial";
                        modelInventory.EquipmentId = reqModel.equpmentID;
                        ;
                        modelInventory.ProductionRequestId = reqModel.proOrderID;
                        modelInventory.BatchConsumeRequirementId = reqModel.batchConsumeRequirementId;
                        modelInventory.BatchId = reqModel.batchID;
                        modelInventory.QuantityUomId = modelMaterialInvent.QuantityUomId;
                        modelInventory.ContainerId = "";
                        listVent.Add(modelInventory);

                        #region 清空库存信息

                        await _materialInventoryEntityDal.DeleteById(modelMaterialInvent.ID);

                        #endregion

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(userId);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = storage_location;
                        trans.NewStorageLocation = storage_location;
                        trans.OldLotId = lotID;
                        trans.NewLotId = lotID;
                        trans.OldSublotId = reqModel.subID;
                        trans.NewSublotId = subGUIDx;
                        trans.OldExpirationDate = expirationDate;
                        trans.NewExpirationDate = expirationDate;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (reqModel.inputBagWeight != 0)
                            {
                                trans.Quantity =
                                    Math.Round(Convert.ToDecimal(reqModel.inputBagWeight / 1000),
                                        3); // Convert.ToDecimal(reqModel.inputBagWeight);
                            }
                        }
                        else
                        {
                            trans.Quantity =
                                Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                                    3); // Convert.ToDecimal(reqModel.inputBagWeight);
                        }

                        //   trans.Quantity = Math.Round(Convert.ToDecimal(reqModel.inputBagWeight), 3);// Convert.ToDecimal(reqModel.inputBagWeight);
                        trans.QuantityUomId = unitID;
                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移";

                        //新添加的字段（订单和子批次）
                        trans.ProductionExecutionId = "";
                        trans.NewProductionExecutionId = reqModel.proOrderID;
                        trans.OldBatchId = "";
                        trans.NewBatchId = reqModel.batchID;

                        //trans.TransferGroupId
                        trans.OldEquipmentId = eqpmentID;
                        trans.NewEquipmentId = eqpmentID;
                        trans.OldContainerId = "";
                        trans.NewContainerId = reqModel.containerID;
                        //status
                        trans.OldMaterialId = dataModel.MaterialId;
                        trans.NewMaterialId = dataModel.MaterialId;
                        trans.OldLotExternalStatus = dataModel.LStatus;
                        trans.OldSublotExternalStatus = dataModel.SbStatus;
                        trans.NewLotExternalStatus = dataModel.LStatus;
                        trans.NewSublotExternalStatus = dataModel.SbStatus;
                        //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                        //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                        listTran.Add(trans);

                        #endregion
                    }
                    else //这里需要生成2条库存信息
                    {
                        #region 第一条新增(转移的数量)

                        #region 创建子批次生成标签

                        string subGUID = string.Empty;

                        MaterialSubLotEntity modelSubLotOne = new MaterialSubLotEntity();
                        modelSubLotOne.Create(userId);
                        subGUID = modelSubLotOne.ID;
                        modelSubLotOne.Type = "0";
                        modelSubLotOne.ExternalStatus = "3";

                        #region 获取SSCC

                        SSCCModel models = new SSCCModel();
                        models.Type = "";
                        models.NextCode = "";
                        models.MaxCode = "";
                        models.MinCode = "";
                        models.Prefix = "";
                        models.TableName = "";
                        models.TableId = "";
                        models.SequenceType = "";
                        models.ResetType = "";
                        models.FeatureId = "";
                        models.pageIndex = 1;
                        models.pageSize = 10;
                        models.orderByFileds = "";
                        string token = _uIser.GetToken();
                        var ssccString = await HttpHelper.PostAsync<string>("DFM",
                            "api/BaseUniqueNumber/GetUniqueNumber", token, models);

                        if (ssccString.success == true)
                        {
                            modelSubLotOne.SubLotId = ssccString.response.ToString();
                        }

                        listSubLot.Add(modelSubLotOne);

                        #endregion

                        #endregion

                        // 添加库存
                        MaterialInventoryEntity modelInventory = new MaterialInventoryEntity();
                        modelInventory.Create(userId);
                        modelInventory.LotId = lotID;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (reqModel.inputBagWeight != 0)
                            {
                                modelInventory.Quantity =
                                    Math.Round(Convert.ToDecimal(reqModel.inputBagWeight / 1000), 3);
                            }
                        }
                        else
                        {
                            modelInventory.Quantity = Math.Round(Convert.ToDecimal(reqModel.inputBagWeight), 3);
                        }


                        //     modelInventory.Quantity = Math.Round(Convert.ToDecimal(reqModel.inputBagWeight), 3);// Convert.ToDecimal(reqModel.inputBagWeight);
                        modelInventory.SublotId = subGUID;
                        modelInventory.InventoryType = "Partial";
                        modelInventory.EquipmentId = reqModel.equpmentID;
                        ;
                        modelInventory.ProductionRequestId = reqModel.proOrderID;
                        modelInventory.BatchConsumeRequirementId = reqModel.batchConsumeRequirementId;
                        modelInventory.BatchId = reqModel.batchID;
                        modelInventory.QuantityUomId = modelMaterialInvent.QuantityUomId;
                        modelInventory.ContainerId = "";
                        listVent.Add(modelInventory);

                        #endregion

                        #region 第二条新增（剩余的数量）

                        #region 创建子批次生成标签

                        string subGUID1 = string.Empty;

                        MaterialSubLotEntity modelSubLotOne1 = new MaterialSubLotEntity();
                        modelSubLotOne1.Create(userId);
                        subGUID1 = modelSubLotOne1.ID;
                        modelSubLotOne1.Type = "0";
                        modelSubLotOne1.ExternalStatus = "3";

                        #region 获取SSCC

                        var ssccString2 = await HttpHelper.PostAsync<string>("DFM",
                            "api/BaseUniqueNumber/GetUniqueNumber", token, models);
                        if (ssccString2.success == true)
                        {
                            modelSubLotOne1.SubLotId = ssccString2.response.ToString();
                        }

                        listSubLot.Add(modelSubLotOne1);

                        #endregion

                        #endregion

                        // 添加库存
                        MaterialInventoryEntity modelInventory1 = new MaterialInventoryEntity();
                        modelInventory1.Create(userId);
                        modelInventory1.LotId = lotID;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (Inventorynum != 0)
                            {
                                modelInventory1.Quantity = Math.Round(Convert.ToDecimal(Inventorynum / 1000), 3);
                            }
                        }
                        else
                        {
                            modelInventory1.Quantity = Math.Round(Convert.ToDecimal(Inventorynum), 3);
                        }

                        //  modelInventory1.Quantity = Math.Round(Convert.ToDecimal(Inventorynum), 3); // Inventorynum;
                        modelInventory1.SublotId = subGUID1;
                        modelInventory1.InventoryType = "";
                        modelInventory1.EquipmentId = reqModel.equpmentID;
                        //modelInventory1.ProductionRequestId = reqModel.proOrderID;
                        //modelInventory1.BatchId = reqModel.batchID;
                        modelInventory1.QuantityUomId = modelMaterialInvent.QuantityUomId;
                        modelInventory1.ContainerId = "";
                        listVent.Add(modelInventory1);

                        #endregion

                        #region 第三条更新库存信息（数据减去单包数量）（打标签）

                        decimal cha = Math.Round(Convert.ToDecimal(selectQty), 3) -
                                      Math.Round(Convert.ToDecimal(bagsSize), 3);

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (cha != 0)
                            {
                                modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(cha / 1000), 3);
                            }
                        }
                        else
                        {
                            modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(cha), 3);
                        }

                        //扣库存（更新原来的数据）
                        //  modelMaterialInvent.Quantity = Math.Round(Convert.ToDecimal(selectQty), 3) - Math.Round(Convert.ToDecimal(bagsSize), 3);// selectQty - Convert.ToDecimal(bagsSize);
                        modelMaterialInvent.ContainerId = "";
                        modelMaterialInvent.Modify(modelMaterialInvent.ID, _uIser.Name.ToString());
                        upVent.Add(modelMaterialInvent);

                        #endregion

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(userId);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = storage_location;
                        trans.NewStorageLocation = storage_location;
                        trans.OldLotId = lotID;
                        trans.NewLotId = lotID;
                        trans.OldSublotId = reqModel.subID;
                        trans.NewSublotId = subGUID1;
                        trans.OldExpirationDate = expirationDate;
                        trans.NewExpirationDate = expirationDate;

                        if (reqModel.ChangeUnit == "g")
                        {
                            if (bagsSize != 0)
                            {
                                trans.Quantity =
                                    Math.Round(Convert.ToDecimal(bagsSize / 1000),
                                        3); // Convert.ToDecimal(reqModel.inputBagWeight);
                            }
                        }
                        else
                        {
                            trans.Quantity =
                                Math.Round(Convert.ToDecimal(bagsSize),
                                    3); // Convert.ToDecimal(reqModel.inputBagWeight);
                        }

                        //     trans.Quantity = Math.Round(Convert.ToDecimal(bagsSize), 3); //Convert.ToDecimal(bagsSize);
                        trans.QuantityUomId = unitID;
                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Transfer Inventory";
                        trans.Comment = "转移";

                        //新添加的字段（订单和子批次）
                        trans.ProductionExecutionId = "";
                        trans.NewProductionExecutionId = reqModel.proOrderID;
                        trans.OldBatchId = "";
                        trans.NewBatchId = reqModel.batchID;

                        //trans.TransferGroupId
                        trans.OldEquipmentId = eqpmentID;
                        trans.NewEquipmentId = eqpmentID;
                        trans.OldContainerId = "";
                        trans.NewContainerId = reqModel.containerID;
                        //status
                        trans.OldMaterialId = dataModel.MaterialId;
                        trans.NewMaterialId = dataModel.MaterialId;
                        trans.OldLotExternalStatus = dataModel.LStatus;
                        trans.OldSublotExternalStatus = dataModel.SbStatus;
                        trans.NewLotExternalStatus = dataModel.LStatus;
                        trans.NewSublotExternalStatus = dataModel.SbStatus;
                        //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                        //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                        listTran.Add(trans);

                        #endregion
                    }

                    #endregion

                    #region 转移的袋数

                    #endregion

                    #endregion
                }


                #region 打印原有标签

                //更新后的数据 modelMaterialInvent

                #endregion

                #endregion

                #region 插入数据库

                bool subLotResult = true;
                bool inventoryResult = true;
                bool tranResult = true;
                bool upResult = true;
                if (listSubLot.Count > 0)
                {
                    subLotResult = await _materialSubLotServicesDal.Add(listSubLot) > 0;
                }

                if (listVent.Count > 0)
                {
                    inventoryResult = await _materialInventoryEntityDal.Add(listVent) > 0;
                }

                if (listTran.Count > 0)
                {
                    tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                }

                if (upVent.Count > 0)
                {
                    upResult = await _materialInventoryEntityDal.Update(upVent);
                }

                //这里更新batchID状态为12
                if (!string.IsNullOrEmpty(reqModel.batchID))
                {
                    var batchModel = await _BatchEntity.FindEntity(p => p.ID == reqModel.batchID);
                    if (batchModel != null)
                    {
                        string state = batchModel.PrepStatus;
                        if (state == "2")
                        {
                            batchModel.PrepStatus = "12"; //更新状态为备料中
                            batchModel.Modify(batchModel.ID, _uIser.Name.ToString());
                            await _BatchEntity.Update(batchModel);
                        }
                    }
                }


                if (!subLotResult || !inventoryResult || !tranResult || !upResult)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "转移失败，状态" + subLotResult + inventoryResult + tranResult + upResult;
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                result.msg = "转移成功";

                #region 打印标签不考虑是否成功（打印最小标签）

                string equpmentID = reqModel.equpmentID;

                //这里打印标签，upVent打印更新数据的标签，inven

                if (listVent.Count > 0)
                {
                    //打印对应的备料标签
                    for (int i = 0; i < listVent.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(listVent[i].ID);
                        //List<object> objs = new List<object>();
                        //objs.Add(inventViewData);

                        //这里拿打印机，默认拿第一个
                        var printIDData =
                            await _IPrintSelectViewServices
                                .GetPrinit_MoveByEqumentID(reqModel
                                    .equpmentID); //_IPrintSelectViewServices.GetSelectPrinit_Move();
                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            //这里筛选对应的打印机
                            if (!string.IsNullOrEmpty(reqModel.PrintId))
                            {
                                printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                            }

                            if (printIDData == null || printIDData.Count <= 0)
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }

                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;
                            //执行打印                                                                        
                            await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID,
                                teampClassID, inventViewData, inventViewData[0], 1);
                        }
                    }
                }

                if (upVent.Count > 0)
                {
                    //打印库存标签
                    for (int i = 0; i < upVent.Count; i++)
                    {
                        var inventViewData = await _IPrintSelectViewServices.GetInventLabel(upVent[i].ID, "");
                        List<object> objs = new List<object>();
                        objs.Add(inventViewData);

                        //这里拿打印机，默认拿第一个
                        var printIDData = await _IPrintSelectViewServices.GetSelectPrinit_Bag();

                        if (printIDData != null && printIDData.Count >= 0)
                        {
                            //执行打印
                            //     await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", objs, objs[0], 1, "PrintTemplete");
                        }
                    }
                }
                //   string

                #endregion

                return result;

                #endregion
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "转移失败，原因：" + ex.Message + ex.StackTrace;
                return result;
            }
        }


        /// <summary>
        /// 备料转移PartialBag_Merge（前端校验数量）(合并)
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="inputBagWeight">输入拆分数量</param>       
        /// <param name="ssccCode">子批次code</param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_Merge(MergeModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.subID == null || reqModel.ssccCode == null)
                {
                    result.msg = "子批次为空";
                    return result;
                }

                var inventList = await _InventorylistingViewEntitydal.FindList(p =>
                    p.SubLotId == reqModel.ssccCode || p.SlotId == reqModel.subID);
                if (inventList == null || inventList.Count <= 0)
                {
                    result.msg = "库存信息不存在";
                    return result;
                }

                //判断当选扫描的库存数据源
                var mergerList = await _InventorylistingViewEntitydal.FindEntity(p => p.SlotId == reqModel.subID);
                if (inventList == null || inventList.Count <= 0)
                {
                    result.msg = "库存信息不存在";
                    return result;
                }

                if (string.IsNullOrEmpty(mergerList.ContainerId))
                {
                    result.msg = "无法合并可用库存，请合并工单库存";
                    return result;
                }

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                string userId = _uIser.Name.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                List<MaterialInventoryEntity> listVentUp = new List<MaterialInventoryEntity>();

                #region 拆分的目标批次

                //获取拆分批次信息
                List<MaterialSubLotEntity> merGeSubLot = new List<MaterialSubLotEntity>();
                merGeSubLot = await _materialSubLotServicesDal.FindList(p => p.SubLotId == reqModel.ssccCode);
                if (merGeSubLot == null || merGeSubLot.Count == 0)
                {
                    result.msg = "该批次物料不存在";
                    return result;
                }

                //查询拆分的数据
                MaterialSubLotEntity subModel = merGeSubLot[0];
                List<MaterialInventoryEntity> subInventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == subModel.ID);
                MaterialInventoryEntity upMergeMaterialInvent = new MaterialInventoryEntity();
                if (subInventMaterialList == null || subInventMaterialList.Count == 0)
                {
                    result.msg = "该批次物料不存在";
                    return result;
                }

                upMergeMaterialInvent = subInventMaterialList[0];
                upMergeMaterialInvent.Quantity +=
                    Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                        3); //Convert.ToDecimal(reqModel.inputBagWeight);
                listVentUp.Add(upMergeMaterialInvent);

                #endregion

                //获取原子批次库存信息
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);
                MaterialInventoryEntity upModelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "该批次物料不存在";
                    return result;
                }

                upModelMaterialInvent = inventMaterialList[0];
                decimal quy = Math.Round(Convert.ToDecimal(upModelMaterialInvent.Quantity), 3) -
                              Math.Round(Convert.ToDecimal(reqModel.inputBagWeight), 3);
                //upModelMaterialInvent.Quantity - Convert.ToDecimal(reqModel.inputBagWeight);
                if (quy == 0)
                {
                    await _materialInventoryEntityDal.DeleteById(upModelMaterialInvent.ID);
                }
                else
                {
                    upModelMaterialInvent.Quantity -=
                        Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                            3); //Convert.ToDecimal(reqModel.inputBagWeight);
                    listVentUp.Add(upModelMaterialInvent);
                }

                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                //这里写转移记录

                #region MKM_B_MATERIAL_TRANSFER 转移记录

                //写入历史记录
                MaterialTransferEntity trans1 = new MaterialTransferEntity();
                trans1.Create(userId);
                //trans1.ID = Guid.NewGuid().ToString();
                trans1.OldStorageLocation = dataModel.StorageLocation;
                trans1.NewStorageLocation = dataModel.StorageLocation;
                trans1.OldLotId = dataModel.ID;
                trans1.NewLotId = dataModel.ID;
                trans1.OldSublotId = reqModel.subID;
                trans1.NewSublotId = upMergeMaterialInvent.ID;
                trans1.OldExpirationDate = dataModel.ExpirationDate;
                trans1.NewExpirationDate = dataModel.ExpirationDate;
                trans1.Quantity =
                    Math.Round(Convert.ToDecimal(reqModel.inputBagWeight),
                        3); //Convert.ToDecimal(reqModel.inputBagWeight);
                trans1.QuantityUomId = dataModel.UnitId;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans1.Type = "Transfer Inventory";
                trans1.Comment = "称量-合并";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans1.OldEquipmentId = dataModel.EquipmentId;
                trans1.NewEquipmentId = dataModel.EquipmentId;
                trans1.OldContainerId = "";
                trans1.NewContainerId = "";
                //trans1.NewContainerId = containerID;
                //status
                trans1.OldMaterialId = dataModel.MaterialId;
                trans1.NewMaterialId = dataModel.MaterialId;
                trans1.OldLotExternalStatus = dataModel.LStatus;
                trans1.OldSublotExternalStatus = dataModel.SbStatus;
                trans1.NewLotExternalStatus = dataModel.LStatus;
                trans1.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans1);

                #endregion

                _unitOfWork.BeginTran();

                //更新库存信息
                bool inventMaterialUp = await _materialInventoryEntityDal.Update(listVentUp);
                //更新转移表
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;

                //这里执行新增数据

                if (!inventMaterialUp || !tranResult)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "转移失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                return result;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "转移失败";
                return result;
            }
        }


        public async Task<MessageModel<string>> PrintMINLable(MergeModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (string.IsNullOrEmpty(reqModel.EqupmentID))
                {
                    result.msg = "请选择节点";
                    return result;
                }

                if (string.IsNullOrEmpty(reqModel.PrintId))
                {
                    result.msg = "请选择打印机";
                    return result;
                }

                string[] ids = reqModel.IDS;
                if (ids == null || ids.Length <= 0)
                {
                    result.msg = "请选择补打标签";
                    return result;
                }

                var inventDatas = await _InventorylistingViewEntitydal.Db.Queryable<InventorylistingViewEntity>()
                    .In(p => p.ID, ids).ToListAsync();
                if (inventDatas.Where(p => string.IsNullOrEmpty(p.ProductionOrderNo)).ToList().Count > 0)
                {
                    result.msg = "请选择工单库存内数据进行补打";
                    return result;
                }

                for (int i = 0; i < ids.Length; i++)
                {
                    var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(ids[i]);
                    // 这里拿打印机，（默认当前节点）
                    var printIDData =
                        await _IPrintSelectViewServices
                            .GetPrinit_MoveByEqumentID(reqModel
                                .EqupmentID); //_IPrintSelectViewServices.GetSelectPrinit_Move();
                    if (printIDData != null && printIDData.Count >= 0)
                    {
                        if (!string.IsNullOrEmpty(reqModel.PrintId))
                        {
                            printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                        }

                        if (printIDData == null || printIDData.Count <= 0)
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }

                        string teampID = printIDData[0].TemplateId;
                        string teampClassID = printIDData[0].TemplateClassId;
                        string printID = printIDData[0].ID;
                        //执行打印                                                                        
                        await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID, teampClassID,
                            inventViewData, inventViewData[0], 1);
                    }
                }

                result.success = true;
                return result;
            }
            catch (Exception ex)
            {
                result.msg = "补打失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 备料转移FullAmount（前端校验数量）整批转移(拼锅)
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_FullAmount(FullAmountModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (string.IsNullOrEmpty(reqModel.equpmentID))
                {
                    result.msg = "请确定是否选择房间，请尝试重新打开菜单页";
                    return result;
                }

                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行转移", state);
                        return result;
                    }
                }

                #endregion

                string userId = _uIser.Name.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();
                if (dataModel == null)
                {
                    result.msg = "请选中操作数据";
                    return result;
                }

                decimal inQty = dataModel.InQuantity;

                ///查询当前批次库存信息
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);
                MaterialInventoryEntity upModelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "该批次不存在";
                    return result;
                }

                if (!string.IsNullOrEmpty(inventMaterialList[0].ContainerId))
                {
                    result.msg = "当前追溯码已经绑定容器";
                    return result;
                }

                #region 限制批次

                string pID = inventMaterialList[0].ProductionRequestId;
                string mID = dataModel.MaterialId;
                string lotCode = dataModel.LBatch;
                if (!string.IsNullOrEmpty(pID) && !string.IsNullOrEmpty(mID))
                {
                    var ppList =
                        await _PPoConsumeRequirementEntitDal.FindList(p =>
                            p.ProductionOrderId == pID && p.MaterialId == mID);
                    if (ppList != null && ppList.Count > 0)
                    {
                        string mLot = ppList[0].MaterialLotNo;

                        if (!string.IsNullOrEmpty(mLot))
                        {
                            if (lotCode != mLot)
                            {
                                result.msg = "所选库存非指定批次，不允许使用!";
                                return result;
                            }
                        }
                    }
                }

                #endregion

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                if (string.IsNullOrEmpty(reqModel.containerID))
                {
                    result.msg = "请选择容器";
                    return result;
                }

                //获取容器信息
                ContainerEntity model = await _dalContainerEntity.FindEntity(reqModel.containerID);
                //判断当前容器是否是完成状态
                if (model != null)
                {
                    string conState = model.Status;
                    if (conState == "3")
                    {
                        result.msg = "当前容器已完成，请选择其他容器";
                        return result;
                    }
                }

                //
                upModelMaterialInvent = inventMaterialList[0];
                decimal inventValue = inventMaterialList[0].Quantity;

                if (inventMaterialList[0].Quantity < 0)
                {
                    result.msg = "转移数量为负数";
                    return result;
                }

                //进行绑定容器
                upModelMaterialInvent.Modify(upModelMaterialInvent.ID, _uIser.ID.ToString());
                upModelMaterialInvent.ContainerId = reqModel.containerID == null ? "" : reqModel.containerID;

                if (string.IsNullOrEmpty(upModelMaterialInvent.BatchId))
                {
                    upModelMaterialInvent.InventoryType = "Full";
                    upModelMaterialInvent.ProductionRequestId = reqModel.proOrderID;
                    upModelMaterialInvent.BatchId = reqModel.batchID;
                }

                //else
                //{
                //    upModelMaterialInvent.InventoryType = "Full";
                //}
                upModelMaterialInvent.EquipmentId = reqModel.equpmentID;

                //写入历史

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;


                #region 走业务流程

                _unitOfWork.BeginTran();


                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                //string inventGUID = Guid.NewGuid().ToString();
                //string transer1GUID = Guid.NewGuid().ToString();
                //string transer2GUID = Guid.NewGuid().ToString();


                //库存表(添加)               

                #region 写入容器记录 1

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userId);
                //hisModel1.ID = transer1GUID;
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = eqpmentID;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: in-use, Previous Status: pending";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = inventValue.ToString();
                hisModel1.QuantityUomId = unitID;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = expirationDate;

                listTranHis.Add(hisModel1);

                #endregion

                #region 写入容器记录2

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                hisModel.Create(userId);
                //hisModel.ID = transer2GUID.ToString();
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ContainerCode = model.Name;
                hisModel.Type = "Transfer";
                hisModel.EquipmentId = eqpmentID;
                hisModel.State = "4";
                hisModel.Comment = "Full Bag Transfer";
                hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                hisModel.BatchId = reqModel.batchID; //工单批次ID
                hisModel.Status = "Full";
                hisModel.Quantity = inventValue.ToString();
                hisModel.QuantityUomId = unitID;
                hisModel.SublotId = reqModel.subID;
                hisModel.LotId = lotID;
                hisModel.MaterialId = dataModel.MaterialId;
                //   hisModel
                hisModel.ContainerId = reqModel.containerID;
                hisModel.ExpirationDate = expirationDate;
                listTranHis.Add(hisModel);

                #endregion

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userId);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = reqModel.subID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(inventValue), 3); // inventValue;
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "拼锅-整袋转移";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = reqModel.equpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);

                #endregion


                //批量提交

                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                //扣库存（更新原来的数据）
                bool upMaterialInvent = await _materialInventoryEntityDal.Update(upModelMaterialInvent);


                //这里执行新增数据
                if (!hisResult || !tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();

                    result.msg = "转移失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                #region 打印标签

                if (upModelMaterialInvent != null)
                {
                    //打印对应的备料标签                   
                    var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(upModelMaterialInvent.ID);

                    #region 打印新逻辑（根据打印机名字来拿对应的模板）

                    List<PrintSelectViewEntity> printIDData = new List<PrintSelectViewEntity>();

                    //  根据打印机ID查询打印code
                    if (!string.IsNullOrEmpty(reqModel.PrintId))
                    {
                        var entityMode = await _LabelPrinterEntityDal.FindEntity(reqModel.PrintId);

                        if (entityMode != null)
                        {
                            string pCode = entityMode.Code;
                            string pName = entityMode.Description;

                            if (pCode.Contains("移动"))
                            {
                                //移动的打印
                                printIDData =
                                    await _IPrintSelectViewServices.GetPrinit_MoveBagByEqumentID(reqModel.equpmentID);
                            }
                            else
                            {
                                printIDData =
                                    await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);
                            }
                        }
                        else
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }
                    }
                    else
                    {
                        result.msg = "转移成功，请配置打印机";
                        return result;
                    }

                    #endregion

                    //这里拿打印机，默认拿第一个
                    //       var printIDData = await _IPrintSelectViewServices.GetPrinit_MoveByEqumentID(reqModel.equpmentID);//_IPrintSelectViewServices.GetSelectPrinit_Move();

                    if (printIDData != null && printIDData.Count >= 0)
                    {
                        try
                        {
                            //这里筛选对应的打印机
                            if (!string.IsNullOrEmpty(reqModel.PrintId))
                            {
                                printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                            }

                            if (printIDData == null || printIDData.Count <= 0)
                            {
                                result.msg = "转移成功，请配置打印机";
                                return result;
                            }

                            string teampID = printIDData[0].TemplateId;
                            string teampClassID = printIDData[0].TemplateClassId;
                            string printID = printIDData[0].ID;

                            //判断当前数据是否包含工单id
                            string pro = inventMaterialList[0].ProductionRequestId;

                            if (!string.IsNullOrEmpty(pro))
                            {
                            }
                            else
                            {
                                //执行打印                                                                        
                                await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID,
                                    teampClassID, inventViewData, inventViewData[0], 1);
                            }

                            // await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                        }
                        catch (Exception ex)
                        {
                            result.success = true;
                            result.msg = "转移成功，打印失败：" + ex.Message;
                            return result;
                        }
                        //执行打印
                    }
                }

                #endregion

                result.success = true;
                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();

                result.msg = "转移失败";
                return result;
            }
        }

        /// <summary>
        /// 称量备料-整袋转移()
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_NewFullAmount(FullAmountModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                #region 判断投料状态

                //查询当前工单状态
                string proID = reqModel.batchID;
                var proEntity = await _BatchEntity.FindEntity(P => P.ID == proID);
                if (proEntity != null)
                {
                    string pre_State = proEntity.PrepStatus.ToString();
                    string state = GetPre_State(pre_State);
                    if (state != "待备料" && state != "备料中" && state != "复称完成" && state != "称量完成")
                    {
                        result.msg = string.Format("当前工单状态:{0},无法进行称量备料转移", state);
                        return result;
                    }
                }

                #endregion

                if (string.IsNullOrEmpty(reqModel.equpmentID))
                {
                    result.msg = "请确定是否选择房间，请尝试重新打开菜单页";
                    return result;
                }

                string userId = _uIser.Name.ToString();
                //根据选择的子批次获取对应的数据
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.subID), a => a.SubId == reqModel.subID)
                    .ToExpression();
                BBatchDetailMaterialViewEntity dataModel = _dalBBatchDetailViewEntity.Db
                    .Queryable<BBatchDetailMaterialViewEntity>().Where(whereExpression).ToList().FirstOrDefault();

                ///查询当前批次库存信息
                List<MaterialInventoryEntity> inventMaterialList =
                    await _materialInventoryEntityDal.FindList(p => p.SublotId == reqModel.subID);
                MaterialInventoryEntity upModelMaterialInvent = new MaterialInventoryEntity();
                if (inventMaterialList == null || inventMaterialList.Count == 0)
                {
                    result.msg = "当前批次物料不存在!";
                    return result;
                }

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventMaterialList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                //
                upModelMaterialInvent = inventMaterialList[0];
                decimal inventValue = inventMaterialList[0].Quantity;

                if (inventValue < 0)
                {
                    result.msg = "转移数量为负数";
                    return result;
                }

                //进行工单和批次绑定
                upModelMaterialInvent.ProductionRequestId = reqModel.proOrderID;
                upModelMaterialInvent.BatchId = reqModel.batchID;
                upModelMaterialInvent.InventoryType = "Partial";
                upModelMaterialInvent.EquipmentId = reqModel.equpmentID;

                upModelMaterialInvent.Modify(upModelMaterialInvent.ID, _uIser.Name.ToString());

                //写入历史

                string lotID = dataModel.LotId;
                string storage_location = dataModel.StorageLocation;
                string eqpmentID = dataModel.EquipmentId;
                string unitID = dataModel.UnitId;
                DateTime expirationDate = dataModel.ExpirationDate;

                #region 走业务流程

                _unitOfWork.BeginTran();

                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                #region 写入转移历史

                //写入历史记录
                MaterialTransferEntity trans = new MaterialTransferEntity();
                trans.Create(userId);
                //trans.ID = Guid.NewGuid().ToString();
                trans.OldStorageLocation = storage_location;
                trans.NewStorageLocation = storage_location;
                trans.OldLotId = lotID;
                trans.NewLotId = lotID;
                trans.OldSublotId = reqModel.subID;
                trans.NewSublotId = reqModel.subID;
                trans.OldExpirationDate = expirationDate;
                trans.NewExpirationDate = expirationDate;
                trans.Quantity = Math.Round(Convert.ToDecimal(inventValue), 3); //inventValue;
                trans.QuantityUomId = unitID;
                //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                trans.Type = "Transfer Inventory";
                trans.Comment = "称量-整袋转移";
                //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                //trans.TransferGroupId
                trans.OldEquipmentId = eqpmentID;
                trans.NewEquipmentId = eqpmentID;
                trans.OldContainerId = "";
                trans.NewContainerId = reqModel.containerID;
                //status
                trans.OldMaterialId = dataModel.MaterialId;
                trans.NewMaterialId = dataModel.MaterialId;
                trans.OldLotExternalStatus = dataModel.LStatus;
                trans.OldSublotExternalStatus = dataModel.SbStatus;
                trans.NewLotExternalStatus = dataModel.LStatus;
                trans.NewSublotExternalStatus = dataModel.SbStatus;

                //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                listTran.Add(trans);

                #endregion


                //批量提交
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                //扣库存（更新原来的数据）
                bool upMaterialInvent = await _materialInventoryEntityDal.Update(upModelMaterialInvent);


                //这里执行新增数据
                if (!tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "转移失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;

                #region 打印标签

                if (upModelMaterialInvent != null)
                {
                    //打印对应的备料标签                   
                    var inventViewData = await _IPrintSelectViewServices.GetMinLabelData(upModelMaterialInvent.ID);
                    //List<object> objs = new List<object>();
                    //objs.Add(inventViewData);

                    //这里拿打印机，默认拿第一个
                    var printIDData =
                        await _IPrintSelectViewServices
                            .GetPrinit_MoveByEqumentID(reqModel
                                .equpmentID); //_IPrintSelectViewServices.GetSelectPrinit_Move();

                    if (printIDData != null && printIDData.Count >= 0)
                    {
                        //这里筛选对应的打印机
                        if (!string.IsNullOrEmpty(reqModel.PrintId))
                        {
                            printIDData = printIDData.Where(p => p.ID == reqModel.PrintId).ToList();
                        }

                        if (printIDData == null || printIDData.Count <= 0)
                        {
                            result.msg = "转移成功，请配置打印机";
                            return result;
                        }

                        string teampID = printIDData[0].TemplateId;
                        string teampClassID = printIDData[0].TemplateClassId;
                        string printID = printIDData[0].ID;
                        //执行打印                                                                        
                        await _IPrintSelectViewServices.PrintCodeByEquipmentIdFactory(printID, teampID, teampClassID,
                            inventViewData, inventViewData[0], 1);

                        //执行打印
                        //  await _IPrintSelectViewServices.PrintCodeByEquipmentId(printIDData[0].ID, "02308281-5121-6559-163e-0370f6000000", inventViewData, inventViewData[0], 1, "PrintBagTemplete");
                    }
                }

                #endregion

                return result;

                #endregion
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "转移失败";
                return result;
            }
        }

        /// <summary>
        /// 拼锅-移除
        /// </summary>      
        /// <param name="subID">子批次（最下面一个批次选择的ID）</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID(来自于主界面)</param>
        /// <param name="batchID">批次ID来自于主界</param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_Remove(RemoveBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.subIDs.Length <= 0)
                {
                    result.msg = "请选中要移除的数据";
                    return result;
                }

                //查询所有的子批次的状态信息
                var inventList = await _materialInventoryEntityDal.Db.Queryable<MaterialInventoryEntity>()
                    .In(p => p.SublotId, reqModel.subIDs)
                    .Where(Expressionable.Create<MaterialInventoryEntity>().ToExpression()).ToListAsync();

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                string userId = _uIser.Name.ToString();
                List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<ContainerEntity> conList = new List<ContainerEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                for (int i = 0; i < reqModel.subIDs.Length; i++)
                {
                    string subLotID = reqModel.subIDs[i];
                    if (subLotID != string.Empty)
                    {
                        #region 循环清空库存

                        List<BBatchDetailMaterialViewEntity> list =
                            await _dalBBatchDetailMaterialViewEntity.FindList(p => p.SubId == subLotID);
                        BBatchDetailMaterialViewEntity modelBBath = new BBatchDetailMaterialViewEntity();
                        //库存ID
                        string inventID = list[0].ID;
                        modelBBath = list[0];
                        List<MaterialInventoryEntity> inventMaterialList =
                            await _materialInventoryEntityDal.FindList(p => p.SublotId == subLotID);
                        if (inventMaterialList == null || inventMaterialList.Count == 0)
                        {
                            result.msg = "该批次物料不存在";
                            return result;
                        }


                        //获取库存
                        MaterialInventoryEntity inventUpModel = new MaterialInventoryEntity();
                        inventUpModel = inventMaterialList[0];
                        string contairID = inventUpModel.ContainerId;
                        inventUpModel.Modify(inventUpModel.ID, userId);
                        inventUpModel.ContainerId = "";
                        //如果等于整袋转移菜才进行清空数据
                        if (inventUpModel.InventoryType == "Full")
                        {
                            inventUpModel.BatchId = "";
                            inventUpModel.ProductionRequestId = "";
                            inventUpModel.InventoryType = "";
                        }

                        #region 这里需要进行位置改变

                        string oldEqupID = string.Empty;
                        var tranListHis = await _materialTransferEntityDal.FindList(p => p.NewSublotId == subLotID);
                        tranListHis = tranListHis.OrderByDescending(P => P.CreateDate).ToList();
                        if (tranListHis != null && tranListHis.Count > 0)
                        {
                            oldEqupID = tranListHis[0].OldEquipmentId;
                            if (!string.IsNullOrEmpty(oldEqupID))
                            {
                                inventUpModel.EquipmentId = tranListHis[0].OldEquipmentId;
                            }
                        }

                        #endregion

                        //inventUpModel.BatchId = "";
                        //inventUpModel.ProductionRequestId = "";
                        //inventUpModel.InventoryType = "";
                        upInvent.Add(inventUpModel);

                        #region 这里处理容器

                        if (i == 0)
                        {
                            #region 判断当前容器状态等于3，直接提示容器已经完成，无法移除袋子

                            var container = await _ContainerEntity.FindEntity(p => p.ID == contairID);
                            if (container.Status == "3")
                            {
                                result.msg = "容器已经完成，无法移除袋子";
                                return result;
                            }

                            #endregion

                            #region 这里写上如果当前容器状态等于3，默认给改成1

                            if (container.Status == "3")
                            {
                                container.Status = "1";
                                container.Modify(container.ID, userId);
                                conList.Add(container);
                            }

                            #endregion
                        }

                        #endregion

                        #endregion

                        #region 写三条日志

                        ContainerEntity model = await _dalContainerEntity.FindEntity(contairID);
                        model.Modify(model.ID, userId);
                        model.ProductionBatchId = null;
                        //conList.Add(model);
                        //string inventGUID = Guid.NewGuid().ToString();
                        //string transer1GUID = Guid.NewGuid().ToString();
                        //string transer2GUID = Guid.NewGuid().ToString();

                        #region 写入容器记录 1

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                        hisModel1.Create(userId);
                        //hisModel1.ID = transer1GUID;
                        //   hisModel
                        hisModel1.ContainerId = contairID;
                        hisModel1.ContainerCode = model.Name;
                        hisModel1.State = "2";
                        hisModel1.Type = "Container Inventory Remove";
                        hisModel1.EquipmentId = inventUpModel.EquipmentId;
                        //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                        //hisModel.State = containerModel.Status;
                        hisModel1.Comment = "拼锅-移除";
                        //hisModel1.ProductOrderId = proOrderID;// 工单ID
                        //hisModel1.BatchId = batchID;//工单批次ID
                        //hisModel1.MaterialId = inventModel.MaterialId;
                        //hisModel1.SublotId = inventModel.SubLotId;
                        hisModel1.Quantity = inventUpModel.Quantity.ToString();
                        hisModel1.QuantityUomId = inventUpModel.QuantityUomId;
                        //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                        //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                        //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                        // hisModel.Status 批次执行状态
                        //hisModel.ContainerCode= containerModel. 容器编号
                        //hisModel.MaterialProducedActualId 物料产出记录ID
                        //hisModel.MaterialConsumedActualId 物料消耗记录ID
                        //hisModel1.LotId = inventModel.LotId;
                        //hisModel1.ExpirationDate = modelBBath.ExpirationDate;

                        listTranHis.Add(hisModel1);

                        #endregion

                        #region 写入容器记录2

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userId);
                        //hisModel.ID = transer2GUID.ToString();
                        hisModel.ContainerId = modelBBath.ContainerId;
                        hisModel.ContainerCode = model.Name;
                        hisModel.Type = "Transfer";
                        hisModel.EquipmentId = inventUpModel.EquipmentId;
                        hisModel.State = "4";
                        hisModel.Comment = "Container Inventory Remove";
                        hisModel.ProductOrderId = reqModel.proOrderID; // 工单ID
                        hisModel.BatchId = reqModel.batchID; //工单批次ID
                        hisModel.Status = "";
                        hisModel.Quantity = inventUpModel.Quantity.ToString();
                        hisModel.QuantityUomId = inventUpModel.QuantityUomId;
                        hisModel.SublotId = inventUpModel.SublotId;
                        hisModel.LotId = inventUpModel.LotId;
                        hisModel.MaterialId = modelBBath.MaterialId;
                        //   hisModel
                        hisModel.ContainerId = contairID;
                        hisModel.ExpirationDate = modelBBath.ExpirationDate;
                        listTranHis.Add(hisModel);

                        #endregion

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(userId);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventUpModel.StorageLocation;
                        trans.NewStorageLocation = inventUpModel.StorageLocation;
                        trans.OldLotId = inventUpModel.LotId;
                        trans.NewLotId = inventUpModel.LotId;
                        trans.OldSublotId = inventUpModel.SublotId;
                        trans.NewSublotId = inventUpModel.SublotId;
                        trans.OldExpirationDate = modelBBath.ExpirationDate;
                        trans.NewExpirationDate = modelBBath.ExpirationDate;
                        trans.Quantity =
                            Math.Round(Convert.ToDecimal(inventUpModel.Quantity), 3); // inventUpModel.Quantity;
                        trans.QuantityUomId = inventUpModel.QuantityUomId;
                        //trans.ProductionExecutionId = inventoryModel.ProductionRequestId;
                        trans.Type = "Container Remove";
                        trans.Comment = "拼锅-移除";
                        //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventUpModel.EquipmentId;
                        trans.NewEquipmentId = oldEqupID;
                        trans.OldContainerId = inventUpModel.ContainerId;
                        trans.NewContainerId = "";
                        //status
                        trans.OldMaterialId = modelBBath.MaterialId;
                        trans.NewMaterialId = modelBBath.MaterialId;
                        trans.OldLotExternalStatus = modelBBath.LStatus;
                        trans.OldSublotExternalStatus = modelBBath.SbStatus;
                        trans.NewLotExternalStatus = modelBBath.LStatus;
                        trans.NewSublotExternalStatus = modelBBath.SbStatus;
                        //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                        //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                        listTran.Add(trans);

                        #endregion

                        #endregion
                    }
                }

                #region 保存

                _unitOfWork.BeginTran();
                //批量提交
                bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                //扣库存（更新原来的数据）
                bool upMaterialInvent = await _materialInventoryEntityDal.Update(upInvent);
                bool upCon = true;
                if (conList.Count > 0)
                {
                    upCon = await _dalContainerEntity.Update(conList);
                }

                //这里执行新增数据
                if (!hisResult || !tranResult || !upMaterialInvent || !upCon)
                {
                    _unitOfWork.RollbackTran();

                    result.msg = "移除袋子失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                return result;

                #endregion
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();

                result.msg = "移除袋子失败" + ex.Message;
                return result;
            }
        }


        /// <summary>
        /// 称量备料-移除袋子
        /// </summary>      
        /// <param name="subID">子批次（最下面一个批次选择的ID）</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID(来自于主界面)</param>
        /// <param name="batchID">批次ID来自于主界</param>      
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationNewTransfer_Remove(RemoveBagModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (reqModel.subIDs == null || reqModel.subIDs.Length <= 0)
                {
                    result.msg = "请选择需要移袋的数据";
                    return result;
                }

                //查询包含袋子的总数据
                var inventList = await _materialInventoryEntityDal.Db.Queryable<MaterialInventoryEntity>()
                    .In(p => p.SublotId, reqModel.subIDs)
                    .Where(Expressionable.Create<MaterialInventoryEntity>().ToExpression()).ToListAsync();
                if (inventList == null || inventList.Count <= 0)
                {
                    result.msg = "请选择需要移袋的数据";
                    return result;
                }

                //int all = inventList.Where(p => !string.IsNullOrEmpty(p.ProductionRequestId)).Count();
                //if (all >= 0)
                //{
                //    result.msg = "存在已经拼锅数据";
                //    return result;
                //}

                #region 判断当前批次和子批次数据

                //查询批次状态是否为锁定
                //查询子批次状态是否为锁定
                string msg =
                    await _inventorylistingViewServices.GetStateByInventID(inventList.GroupBy(p => p.ID)
                        .Select(p => p.Key).ToArray());

                if (!string.IsNullOrEmpty(msg))
                {
                    result.msg = msg;
                    return result;
                }

                #endregion

                string userId = _uIser.Name.ToString();
                List<MaterialInventoryEntity> upInvent = new List<MaterialInventoryEntity>();
                // List<ContainerEntity> upCon = new List<ContainerEntity>();
                List<ContainerHistoryEntity> listTranHis = new List<ContainerHistoryEntity>();
                List<MaterialTransferEntity> listTran = new List<MaterialTransferEntity>();

                for (int i = 0; i < reqModel.subIDs.Length; i++)
                {
                    string subLotID = reqModel.subIDs[i];
                    if (subLotID != string.Empty)
                    {
                        #region 循环清空库存工单ID和子批次id

                        List<BBatchDetailMaterialViewEntity> list =
                            await _dalBBatchDetailMaterialViewEntity.FindList(p => p.SubId == subLotID);
                        BBatchDetailMaterialViewEntity modelBBath = new BBatchDetailMaterialViewEntity();
                        //库存ID
                        //string inventID = list[0].ID;
                        //modelBBath = list[0];
                        List<MaterialInventoryEntity> inventMaterialList =
                            await _materialInventoryEntityDal.FindList(p => p.SublotId == subLotID);
                        if (inventMaterialList == null || inventMaterialList.Count == 0)
                        {
                            result.msg = "该批次物料不存在";
                            return result;
                        }
                        //这里获取物料信息

                        var lotData = await _materialLotEntityDal.FindEntity(p => p.ID == inventMaterialList[0].LotId);

                        //获取库存
                        MaterialInventoryEntity inventUpModel = new MaterialInventoryEntity();
                        inventUpModel = inventMaterialList[0];
                        inventUpModel.Modify(inventUpModel.ID, userId);
                        //  string contairID = inventUpModel.ContainerId==null?"":inventUpModel.ContainerId;
                        inventUpModel.ProductionRequestId = "";
                        inventUpModel.BatchId = "";
                        inventUpModel.InventoryType = "";
                        inventUpModel.ContainerId = inventUpModel.ContainerId == null ? "" : inventUpModel.ContainerId;
                        upInvent.Add(inventUpModel);

                        #endregion

                        #region 写二条日志

                        #region 写入转移历史

                        //写入历史记录
                        MaterialTransferEntity trans = new MaterialTransferEntity();
                        trans.Create(userId);
                        //trans.ID = Guid.NewGuid().ToString();
                        trans.OldStorageLocation = inventUpModel.StorageLocation;
                        trans.NewStorageLocation = inventUpModel.StorageLocation;
                        trans.OldLotId = inventUpModel.LotId;
                        trans.NewLotId = inventUpModel.LotId;
                        trans.OldSublotId = inventUpModel.SublotId;
                        trans.NewSublotId = inventUpModel.SublotId;
                        trans.OldExpirationDate = modelBBath.ExpirationDate;
                        trans.NewExpirationDate = modelBBath.ExpirationDate;
                        trans.Quantity =
                            Math.Round(Convert.ToDecimal(inventUpModel.Quantity), 3); //inventUpModel.Quantity;
                        trans.QuantityUomId = inventUpModel.QuantityUomId;
                        trans.ProductionExecutionId = inventMaterialList[0].ProductionRequestId;
                        trans.Type = "Batch Pallet Remove";
                        trans.Comment = "称量-移除";
                        trans.OldBatchId = inventMaterialList[0].BatchId;

                        //trans.NewEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.OldEquipmentRequirementId = inventoryModel.EquipmentRequirementId;
                        //trans.TransferGroupId
                        trans.OldEquipmentId = inventUpModel.EquipmentId;
                        trans.NewEquipmentId = inventUpModel.EquipmentId;
                        trans.OldContainerId = inventMaterialList[0].ContainerId;
                        trans.NewContainerId = "";

                        //status
                        trans.OldMaterialId = modelBBath.MaterialId;
                        trans.NewMaterialId = modelBBath.MaterialId;
                        trans.OldLotExternalStatus = modelBBath.LStatus;
                        trans.OldSublotExternalStatus = modelBBath.SbStatus;
                        trans.NewLotExternalStatus = modelBBath.LStatus;
                        trans.NewSublotExternalStatus = modelBBath.SbStatus;
                        //trans.PhysicalQuantity = inventoryModel.MaxVolume; //物理数量
                        //trans.TareQuantity = inventoryModel.TareWeight.Value;  //皮数量

                        trans.NewMaterialId = lotData == null ? "" : lotData.MaterialId;
                        trans.OldMaterialId = lotData == null ? "" : lotData.MaterialId;

                        listTran.Add(trans);

                        #endregion

                        #endregion
                    }
                }

                #region 保存

                _unitOfWork.BeginTran();
                //批量提交
                // bool hisResult = await _ContainerHistoryEntityDal.Add(listTranHis) > 0;
                bool tranResult = await _materialTransferEntityDal.Add(listTran) > 0;
                //扣库存（更新原来的数据）
                bool upMaterialInvent = await _materialInventoryEntityDal.Update(upInvent);

                //更新容器
                //这里执行新增数据
                if (!tranResult || !upMaterialInvent)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "移除袋子失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;
                return result;

                #endregion
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "移除袋子失败" + ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 获取实际称量值
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<decimal> DateActurelNum(CompletePalletModel reqModel)
        {
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                .ToExpression();
            var data = await _dal.Db.Queryable<BBatchDetailMaterialViewEntity>().In(P => P.ID, reqModel.containerIDs)
                .Where(whereExpression).ToListAsync();
            decimal acturelNum = data.Sum(p => p.InQuantity);
            return acturelNum;
        }

        /// <summary>
        /// 容器完成
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_CompletePallet(CompletePalletModel reqModel)
        {
            var result1 = new MessageModel<string>();
            result1.success = false;
            try
            {
                //判断当前容器状态是否为完成
                ContainerEntity model = await _dalContainerEntity.QueryById(reqModel.containerID);
                if (model.Status == "3")
                {
                    result1.msg = "当前托盘已完成拼锅，请重新选择托盘";
                    return result1;
                }
                //reqModel.containerIDs;

                string userId = _uIser.Name.ToString();
                List<BBatchDetailMaterialViewEntity> result = new List<BBatchDetailMaterialViewEntity>();
                RefAsync<int> dataCount = 0;
                var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    .ToExpression();
                var data = await _dal.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .In(P => P.ContainerId, reqModel.containerIDs)
                    .Where(whereExpression).ToListAsync();
                decimal acturelNum = data.Sum(p => p.InQuantity);
                decimal containerNum = data.Where(p => p.ContainerId == reqModel.containerID).Sum(p => p.InQuantity);

                if (reqModel.TagWeight == null || reqModel.TagWeight == "")
                {
                    result1.msg = "目标值不能为空";
                    return result1;
                }
                // if (acturelNum >= Convert.ToDecimal(reqModel.TagWeight))
                // {
                //获取容器状态(更新容器状态) 

                string oldState = model.Status;
                model.Status = "3";
                _unitOfWork.BeginTran();

                #region 判断当前批次和订单号下的拼锅数据是否打勾和称量备料数据是否存在对应的容器ID (容器还要判断是完成状态)

                //订单ID
                string proID = model.ProductionRequestId;
                string ppmBatch = model.ProductionBatchId;

                #region 处理整包逻辑

                //var proOder = await _bBdetailIiViewEntityDal.FindList(p => p.BatchId == ppmBatch && p.ProductionOrderId == proID);
                //int totalPro = proOder.Count;
                //int finishPro = proOder.Where(p => p.CompleteStates == "OK").Count();

                #endregion

                #region 处理部分包逻辑

                //批次ID(这里直接根据库存判断)
                var inventByBatch = await _materialInventoryEntityDal.FindList(p => p.BatchId == ppmBatch);
                int totalBatch = inventByBatch.Count;
                int finishBatch = inventByBatch.Where(p => p.ContainerId != null && p.ContainerId != "").Count();

                #endregion


                //这里查询容器的状态
                var conList = inventByBatch.Where(p => p.ContainerId != null && p.ContainerId != "");
                string[] conIDArray = conList.GroupBy(p => new { p.ContainerId }).Select(i => i.Key.ContainerId)
                    .ToArray();

                //查询所有容器集合
                var conListData = await _dalContainerEntity.Db.Queryable<ContainerEntity>().In(p => p.ID, conIDArray)
                    .ToListAsync();
                int conCount = conListData.Count;
                int conFinishCount = conListData.Where(p => p.Status == "3").Count() + 1;

                #region 处理完成托盘逻辑

                //  var proData = await GetByProOrderID(reqModel.ProID);

                var proData = await _BatchEntity.FindList(P => P.ProductionOrderId == reqModel.ProID);

                String batchID = String.Empty;
                int isFinish = 0;
                int noPG = 0;
                int count = proData.Count;

                var batchDatas = await _dalBBatchDetailIiViewEntity.Db
                    .Queryable<BBatchDetailIiViewEntity>() //.In(p => p.MaterialId, mIDs)
                    .Where(a => a.BatchId == reqModel.BatchID).ToListAsync();

                //查询订单下所有的批信息
                for (int i = 0; i < proData.Count; i++)
                {
                    batchID = proData[i].ID;
                    int totalB = batchDatas.Count;
                    int finishB = batchDatas.Where(p => p.CompleteStates == "OK" && p.IsPg == "0").Count();
                    if (totalB == finishB)
                    {
                        isFinish = isFinish + 1;
                    }

                    int isPG = batchDatas.Where(P => P.IsPg == "1").Count();
                    if (isPG > 0)
                    {
                        noPG = noPG + 1;
                    }
                }

                #endregion

                var batchData = await _dal.FindEntity(p => p.ID == reqModel.ProID);
                if (batchData == null)
                {
                    result1.msg = "未找到订单信息";
                    return result1;
                }

                if (isFinish != count)
                {
                    if (isFinish + noPG != count)
                    {
                        result1.msg = "请先完成订单所有批次";
                        return result1;
                    }
                }


                //更新工单批次状态为拼锅完成（所有的工单批次）
                //var BatchEntity = await _BatchEntity.FindEntity(x => x.ID == ppmBatch);
                //if (BatchEntity == null)
                //{
                //    result1.msg = "未找到订单批次信息";
                //    return result1;
                //}
                //BatchEntity.PrepStatus = "3";//拼锅完成
                //BatchEntity.Modify(BatchEntity.ID, _uIser.Name.ToString());
                //bool Batch = await _BatchEntity.Update(BatchEntity);

                //根据工单批量修改PPM表为3（拼锅完成）

                //这里需要从工单关联对应的批次信息一起完成
                List<BatchEntity> listUpBatch = new List<BatchEntity>();

                var BatchList = await _BatchEntity.FindList(x =>
                    x.ProductionOrderId == reqModel.ProID && x.ID == reqModel.BatchID);
                for (int x = 0; x < BatchList.Count; x++)
                {
                    BatchList[x].PrepStatus = "3"; //拼锅完成
                    BatchList[x].Modify(BatchList[x].ID, _uIser.Name.ToString());
                    listUpBatch.Add(BatchList[x]);
                }

                if (listUpBatch == null || listUpBatch.Count <= 0)
                {
                    result1.msg = "未找到订单批次信息";
                    return result1;
                }

                bool Batch = await _BatchEntity.Update(listUpBatch);
                if (Batch == false)
                {
                    result1.msg = "更新PPM批次失败";
                    return result1;
                }
                ////满足才进行批次拼锅完成
                //if (totalBatch == finishBatch && totalPro == finishPro && conCount == conFinishCount)
                //{
                //    //更新工单批次状态为拼锅完成
                //    var BatchEntity = await _BatchEntity.FindEntity(x => x.ID == ppmBatch);
                //    if (BatchEntity == null)
                //    {
                //        result1.msg = "未找到订单批次信息";
                //        return result1;
                //    }
                //    BatchEntity.PrepStatus = "3";//拼锅完成
                //    BatchEntity.Modify(BatchEntity.ID, _uIser.Name.ToString());
                //    bool Batch = await _BatchEntity.Update(BatchEntity);
                //}
                //else
                //{
                //    if (totalBatch != finishBatch)
                //    {
                //        result1.msg = "部分包未配置完成，请检查";
                //    }
                //    if (totalPro == finishPro)
                //    {
                //        result1.msg = "整包未配置完成，请检查";
                //    }
                //    else
                //    {
                //        result1.msg = "完成失败，容器存在异常";
                //    }
                //    return result1;
                //}

                #endregion

                #region 这里需要查询最新的时间信息

                //List<ContainerHistoryEntity> listHis = new List<ContainerHistoryEntity>();
                //listHis = await _ContainerHistoryEntityDal.FindList(p => p.ContainerId == reqModel.containerID && p.ExpirationDate != null);
                //ContainerHistoryEntity modelCHis = listHis.OrderByDescending(p => p.CreateDate).FirstOrDefault();
                //DateTime exTime = DateTime.Now;
                //if (modelCHis != null)
                //{
                //    exTime = modelCHis.ExpirationDate.Value;
                //}

                #endregion


                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userId);
                //hisModel1.ID = Guid.NewGuid().ToString();
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = model.Status;
                hisModel1.Type = "Complete";
                hisModel1.EquipmentId = model.EquipmentId;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: complete, Previous Status: in-use";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = containerNum.ToString();
                hisModel1.QuantityUomId = reqModel.uomId;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                //hisModel1.ExpirationDate = exTime;


                bool hisResult = await _dalContainerEntity.Update(model);
                bool tranResult = await _ContainerHistoryEntityDal.Add(hisModel1) > 0;
                //这里执行新增数据
                if (!hisResult || !tranResult)
                {
                    _unitOfWork.RollbackTran();

                    result1.msg = "操作失败";
                    return result1;
                }
                // _unitOfWork.RollbackTran();

                _unitOfWork.CommitTran();
                result1.success = true;
                return result1;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();

                result1.msg = "操作失败,原因：" + ex.Message + ex.StackTrace;
                return result1;
            }
        }


        /// <summary>
        /// 容器打开
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_OpenPallet(OpenPalletModel reqModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                string userId = _uIser.Name.ToString();
                //获取容器状态(更新容器状态)
                ContainerEntity model = await _dalContainerEntity.QueryById(reqModel.containerID);

                //判断当前容器状态
                if (model.Status != "3")
                {
                    result.msg = "当前状态无需打开托盘";
                    return result;
                }

                string oldState = model.Status;
                model.Status = "1";

                //var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>().
                //    AndIF(!string.IsNullOrEmpty(reqModel.containerID), a => a.ContainerId == reqModel.containerID)
                //                 .ToExpression();

                //List<BBatchDetailIiViewEntity> listBBatch = new List<BBatchDetailIiViewEntity>();
                //var data = await _dal.Db.Queryable<BBatchDetailMaterialViewEntity>().In(P => P.ContainerId, reqModel.containerIDs)
                //  .Where(p => p.ID != "").ToListAsync();
                //List<BBatchDetailMaterialViewEntity> containerNum = new List<BBatchDetailMaterialViewEntity>();
                //    data.Where(p => p.ContainerId == reqModel.containerID).Sum(p => p.InQuantity);
                //var uomIdModel = data.Where(p => p.ContainerId == reqModel.containerID).FirstOrDefault();
                //
                List<BBatchDetailMaterialViewEntity> containerNum =
                    await _dalBBatchDetailMaterialViewEntity.FindList(p => p.ContainerId == reqModel.containerID);
                BBatchDetailMaterialViewEntity uomIdModel = new BBatchDetailMaterialViewEntity();
                uomIdModel = containerNum[0];

                #region 这里需要查询最新的时间信息

                List<ContainerHistoryEntity> listHis = new List<ContainerHistoryEntity>();
                listHis = await _ContainerHistoryEntityDal.FindList(p => p.ContainerId == reqModel.containerID);
                ContainerHistoryEntity modelCHis = listHis.OrderByDescending(p => p.CreateDate).FirstOrDefault();
                DateTime exTime = DateTime.Now;
                if (modelCHis != null)
                {
                    exTime = modelCHis.CreateDate;
                }

                #endregion

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.Create(userId);
                //hisModel1.ID = Guid.NewGuid().ToString();
                //   hisModel
                hisModel1.ContainerId = reqModel.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = model.Status;
                hisModel1.Type = "Change Status";
                hisModel1.EquipmentId = model.EquipmentId;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: inactive, Previous Status: in-use";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = uomIdModel.InQuantity.ToString();
                hisModel1.QuantityUomId = uomIdModel.UnitId;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                hisModel1.ExpirationDate = exTime;


                var BatchEntity = await _BatchEntity.FindEntity(x => x.ID == model.ProductionBatchId);
                if (BatchEntity == null)
                {
                    result.msg = "未找到订单批次信息";
                    return result;
                }

                _unitOfWork.BeginTran();

                BatchEntity.PrepStatus = "10"; //复称完成(避免批次状态重复)
                BatchEntity.Modify(BatchEntity.ID, _uIser.Name.ToString());
                bool Batch = await _BatchEntity.Update(BatchEntity);

                bool hisResult = await _dalContainerEntity.Update(model);
                bool tranResult = await _ContainerHistoryEntityDal.Add(hisModel1) > 0;

                //这里执行新增数据
                if (!hisResult || !tranResult || !Batch)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "打开容器操作失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                result.success = true;
                result.msg = "打开成功";
                return result;
            }
            catch (Exception ex)
            {
                _unitOfWork.RollbackTran();
                result.msg = "打开容器操作失败" + ex.Message;
                return result;
            }
        }


        /// <summary>
        /// 删除容器
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> MPreparationTransfer_DeletePallet(DeletePalletModel models)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                var cList = await _materialInventoryEntityDal.FindList(p => p.ContainerId == models.containerID);

                if (cList != null && cList.Count > 0)
                {
                    result.msg = "当前容器已经绑定工单批次信息";
                    return result;
                }

                ////判断当前容器是否有且只有一条容器记录
                //if (_ContainerHistoryEntityDal.FindList(p => p.ContainerId == models.containerID).Result.Count() > 1)
                //{
                //    result.msg = "当前容器已经绑定";
                //    return result;
                //}
                //获取容器状态(更新容器状态)
                ContainerEntity model = await _dalContainerEntity.QueryById(models.containerID);
                string oldState = model.Status;
                model.Status = "1";

                //写入容器记录表(Add)
                ContainerHistoryEntity hisModel1 = new ContainerHistoryEntity();
                hisModel1.ID = Guid.NewGuid().ToString();
                //   hisModel
                hisModel1.ContainerId = models.containerID;
                hisModel1.ContainerCode = model.Name;
                hisModel1.State = "4";
                hisModel1.Type = "Clean";
                hisModel1.EquipmentId = model.EquipmentId;
                //   hisModel1.EquipmentRequirementId = inventModel.EquipmentRequirementId;
                //hisModel.State = containerModel.Status;
                hisModel1.Comment = "New Status: Delete, Previous Status: in-use";
                //hisModel1.ProductOrderId = proOrderID;// 工单ID
                //hisModel1.BatchId = batchID;//工单批次ID
                //hisModel1.MaterialId = inventModel.MaterialId;
                //hisModel1.SublotId = inventModel.SubLotId;
                hisModel1.Quantity = models.actualWeight == null ? "0" : models.actualWeight.ToString();
                hisModel1.QuantityUomId = models.uomId == null ? "" : models.uomId;
                //hisModel.BATCH_CONSUMED_REQUIREMENT_ID 批次用量需求ID
                //hisModel.CONSUMED_REQUIREMENT_ID 工单用量需求ID
                //hisModel.PRODUCTION_EXECUTION_ID 工单执行ID
                // hisModel.Status 批次执行状态
                //hisModel.ContainerCode= containerModel. 容器编号
                //hisModel.MaterialProducedActualId 物料产出记录ID
                //hisModel.MaterialConsumedActualId 物料消耗记录ID
                //hisModel1.LotId = inventModel.LotId;
                // hisModel1.ExpirationDate = exTime;

                bool deleteResult = await _dalContainerEntity.DeleteById(models.containerID);
                hisModel1.Modify(hisModel1.ID, _uIser.Name.ToString());
                bool hisResult = await _ContainerHistoryEntityDal.Add(hisModel1) > 0;

                //这里执行新增数据
                if (!deleteResult || !hisResult)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "删除失败";
                    return result;
                }

                _unitOfWork.CommitTran();

                result.success = true;
                return result;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "删除失败";
                return result;
            }
        }

        #region 按照批次备料 _bBatchDetailbymaterialViewEntitydal

        /// <summary>
        /// 备料NewMaterial跳转第一界面根据proOrderID查询批次(支持多订单)
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<List<BBatchDetailbymaterialViewEntity>> GetByProOrderID_NewMaterial(string[] proOrderId)
        {
            List<BBatchDetailbymaterialViewEntity> result = new List<BBatchDetailbymaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailbymaterialViewEntity>()
                .And(a => a.ProductionOrderId.IsIn(proOrderId))
                .ToExpression();
            var data = await _bBatchDetailbymaterialViewEntitydal.Db.Queryable<BBatchDetailbymaterialViewEntity>()
                .Where(whereExpression).OrderBy(p => p.ProductionOrderId).ToListAsync();
            return data;
        }

        /// <summary>
        /// 根据批次ID获取对应的物料信息
        /// </summary>
        /// <param name="materialID">物料ID</param>
        /// <returns></returns>
        public async Task<List<BBatchDetailIiViewEntity>> GetList_NewByMaterialID(string materialID)
        {
            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailIiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(materialID), a => a.MaterialId == materialID)
                .ToExpression();
            var data = await _dalBBatchDetailIiViewEntity.Db.Queryable<BBatchDetailIiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }


        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<PageModel<MPreparationViewEntity>>
            GetMPreparationII(PreparationIIModel model) //,string EquipmentID)
        {
            //筛选物料
            //    List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);

            #region 查询

            var whereExpression = Expressionable.Create<BatchConsumeRequirementEntity,
                    PoConsumeRequirementEntity, ProductionOrderEntity>()
                .And((a, b, c) => model.proOrderId.Contains(c.ID))
                .ToExpression();

            var data = await _dal.QueryMuch<BatchConsumeRequirementEntity,
                PoConsumeRequirementEntity, ProductionOrderEntity, MPreparationViewEntity>(
                (a, b, c) => new object[]
                {
                    JoinType.Inner, a.PoConsumeRequirementId == b.ID,
                    JoinType.Inner, c.ID == b.ProductionOrderId
                },
                (a, b, c) => new MPreparationViewEntity
                {
                    MId = b.MaterialId,
                    InQuantity = b.WeighingQty,
                    UnitId = b.UnitId,
                    MQuantityunit = a.ChangeUnit
                },
                whereExpression
            );
            //string[] mLists = mpList.GroupBy(p => p.MaterialID).Select(p => p.Key).ToArray();
            //data = data.Where(p => mLists.Contains(p.MId)).ToList();

            var gData1 = data.GroupBy(p => new { p.MId, p.MQuantityunit });
            var gData = gData1.GroupBy(p => p.Key.MId).Select(k => k.Key).ToArray();
            var mList = await _MaterialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, gData)
                .ToListAsync();
            var listInvent = await _dalInventKcViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId && gData.Contains(p.MaterialId)).ToListAsync();
            List<MPreparationViewEntity> MPreparationModel = new List<MPreparationViewEntity>();

            foreach (var item in gData1)
            {
                MPreparationViewEntity obj = new MPreparationViewEntity();
                var qty = item.Sum(p => p.InQuantity);
                obj.QSum = qty == null ? 0 : Convert.ToDecimal(Math.Round(qty.Value, 4));
                if (item.Key.MQuantityunit == "g" || item.Key.MQuantityunit == "G")
                {
                    obj.QSum = obj.QSum * 1000;
                }

                obj.MId = item.Key.MId;
                obj.MName = mList.Where(p => p.ID == item.Key.MId).FirstOrDefault().NAME;
                obj.MCode = mList.Where(p => p.ID == item.Key.MId).FirstOrDefault().Code;
                obj.InQuantity = listInvent.Where(P => P.MaterialId == item.Key.MId).Sum(p => p.Quantity);
                obj.UnitId = listInvent.Where(P => P.MaterialId == item.Key.MId).FirstOrDefault()?.MaterialUnit1;
                obj.MQuantityunit = obj.UnitId;
                obj.MQuantityunit = item.Key.MQuantityunit;
                obj.ID = obj.MId;
                MPreparationModel.Add(obj);
            }

            //筛选可以包含复称属性物料
            var resultMcode = MPreparationModel.GroupBy(p => p.MId).Select(p => p.Key).ToArray();
            var result = await _materialPropertyDal.Db.Queryable<MMaterialPropertyViewEntity>()
                .Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1")
                .In(p => p.MaterialId, resultMcode).ToListAsync();
            var mSelect = result.GroupBy(p => p.MaterialId).Select(p => p.Key).ToArray();


            MPreparationModel = MPreparationModel.Where(p => mSelect.Contains(p.MId)).ToList();
            PageModel<MPreparationViewEntity> results1 = new PageModel<MPreparationViewEntity>();
            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = MPreparationModel.OrderBy(p => p.CreateDate).Skip(startIndex).Take(model.pageSize).ToList();
            results1.dataCount = MPreparationModel.Count;
            results1.data = rDat;
            return results1;

            #endregion
        }

        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<PageModel<MPreparationViewEntity>>
            GetMPreparationIIOLD(PreparationIIModel model) //,string EquipmentID)
        {
            var data = await _MPreparationViewEntity.Db.Queryable<MPreparationViewEntity>()
                .In(p => p.Pro_Id, model.proOrderId) //.In(p => p.MId, mIDs)
                .ToListAsync();
            data = data.Where(A => A.EquipmentId == model.EquipmentId).ToList();

            PageModel<BBatchDetailMaterialViewEntity> results = new PageModel<BBatchDetailMaterialViewEntity>();

            List<MPreparationViewEntity> resultList = (from a in data
                group a by new
                {
                    //a.EquipmentId,
                    // a.Pro_Id,
                    a.MCode,
                    a.MName,
                    //    a.SegmentCode,
                    // a.SegmentName,
                    // a.EquipmentName,
                    a.UnitId,
                    //a.InQuantity,
                    //   a.UnitId,
                    a.ID
                }
                into g
                let x = g.Where(p => p.CompleteStates == "").Count() > 0 ? "" : "OK"
                select new MPreparationViewEntity
                {
                    //EquipmentId = g.Key.EquipmentId,
                    Pro_Id = string.Join(",", g.Select(d => d.Pro_Id)),
                    MCode = g.Key.MCode,
                    MName = g.Key.MName,
                    SegmentCode =
                        string.Join(",",
                            g.Select(d =>
                                d.SegmentCode)), //+"-" +string.Join(",", g.Select(d => d.SegmentName)),//g.Key.SegmentCode,
                    // SegmentName  = g.Key.SegmentName,
                    EquipmentName = string.Join(",", g.Select(d => d.EquipmentName)),
                    QSum = g.Sum(p => p.QSum.Value),
                    CompleteStates = x,
                    MQuantityunit = g.Key.UnitId,
                    //InQuantity = g.Key.InQuantity,
                    UnitId = g.Key.UnitId,
                    ID = g.Key.ID
                }).ToList();


            string[] str = resultList.GroupBy(p => p.ID).Select(p => p.Key).ToArray();
            var listInvent = await _dalInventKcViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId).In(p => p.MaterialId, str).ToListAsync();
            DateTime t4 = DateTime.Now;
            string mCode = string.Empty;
            List<InvnetqtyKyViewEntity> searchData = new List<InvnetqtyKyViewEntity>();
            for (int i = 0; i < resultList.Count; i++)
            {
                mCode = resultList[i].MCode;
                searchData = listInvent.Where(p => p.MaterialCode == mCode).ToList();
                if (searchData == null || searchData.Count == 0)
                {
                    resultList[i].InQuantity = 0;
                }
                else
                {
                    decimal total = searchData.Sum(p => p.Quantity);
                    //resultList[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
                    resultList[i].InQuantity = total;
                }

                #region 去重复操作，首先

                resultList[i].EquipmentName = RemoveDuplicate(resultList[i].EquipmentName);
                resultList[i].SegmentCode = RemoveDuplicatePro(resultList[i].SegmentCode);

                #endregion
            }

            PageModel<MPreparationViewEntity> results1 = new PageModel<MPreparationViewEntity>();

            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = resultList.OrderBy(p => p.CreateDate).Skip(startIndex).Take(model.pageSize).ToList();
            results1.dataCount = resultList.Count;
            results1.data = rDat;
            return results1;
        }

        #endregion


        /// <summary>
        /// 获取登录名
        /// </summary>
        /// <param name="loginName"></param>
        /// <returns></returns>
        public async Task<string> GetUserName(string loginName)
        {
            var userInfo = await _dalUserinfoEntity.FindList(p => p.LoginName == loginName);
            if (userInfo == null)
            {
                return loginName;
            }
            else
            {
                return userInfo.FirstOrDefault().UserName;
            }
        }


        /// <summary>
        /// 执行打印
        /// </summary>
        /// <param name="plateAddModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ReprintSavePG(MPPallentAddModel plateAddModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                if (plateAddModel.TS == "0")
                {
                    result.msg = "请输入托数";
                    return result;
                }

                if (string.IsNullOrEmpty(plateAddModel.Totalts))
                {
                    result.msg = "请输入总托数";
                    return result;
                }

                if (string.IsNullOrEmpty(plateAddModel.PrintID))
                {
                    result.msg = "请选择打印机";
                    return result;
                }

                #region 处理工单信息

                string[] ids = plateAddModel.Pids;
                if (ids == null && ids.Length <= 0)
                {
                    result.msg = "请选择工单";
                    return result;
                }

                //工单数据(所有工单数据)
                var proDataList = await _dal.Db.Queryable<MaterialPreparationViewEntity>().In(p => p.ID, ids)
                    .ToListAsync();
                //获取需要备料的批次信息
                var batchData = await _BatchEntity.Db.Queryable<BatchEntity>()
                    .In(p => p.ProductionOrderId, plateAddModel.Pids).OrderBy(p => p.ProductionOrderId).ToListAsync();
                //判断容器是否包含批次信息
                var bIDS = batchData.GroupBy(P => P.ID).Select(P => P.Key).ToArray();
                var conList = await _dalContainerEntity.Db.Queryable<ContainerEntity>()
                    .In(P => P.ProductionBatchId, bIDS).ToListAsync();
                if (conList != null && conList.Count > 0)
                {
                    //所有工单
                    var pList = await _ProductionOrderEntitDal.Db.Queryable<ProductionOrderEntity>().In(p => p.ID, ids)
                        .ToListAsync();
                    var pLis = conList.GroupBy(p => p.ProductionRequestId).Select(p => p.Key).ToArray();
                    var r = pList.Where(p => pLis.Contains(p.ID)).ToList();
                    string msg = string.Empty;
                    for (int i = 0; i < r.Count; i++)
                    {
                        msg += "工单号:" + r[i].ProductionOrderNo;
                    }

                    result.msg = msg + "已存在托盘信息";
                    return result;
                }

                List<ContainerEntity> containerList = new List<ContainerEntity>();
                List<ContainerHistoryEntity> containerHisList = new List<ContainerHistoryEntity>();

                var unitModel = await _UnitmanageEntitydal.FindEntity(p => p.Name == "KG");

                int Name = 0;

                Random rand = new Random();

                for (int i = 0; i < ids.Length; i++)
                {
                    //筛选list
                    string proID = ids[i];
                    var list = batchData.Where(P => P.ProductionOrderId == proID).ToList();

                    for (int j = 0; j < list.Count; j++)
                    {
                        Name++;
                        string bID = list[j].ID;

                        #region 创建托盘信息

                        //去DFM_M_BASE_UNIQUE_NUMBER中找到type=BATCH_PALLET的条目，获取NEXT_CODE
                        BaseUniqueNumberEntity model =
                            await _dalUniqueNumberEntity.FindEntity(p => p.Type == "BATCH_PALLET");
                        int value = rand.Next(100, 999);
                        string nextContarnerCode = DateTime.Now.ToString("yyyyMMddHHmmss") + value;

                        //string name = Name.ToString();
                        //string dateTime = DateTime.Now.ToString("yyyyMMddHHmmss");

                        //string nextContarnerCode = dateTime + name;
                        if (model != null)
                        {
                            nextContarnerCode = model.NextCode;
                        }

                        //这里查询是否存在重复数据
                        var cLists = await _dalContainerEntity.FindList(p => p.Name == nextContarnerCode);
                        if (cLists != null && cLists.Count > 0)
                        {
                            Random rand1 = new Random();
                            int value1 = rand1.Next(1, 9);
                            nextContarnerCode = DateTime.Now.ToString("yyyyMMddHHmmss") + value1.ToString();
                        }

                        #region 新增容器

                        string userID = _uIser.Name.ToString();
                        string guid = string.Empty;
                        ContainerEntity cModel = new ContainerEntity();
                        //新增5字段
                        cModel.Create(userID);
                        //获取id
                        guid = cModel.ID;
                        //容器名称
                        cModel.Name = nextContarnerCode;
                        cModel.Comment = "Batch Pallet created";
                        cModel.Status = "1";
                        //class：数据字典中的containerclass=1
                        cModel.Class = "BatchPallet";
                        cModel.TareWeight = 0;
                        //重量单位
                        cModel.WeightUomId = unitModel.ID; //"02408141-0070-5701-163e-0370f6000000"; //默认kg  
                        //批次
                        cModel.ProductionBatchId = bID;
                        //工单
                        cModel.ProductionRequestId = proID;
                        //设备
                        cModel.EquipmentId = plateAddModel.EquipmentId;
                        containerList.Add(cModel);

                        #endregion

                        #region 写入容器记录表

                        //写入容器记录表(Add)
                        ContainerHistoryEntity hisModel = new ContainerHistoryEntity();
                        hisModel.Create(userID);
                        hisModel.ContainerId = guid;
                        hisModel.ContainerCode = nextContarnerCode;
                        hisModel.Type = "Created";
                        hisModel.EquipmentId = plateAddModel.EquipmentId;
                        hisModel.EquipmentRequirementId = bID;
                        hisModel.State = "1";
                        hisModel.Comment = "Batch Pallet created";
                        hisModel.ProductOrderId = proID; // 工单ID
                        hisModel.BatchId = bID; //工单批次ID  
                        containerHisList.Add(hisModel);

                        #endregion

                        #endregion
                    }
                }

                #endregion

                _unitOfWork.BeginTran();


                //这里判断当前编码是否重复，
                //判断容器编码是否存在
                string[] codeS = containerList.GroupBy(P => P.Name).Select(P => P.Key).ToArray();
                var cList = await _dalContainerEntity.Db.Queryable<ContainerEntity>().In(P => P.Name, codeS)
                    .ToListAsync();
                if (cList.Count > 0)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建托盘失败，存在重复的容器";
                    return result;
                }

                if (codeS != null)
                {
                    if (codeS.Length != containerList.Count)
                    {
                        _unitOfWork.RollbackTran();
                        result.msg = "创建托盘失败，存在重复的容器";
                        return result;
                    }
                }

                bool rqResult = await _ContainerHistoryEntityDal.Add(containerHisList) > 0;
                bool insertContainer = await _dalContainerEntity.Add(containerList) > 0;
                if (!insertContainer || !rqResult)
                {
                    _unitOfWork.RollbackTran();
                    result.msg = "创建托盘失败";
                    return result;
                }

                _unitOfWork.CommitTran();
                result.success = true;

                #region 打印

                for (int i = 0; i < containerList.Count; i++)
                {
                    //执行打印
                    //查询当前工单是否存在
                    string pID = containerList[i].ProductionRequestId;
                    string conCode = containerList[i].Name;
                    string batchID = containerList[i].ProductionBatchId;
                    string conName = containerList[i].Name;
                    var proData = proDataList.Where(p => p.ID == pID).FirstOrDefault();
                    var batch = batchData.Where(p => p.ID == batchID).FirstOrDefault();
                    string batchNumber = batch.Number;
                    //缸序
                    string gx = proData.Sequence.ToString() + "/" + proData.Sequencetotal.ToString();
                    //缸重
                    decimal targetQuantity = batch.TargetQuantity.Value;
                    //(1/2) 当前批次和批次总量
                    string pc = "(" + batchNumber + "/";
                    var countB = await _BatchEntity.FindList(p => p.ProductionOrderId == pID);
                    pc += countB.Count + ")";
                    string factoryNane = "";
                    string proNo = proData.ProOrder;
                    string pfName = proData.MaterinalName;
                    string line = proData.LineCode;
                    //配方
                    string pf = proData.FormulaNo;
                    //板数等于 总托数据/托数
                    int dividend = Convert.ToInt32(plateAddModel.Totalts.Trim()); // 被除数
                    int divisor = 0; // Convert.ToInt32(plateAddModel.TS.Trim());   // 除数
                    int remainder = 0;
                    int bsNumber = 0;
                    if (divisor != 0)
                    {
                        remainder = dividend % divisor; // 计算余数
                        bsNumber = dividend / divisor; // 计算除法结果
                    }

                    // 如果余数不为0，则加1
                    if (remainder != 0)
                    {
                        bsNumber++;
                    }

                    string blDate = proData.ModifyDate.ToString("yyyy-MM-dd");
                    string planDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
                    string sText = plateAddModel.WNumberText;
                    string unit = plateAddModel.unit;
                    string remark = plateAddModel.Remark;
                    string con = plateAddModel.ContainerName;

                    #region 打印

                    string printID = plateAddModel.PrintID;
                    //绑定数据
                    var uName = await GetUserName(_uIser.Name);
                    //绑定数据
                    List<Object> objList = new List<Object>();
                    //加入数据源
                    object obj = new TeamplateContainerLabel
                    {
                        ///WMS标签数据源
                        Plant = factoryNane,
                        PONumber = proNo + pc,
                        MaterialName = pfName, //Replace('_', ' ') ,
                        LineCode = line,
                        TankOrder = gx, //工序
                        Formula = pf,
                        TankWeight = targetQuantity.ToString(), //gWeight.ToString(),//缸重
                        PreparationDate = blDate,
                        NUM = plateAddModel.Totalts.ToString(),
                        PlanDate = planDate,
                        WaterWeight = sText,
                        Unit = unit,
                        Remark = remark,
                        Operator = uName,
                        Reviewer = "",
                        Pallet_Number = conName // con
                    };
                    objList.Add(obj);

                    #endregion

                    ////PrintTemplete                   库存标签打印
                    ////PrintBagTemplete                备料包库存标签模板
                    ////PrintPalletTemplete             备料托盘库存标签模板
                    //执行打印
                    var pResult = await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID,
                        "02308281-5121-6559-163e-0370f6000000", objList, objList[0], 1, "PrintPalletTemplete");
                    result.success = pResult.success;
                    result.msg = pResult.msg;
                }

                #endregion

                return result;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "新增失败";
                return result;
            }
        }

        /// <summary>
        /// 执行打印
        /// </summary>
        /// <param name="plateAddModel"></param>
        /// <returns></returns>
        public async Task<MessageModel<string>> ReprintSave(MPPallentAddModel plateAddModel)
        {
            var result = new MessageModel<string>();
            result.success = false;
            try
            {
                //查询当前工单是否存在
                string pID = plateAddModel.PID;
                string conCode = plateAddModel.ContainerName;
                string batchID = plateAddModel.BatchId;

                if (string.IsNullOrEmpty(pID))
                {
                    result.msg = "请选择工单号";
                    return result;
                }

                if (string.IsNullOrEmpty(batchID))
                {
                    result.msg = "请选择批次";
                    return result;
                }

                if (string.IsNullOrEmpty(conCode))
                {
                    result.msg = "请添加容器";
                    return result;
                }

                //if (string.IsNullOrEmpty(plateAddModel.TS))
                //{
                //    result.msg = "请输入托数";
                //    return result;
                //}

                if (plateAddModel.TS == "0")
                {
                    result.msg = "请输入托数";
                    return result;
                }

                if (string.IsNullOrEmpty(plateAddModel.Totalts))
                {
                    result.msg = "请输入总托数";
                    return result;
                }

                if (string.IsNullOrEmpty(plateAddModel.PrintID))
                {
                    result.msg = "请选择打印机";
                    return result;
                }

                //工单数据
                var proData = await _dal.FindEntity(p => p.ID == pID);
                if (proData == null)
                {
                    result.msg = "工单信息不存在";
                    return result;
                }

                #region 查询产线节点

                var proEntity = await _ProductionOrderEntitDal.FindEntity(p => p.ProductionOrderNo == proData.ProOrder);
                if (proEntity == null)
                {
                    result.msg = "工单信息不存在";
                    return result;
                }

                string proRemark = proEntity.Remark;
                if (!string.IsNullOrEmpty(proRemark))
                {
                    plateAddModel.Remark = plateAddModel.Remark + " 订单备注:" + proRemark;
                }

                //产线节点
                var eEntity = await _EquipmentEntitydal.FindEntity(p => p.ID == proEntity.FillLineId);
                string eCode = string.Empty;
                if (eEntity == null)
                {
                    result.msg = "产线节点不存在";
                    return result;
                }

                eCode = eEntity.EquipmentCode;

                #endregion


                var batch = await _BatchEntity.FindEntity(p => p.ID == batchID);
                if (batch == null)
                {
                    result.msg = "批次信息不存在";
                    return result;
                }

                string batchNumber = batch.Number;
                //缸序
                string gx = proData.Sequence.ToString() + "/" + proData.Sequencetotal.ToString();
                //缸重
                decimal targetQuantity = batch.TargetQuantity.Value;
                //(1/2) 当前批次和批次总量
                string pc = "(" + batchNumber + "/";

                var countB = await _BatchEntity.FindList(p => p.ProductionOrderId == pID);
                pc += countB.Count + ")";


                //    var poActual = await _PoConsumeActualEntityDal.FindList(p => p.ID != null);
                //     var poExecutionData = await _PoProducedExecutionEntityDal.FindList(p => p.ID != null);

                //var serachData = (from a in poActual
                //                  join b in poExecutionData on a.ProductExecutionId equals b.ID
                //                  where b.BatchId == batch.ID
                //                  group a by new
                //                  {
                //                      a.Quantity,
                //                      b.ma
                //                      b.MaterialId
                //                  } into g
                //                  select new MpModel
                //                  {
                //                      EquipmentId = g.Key.EquipmentId,
                //                      MaterialID = g.Key.MaterialId

                //                  }).ToList();


                //PPM_B_PO_PRODUCED_EXECUTION
                // PPM_B_PO_CONSUME_ACTUAL
                // PPM_B_BATCH


                string factoryNane = "";
                string proNo = proData.ProOrder;
                string pfName = proData.MaterinalName;
                string line = proData.LineCode;
                //  string gx = plateAddModel.XHNumber + "/" + proData.PrepStatustotalcount == null ? "0" : proData.PrepStatustotalcount.Value.ToString();
                //配方
                string pf = proData.FormulaNo;

                // 缸重(需要根据容器计算总量)
                // 这里获取当前缸重
                //var totalConData = await _dalBBatchDetailMaterialViewEntity.FindList(p => p.ContainerId == plateAddModel.ContainerID && p.EquipmentId == plateAddModel.EquipmentId);
                //if (totalConData == null || totalConData.Count == 0)
                //{
                //    result.msg = "请确认容器内容是否正确";
                //    return result;
                //}
                //decimal gWeight = Math.Round(totalConData.Sum(p => p.InQuantity), 3);
                //var gzList = totalConData.Where(p => p.GzQty > 0).ToList();
                //if (gzList != null && gzList.Count > 0)
                //{
                //    if (gzList[0].GzQty != null)
                //    {
                //        gWeight = gzList[0].GzQty.Value;

                //    }
                //}


                //if (gWeight <= 0)
                //{
                //    result.msg = "不存在缸重";
                //    return result;
                //}

                //板数等于 总托数据/托数
                int dividend = Convert.ToInt32(plateAddModel.Totalts.Trim()); // 被除数
                int divisor = 0; // Convert.ToInt32(plateAddModel.TS.Trim());   // 除数
                int remainder = 0;
                int bsNumber = 0;
                if (divisor != 0)
                {
                    remainder = dividend % divisor; // 计算余数
                    bsNumber = dividend / divisor; // 计算除法结果
                }

                // 如果余数不为0，则加1
                if (remainder != 0)
                {
                    bsNumber++;
                }

                string blDate = proData.ModifyDate.ToString("yyyy-MM-dd");
                string planDate = proData.PlanStartTime.Value.ToString("yyyy-MM-dd");
                string sText = plateAddModel.WNumberText;
                //if (sText.Contains("+"))
                //{
                //    sText = sText.Replace('+', ' ');
                //}
                string unit = plateAddModel.unit;
                string remark = plateAddModel.Remark;
                string con = plateAddModel.ContainerName;

                #region 打印

                string printID = plateAddModel.PrintID;
                //string templateID = "";
                //string templateclassID = "";
                ////这里查询模板ID 
                ////PrintTemplete                   库存标签打印
                ////PrintBagTemplete                备料包库存标签模板
                ////PrintPalletTemplete             备料托盘库存标签模板

                //var models = await GetTeampID(plateAddModel.EquipmentId, "PrintTemplete");
                //if (models == null)
                //{
                //    result.msg = "请配置打印机";
                //    return result;
                //}

                //templateID = models.ID;
                //templateclassID = models.TemplateClassId;
                //绑定数据
                var uName = await GetUserName(_uIser.Name);
                //绑定数据
                List<Object> objList = new List<Object>();
                //加入数据源
                object obj = new TeamplateContainerLabel
                {
                    ///WMS标签数据源
                    Plant = factoryNane,
                    PONumber = proNo + pc,
                    MaterialName = pfName, //Replace('_', ' ') ,
                    LineCode = line,
                    TankOrder = gx, //工序
                    Formula = pf,
                    TankWeight = targetQuantity.ToString(), //gWeight.ToString(),//缸重
                    PreparationDate = blDate,
                    NUM = plateAddModel.Totalts.ToString(),
                    PlanDate = planDate,
                    WaterWeight = sText,
                    Unit = unit,
                    Remark = remark,
                    Operator = uName,
                    Reviewer = "",
                    Pallet_Number = con,
                    Fill_Code = eCode
                };
                objList.Add(obj);

                #endregion

                ////PrintTemplete                   库存标签打印
                ////PrintBagTemplete                备料包库存标签模板
                ////PrintPalletTemplete             备料托盘库存标签模板
                //执行打印
                var pResult = await _IPrintSelectViewServices.PrintCodeByEquipmentId(printID,
                    "02308281-5121-6559-163e-0370f6000000", objList, objList[0], 1, "PrintPalletTemplete");

                result.success = pResult.success;
                result.msg = pResult.msg;


                return result;
            }
            catch
            {
                _unitOfWork.RollbackTran();
                result.msg = "打印失败";
                return result;
            }
        }

        public async Task<bool> SaveForm(MaterialPreparationViewEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }


        #region 称量备料

        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="state"></param>
        /// <returns></returns>
        private string GetPre_State(string state)
        {
            if (!string.IsNullOrEmpty(state))
            {
                switch (state)
                {
                    case "1":
                        return "待备料";
                    case "2":
                        return "待备料";
                    case "3":
                        return "已拼锅";
                    case "4":
                        return "拼锅复检";
                    case "6":
                        return "开始预检查";
                    case "7":
                        return "预检查完成";
                    case "8":
                        return "投料中";
                    case "9":
                        return "投料完成";
                    case "10":
                        return "复称完成";
                    case "11":
                        return "称量完成";
                    case "12":
                        return "备料中";
                    default:
                        break;
                }
            }

            return "";
        }

        /// <summary>
        /// 工单工序批次备料查询 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<PageModel<MaterialPreparationViewEntity>> GetPageList_CLBL(
            MaterialPreparationViewRequestModel reqModel)
        {
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProLine), a => a.ProLine == reqModel.ProLine)
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductFamily), a => a.ProductFamily == reqModel.ProductFamily)
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode == reqModel.EquipmentCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName) //班次
                //时间
                .AndIF(reqModel.StarTime != null, a => a.PlanStartTime >= reqModel.StarTime)
                .AndIF(reqModel.EndTime != null, a => a.PlanStartTime <= reqModel.EndTime)
                .ToExpression();
            var data = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime)
                .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            result.dataCount = dataCount;
            result.data = data;
            return result;
        }

        public async Task<PageModel<MaterialPreparationViewEntity>> Get_CLBLNew(
            MaterialPreparationViewRequestModel reqModel)
        {
            //第一步筛选所有数据
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();

            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProLine), a => a.ProLine == reqModel.ProLine)
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductFamily), a => a.ProductFamily == reqModel.ProductFamily)
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode), a => a.EquipmentCode == reqModel.EquipmentCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName) //班次
                .AndIF(!string.IsNullOrEmpty(reqModel.FormulaNo), a => a.FormulaNo.Contains(reqModel.FormulaNo))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProOrder), a => a.ProOrder.Contains(reqModel.ProOrder))
                .AndIF(reqModel.StarTime != null, a => a.PlanStartTime >= reqModel.StarTime)
                .AndIF(!string.IsNullOrEmpty(reqModel.keyWord),
                    a => a.ProOrder.Contains(reqModel.keyWord) || a.MaterinalCode.Contains(reqModel.keyWord) ||
                         a.FormulaNo.Contains(reqModel.keyWord) || a.MaterinalName.Contains(reqModel.keyWord) ||
                         a.EquipmentCode.Contains(reqModel.keyWord) || a.ShiftName.Contains(reqModel.keyWord))
                .AndIF(reqModel.EndTime != null, a => a.PlanStartTime <= reqModel.EndTime)
                .And(p => p.Sapordertype == "ZXH2")
                .ToExpression();


            //   

            if (string.IsNullOrEmpty(reqModel.RoomID))
            {
                var dataAll = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                    .Where(whereExpression).OrderByDescending(p => p.PlanStartTime).OrderBy(p => p.Sequence)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                //筛选订单
                string[] proID = dataAll.Select(p => p.ID).ToArray();
                if (proID != null && proID.Length > 0)
                {
                    var batchList = await _BatchEntity.Db.Queryable<BatchEntity>()
                        .In(p => p.ProductionOrderId, proID).ToListAsync();
                    for (int i = 0; i < dataAll.Count; i++)
                    {
                        String id = dataAll[i].ID;
                        var batch = batchList.Where(p => p.ProductionOrderId == id).OrderBy(p => p.Number).ToList()
                            .FirstOrDefault();
                        if (batch != null)
                        {
                            string pre_State = string.Empty;
                            pre_State = batch.PrepStatus;
                            if (!string.IsNullOrEmpty(pre_State))
                            {
                                string state = GetPre_State(pre_State);
                                dataAll[i].NowState = state;
                            }
                        }
                    }
                }

                result.dataCount = dataCount;
                result.data = dataAll;
                return result;
            }

            var dataF = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime).ToListAsync();

            #region 筛选数据

            List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);

            #endregion

            //筛选所有的ID   
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            mIDs = _MaterialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, mIDs)
                .Select(mp => mp.ID).ToArray();

            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            //筛选
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();


            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            //筛选订单
            string[] proIDs = dataF.Select(p => p.ID).ToArray();
            if (proIDs == null || proIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            #region 这里筛选工单

            //订单
            //      var proList = await _ProductionOrderEntitDal.Db.Queryable<ProductionOrderEntity>().In(p => p.ID, proIDs).ToListAsync();

            var poM = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MaterialId, mIDs).ToListAsync();
            if (poM == null || poM.Count <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            var batchQTY = await _BatchConsumeRequirementEntityDal.FindList(P => P.Quantity > 0);

            string[] proIDsSearch = (from a in poM
                join b in batchQTY on a.ID equals b.PoConsumeRequirementId
                select new
                {
                    ID = a.ProductionOrderId
                }).Distinct().ToList().Select(p => p.ID).ToArray();

            #endregion

            // 筛选订单和物料信息
            //List<ClblDiiViewEntity> datas = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.ID, proIDs).In(p => p.MaterialId, mIDs)
            //   .Where(p => p.MQuantityTotal > 0).ToListAsync();

            ////筛选最终需要的数据
            //string[] proIDsSearch = datas.Select(p => p.ID).ToArray();
            if (proIDsSearch == null || proIDsSearch.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }


            List<MaterialPreparationViewEntity> list = new List<MaterialPreparationViewEntity>();
            //筛选批次
            var batchListNew = await _BatchEntity.Db.Queryable<BatchEntity>()
                .In(p => p.ProductionOrderId, proIDs).ToListAsync();
            for (int i = 0; i < dataF.Count; i++)
            {
                string id = dataF[i].ID;
                int count = proIDsSearch.Where(p => p == id).Count();
                if (count > 0)
                {
                    var batch = batchListNew.Where(p => p.ProductionOrderId == id).OrderBy(p => p.Number).ToList()
                        .FirstOrDefault();
                    if (batch != null)
                    {
                        string pre_State = string.Empty;
                        pre_State = batch.PrepStatus;
                        if (!string.IsNullOrEmpty(pre_State))
                        {
                            string state = GetPre_State(pre_State);
                            dataF[i].NowState = state;
                        }
                    }

                    list.Add(dataF[i]);
                }
            }


            //int counts = list.Count;

            //var data = list.OrderBy(p => p.pro).ThenBy(p => p.PlanStartTime).ToList().ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.Sequence, OrderType.Desc);
            //return data;


            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          

            // 获取指定页的数据
            //  var rDat1 = list.OrderByDescending(p => p.PlanStartTime).ThenBy(p => p.Sequence).Skip(startIndex).Take(reqModel.pageSize).ToList();
            var rDat = list.OrderBy(p => p.LineCode).ThenBy(p => p.PlanStartTime).ThenBy(p => p.Psequence)
                .ThenBy(p => p.ProOrder).Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = list.Count;
            result.data = rDat;
            return result;
        }


        public async Task<PageModel<MaterialPreparationViewEntity>> Get_YLLabelNew(
            MaterialPreparationViewRequestModel reqModel)
        {
            //第一步筛选所有数据
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();


            if (string.IsNullOrEmpty(reqModel.RoomID))
            {
                reqModel.RoomID = "02409291-0245-2078-5056-a3989b000000";
            }

            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProLine), a => a.ProLine.Contains(reqModel.ProLine))
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductFamily),
                    a => a.ProductFamily.Contains(reqModel.ProductFamily))
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentCode),
                    a => a.EquipmentCode.Contains(reqModel.EquipmentCode))
                //.AndIF(!string.IsNullOrEmpty(reqModel.MCode), a => a.MaterinalCode.Contains(reqModel.MCode))
                .AndIF(!string.IsNullOrEmpty(reqModel.FormulaNo), a => a.FormulaNo.Contains(reqModel.FormulaNo))
                .AndIF(!string.IsNullOrEmpty(reqModel.Quantity), a => a.PlanQty.ToString().Contains(reqModel.Quantity))
                .AndIF(!string.IsNullOrEmpty(reqModel.ShiftName), a => a.ShiftName == reqModel.ShiftName) //班次
                .AndIF(reqModel.StarTime != null, a => a.PlanStartTime >= reqModel.StarTime)
                .AndIF(reqModel.EndTime != null, a => a.PlanStartTime <= reqModel.EndTime)
                .And(p => p.Sapordertype == "ZXH2")
                .ToExpression();


            if (string.IsNullOrEmpty(reqModel.RoomID))
            {
                var dataAll = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                    .Where(whereExpression).OrderByDescending(p => p.PlanStartTime)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

                result.dataCount = dataCount;
                result.data = dataAll;
                return result;
            }

            var dataF = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime).ToListAsync();


            #region 筛选数据

            List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);

            #endregion

            //筛选所有的ID   
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            //  mIDs = _MaterialEntitydal.Db.Queryable<DFM.Model.Models.MaterialEntity>().In(p => p.ID, mIDs).Select(mp => mp.ID).ToArray();
            //筛选
            //       mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>().In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1").Select(mp => mp.MaterialId).ToArray();
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "PrepByPO" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();

            //筛选订单
            string[] proIDs = dataF.Select(p => p.ID).ToArray();
            if (proIDs == null || proIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }


            if (mIDs == null || mIDs.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            #region 这里筛选工单

            //订单
            // var proList = await _ProductionOrderEntitDal.Db.Queryable<ProductionOrderEntity>().In(p => p.ID, proIDs).ToListAsync();

            var poM = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MaterialId, mIDs).ToListAsync();
            var batchQTY = await _BatchConsumeRequirementEntityDal.FindList(P => P.Quantity > 0);

            string[] proIDsSearch = (from a in poM
                join b in batchQTY on a.ID equals b.PoConsumeRequirementId
                select new
                {
                    ID = a.ProductionOrderId
                }).Distinct().ToList().Select(p => p.ID).ToArray();

            #endregion

            if (proIDsSearch == null || proIDsSearch.Length <= 0)
            {
                result.dataCount = 0;
                result.data = new List<MaterialPreparationViewEntity>();
                return result;
            }

            //这里拼接需要处理的数据(所有)
            // var dataMaterialLabel = await _MaterialLabelViewEntityDal.Db.Queryable<MaterialLabelViewEntity>().In(p => p.ProductionOrderId, proIDs).In(p => p.MId, mIDs).ToListAsync();

            var dataMaterialLabel = await _MlabelCreateViewEntityDal.Db.Queryable<MlabelCreateViewEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MId, mIDs).ToListAsync();


            List<MaterialPreparationViewEntity> list = new List<MaterialPreparationViewEntity>();
            for (int i = 0; i < dataF.Count; i++)
            {
                string id = dataF[i].ID;
                int count = proIDsSearch.Where(p => p == id).Count();
                if (count > 0)
                {
                    var resultData = dataMaterialLabel.Where(p => p.ProductionOrderId == id)
                        .GroupBy(p => new { p.MName, p.MCode, p.TagerQty, p.UintName })
                        .Select(p => new { p.Key.MName, p.Key.MCode, p.Key.TagerQty, p.Key.UintName }).ToList();
                    //这里完成拼接操作
                    string datasMSG = string.Empty;
                    for (int J = 0; J < resultData.Count(); J++)
                    {
                        string msg = resultData[J].MName + ":" + resultData[J].MCode + "  " + resultData[J].TagerQty +
                                     resultData[J].UintName + ";";
                        datasMSG += msg;
                    }

                    dataF[i].Mdetialdesc = datasMSG;
                    list.Add(dataF[i]);
                }
            }

            //查询数据
            list = list.Where(P => P.Mdetialdesc.Contains(reqModel.MCode)).OrderBy(p => p.Sequence).ToList();
            int counts = list.Count;


            int startIndex = (reqModel.pageIndex - 1) * reqModel.pageSize; // 计算开始的索引          

            // 获取指定页的数据
            var rDat = list.OrderBy(p => p.ProOrder).ThenBy(p => p.FormulaNo).ThenBy(p => p.PlanStartTime)
                .Skip(startIndex).Take(reqModel.pageSize).ToList();
            result.dataCount = list.Count;
            result.data = rDat;
            return result;


            //var data = list.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.PlanStartTime, OrderType.Desc);
            //return data;
        }


        #region 下拉框

        #region 工单

        /// <summary>
        /// 筛选工单下拉框根据roomID
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SelectDataModel>> Get_SelectPro(MaterialPreparationViewRequestModel reqModel)
        {
            if (string.IsNullOrEmpty(reqModel.PIDS))
            {
                return new List<SelectDataModel>();
            }

            string[] pIDS = reqModel.PIDS.Split(',').ToArray();

            if (pIDS.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            //第一步筛选所有数据
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();
            var dataF = await _dal.Db.Queryable<MaterialPreparationViewEntity>().In(P => P.ID, pIDS)
                .Where(p => p.ID != null).OrderByDescending(p => p.PlanStartTime).ToListAsync();

            #region 筛选数据

            List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);

            #endregion

            //筛选所有的ID   
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            mIDs = _MaterialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, mIDs)
                .Select(mp => mp.ID).ToArray();
            //筛选
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();

            //筛选订单
            string[] proIDs = dataF.Select(p => p.ID).ToArray();
            if (proIDs == null || proIDs.Length <= 0)
            {
                return new List<SelectDataModel>();
            }


            #region 这里筛选工单

            var poM = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MaterialId, mIDs).ToListAsync();
            var batchQTY = await _BatchConsumeRequirementEntityDal.FindList(P => P.Quantity > 0);

            string[] proIDsSearch = (from a in poM
                join b in batchQTY on a.ID equals b.PoConsumeRequirementId
                select new
                {
                    ID = a.ProductionOrderId
                }).Distinct().ToList().Select(p => p.ID).ToArray();

            #endregion

            if (proIDsSearch == null || proIDsSearch.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            List<SelectDataModel> list = new List<SelectDataModel>();

            for (int i = 0; i < dataF.Count; i++)
            {
                string id = dataF[i].ID;
                int count = proIDsSearch.Where(p => p == id).Count();
                if (count > 0)
                {
                    SelectDataModel model = new SelectDataModel();
                    model.ID = dataF[i].ID;
                    model.FormulaNo = dataF[i].FormulaNo;
                    model.KeyText = "工单:" + dataF[i].ProOrder + "-需求数量" + dataF[i].PlanQty;
                    model.KeyValue = dataF[i].ProOrder;
                    model.PlanNumber = dataF[i].PlanQty;
                    list.Add(model);
                }
            }

            return list;
        }

        #endregion

        #region 批次

        /// <summary>
        /// 根据工单ProID和ROOMID 筛选物料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SelectDataModel>> Get_SelectMaterial(MaterialPreparationViewRequestModel reqModel)
        {
            //根据ROOM筛选可用的物料
            List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();

            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "PrepByPO" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();

            if (mIDs == null || mIDs.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            string proID = reqModel.ProID;
            //筛选可用的物料和批次信息
            var data = await _dal.Db.Queryable<MaterialLabelViewEntity>().Where(P => P.ProductionOrderId == proID)
                .In(p => p.MId, mIDs).ToListAsync();

            var result = (from a in data
                group a by
                    new
                    {
                        a.BatchId,
                        a.BNumber,
                        a.UId,
                        a.UintName,
                        a.TagerQty,
                        a.MId,
                        a.MCode,
                        a.MName,
                        a.BagSize,
                        a.Sapformula,
                    }
                into g
                select new SelectDataModel
                {
                    KeyText = "批次:" + g.Key.BNumber + "-需求:" + g.Key.TagerQty + "-物料:" + g.Key.BNumber + " " +
                              g.Key.MName + "-单位:" + g.Key.UintName + "-单包重量" + g.Key.BagSize,
                    BatchId = g.Key.BatchId,
                    BNumber = g.Key.BNumber,
                    UId = g.Key.UId,
                    UintName = g.Key.UintName,
                    MId = g.Key.MId,
                    MCode = g.Key.MCode,
                    TagerQty = g.Key.TagerQty.ToString(),
                    MName = g.Key.MName,
                    BagSize = g.Key.BagSize,
                    FormulaNo = g.Key.Sapformula
                }).ToList();

            return result;
        }

        #endregion

        #region 物料   单位 ， 单包数量

        /// <summary>
        /// 根据工单ProID和ROOMID 筛选物料
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        public async Task<List<SelectDataModel>> Get_SelectMaterialOLD(MaterialPreparationViewRequestModel reqModel)
        {
            //第一步筛选所有数据
            PageModel<MaterialPreparationViewEntity> result = new PageModel<MaterialPreparationViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<MaterialPreparationViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProID), a => a.ID == reqModel.ProID)
                .ToExpression();
            var dataF = await _dal.Db.Queryable<MaterialPreparationViewEntity>()
                .Where(whereExpression).OrderByDescending(p => p.PlanStartTime).ToListAsync();

            #region 筛选数据

            List<MpModel> mpList = await Get_DisMaterial(reqModel.RoomID);

            #endregion

            //筛选所有的ID   
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            mIDs = _MaterialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, mIDs)
                .Select(mp => mp.ID).ToArray();
            //筛选
            mIDs = _MaterialPropertyValueEntityDal.Db.Queryable<PPM.Model.Models.MaterialPropertyValueEntity>()
                .In(p => p.MaterialId, mIDs).Where(p => p.PropertyCode == "RequiresPreWeigh" && p.PropertyValue == "1")
                .Select(mp => mp.MaterialId).ToArray();

            //筛选订单
            string[] proIDs = dataF.Select(p => p.ID).ToArray();
            if (proIDs == null || proIDs.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            #region 这里筛选工单

            var poM = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MaterialId, mIDs).ToListAsync();
            var batchQTY = await _BatchConsumeRequirementEntityDal.FindList(P => P.Quantity > 0);

            string[] proIDsSearch = (from a in poM
                join b in batchQTY on a.ID equals b.PoConsumeRequirementId
                select new
                {
                    ID = a.ProductionOrderId
                }).Distinct().ToList().Select(p => p.ID).ToArray();

            #endregion

            if (proIDsSearch == null || proIDsSearch.Length <= 0)
            {
                return new List<SelectDataModel>();
            }

            var dataMaterialLabel = await _MlabelCreateViewEntityDal.Db.Queryable<MlabelCreateViewEntity>()
                .In(p => p.ProductionOrderId, proIDs).In(p => p.MId, mIDs).ToListAsync();

            List<SelectDataModel> list = new List<SelectDataModel>();
            for (int i = 0; i < dataF.Count; i++)
            {
                string id = dataF[i].ID;
                int count = proIDsSearch.Where(p => p == id).Count();
                if (count > 0)
                {
                    var resultData = dataMaterialLabel.Where(p => p.ProductionOrderId == id)
                        .GroupBy(p => new { p.MName, p.MCode, p.TagerQty, p.UintName })
                        .Select(p => new { p.Key.MName, p.Key.MCode, p.Key.TagerQty, p.Key.UintName }).ToList();
                    //这里完成拼接操作
                    string datasMSG = string.Empty;
                    for (int J = 0; J < resultData.Count(); J++)
                    {
                        SelectDataModel model = new SelectDataModel();
                        model.ID = resultData[J].MCode;
                        model.KeyValue = resultData[J].MCode;
                        model.KeyText = resultData[J].MCode + "-" + resultData[J].MName;
                        list.Add(model);
                    }
                }
            }

            return list;
        }

        #endregion

        #endregion


        ///// <summary>
        ///// 根据批次ID获取对应的物料信息
        ///// </summary>
        ///// <param name="batchID">批次ID</param>
        ///// <returns></returns>
        //public async Task<List<BBdetailIiViewEntity>> (string batchID)
        //{
        //    List<BBdetailIiViewEntity> result = new List<BBdetailIiViewEntity>();
        //    RefAsync<int> dataCount = 0;
        //    var whereExpression = Expressionable.Create<BBdetailIiViewEntity>().
        //        AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID)
        //                     .ToExpression();
        //    var data = await _bBdetailIiViewEntityDal.Db.Queryable<BBdetailIiViewEntity>()
        //        .Where(whereExpression).ToListAsync();
        //    return data;
        //}


        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<PageModel<MPreparationViewEntity>>
            GetMPreparationII_CLBL(PreparationIIModel model) //,string EquipmentID)
        {
            //筛选可用物料
            List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);

            //获取当前可以看的物料信息
            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<MPreparationViewEntity>();
            }

            #region 查询

            var whereExpression = Expressionable.Create<BatchConsumeRequirementEntity,
                    PoConsumeRequirementEntity, ProductionOrderEntity>()
                .And((a, b, c) => model.proOrderId.Contains(c.ID) && mIDs.Contains(b.MaterialId))
                .ToExpression();

            var data = await _dal.QueryMuch<BatchConsumeRequirementEntity,
                PoConsumeRequirementEntity, ProductionOrderEntity, MPreparationViewEntity>(
                (a, b, c) => new object[]
                {
                    JoinType.Inner, a.PoConsumeRequirementId == b.ID,
                    JoinType.Inner, c.ID == b.ProductionOrderId
                },
                (a, b, c) => new MPreparationViewEntity
                {
                    MId = b.MaterialId,
                    InQuantity = b.WeighingQty,
                    UnitId = b.UnitId,
                    MQuantityunit = a.ChangeUnit
                },
                whereExpression
            );

            //  data = data.Where(p => mIDs.Contains(p.MId)).ToList();
            //var gData1 = data.GroupBy(p => p.MId);

            var gData1 = data.GroupBy(p => new { p.MId, p.MQuantityunit });
            var gData = gData1.GroupBy(p => p.Key.MId).Select(k => k.Key).ToArray();
            var mList = await _MaterialEntitydal.Db.Queryable<MaterialEntity>().In(p => p.ID, gData)
                .ToListAsync();
            var listInvent = await _dalInventKcViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId && gData.Contains(p.MaterialId)).ToListAsync();
            List<MPreparationViewEntity> MPreparationModel = new List<MPreparationViewEntity>();

            foreach (var item in gData1)
            {
                MPreparationViewEntity obj = new MPreparationViewEntity();
                var qty = item.Sum(p => p.InQuantity);
                obj.QSum = qty == null ? 0 : Convert.ToDecimal(Math.Round(qty.Value, 4));
                if (item.Key.MQuantityunit == "g" || item.Key.MQuantityunit == "G")
                {
                    obj.QSum = obj.QSum * 1000;
                }

                obj.MId = item.Key.MId;
                obj.MName = mList.Where(p => p.ID == item.Key.MId).FirstOrDefault().NAME;
                obj.MCode = mList.Where(p => p.ID == item.Key.MId).FirstOrDefault().Code;
                obj.InQuantity = listInvent.Where(P => P.MaterialId == item.Key.MId).Sum(p => p.Quantity);
                obj.UnitId = listInvent.Where(P => P.MaterialId == item.Key.MId).FirstOrDefault()?.MaterialUnit1;
                obj.MQuantityunit = item.Key.MQuantityunit; //obj.UnitId;
                obj.ID = obj.MId;
                MPreparationModel.Add(obj);
            }

            PageModel<MPreparationViewEntity> results1 = new PageModel<MPreparationViewEntity>();
            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = MPreparationModel.OrderBy(p => p.CreateDate).Skip(startIndex).Take(model.pageSize).ToList();
            results1.dataCount = MPreparationModel.Count;
            results1.data = rDat;
            return results1;

            #endregion
        }


        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<PageModel<MPreparationViewEntity>>
            GetMPreparationII_CLBLOKD(PreparationIIModel model) //,string EquipmentID)
        {
            List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<MPreparationViewEntity>();
            }

            List<MPreparationViewEntity> MPreparationModel = new List<MPreparationViewEntity>();
            List<BBatchDetailIiViewEntity> result = new List<BBatchDetailIiViewEntity>();
            RefAsync<int> dataCount = 0;

            //查询需求物料
            result = await _PPoConsumeRequirementEntitDal.Db.Queryable<BBatchDetailIiViewEntity>()
                .In(p => p.ProductionOrderId, model.proOrderId)
                .In(p => p.MaterialId, mIDs).ToListAsync();

            string[] str = result.GroupBy(p => new { p.MaterialId }).Select(i => i.Key.MaterialId).Distinct().ToArray();
            if (str == null && str.Length <= 0)
            {
                return new PageModel<MPreparationViewEntity>();
            }

            List<string> listStr = str.ToList();

            //     DateTime s = DateTime.Now;
            var data = await _MPreparationViewEntity.Db.Queryable<MPreparationViewEntity>()
                .In(p => p.Pro_Id, model.proOrderId) //In(P => P.MId, str)
                //.Where(A =>  A.InQuantity != null)//.In(p => p.MId, mIDs)
                .ToListAsync();
            data = data.Where(p => listStr.Contains(p.MId)).ToList();

            //  DateTime end = DateTime.Now;
            //   TimeSpan sp = end - s;
            //  double s1 = sp.TotalSeconds;
            PageModel<BBatchDetailMaterialViewEntity> results = new PageModel<BBatchDetailMaterialViewEntity>();

            List<MPreparationViewEntity> resultList = (from a in data
                group a by new
                {
                    // a.EquipmentId,
                    // a.Pro_Id,
                    a.MCode,
                    a.MName,
                    //    a.SegmentCode,
                    // a.SegmentName,
                    // a.EquipmentName,
                    a.UnitId,
                    //a.InQuantity,
                    a.MQuantityunit,
                    a.ID
                }
                into g
                let x = g.Where(p => p.CompleteStates == "").Count() > 0 ? "" : "OK"
                select new MPreparationViewEntity
                {
                    //EquipmentId = g.Key.EquipmentId,
                    Pro_Id = string.Join(",", g.Select(d => d.Pro_Id)),
                    MCode = g.Key.MCode,
                    MName = g.Key.MName,
                    SegmentCode =
                        string.Join(",",
                            g.Select(d =>
                                d.SegmentCode)), //+"-" +string.Join(",", g.Select(d => d.SegmentName)),//g.Key.SegmentCode,
                    // SegmentName  = g.Key.SegmentName,
                    EquipmentName = string.Join(",", g.Select(d => d.EquipmentName)),
                    QSum = g.Sum(p => p.QSum.Value),
                    CompleteStates = x,
                    MQuantityunit = g.Key.MQuantityunit,
                    //InQuantity = g.Key.InQuantity,
                    UnitId = g.Key.UnitId,
                    ID = g.Key.ID
                }).ToList();

            //绑定数据
            var listInvent = await _dalInventKcViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId).In(p => p.MaterialId, str).ToListAsync();
            // var listInvent = await _dalInventKcViewEntity.FindList(p => p.ID != null);
            string mCode = string.Empty;
            string sprintStr = string.Empty;
            for (int i = 0; i < resultList.Count; i++)
            {
                mCode = resultList[i].MCode;

                #region 去重复操作，首先

                resultList[i].EquipmentName = RemoveDuplicate(resultList[i].EquipmentName);
                resultList[i].SegmentCode = RemoveDuplicatePro(resultList[i].SegmentCode);

                #endregion

                //筛选

                var invnetModel = listInvent.Where(p => p.MaterialCode == mCode).ToList();
                if (invnetModel == null || invnetModel.Count == 0)
                {
                    resultList[i].InQuantity = 0;
                }
                else
                {
                    //resultList[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
                    resultList[i].InQuantity = invnetModel.Sum(p => p.Quantity);
                }
            }

            //var results1 = resultList.ToPageModel(model.pageIndex, model.pageSize, c => c.EquipmentId, OrderType.Asc);

            //int pageIndex = model.pageIndex; // 当前页码，从1开始
            //int pageSize = model.pageSize; // 每页的数据数量
            //                               // resultList = resultList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

            //return results1;

            PageModel<MPreparationViewEntity> results1 = new PageModel<MPreparationViewEntity>();

            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = resultList.OrderBy(p => p.CreateDate).Skip(startIndex).Take(model.pageSize).ToList();
            results1.dataCount = resultList.Count;
            results1.data = rDat;
            return results1;
        }


        /// <summary>
        /// 去重转换(按照设备)
        /// </summary>
        /// <param name="name"></param>
        /// <returns></returns>
        private string RemoveDuplicate(string name)
        {
            string[] result = name.Split(',').Where(s => !string.IsNullOrEmpty(s)).ToArray();

            if (result.Length <= 0)
            {
                return name;
            }
            else
            {
                List<string> arrayList = new List<string>();
                for (int i = 0; i < result.Length; i++)
                {
                    string[] sprintD = result[i].Split('、').Where(s => !string.IsNullOrEmpty(s)).ToArray();
                    for (int j = 0; j < sprintD.Length; j++)
                    {
                        arrayList.Add(sprintD[j]);
                    }
                }

                arrayList = arrayList.Distinct().ToList();
                string returnString = string.Empty;

                for (int i = 0; i < arrayList.Count; i++)
                {
                    if (i == arrayList.Count - 1)
                    {
                        returnString += arrayList[i];
                    }
                    else
                    {
                        returnString += arrayList[i] + "、";
                    }
                }

                return returnString;
            }
        }


        /// <summary>
        /// 工单
        /// </summary>
        /// <param name="EpquipMent"></param>
        /// <returns></returns>
        private string RemoveDuplicatePro(string proCode)
        {
            string[] result = proCode.Split(',').Where(s => !string.IsNullOrEmpty(s)).ToArray();

            if (result.Length <= 0)
            {
                return proCode;
            }
            else
            {
                List<string> arrayList = new List<string>();
                for (int i = 0; i < result.Length; i++)
                {
                    arrayList.Add(result[i]);
                }

                arrayList = arrayList.Distinct().ToList();
                string returnString = string.Empty;

                for (int i = 0; i < arrayList.Count; i++)
                {
                    if (i == arrayList.Count - 1)
                    {
                        returnString += arrayList[i];
                    }
                    else
                    {
                        returnString += arrayList[i] + "、";
                    }
                }

                return returnString;
            }
        }


        public async Task<PageModel<ClblDiiViewEntity>> GetPageListByMaterial_CLBL(BBatchDModel model)
        {
            //查询可用库存
            var listInvent = await _dalInventKcViewEntity.FindList(p => p.MaterialId == model.MaterialId);

            PageModel<ClblDiiViewEntity> result = new PageModel<ClblDiiViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<ClblDiiViewEntity>().And(p => p.MQuantityTotal > 0)
                .ToExpression();
            List<ClblDiiViewEntity> data = new List<ClblDiiViewEntity>();


            if (model.ProId != null && model.ProId.Length > 0)
            {
                data = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>()
                    .In(p => p.ProductionOrderId, model.ProId)
                    .Where(whereExpression)
                    .Where(p => p.MaterialId == model.MaterialId)
                    .OrderByDescending(p => p.CreateDate)
                    .ToListAsync();

                //这里需要分组求和一次
            }
            else
            {
                data = await _dalClblDiiViewEntity.Db
                    .Queryable<ClblDiiViewEntity>() //.In(p => p.ProductionOrderId, model.ProId)
                    .Where(whereExpression)
                    .Where(p => p.MaterialId == model.MaterialId)
                    .OrderByDescending(p => p.CreateDate)
                    .ToListAsync();
            }

            string mCode = string.Empty;
            string changeUint = string.Empty;
            for (int i = 0; i < data.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = data[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    if (data[i].MQuantity >= data[i].MinPvalue && data[i].MQuantity <= data[i].MaxPvalue)
                    {
                        data[i].CompleteStates = "OK";
                    }
                }

                changeUint = data[i].ChangeUnit;
                mCode = data[i].MCode;
                var invnetModel = listInvent.Where(p => p.MaterialCode == mCode && p.EquipmentId == model.EqumentId)
                    .ToList();
                if (invnetModel == null || invnetModel.Count == 0)
                {
                    data[i].InQuantity = 0;
                }
                else
                {
                    data[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
                    data[i].InQuantity = invnetModel.Sum(p => p.Quantity);
                }

                if (changeUint == "g" || changeUint == "G")
                {
                    data[i].MaterialUnit1 = "g";
                    data[i].TagpSUnit = "g";
                    data[i].QuantityTotalUnit = "g";
                    data[i].MQuantityunit = "g";
                    //data[i].MQuantity = data[i].MQuantity * 1000;

                    data[i].TagpS = data[i].TagpS * 1000;
                    data[i].ParitialPage =
                        (Convert.ToDecimal(data[i].ParitialPage) * Convert.ToDecimal(1000)).ToString();
                    data[i].MinPvalue = data[i].MinPvalue * 1000;
                    data[i].MaxPvalue = data[i].MaxPvalue * 1000;
                    data[i].MQuantity = data[i].MQuantity * 1000;
                    data[i].MQuantityTotal = data[i].MQuantityTotal * 1000;
                    //data[i].MaterialUnit1 = changeUint;
                }

                //if (changeUint == "g")
                //{
                //    data[i].TagpS = data[i].TagpS * 1000;
                //    data[i].ParitialPage = Convert.ToDecimal(data[i].ParitialPage).ToString();
                //    data[i].MinPvalue = data[i].MinPvalue * 1000;
                //    data[i].MaxPvalue = data[i].MaxPvalue * 1000;
                //    data[i].MQuantity = data[i].MaxPvalue * 1000;
                //    data[i].MQuantityTotal = data[i].MQuantityTotal * 1000;
                //    data[i].MaterialUnit1 = changeUint;

                //}
            }


            PageModel<ClblDiiViewEntity> results1 = new PageModel<ClblDiiViewEntity>();
            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = data.OrderBy(p => p.ProductionOrderNo).ThenBy(p => p.MBatchNumber).Skip(startIndex)
                .Take(model.pageSize).ToList();
            results1.dataCount = data.Count;
            results1.data = rDat;
            return results1;

            //var results1 = data.ToPageModel(model.pageIndex, model.pageSize, c => c.EquipmentId, OrderType.Asc);

            //int pageIndex = model.pageIndex; // 当前页码，从1开始
            //int pageSize = model.pageSize; // 每页的数据数量
            //                               // resultList = resultList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

            //return results1;
        }


        /// <summary>
        /// 根据物料ID查询最后界面数据源
        /// </summary>
        /// <param name="MaterialId"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial_CLBLTop(
            BBatchDetailMaterialViewRequestModel reqModel, bool ProOrderid, bool Batchid, bool isType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                //location
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc).ToExpression();

            if (ProOrderid == true && Batchid == true)
            {
                whereExpression = whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    //加入查询条件(时间)
                    //batch pallet id
                    .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderid), a => a.PId == reqModel.ProOrderid)
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    //pro
                    .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId == reqModel.BatchId)
                    .And(p => p.HType == "Partial").ToExpression();
            }

            if (isType == true)
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression)
                    .OrderByDescending(p => p.CreateDate).ToListAsync();


                List<BBatchDetailMaterialViewEntity> resultList = (from x in data
                    group x by new
                    {
                        x.LotId,
                        x.SubId,
                        x.LBatch,
                        x.UnitId,
                        x.LStatus,
                        x.SbSscc,
                        x.SbStatus,
                        x.InQuantity,
                        x.MaterialUnit1,
                        x.HType,
                        x.ProductionOrderNo,
                        x.BatchCode,
                        x.InventBin,
                        x.ExpirationDate,
                        x.ID,
                        x.CreateDate,
                        x.CreateUserId,
                        x.ModifyDate,
                        x.ModifyUserId,
                        //x.UpdateTimeStamp
                    }
                    into g
                    select new BBatchDetailMaterialViewEntity
                    {
                        LotId = g.Key.LotId,
                        SubId = g.Key.SubId,
                        LBatch = g.Key.LBatch,
                        UnitId = g.Key.UnitId,
                        LStatus = g.Key.LStatus,
                        SbSscc = g.Key.SbSscc,
                        SbStatus = g.Key.SbStatus,
                        InQuantity = g.Key.InQuantity,
                        MaterialUnit1 = g.Key.MaterialUnit1,
                        HType = g.Key.HType,
                        ProductionOrderNo = g.Key.ProductionOrderNo,
                        BatchCode = g.Key.BatchCode,
                        InventBin = g.Key.InventBin,
                        ExpirationDate = g.Key.ExpirationDate,
                        ID = g.Key.ID,
                        CreateDate = g.Key.CreateDate,
                        CreateUserId = g.Key.CreateUserId,
                        ModifyDate = g.Key.ModifyDate,
                        ModifyUserId = g.Key.ModifyUserId,
                        //UpdateTimeStamp = g.Key.UpdateTimeStamp
                    }).ToList();


                // PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
                var results1 = resultList.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.ExpirationDate,
                    OrderType.Asc);
                int pageIndex = reqModel.pageIndex; // 当前页码，从1开始
                int pageSize = reqModel.pageSize; // 每页的数据数量
                // resultList = resultList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

                return results1;
            }
            else
            {
                //筛选出工单为空的
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.HType == null || p.HType == "")
                    .Where(p => string.IsNullOrEmpty(p.ProductionRequestId))
                    .OrderBy(p => p.ExpirationDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                result.dataCount = dataCount;
                result.data = data;
            }

            return result;
        }


        /// <summary>
        /// 根据物料ID查询最后界面数据源
        /// </summary>
        /// <param name="MaterialId"></param>
        /// <returns></returns>
        public async Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial_CLBL(
            BBatchDetailMaterialViewRequestModel reqModel, bool ProOrderid, bool Batchid, bool isType)
        {
            PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            RefAsync<int> dataCount = 0;
            var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                //加入查询条件(时间)
                //batch pallet id
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                //pro
                .AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
                //location
                .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc).ToExpression();
            //Status
            //  .AndIF(!string.IsNullOrEmpty(reqModel.ConStatus), a => a.ContainerStatus == reqModel.ConStatus)
            // //bin
            // .AndIF(!string.IsNullOrEmpty(reqModel.LocationS), a => a.LocationS == reqModel.LocationS)
            //  //时间
            //  .AndIF(reqModel.StarTime != null, a => a.StatesTime >= reqModel.StarTime)
            //  .AndIF(reqModel.EndTime != null, a => a.StatesTime <= reqModel.EndTime)
            ////machine
            //.AndIF(!string.IsNullOrEmpty(reqModel.CMachine), a => a.CMachine == reqModel.CMachine).ToExpression();

            if (ProOrderid == true && Batchid == true)
            {
                whereExpression = whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>()
                    //加入查询条件(时间)
                    //batch pallet id
                    .AndIF(!string.IsNullOrEmpty(reqModel.ProOrderid), a => a.PId == reqModel.ProOrderid)
                    .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId)
                    //pro
                    .AndIF(!string.IsNullOrEmpty(reqModel.BatchId), a => a.BatchId == reqModel.BatchId)
                    .And(p => p.HType == "Partial").ToExpression();
            }

            if (isType == true)
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression)
                    .OrderByDescending(p => p.CreateDate).ToListAsync();


                List<BBatchDetailMaterialViewEntity> resultList = (from x in data
                    group x by new
                    {
                        x.MaterialId,
                        x.LotId,
                        x.SubId,
                        x.LBatch,
                        x.UnitId,
                        x.LStatus,
                        x.SbSscc,
                        x.SbStatus,
                        x.InQuantity,
                        x.MaterialUnit1,
                        x.HType,
                        x.ProductionOrderNo,
                        x.BatchCode,
                        x.InventBin,
                        x.ExpirationDate,
                        x.ID,
                        x.CreateDate,
                        x.CreateUserId,
                        x.ModifyDate,
                        x.ModifyUserId,
                        //x.UpdateTimeStamp
                    }
                    into g
                    select new BBatchDetailMaterialViewEntity
                    {
                        MaterialId = g.Key.MaterialId,
                        LotId = g.Key.LotId,
                        SubId = g.Key.SubId,
                        LBatch = g.Key.LBatch,
                        UnitId = g.Key.UnitId,
                        LStatus = g.Key.LStatus,
                        SbSscc = g.Key.SbSscc,
                        SbStatus = g.Key.SbStatus,
                        InQuantity = g.Key.InQuantity,
                        MaterialUnit1 = g.Key.MaterialUnit1,
                        HType = g.Key.HType,
                        ProductionOrderNo = g.Key.ProductionOrderNo,
                        BatchCode = g.Key.BatchCode,
                        InventBin = g.Key.InventBin,
                        ExpirationDate = g.Key.ExpirationDate,
                        ID = g.Key.ID,
                        CreateDate = g.Key.CreateDate,
                        CreateUserId = g.Key.CreateUserId,
                        ModifyDate = g.Key.ModifyDate,
                        ModifyUserId = g.Key.ModifyUserId,
                        //UpdateTimeStamp = g.Key.UpdateTimeStamp
                    }).ToList();


                //这里拿需求
                var batchPo = await _BatchConsumeRequirementEntityDal.FindList(p => p.BatchId == reqModel.BatchId);
                //这里拿需求
                if (batchPo == null || batchPo.Count <= 0)
                {
                    resultList = new List<BBatchDetailMaterialViewEntity>();
                }
                else
                {
                    var oCon = batchPo.GroupBy(p => p.PoConsumeRequirementId).Select(p => p.Key).ToArray();
                    var listConsumeReq = await _PPoConsumeRequirementEntitDal.Db.Queryable<PoConsumeRequirementEntity>()
                        .In(p => p.ID, oCon).Where(p => p.ChangeUnit == "g" || p.ChangeUnit == "G").ToListAsync();
                    if (listConsumeReq != null & listConsumeReq.Count > 0)
                    {
                        List<string> mIDS = listConsumeReq.GroupBy(p => p.MaterialId).Select(p => p.Key).ToList();

                        for (int i = 0; i < resultList.Count; i++)
                        {
                            string mID = resultList[i].MaterialId;
                            if (mIDS.Contains(mID))
                            {
                                resultList[i].InQuantity = Math.Round(resultList[i].InQuantity, 6) * 1000;
                                resultList[i].MaterialUnit1 = "g";
                            }
                        }
                    }
                }

                //if (reqModel.ChangeUnit == "g" && !string.IsNullOrEmpty(reqModel.MATERIAL_CODE))
                //{
                //    for (int i = 0; i < resultList.Count; i++)
                //    {
                //        if (resultList[i].MaterialCode == reqModel.MATERIAL_CODE)
                //        {
                //            resultList[i].InQuantity = Math.Round(Convert.ToDecimal(resultList[i].InQuantity), 3);
                //        }
                //    }
                //}


                // PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
                var results1 = resultList.ToPageModel(reqModel.pageIndex, reqModel.pageSize, c => c.ModifyDate,
                    OrderType.Asc);
                int pageIndex = reqModel.pageIndex; // 当前页码，从1开始
                int pageSize = reqModel.pageSize; // 每页的数据数量
                // resultList = resultList.Skip((pageIndex - 1) * pageSize).Take(pageSize).ToList();

                return results1;
            }
            else
            {
                var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
                    .Where(whereExpression).Where(p => p.HType == null || p.HType == "")
                    .OrderByDescending(p => p.CreateDate)
                    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
                result.dataCount = dataCount;
                result.data = data;
            }

            return result;

            //PageModel<BBatchDetailMaterialViewEntity> result = new PageModel<BBatchDetailMaterialViewEntity>();
            //RefAsync<int> dataCount = 0;
            //var whereExpression = Expressionable.Create<BBatchDetailMaterialViewEntity>().
            //    AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), a => a.MaterialId == reqModel.MaterialId).AndIF(!string.IsNullOrEmpty(reqModel.EquipmentId), a => a.EquipmentId == reqModel.EquipmentId)
            //     .AndIF(!string.IsNullOrEmpty(reqModel.SbSscc), a => a.SbSscc == reqModel.SbSscc)
            //                 .ToExpression();

            //var data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
            //    .Where(whereExpression)
            //    .Where(p => p.ProductionOrderNo == null || p.ProductionOrderNo == "")
            //    .Where(p => p.BatchCode == null || p.BatchCode == "").OrderByDescending(p => p.CreateDate)
            //    .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);

            //if (ProOrderid == true && Batchid == true)
            //{
            //    data = await _dalBBatchDetailMaterialViewEntity.Db.Queryable<BBatchDetailMaterialViewEntity>()
            //        .Where(whereExpression).Where(p => p.PId == reqModel.ProOrderid && p.BatchCode == reqModel.BatchId).OrderByDescending(p => p.CreateDate).OrderByDescending(p => p.CreateDate)
            //        .ToPageListAsync(reqModel.pageIndex, reqModel.pageSize, dataCount);
            //}
            //result.dataCount = dataCount;
            //result.data = data;
            //return result;
        }


        public async Task<List<MBatchriiViewEntity>> GetSegmentList_CLBL(MBatchriiViewRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<MBatchriiViewEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.ProductionOrderId), a => a.ID.Equals(reqModel.ProductionOrderId))
                .ToExpression();
            var data = await _SegmentlistEntitydal.Db.Queryable<MBatchriiViewEntity>()
                .Where(whereExpression).ToListAsync();
            return data;
        }

        /// <summary>
        /// 备料跳转第一界面根据proOrderID、EquipmentId查询批次
        /// </summary>
        /// <param name="proOrderId"></param>
        /// <returns></returns>
        public async Task<List<BclblDetailViewEntity>> GetByList_CLBL(string proOrderId, string eqpmentid)
        {
            List<BclblDetailViewEntity> result = new List<BclblDetailViewEntity>();
            RefAsync<int> dataCount = 0;
            // var whereExpression = Expressionable.Create<BclblDetailViewEntity>()
            //    .AndIF(!string.IsNullOrEmpty(proOrderId), a => a.ProductionOrderId == proOrderId)
            //    .AndIF(!string.IsNullOrEmpty(eqpmentid), a => a.EquipmentId == eqpmentid)
            //               .ToExpression();
            var data = await _bclblDetailViewEntityDal.Db.Queryable<BclblDetailViewEntity>()
                .Where(p => p.ProductionOrderId == proOrderId).OrderBy(p => p.BatchNumber).ToListAsync();
            return data.Where(p => p.EquipmentId == eqpmentid).OrderBy(p => p.BatchNumber).ToList();
        }


        /// <summary>
        /// 称量备料，备料明细页面
        /// </summary>
        /// <param name="batchID"></param>
        /// <param name="eqpmentID"></param>
        /// <returns></returns>
        public async Task<List<ClblDiiViewEntity>> GetPrepareationListByBatchID(string batchID, string eqpmentID)
        {
            var query = await _dal.Db
                .Queryable<PoConsumeRequirementEntity, BatchConsumeRequirementEntity, EquipmentMaterialEntity,
                    MaterialInventoryEntity, ContainerEntity, MaterialLotEntity, MaterialEntity, UnitmanageEntity>((t1,
                        t2, t3, t4, t5, t6, t7, t8) =>
                    new object[]
                    {
                        // 左连接 t2 (BatchConsumeRequirementEntity) 到 t1
                        JoinType.Left, t1.ID == t2.PoConsumeRequirementId,
                        // 内连接 t3 (EquipmentEntity) 到 t1
                        JoinType.Inner, t1.MaterialId == t3.ID,
                        // 左连接 t4 (MaterialInventoryEntity) 到 t2
                        JoinType.Left, t2.ID == t4.BatchConsumeRequirementId,
                        // 左连接 t5 (ContainerEntity) 到 t2 和 t4
                        JoinType.Left, t5.ProductionBatchId == t2.BatchId && t4.ContainerId == t5.ID,
                        // 左连接 t6 (MaterialLotEntity) 到 t4
                        JoinType.Left, t4.LotId == t6.ID,
                        JoinType.Left, t1.MaterialId == t7.ID,
                        JoinType.Left, t7.Unit == t8.ID
                    })
                .Where((t1, t2, t3, t4, t5, t6, t7, t8)
                    => t3.ID == eqpmentID)
                .Where((t1, t2, t3, t4, t5, t6, t7, t8)
                    => t2.BatchId == batchID)
                .GroupBy((t1, t2, t3, t4, t5, t6, t7, t8)
                    => new
                    {
                        t1.MaterialId,
                        t1.MaterialCode,
                        t1.MaterialDescription,
                        t2.BatchId,
                        t4.InventoryType,
                        t1.WeighingQty,
                        t8.Shortname,
                    })
                .Select((t1, t2, t3, t4, t5, t6, t7, t8) => new ClblDiiViewEntity
                {
                    MaterialId = t1.MaterialId,
                    MCode = t1.MaterialCode,
                    MName = t1.MaterialDescription,
                    BatchId = t2.BatchId,
                    MQuantity = SqlFunc.AggregateSum(t1.WeighingQty),
                    MQuantityTotal = t1.WeighingQty ?? 0m,
                    MaterialUnit1 = t8.Shortname
                }).ToListAsync();
            if (query.Count == 0) return query;
            List<string> materialIdList = query.Select(p => p.MaterialId).ToList();
            if (materialIdList.Count == 0) return query;

            var materialPropertyTask = _materialPropertyDal.Db
                .Queryable<MaterialPropertyValueEntity>()
                .Where(a => materialIdList.Contains(a.MaterialId))
                .ToListAsync();

            var propertyTask = _classDal.Db
                .Queryable<PropertyEntity, ClassEntity>((t1, t2) => new object[]
                {
                    JoinType.Left, t1.ClassId == t2.ID,
                })
                .Where((t1, t2) => t2.ClassCode == "MaterialClass")
                .ToListAsync();

            // 并行执行两个查询
            await Task.WhenAll(materialPropertyTask, propertyTask);

            // 获取结果
            List<MaterialPropertyValueEntity> materialPropertyList = materialPropertyTask.Result;

            List<PropertyEntity> propertyList = propertyTask.Result;

            query.ForEach(p =>
            {
                List<MaterialPropertyValueEntity> materialPropertyValueList =
                    materialPropertyList.Where(a => a.MaterialId == p.MaterialId).ToList();

                #region 计算是否完成备料

                // 获取原始百分比字符串值
                var maxPercentStr = GetMaterialPropertyValue(p.MaterialId,
                    MaterialPreparationViewServices.PREWEIGH_TOLERANCE_MAX_PERCENT,
                    materialPropertyValueList,
                    propertyList);

                // 转换为小数百分比（10% -> 0.1m）
                var PreweighToleranceMaxPercent = decimal.Parse(maxPercentStr) / 100m;

                // 后续运算示例：
                p.MaxPvalue = p.MQuantityTotal * (1 + PreweighToleranceMaxPercent);
                p.SetMax = Convert.ToInt32(maxPercentStr);

                // 获取原始百分比字符串值
                var minPercentStr = GetMaterialPropertyValue(p.MaterialId,
                    MaterialPreparationViewServices.PREWEIGH_TOLERANCE_MIN_PERCENT,
                    materialPropertyValueList,
                    propertyList);

                // 转换为小数百分比（10% -> 0.1m）
                var PreweighToleranceMinPercent = decimal.Parse(minPercentStr) / 100m;

                // 后续运算示例：
                p.MinPvalue = p.MQuantityTotal * (1 - PreweighToleranceMinPercent);
                p.SetMin = Convert.ToInt32(minPercentStr);

                p.CompleteStates = "";
                if (p.MQuantity >= p.MinPvalue && p.MQuantity <= p.MaxPvalue)
                {
                    p.CompleteStates = "OK";
                }

                #endregion

                // var fullBagSizeStr = GetMaterialPropertyValue(p.MaterialId,
                //     MaterialPreparationViewServices.FULL_BAG_WEIGHT, materialPropertyList, propertyList);
                // p.BagSize = fullBagSizeStr;
                //
                //
                // var checkState = GetMaterialPropertyValue(p.MaterialId,
                //     MaterialPreparationViewServices.NEED_WEIGHING_CHECK, materialPropertyList, propertyList);
                // p.CheakState = int.Parse(checkState) == 1 ? "是" : "否";
                //
                // p.BagS = Convert.ToInt32(p.FullFinish / decimal.Parse(p.BagSize));
            });
            return query;
        }

        /// <summary>
        /// 根据物料ID、属性编码获取属性值
        /// </summary>
        /// <param name="materialId"></param>
        /// <param name="propertyCode"></param>
        /// <param name="materialPropertyList"></param>
        /// <param name="propertyList"></param>
        /// <returns></returns>
        private string GetMaterialPropertyValue(string materialId, string propertyCode,
            List<MaterialPropertyValueEntity> materialPropertyList,
            List<PropertyEntity> propertyList)
        {
            // 优先从物料属性列表获取
            var materialProperty = materialPropertyList.FirstOrDefault(p =>
                p.MaterialId == materialId &&
                p.PropertyCode == propertyCode);

            if (materialProperty != null)
            {
                return materialProperty.PropertyValue;
            }

            // 如果物料属性不存在，从属性主表获取默认值
            var defaultProperty = propertyList.FirstOrDefault(p =>
                p.PropertyCode == propertyCode);

            return defaultProperty?.DefaultValue;
        }


        /// <summary>
        /// 称量备料 第二界面的明细
        /// </summary>
        /// <param name="batchID"></param>
        /// <param name="eqpmentID"></param>
        /// <returns></returns>
        public async Task<List<ClblDiiViewEntity>> GetListByBatchID_CLBL(string batchID, string eqpmentID)
        {
            List<MpModel> mpList = await Get_DisMaterial(eqpmentID);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new List<ClblDiiViewEntity>();
            }

            List<ClblDiiViewEntity> result = new List<ClblDiiViewEntity>();
            RefAsync<int> dataCount = 0;
            //var whereExpression = Expressionable.Create<ClblDiiViewEntity>().
            //    AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID).And(p => p.MQuantityTotal > 0).And(p => !p.IsShow.Contains("1"))
            //                 .ToExpression();
            //List<ClblDiiViewEntity> data = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.MaterialId, mIDs)
            //    .Where(whereExpression).ToListAsync();


            //var whereExpression = Expressionable.Create<ClblDiiViewEntity>().
            //   AndIF(!string.IsNullOrEmpty(batchID), a => a.BatchId == batchID).And(p => p.MQuantityTotal > 0).And(p => !p.IsShow.Contains("1"))
            //                .ToExpression();
            //List<ClblDiiViewEntity> data = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.MaterialId, mIDs)
            //    .Where(whereExpression).ToListAsync();

            List<ClblDiiViewEntity> data = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>()
                .Where(a => a.BatchId == batchID).ToListAsync();
            List<string> lMIDS = mIDs.ToList();
            data = data.Where(p => lMIDS.Contains(p.MaterialId))
                .Where(p => p.MQuantityTotal > 0 && !p.IsShow.Contains("1")).ToList();
            var resultData = (from a in data
                group a by new
                {
                    a.ID,
                    a.ModifyDate,
                    a.CreateDate,
                    a.ModifyUserId,
                    a.CreateUserId,
                    //  a.UpdateTimeStamp,

                    a.EquipmentId,
                    a.OnlyId,
                    a.ProductionOrderNo,
                    a.FormulaNo,
                    //   a.EquipmentName,
                    a.MBatchNumber,
                    a.MName,
                    a.MCode,
                    //  a.MQuantity,
                    a.MQuantityunit,
                    a.MQuantityTotal,
                    a.QuantityTotalUnit,
                    a.TUintid,
                    a.InQuantity,
                    a.MaterialUnit1,
                    a.BagSize,
                    a.FullPage,
                    // a.TagpS,
                    a.ParitialPage,
                    a.TagpSUnit,
                    a.FullFinish,
                    a.BagS,
                    a.CompleteStates,
                    a.ConsumedStates,
                    a.MaterialId,
                    a.MinPvalue,
                    a.SetMin,
                    a.MaxPvalue,
                    a.SetMax,
                    a.ConSumed,
                    a.BatchId,
                    a.ProductionOrderId
                }
                into g
                select new ClblDiiViewEntity
                {
                    MQuantity = g.Sum(g => g.MQuantity),
                    ID = g.Key.ID,
                    ModifyDate = g.Key.ModifyDate,
                    CreateDate = g.Key.CreateDate,
                    ModifyUserId = g.Key.ModifyUserId,
                    CreateUserId = g.Key.CreateUserId,
                    // UpdateTimeStamp = g.Key.UpdateTimeStamp,
                    EquipmentId = g.Key.EquipmentId,
                    OnlyId = g.Key.OnlyId,
                    ProductionOrderNo = g.Key.ProductionOrderNo,
                    FormulaNo = g.Key.FormulaNo,
                    //   EquipmentName = g.Key.EquipmentName,
                    MBatchNumber = g.Key.MBatchNumber,
                    MName = g.Key.MName,
                    MCode = g.Key.MCode,
                    MQuantityTotal = g.Key.MQuantityTotal,
                    MQuantityunit = g.Key.MQuantityunit,
                    QuantityTotalUnit = g.Key.QuantityTotalUnit,
                    TUintid = g.Key.TUintid,
                    InQuantity = g.Key.InQuantity,
                    MaterialUnit1 = g.Key.MaterialUnit1,
                    BagSize = g.Key.BagSize,
                    FullPage = g.Key.FullPage,
                    // TagpS = g.Key.TagpS,
                    ParitialPage = g.Key.ParitialPage,
                    TagpSUnit = g.Key.TagpSUnit,
                    FullFinish = g.Key.FullFinish,
                    BagS = g.Key.BagS,
                    CompleteStates = g.Key.CompleteStates,
                    ConsumedStates = g.Key.ConsumedStates,
                    MaterialId = g.Key.MaterialId,
                    MinPvalue = g.Key.MinPvalue,
                    SetMin = g.Key.SetMin,
                    MaxPvalue = g.Key.MaxPvalue,
                    SetMax = g.Key.SetMax,
                    ConSumed = g.Key.ConSumed,
                    BatchId = g.Key.BatchId,
                    ProductionOrderId = g.Key.ProductionOrderId
                }).ToList();

            //循环一次判断g
            for (int i = 0; i < resultData.Count; i++)
            {
                //这里再次判断是否完成
                var CompleteStates = resultData[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    if (resultData[i].MQuantity >= resultData[i].MinPvalue &&
                        resultData[i].MQuantity <= resultData[i].MaxPvalue)
                    {
                        resultData[i].CompleteStates = "OK";
                    }
                }

                string unit = resultData[i].MQuantityunit;
                if (unit == "g" || unit == "G")
                {
                    resultData[i].MQuantityTotal = Math.Round(resultData[i].MQuantityTotal.Value * 1000, 3);
                    resultData[i].MQuantity = Math.Round(resultData[i].MQuantity.Value * 1000, 3);
                    ;
                }
            }

            return resultData;
        }


        public async Task<PageModel<InventObj>> GetInventState(BBatchDetailIIModel model)
        {
            PageModel<InventObj> result = new PageModel<InventObj>();
            RefAsync<int> dataCount = 0;

            try
            {
                List<InventObj> list = new List<InventObj>();
                if (string.IsNullOrEmpty(model.SSCC))
                {
                    return result;
                }

                //拿数据
                var resultMsg = await _InventorylistingViewEntitydal.FindEntity(p =>
                    p.Sscc == model.SSCC && string.IsNullOrEmpty(p.ContainerId) && p.MaterialCode == model.MCode);
                if (resultMsg == null)
                {
                    return result;
                }

                List<InventObj> obj = new List<InventObj>();

                InventObj models = new InventObj();
                models.InQuantity = resultMsg.Quantity.Value;
                models.ID = resultMsg.ID;
                models.SubId = resultMsg.SlotId;
                models.SbSscc = resultMsg.Sscc;
                if (string.IsNullOrEmpty(resultMsg.ProductionOrderNo))
                {
                    //这里是可用库存
                    models.Remark = "ky";
                }
                else
                {
                    //这里是工单库存
                    models.Remark = "gd";
                }

                list.Add(models);
                result.dataCount = list.Count;
                result.data = list;
                return result;
            }
            catch (Exception)
            {
                return result;
            }
        }


        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="batchIDS">批次ID组</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        public async Task<PageModel<ClblDiiViewEntity>> GetPageListByBatchIDS_CLBL(BBatchDetailIIModel model)
        {
            PageModel<ClblDiiViewEntity> result = new PageModel<ClblDiiViewEntity>();
            RefAsync<int> dataCount = 0;

            List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<ClblDiiViewEntity>();
            }

            //  var listInvent = await _dalInventKcViewEntity.FindList(p => p.ID != null);

            var listInvent = await _dalClblDiiViewEntity.Db.Queryable<InvnetqtyKyViewEntity>()
                .Where(p => p.EquipmentId == model.EquipmentId).In(p => p.MaterialId, mIDs).ToListAsync();

            //var whereExpression = Expressionable.Create<ClblDiiViewEntity>().
            //    And(p => p.MQuantityTotal > 0).And(p => !p.IsShow.Contains("1")).ToExpression();
            //var data1 = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.BatchId, model.ID).In(p => p.MaterialId, mIDs)
            //   .Where(whereExpression).OrderBy(p => p.MaterialId).ToListAsync();


            List<string> lMIDS = mIDs.ToList();
            var data1 = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.BatchId, model.ID)
                .ToListAsync();
            data1 = data1.Where(p => lMIDS.Contains(p.MaterialId))
                .Where(p => p.MQuantityTotal > 0 && !p.IsShow.Contains("1")).ToList();

            var data = (from a in data1
                group a by new
                {
                    a.Remark,
                    a.ChangeUnit,
                    a.CheakState,
                    a.PrepStatuscount,
                    a.ID,
                    a.ModifyDate,
                    a.CreateDate,
                    a.ModifyUserId,
                    a.CreateUserId,
                    a.EquipmentId,
                    a.OnlyId,
                    a.ProductionOrderNo,
                    a.FormulaNo,
                    a.LineCode,
                    //   a.EquipmentName,
                    a.MBatchNumber,
                    a.MName,
                    a.MCode,
                    a.MQuantityunit,
                    a.MQuantityTotal,
                    a.QuantityTotalUnit,
                    a.TUintid,
                    a.InQuantity,
                    a.MaterialUnit1,
                    a.BagSize,
                    a.FullPage,
                    a.ParitialPage,
                    a.TagpSUnit,
                    a.FullFinish,
                    a.BagS,
                    a.CompleteStates,
                    a.ConsumedStates,
                    a.MaterialId,
                    a.MinPvalue,
                    a.SetMin,
                    a.MaxPvalue,
                    a.SetMax,
                    a.ConSumed,
                    a.BatchId,
                    a.ProductionOrderId,
                    a.Sequencetotal,
                }
                into g
                select new ClblDiiViewEntity
                {
                    Remark = g.Key.Remark,
                    ChangeUnit = g.Key.ChangeUnit,
                    CheakState = g.Key.CheakState,
                    PrepStatuscount = g.Key.PrepStatuscount,
                    MQuantity = g.Sum(g => g.MQuantity),
                    TagpS = g.Sum(g => g.TagpS),
                    ID = g.Key.ID,
                    ModifyDate = g.Key.ModifyDate,
                    CreateDate = g.Key.CreateDate,
                    ModifyUserId = g.Key.ModifyUserId,
                    CreateUserId = g.Key.CreateUserId,
                    EquipmentId = g.Key.EquipmentId,
                    OnlyId = g.Key.OnlyId,
                    ProductionOrderNo = g.Key.ProductionOrderNo,
                    FormulaNo = g.Key.FormulaNo,
                    LineCode = g.Key.LineCode,
                    //    EquipmentName = g.Key.EquipmentName,
                    MBatchNumber = g.Key.MBatchNumber,
                    MName = g.Key.MName,
                    MCode = g.Key.MCode,
                    MQuantityTotal = g.Key.MQuantityTotal,
                    MQuantityunit = g.Key.MQuantityunit,
                    QuantityTotalUnit = g.Key.QuantityTotalUnit,
                    TUintid = g.Key.TUintid,
                    InQuantity = g.Key.InQuantity,
                    MaterialUnit1 = g.Key.MaterialUnit1,
                    BagSize = g.Key.BagSize,
                    FullPage = g.Key.FullPage,
                    ParitialPage = g.Key.ParitialPage,
                    TagpSUnit = g.Key.TagpSUnit,
                    FullFinish = g.Key.FullFinish,
                    BagS = g.Key.BagS,
                    CompleteStates = g.Key.CompleteStates,
                    ConsumedStates = g.Key.ConsumedStates,
                    MaterialId = g.Key.MaterialId,
                    MinPvalue = g.Key.MinPvalue,
                    SetMin = g.Key.SetMin,
                    MaxPvalue = g.Key.MaxPvalue,
                    SetMax = g.Key.SetMax,
                    ConSumed = g.Key.ConSumed,
                    BatchId = g.Key.BatchId,
                    Sequencetotal = g.Key.Sequencetotal,
                    ProductionOrderId = g.Key.ProductionOrderId
                }).ToList();

            // 这里重新查询库存信息
            string mCode = string.Empty;
            string changeUint = string.Empty;
            for (int i = 0; i < data.Count; i++)
            {
                changeUint = data[i].ChangeUnit;
                mCode = data[i].MCode;
                var invnetModel = listInvent.Where(p => p.MaterialCode == mCode).ToList();
                if (invnetModel == null || invnetModel.Count == 0)
                {
                    data[i].InQuantity = 0;
                }
                else
                {
                    data[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
                    data[i].InQuantity = invnetModel.Sum(p => p.Quantity);
                }

                //这里再次判断是否完成
                var CompleteStates = data[i].CompleteStates;
                if (CompleteStates != "OK")
                {
                    if (data[i].MQuantity >= data[i].MinPvalue && data[i].MQuantity <= data[i].MaxPvalue)
                    {
                        data[i].CompleteStates = "OK";
                    }
                }

                if (changeUint == "g" || changeUint == "G")
                {
                    data[i].ChangeUnit = "g";
                    data[i].MaterialUnit1 = "g";
                    data[i].TagpSUnit = "g";
                    data[i].QuantityTotalUnit = "g";
                    data[i].MQuantityunit = "g";
                    //data[i].MQuantity = data[i].MQuantity * 1000;

                    data[i].TagpS = data[i].TagpS * 1000;
                    data[i].ParitialPage =
                        (Convert.ToDecimal(data[i].ParitialPage) * Convert.ToDecimal(1000)).ToString();
                    data[i].MinPvalue = data[i].MQuantityTotal * 1000 -
                                        (data[i].MQuantityTotal * 1000 / 100 * data[i].SetMin);
                    data[i].MaxPvalue = data[i].MQuantityTotal * 1000 +
                                        (data[i].MQuantityTotal * 1000 / 100 * data[i].SetMax);
                    data[i].MQuantity = data[i].MQuantity * 1000;
                    data[i].BagSize = (Convert.ToInt32(data[i].BagSize) * 1000).ToString();
                    data[i].MQuantityTotal = data[i].MQuantityTotal * 1000;
                    //data[i].MaterialUnit1 = changeUint;
                }

                ////这里进行单位转换
                //if (data[i].ChangeUnit == "g") 
                //{
                //    data[i].TagpS = Math.Round(Convert.ToDecimal(data[i].TagpS), 3); ;
                //    data[i].ParitialPage = Math.Round(Convert.ToDecimal(data[i].ParitialPage), 3); ;
                //    data[i].MaxPvalue = Math.Round(Convert.ToDecimal(data[i].MaxPvalue), 3); ;
                //    data[i].MinPvalue = Math.Round(Convert.ToDecimal(data[i].MinPvalue), 3); ;
                //    data[i].MQuantityTotal = Math.Round(Convert.ToDecimal(data[i].MQuantityTotal), 3); ;
                //}
            }


            var data2 = data;

            int startIndex = (model.pageIndex - 1) * model.pageSize; // 计算开始的索引          
            var rDat = data2.OrderBy(p => p.ProductionOrderNo).ThenBy(p => p.Sequence).Skip(startIndex)
                .Take(model.pageSize).ToList();
            result.dataCount = data2.Count;
            result.data = rDat;
            return result;

            //var resultData = data.ToPageModel(model.pageIndex, model.pageSize, c => c.ProductionOrderNo, OrderType.Asc);
            //result.dataCount = data.Count;
            //result.data = resultData.data;
            //return result;
            //.ToPageListAsync(model.pageIndex, model.pageSize, dataCount);

            //这里重新查询库存信息
            //string mCode = string.Empty;
            //for (int i = 0; i < data.Count; i++)
            //{
            //    mCode = data[i].MCode;
            //    var invnetModel = listInvent.Where(p => p.MaterialCode == mCode && p.EquipmentId == model.EquipmentId).ToList();
            //    if (invnetModel == null || invnetModel.Count == 0)
            //    {
            //        data[i].InQuantity = 0;
            //    }
            //    else
            //    {
            //        data[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
            //        data[i].InQuantity = invnetModel.Sum(p => p.Quantity);
            //    }

            //}

            ////result.dataCount = dataCount;
            ////result.data = data;
            //return result;
        }


        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="batchIDS">批次ID组</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        public async Task<PageModel<ClblDiiViewEntity>> GetPageListByBatchIDS_CLBLBYID(BBatchDetailIIModel model)
        {
            List<MpModel> mpList = await Get_DisMaterial(model.EquipmentId);

            string[] mIDs = mpList.Select(mp => mp.MaterialID).ToArray();
            if (mIDs == null || mIDs.Length <= 0)
            {
                return new PageModel<ClblDiiViewEntity>();
            }

            PageModel<ClblDiiViewEntity> result = new PageModel<ClblDiiViewEntity>();
            RefAsync<int> dataCount = 0;

            var listInvent = await _dalInventKcViewEntity.FindList(p => p.ID != null);
            var whereExpression = Expressionable.Create<ClblDiiViewEntity>().And(p => p.MQuantityTotal > 0)
                .ToExpression();
            var data = await _dalClblDiiViewEntity.Db.Queryable<ClblDiiViewEntity>().In(p => p.BatchId, model.ID)
                .In(p => p.MaterialId, mIDs)
                .Where(whereExpression).OrderByDescending(p => p.CreateDate)
                .ToPageListAsync(model.pageIndex, model.pageSize, dataCount);

            //这里重新查询库存信息

            string mCode = string.Empty;
            for (int i = 0; i < data.Count; i++)
            {
                mCode = data[i].MCode;
                var invnetModel = listInvent.Where(p => p.MaterialCode == mCode && p.EquipmentId == model.EquipmentId)
                    .ToList();
                if (invnetModel == null || invnetModel.Count == 0)
                {
                    data[i].InQuantity = 0;
                }
                else
                {
                    data[i].MaterialUnit1 = invnetModel[0].MaterialUnit1;
                    data[i].InQuantity = invnetModel.Sum(p => p.Quantity);
                }
            }

            result.dataCount = dataCount;
            result.data = data;
            return result;
        }


        #region 关联筛选数据

        /// <summary>
        /// 筛选当前节点下可用物料 02406241-3593-4874-163e-0370f6000000
        /// </summary>
        /// <param name="eqpID"></param>
        /// <returns></returns>
        public async Task<List<MpModel>> Get_DisMaterial(string eqpID)
        {
            List<MpModel> list = new List<MpModel>();
            ///查询数据

            var eqmMaterial = await _dalEquipmentMaterialEntity.FindList(P => P.EquipmentId == eqpID);

            if (!string.IsNullOrEmpty(eqpID))
            {
                eqmMaterial = eqmMaterial.Where(P => P.EquipmentId == eqpID).ToList();
            }


            if (eqmMaterial != null && eqmMaterial.Count > 0)
            {
                for (int i = 0; i < eqmMaterial.Count; i++)
                {
                    string mID = eqmMaterial[i].MaterialId;

                    if (string.IsNullOrEmpty(mID))
                    {
                        continue;
                    }

                    MpModel model = new MpModel();
                    model.MaterialID = mID;
                    model.EquipmentId = eqmMaterial[i].EquipmentId;
                    list.Add(model);
                }
            }

            var materialGropu = await _dalMaterialGroupMappingEntity.FindList(p => p.ID != null);

            List<MpModel> result = (from a in eqmMaterial
                join b in materialGropu on a.GroupId equals b.MaterialGroupId
                where a.Type == "Include" && a.GroupId != null && a.GroupId != ""
                group a by new
                {
                    a.EquipmentId,
                    b.MaterialId
                }
                into g
                select new MpModel
                {
                    EquipmentId = g.Key.EquipmentId,
                    MaterialID = g.Key.MaterialId
                }).ToList();

            //合并
            list.AddRange(result);
            //去重复
            list = list.Distinct().OrderBy(p => p.MaterialID).ToList();

            return list;
        }

        #endregion

        #endregion

        #region 打印类

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="prinitID">模板ID</param>
        /// <param name="quipmentId">room</param>
        /// <param name="listobj">数据集合</param>
        /// <param name="dataList">数据表头用[0]</param>
        /// <param name="size">几张</param>
        /// <param name="teampType">PrintTemplete  库存标签打印 PrintBagTemplete  备料包库存标签模板   PrintPalletTemplete     备料托盘库存标签模板        
        /// <returns></returns>
        public async Task<MessageModel<string>> PrintCodeByEquipmentId(string prinitID, string quipmentId,
            List<object> listobj, object dataList, int size, string teampType)
        {
            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                ////PrintTemplete                   库存标签打印
                ////PrintBagTemplete                备料包库存标签模板
                ////PrintPalletTemplete             备料托盘库存标签模板
                var models = await GetTeampID(quipmentId, teampType);
                if (models == null)
                {
                    pResult.msg = "请配置打印机";
                    return pResult;
                }

                string templateID = models.ID;
                string templateclassID = models.TemplateClassId;
                ;

                Dictionary<string, object> dic = new Dictionary<string, object>();

                #region 获取列名

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数

                #region 打印机id

                #region 打印实体

                string token = _uIser.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion

                //获取数据
                var printResult =
                    await HttpHelper.PostAsync<List<LabelPrinterEntity>>("DFM",
                        "api/LabelPrinter/GetList", token, model);
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].

                        #region 查询Temp数据源

                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, templateID, templateclassID, dic,
                            size, pSizeId, equID);
                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = "打印成功";
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion
        }


        /// <summary>
        /// 获取指定列数据源
        /// </summary>
        /// <param name="obj"></param>
        /// <param name="propertyName"></param>
        /// <returns></returns>
        public static object GetValueByReflection(object obj, string propertyName)
        {
            // 使用反射获取属性值
            PropertyInfo property = obj.GetType().GetProperty(propertyName);
            return property?.GetValue(obj, null);
        }

        /// <summary>
        /// 获取列名
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="list"></param>
        /// <returns></returns>
        public static List<string> GetColumnNames<T>(List<T> list)
        {
            if (list.Count == 0)
                return new List<string>();
            ;
            PropertyInfo[] properties = list[0].GetType().GetProperties();
            List<string> columnNames = new List<string>();

            foreach (PropertyInfo property in properties)
            {
                columnNames.Add(property.Name);
            }

            return columnNames;
        }

        /// <summary>
        /// 根据设备ID拿对应属性值
        /// </summary>
        /// <param name="equipmentId"></param>
        /// <param name="propertyCode"></param>
        /// <returns></returns>
        public async Task<LabelTempleteEntity> GetTeampID(string equipmentId, string propertyCode)
        {
            LabelTempleteEntity model = new LabelTempleteEntity();
            try
            {
                var value = string.Empty;
                //获取allData
                MessageModel<List<EquFunctionPropertyModel>> apiResult_EquFunctionPropertys =
                    await HttpHelper.PostAsync<List<EquFunctionPropertyModel>>("DFM",
                        "api/FunctionPropertyValue/GetEquActiveFunctionPropertyValueList", _uIser.GetToken(),
                        new { EquipmentId = equipmentId });
                var equFunctionPropertys = apiResult_EquFunctionPropertys.response;
                if (equFunctionPropertys?.Count > 0)
                {
                    foreach (var item in equFunctionPropertys)
                    {
                        var pr = item.ActiveFunctionPropertyList?.Find(x => x.PropertyCode == propertyCode);
                        if (pr != null)
                        {
                            value = !string.IsNullOrEmpty(pr.ActualValue) ? pr.ActualValue : pr.DefaultValue;
                        }

                        if (!string.IsNullOrEmpty(value))
                        {
                            break;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(value))
                {
                    //获取对应的模板ID
                    MessageModel<List<LabelTempleteEntity>> api_LabelTeamp =
                        await HttpHelper.PostAsync<List<LabelTempleteEntity>>("DFM",
                            "api/LabelTemplete/GetList", _uIser.GetToken(), new { Code = value });
                    var data_LabelTeamp = api_LabelTeamp.response;

                    if (data_LabelTeamp?.Count > 0)
                    {
                        model = data_LabelTeamp[0];
                        return model;
                    }
                }

                return model;
            }
            catch
            {
                return model;
            }
        }

        /// <summary>
        /// 打印数据
        /// </summary>
        /// <param name="prinitID">打印id</param>
        /// <param name="teampID">模板Code</param>
        /// <param name="teampClassID">类型code</param>
        /// <param name="listobj">数据源</param>
        /// <param name="dataList">数据列</param>
        /// <returns></returns>
        public async Task<MessageModel<string>> PrintByCode(string prinitID, string teampID, string teampClassID,
            List<object> listobj, object dataList)
        {
            var pResult = new MessageModel<string>();
            pResult.success = false;

            try
            {
                Dictionary<string, object> dic = new Dictionary<string, object>();

                #region 获取列名

                List<string> columnNames = GetColumnNames<object>(listobj);

                foreach (string columnName in columnNames)
                {
                    object value = GetValueByReflection(dataList, columnName);
                    //绑定dic
                    dic.Add(columnName, value);
                }

                #endregion

                string equID = "";
                string pClassID = "";
                string pCode = string.Empty;
                string pSizeId = string.Empty;
                string printID = string.Empty;
                string teamID = string.Empty;

                //查询打印等参数

                #region 打印机id

                #region 打印实体

                string token = _uIser.GetToken();
                LabelPrinterRequestModel model = new LabelPrinterRequestModel();
                model.ID = prinitID;
                model.pageIndex = 1;
                model.pageSize = 1000;

                #endregion

                #region 模板实体

                string tempData = string.Empty;
                string tempID = string.Empty;

                #endregion

                //获取数据
                var printResult =
                    await HttpHelper.PostAsync<List<LabelPrinterEntity>>("DFM",
                        "api/LabelPrinter/GetList", token, model);
                if (printResult.success == true)
                {
                    var pritModel = printResult.response;
                    if (pritModel != null && pritModel.Count > 0)
                    {
                        printID = pritModel[0].ID;
                        equID = pritModel[0].EquipmentId;
                        pSizeId = pritModel[0].LabelSizeId;
                        pClassID = pritModel[0].PrinterClassId;
                        pCode = pritModel[0].Code;
                        //teamID= pritModel[0].

                        #region 查询Temp数据源

                        //#region 实体

                        ////构造实体
                        //LabelTempleteRequestModel templeteRequestModel = new LabelTempleteRequestModel();
                        //templeteRequestModel.Code = pritModel[0].Code;
                        //templeteRequestModel.LabelSizeId = pSizeId;
                        //templeteRequestModel.PrinterClassId = pClassID;
                        //templeteRequestModel.pageIndex = 1;
                        //templeteRequestModel.pageSize = 1000;

                        //#endregion


                        //执行打印
                        string printMsg = await CurrencyPrints(printID, pClassID, teampID, teampClassID, dic, 1,
                            pSizeId, equID);

                        if (printMsg.Contains("成功"))
                        {
                            pResult.msg = "创建库存成功，打印成功";
                            pResult.success = true;
                            return pResult;
                        }
                        else
                        {
                            pResult.msg = "创建库存成功" + printMsg;
                            return pResult;
                        }

                        #endregion
                    }
                    else
                    {
                        pResult.msg = "未找到对应打印机";
                    }
                }
                else
                {
                    pResult.msg = "未找到对应打印机";
                }

                return pResult;
            }
            catch (Exception ex)
            {
                pResult.msg = ex.Message;
                return pResult;
            }

            #endregion
        }

        /// <summary>
        /// 打印
        /// </summary>
        /// <param name="printerID">打印机ID</param>
        /// <param name="printerClass">打印机类ID</param>
        /// <param name="templateID">模板ID</param>
        /// <param name="printParam">打印参数</param>
        /// <param name="printNum">打印份数</param>
        /// <param name="labelSizeID">打印尺寸</param>
        /// <param name="eqmID">eqmID</param>
        /// <returns></returns>
        public async Task<string> CurrencyPrints(string printerID, string printerClass, string templateID,
            string templateClassID, Dictionary<string, object> printParam, int printNum, string labelSizeID,
            string eqmID)
        {
            #region 打印实体

            string token = _uIser.GetToken();
            LabelPrinterParamModel model = new LabelPrinterParamModel();
            model.EquipmentId = eqmID;

            model.PrinterId = printerID;
            model.PrinterClassId = printerClass;

            model.TemplateId = templateID;
            model.TemplateClassId = templateClassID;


            model.PrintNum = printNum;
            model.LabelSizeId = labelSizeID;
            model.PrintParameters = printParam;

            #endregion

            var printResult = await HttpHelper.PostAsync<string>("DFM", "api/LabelPrint/Print", token, model);
            if (printResult.success == true)
            {
                return "打印成功";
            }
            else
            {
                return "打印失败" + printResult.msg;
            }
        }

        #endregion
    }
}