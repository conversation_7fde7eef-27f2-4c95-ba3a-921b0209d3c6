{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue", "mtime": 1749177894365}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAkFA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/planManagement/batchDcs", "sourcesContent": ["<!--\n * @Descripttion: (批次DCS下发/PPM_B_BATCH_DCS)\n * @version: (1.0)\n * @Author: (admin)\n * @Date: (2025-05-14)\n * @LastEditors: (admin)\n * @LastEditTime: (2025-05-14)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n      \n      <el-form-item label=\"产线\" prop=\"LineName\">\n        <el-input v-model=\"searchForm.LineName\" placeholder=\"请输入产线名称\" />\n      </el-form-item>\n\n      <el-form-item label=\"工单号\" prop=\"PoNo\">\n        <el-input v-model=\"searchForm.PoNo\" placeholder=\"请输入工单号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"批次号\" prop=\"BatchtNo\">\n        <el-input v-model=\"searchForm.BatchtNo\" placeholder=\"请输入批次号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"物料代码\" prop=\"MaterialCode\">\n        <el-input v-model=\"searchForm.MaterialCode\" placeholder=\"请输入物料代码\" />\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n        <!-- <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n          \n          </template>\n        </el-table-column> -->\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delBatchDcs, getBatchDcsList\n} from \"@/api/planManagement/batchDcs\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\n//import { batchDcsColumn } from '@/columns/planManagement/batchDcs.js';\n\n\nexport default {\n  name: 'index.vue',\n  components: {\n    //UploadButton,\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('BatchDcs.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n      {code: 'LineName', width: 130, align: 'center'},\n      {code: 'PoNo', width: 180, align: 'center'},\n      {code: 'BatchtNo', width: 130, align: 'center'},\n      {code: 'MaterialCode', width: 180, align: 'center'},\n      {code: 'MaterialName', width: 180, align: 'center'},\n      {code: 'Unit', width: 130, align: 'center'},\n      {code: 'StandardQuantity', width: 130, align: 'center'},\n      {code: 'PlanQuantity', width: 130, align: 'center'},\n      {code: 'Status', width: 130, align: 'center'},\n      {code: 'SendData', width: 200, align: 'center'},\n      {code: 'ResponseData', width: 200, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'批次DCS下发',\n        serveIp:'baseURL_PPM',\n        // uploadUrl:'/api/BatchDcs/ImportData', //导入\n        // exportUrl:'/api/BatchDcs/ExportData', //导出\n        // DownLoadUrl:'/api/BatchDcs/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      for (let key in this.hansObj) {\n        this.tableName = getTableHead(this.hansObj, this.tableOption)\n      }\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delBatchDcs([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getBatchDcsList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"BatchDcs\": {\n//    \"table\": {\n//        \"poNo\": \"poNo\",\n//        \"batchtNo\": \"batchtNo\",\n//        \"materialCode\": \"materialCode\",\n//        \"unit\": \"unit\",\n//        \"standardQuantity\": \"standardQuantity\",\n//        \"planQuantity\": \"planQuantity\",\n//        \"status\": \"status\",\n//        \"sendData\": \"sendData\",\n//        \"responseData\": \"responseData\",\n//        \"remark\": \"remark\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}