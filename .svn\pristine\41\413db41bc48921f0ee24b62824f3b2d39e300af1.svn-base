<!--
 * @Descripttion: (文档/目录权限控制表/DFM_B_SOP_PERMISSION)
 * @version: (1.0)
 * @Author: (admin)
 * @Date: (2025-05-09)
 * @LastEditors: (admin)
 * @LastEditTime: (2025-05-09)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            				    
      <el-form-item label="授权对象ID(目录ID/文档ID)" prop="targetId">
        <el-select v-model="searchForm.targetId" placeholder="请选择授权对象ID(目录ID/文档ID)">
          <el-option v-for="item in  targetIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="对象类型(1-目录 2-文档)" prop="targetType">
        <el-select v-model="searchForm.targetType" placeholder="请选择对象类型(1-目录 2-文档)">
          <el-option v-for="item in  targetTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="授权类型(1-用户 2-部门)" prop="grantType">
        <el-select v-model="searchForm.grantType" placeholder="请选择授权类型(1-用户 2-部门)">
          <el-option v-for="item in  grantTypeOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="被授权对象ID" prop="grantId">
        <el-select v-model="searchForm.grantId" placeholder="请选择被授权对象ID">
          <el-option v-for="item in  grantIdOptions" :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
        </el-select>
      </el-form-item>
    				    
      <el-form-item label="权限级别(1-预览 2-下载 4-上传 8-删除)" prop="permLevel">
        <el-input v-model="searchForm.permLevel" placeholder="请输入权限级别(1-预览 2-下载 4-上传 8-删除)" />
      </el-form-item>

        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
            {{ $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
              </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
                        <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20, 50, 100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import FormDialog from './form-dialog'
import {
    delSopPermission, getSopPermissionList
} from "@/api/SOP/sopPermission";
import {getTableHead} from "@/util/dataDictionary.js";

import { sopPermissionColumn } from '@/api/SOP/sopPermission.js';


export default {
  name: 'index.vue',
  components: {
    FormDialog,
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('文档/目录权限控制表.table'),
      tableName: [],
      loading: false,
      tableOption: [],
      mainH: 0,
      buttonOption:{
        name:'文档/目录权限控制表',
        serveIp:'baseURL_SOP',
        uploadUrl:'/api/SopPermission/ImportData', //导入
        exportUrl:'/api/SopPermission/ExportData', //导出
        DownLoadUrl:'/api/SopPermission/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName = getTableHead(this.hansObj, this.tableOption)
      }
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delSopPermission([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData(data) {
      getSopPermissionList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}

//<!-- 移到到src/local/en.json和zh-Hans.json -->
//"SopPermission": {
//    "table": {
//        "targetId": "targetId",
//        "targetType": "targetType",
//        "grantType": "grantType",
//        "grantId": "grantId",
//        "permLevel": "permLevel",
//    }
//},
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>