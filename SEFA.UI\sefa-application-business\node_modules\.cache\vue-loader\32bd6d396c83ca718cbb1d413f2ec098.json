{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue?vue&type=style&index=0&id=563e597a&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue", "mtime": 1749179717467}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIOaWsOWinuagt+W8jwo6ZGVlcCguY2hpbGQtZGlhbG9nKSB7CiAgei1pbmRleDogMjAwMSAhaW1wb3J0YW50OwogIAogIC52LW1vZGFsIHsKICAgIHotaW5kZXg6IDIwMDAgIWltcG9ydGFudDsKICB9Cn0K"}, {"version": 3, "sources": ["bomDetailForm.vue"], "names": [], "mappings": ";AAqIA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "bomDetailForm.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')\" \n    :visible.sync=\"dialogVisible\"\n    width=\"800px\"\n    custom-class=\"child-dialog\"\n    :close-on-click-modal=\"false\" \n    :modal-append-to-body=\"false\" \n    :close-on-press-escape=\"false\"\n    @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n        <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\">\n              <!-- {{ dialogForm.MaterialCode }} - {{ dialogForm.MaterialName }} -->\n              <el-select placeholder=\"请选择新替代物料\" v-model=\"dialogForm.MaterialCode\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"替代物料代码\">\n              {{ dialogForm.InsteadMaterialCode }} - {{ dialogForm.InsteadMaterialName }}\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"12\">\n            <el-form-item label=\"新替代物料\">\n              <el-select placeholder=\"请选择新替代物料\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"\">\n              \n            </el-form-item>\n          </el-col> -->\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <!-- <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table> -->\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getInsteadMaterialList,\n    getWeekScheduleBomDetail,\n    changeMaterial\n  } from \"@/api/planManagement/weekSchedule\";\n  //import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      // MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        currentRow: {},\n        insteadMaterialList:[],\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n      },\n      async initInsteadMaterialList(data) {\n        await getInsteadMaterialList(data).then(res => {\n              console.log(\"initInsteadMaterialList\")\n              console.log(res.response)\n              this.insteadMaterialList = res.response\n            });\n      },      \n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.dialogForm = data\n            this.initInsteadMaterialList(data)\n          }\n        })\n      },\n      materialChange(val) {\n            console.log(val);\n            this.insteadMaterialList.forEach(item => {\n                item.value = item.ID;\n                if(item.MaterialCode == val) {\n                    this.dialogForm.MaterialId  = \"\";\n                    this.dialogForm.MaterialCode  = item.MaterialCode;\n                    this.dialogForm.MaterialName  = item.MaterialName;\n                }\n            });\n          },\n      submit() {\n        console.log(\"bomDetailForm.submit\")\n        console.log(this.dialogForm)\n        this.dialogVisible = false\n        this.$emit('saveForm',this.dialogForm)        \n      },\n      // setMaterial(val){\n      //   // console.log(\"setMaterial\")\n      //   // console.log(val)        \n      //   this.dialogForm.MaterialId = val.ID\n      //   this.dialogForm.MaterialCode = val.Code\n      //   this.dialogForm.MaterialName = val.NAME\n      //   this.$forceUpdate()\n      //   // this.matInfo = val        \n      //   // console.log(this.dialogForm.MaterialCode)\n      //   // console.log(this.dialogForm.MaterialName)\n      // },\n      // openMaterialTable(){\n      //   this.$refs['materialTable'].show()\n      // },\n    }\n  }\n  </script>\n\n<style lang=\"scss\" scoped>\n// 新增样式\n:deep(.child-dialog) {\n  z-index: 2001 !important;\n  \n  .v-modal {\n    z-index: 2000 !important;\n  }\n}\n</style>"]}]}