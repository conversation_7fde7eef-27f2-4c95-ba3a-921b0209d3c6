2025-06-10 09:57:58.003 +08:00 [ERR] 
2025-06-10 09:57:58.188 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:bfad1e3e-5bee-4900-859c-d0cab5ed91ac
Error Number:207,State:1,Class:16
2025-06-10 09:58:27.699 +08:00 [ERR] 
2025-06-10 09:58:27.794 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:bfad1e3e-5bee-4900-859c-d0cab5ed91ac
Error Number:207,State:1,Class:16
2025-06-10 09:59:16.647 +08:00 [ERR] 
2025-06-10 09:59:16.747 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:e7440446-d6dc-43b4-b93e-06f66522ba70
Error Number:207,State:1,Class:16
2025-06-10 10:01:08.765 +08:00 [ERR] 
2025-06-10 10:01:08.858 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:e7440446-d6dc-43b4-b93e-06f66522ba70
Error Number:207,State:1,Class:16
2025-06-10 10:02:00.306 +08:00 [ERR] 
2025-06-10 10:02:00.411 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method1042(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:e7440446-d6dc-43b4-b93e-06f66522ba70
Error Number:207,State:1,Class:16
2025-06-10 10:04:01.639 +08:00 [ERR] 
2025-06-10 10:04:01.920 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:0d747505-7457-41b9-a74a-0ebec77f385c
Error Number:207,State:1,Class:16
2025-06-10 10:04:28.396 +08:00 [ERR] 
2025-06-10 10:04:28.505 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:0d747505-7457-41b9-a74a-0ebec77f385c
Error Number:207,State:1,Class:16
2025-06-10 10:04:40.529 +08:00 [ERR] 
2025-06-10 10:04:40.633 +08:00 [ERR] 列名 'BatchConsumeRequirementId' 无效。
【自定义错误】：列名 'BatchConsumeRequirementId' 无效。 
【异常类型】：SqlException 
【异常信息】：列名 'BatchConsumeRequirementId' 无效。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 列名 'BatchConsumeRequirementId' 无效。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 1930
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 306
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:0d747505-7457-41b9-a74a-0ebec77f385c
Error Number:207,State:1,Class:16
2025-06-10 10:32:26.980 +08:00 [ERR] 
2025-06-10 10:32:27.138 +08:00 [ERR] 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
【自定义错误】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【异常类型】：SqlException 
【异常信息】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetPrepareationListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 9587
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 316
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetPrepareationListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 9587
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 316
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:522e6083-d258-4921-bc7a-e481750a2e31
Error Number:8120,State:1,Class:16
2025-06-10 10:32:41.382 +08:00 [ERR] 
2025-06-10 10:32:41.475 +08:00 [ERR] 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
【自定义错误】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【异常类型】：SqlException 
【异常信息】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetPrepareationListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 9587
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 316
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.MATERIAL_CODE' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.MaterialPreparationViewServices.GetPrepareationListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs:line 9587
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 316
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:522e6083-d258-4921-bc7a-e481750a2e31
Error Number:8120,State:1,Class:16
2025-06-10 12:13:25.286 +08:00 [ERR] 
2025-06-10 12:13:25.441 +08:00 [ERR] 选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
【自定义错误】：选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【异常类型】：SqlException 
【异常信息】：选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:19f6e284-08f7-43c4-be92-271323797313
Error Number:8120,State:1,Class:16
2025-06-10 12:13:43.602 +08:00 [ERR] 
2025-06-10 12:13:43.702 +08:00 [ERR] 选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
【自定义错误】：选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【异常类型】：SqlException 
【异常信息】：选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 选择列表中的列 'PPM_B_BATCH_CONSUME_REQUIREMENT.ID' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:19f6e284-08f7-43c4-be92-271323797313
Error Number:8120,State:1,Class:16
2025-06-10 12:19:19.377 +08:00 [ERR] 
2025-06-10 12:19:19.519 +08:00 [ERR] 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.WEIGHING_QTY' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
【自定义错误】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.WEIGHING_QTY' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【异常类型】：SqlException 
【异常信息】：选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.WEIGHING_QTY' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
Microsoft.Data.SqlClient.SqlException (0x80131904): 选择列表中的列 'PPM_B_PO_CONSUME_REQUIREMENT.WEIGHING_QTY' 无效，因为该列没有包含在聚合函数或 GROUP BY 子句中。
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 76
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
ClientConnectionId:0da9bfc8-3501-4d4e-b5b4-d24dad40ad39
Error Number:8120,State:1,Class:16
2025-06-10 12:23:46.442 +08:00 [ERR] 
2025-06-10 12:23:46.620 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 158
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 158
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:26:23.538 +08:00 [ERR] 
2025-06-10 12:26:23.690 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:26:28.955 +08:00 [ERR] 
2025-06-10 12:26:29.062 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:27:01.113 +08:00 [ERR] 
2025-06-10 12:27:01.211 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:27:44.477 +08:00 [ERR] 
2025-06-10 12:27:44.572 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:36:53.573 +08:00 [ERR] 
2025-06-10 12:36:53.681 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:36:55.767 +08:00 [ERR] 
2025-06-10 12:36:55.882 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:36:57.784 +08:00 [ERR] 
2025-06-10 12:36:57.897 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:06.927 +08:00 [ERR] 
2025-06-10 12:37:07.033 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:11.017 +08:00 [ERR] 
2025-06-10 12:37:11.122 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:13.846 +08:00 [ERR] 
2025-06-10 12:37:13.961 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:16.462 +08:00 [ERR] 
2025-06-10 12:37:16.590 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:25.040 +08:00 [ERR] 
2025-06-10 12:37:25.142 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:47.525 +08:00 [ERR] 
2025-06-10 12:37:47.634 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-10 12:37:51.224 +08:00 [ERR] 
2025-06-10 12:37:51.333 +08:00 [ERR] The connection does not support MultipleActiveResultSets.
【自定义错误】：The connection does not support MultipleActiveResultSets. 
【异常类型】：InvalidOperationException 
【异常信息】：The connection does not support MultipleActiveResultSets. 
【堆栈调用】：   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.InvalidOperationException: The connection does not support MultipleActiveResultSets.
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<ExecuteDbDataReaderAsync>b__189_0(Task`1 result)
   at System.Threading.Tasks.ContinuationResultTaskFromResultTask`2.InnerInvoke()
   at System.Threading.Tasks.Task.<>c.<.cctor>b__272_0(Object obj)
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
--- End of stack trace from previous location ---
   at System.Threading.ExecutionContext.RunInternal(ExecutionContext executionContext, ContextCallback callback, Object state)
   at System.Threading.Tasks.Task.ExecuteWithThreadLocal(Task& currentTaskSlot, Thread threadPoolThread)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetDataReaderAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SEFA.MKM.Services.BBatchDetailIiViewServices.GetBatchPreparationDetailList(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\BBatchDetailIiViewServices.cs:line 159
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(String batchID, String eqpmentID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 319
   at lambda_method550(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
