2025-06-11 09:26:46.731 +08:00 [DBG] 调用DFM接口
2025-06-11 09:26:48.603 +08:00 [DBG] 调用DFM接口
2025-06-11 09:26:49.688 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 09:26:49.736 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 09:26:50.054 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 09:26:50.100 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 10:19:09.484 +08:00 [DBG] 调用DFM接口
2025-06-11 10:19:10.558 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 10:19:10.945 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 10:19:14.307 +08:00 [DBG] 调用DFM接口
2025-06-11 10:19:15.364 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 10:19:15.415 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 10:52:23.922 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 10:52:23.996 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 10:52:42.943 +08:00 [DBG] 调用DFM接口
2025-06-11 10:52:44.166 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 10:52:44.223 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:05:23.892 +08:00 [DBG] 调用DFM接口
2025-06-11 11:05:26.300 +08:00 [DBG] 调用DFM接口
2025-06-11 11:05:27.676 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:05:27.736 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:05:28.114 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:05:28.173 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:05:44.584 +08:00 [DBG] 调用DFM接口
2025-06-11 11:05:47.217 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:05:47.278 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:05:47.613 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:05:47.671 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:48:09.128 +08:00 [DBG] 调用DFM接口
2025-06-11 11:48:10.311 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:48:10.406 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:48:10.721 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:48:10.782 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:48:31.322 +08:00 [DBG] 调用DFM接口
2025-06-11 11:48:32.978 +08:00 [DBG] 调用DFM接口
2025-06-11 11:48:34.652 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:48:34.721 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 11:48:34.978 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 11:48:35.040 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 12:55:01.905 +08:00 [DBG] 调用DFM接口
2025-06-11 12:55:04.106 +08:00 [DBG] 调用DFM接口
2025-06-11 12:55:04.106 +08:00 [INF] {"User":"","IP":"::ffff:127.0.0.1","API":"/api/materialpreparationview/getdown","BeginTime":"2025-06-11 12:55:03","OPTime":"68ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
2025-06-11 12:55:06.193 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 12:55:06.271 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 12:55:06.520 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 12:55:06.588 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:30:37.825 +08:00 [DBG] 调用DFM接口
2025-06-11 13:30:39.402 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:30:39.480 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:30:39.928 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:30:39.996 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:35:16.446 +08:00 [DBG] 调用DFM接口
2025-06-11 13:35:19.534 +08:00 [DBG] 调用DFM接口
2025-06-11 13:35:21.209 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:35:21.280 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:35:21.630 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:35:21.697 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:36:38.861 +08:00 [DBG] 调用DFM接口
2025-06-11 13:36:41.109 +08:00 [DBG] 调用DFM接口
2025-06-11 13:36:42.961 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:36:43.030 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:36:43.343 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:36:43.413 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:47:39.808 +08:00 [DBG] 调用DFM接口
2025-06-11 13:47:41.672 +08:00 [DBG] 调用DFM接口
2025-06-11 13:47:43.242 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:47:43.313 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:47:43.698 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:47:43.766 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:51:26.358 +08:00 [DBG] 调用DFM接口
2025-06-11 13:51:28.259 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:51:28.337 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:51:28.645 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:51:28.719 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:53:09.763 +08:00 [DBG] 调用DFM接口
2025-06-11 13:53:11.479 +08:00 [DBG] 调用DFM接口
2025-06-11 13:53:12.890 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:53:12.979 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:53:13.369 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:53:13.443 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:53:44.851 +08:00 [DBG] 调用DFM接口
2025-06-11 13:53:47.578 +08:00 [DBG] 调用DFM接口
2025-06-11 13:53:49.739 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:53:49.818 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:53:50.160 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:53:50.238 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:55:11.489 +08:00 [DBG] 调用DFM接口
2025-06-11 13:55:13.041 +08:00 [DBG] 调用DFM接口
2025-06-11 13:55:14.406 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:55:14.486 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 13:55:14.939 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 13:55:15.013 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:04:25.939 +08:00 [DBG] 调用DFM接口
2025-06-11 14:04:28.193 +08:00 [DBG] 调用DFM接口
2025-06-11 14:04:29.723 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:04:29.801 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:04:30.159 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:04:30.238 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:19:00.139 +08:00 [DBG] 调用DFM接口
2025-06-11 14:19:03.315 +08:00 [DBG] 调用DFM接口
2025-06-11 14:19:05.379 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:19:05.487 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:19:05.869 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:19:05.944 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:21:41.009 +08:00 [DBG] 调用DFM接口
2025-06-11 14:21:44.117 +08:00 [DBG] 调用DFM接口
2025-06-11 14:21:46.091 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:21:46.206 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:21:46.568 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:21:46.650 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:26:29.111 +08:00 [DBG] 调用DFM接口
2025-06-11 14:26:33.498 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:26:33.623 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:27:27.347 +08:00 [DBG] 调用DFM接口
2025-06-11 14:27:29.092 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:27:29.654 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:28:35.693 +08:00 [DBG] 本地访问：/api/materialpreparationview/getpagelistbybatchidsii
2025-06-11 14:28:35.693 +08:00 [DBG] 本地访问：/api/materialpreparationview/getpagelistmaterialpretop
2025-06-11 14:28:38.167 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:28:38.295 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 14:29:43.425 +08:00 [DBG] 调用DFM接口
2025-06-11 14:29:47.082 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 14:29:47.264 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:16:10.219 +08:00 [DBG] 调用DFM接口
2025-06-11 15:16:12.298 +08:00 [DBG] 调用DFM接口
2025-06-11 15:16:14.453 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:16:14.538 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:16:15.414 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:16:15.496 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:16:42.049 +08:00 [DBG] 调用DFM接口
2025-06-11 15:16:45.191 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:16:45.313 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:16:48.474 +08:00 [DBG] 调用DFM接口
2025-06-11 15:16:50.183 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:16:50.267 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:17:08.036 +08:00 [DBG] 调用DFM接口
2025-06-11 15:17:09.853 +08:00 [DBG] 调用DFM接口
2025-06-11 15:17:11.826 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:17:11.908 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:17:12.429 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:17:12.510 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:19:02.836 +08:00 [DBG] 调用DFM接口
2025-06-11 15:19:06.434 +08:00 [DBG] 本地访问：/api/materialpreparationview/getdown
2025-06-11 15:19:06.434 +08:00 [DBG] 调用DFM接口
2025-06-11 15:19:08.627 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:19:08.718 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:19:09.071 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:19:09.161 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:21:45.134 +08:00 [DBG] 调用DFM接口
2025-06-11 15:21:47.262 +08:00 [DBG] 调用DFM接口
2025-06-11 15:21:49.549 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:21:49.642 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:21:49.858 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:21:49.950 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:23:57.417 +08:00 [DBG] 调用DFM接口
2025-06-11 15:24:00.699 +08:00 [DBG] 调用DFM接口
2025-06-11 15:24:02.815 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:24:02.906 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:24:03.516 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:24:03.605 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:39:24.733 +08:00 [DBG] 调用DFM接口
2025-06-11 15:39:28.112 +08:00 [DBG] 调用DFM接口
2025-06-11 15:39:30.997 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:39:31.096 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:39:31.608 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:39:31.728 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:41:07.264 +08:00 [INF] {"User":"","IP":"::ffff:127.0.0.1","API":"/api/materialinventory/getscalelist","BeginTime":"2025-06-11 15:41:06","OPTime":"102ms","RequestMethod":"OPTIONS","RequestData":null,"Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
2025-06-11 15:41:07.264 +08:00 [DBG] 调用DFM接口
2025-06-11 15:41:09.984 +08:00 [DBG] 调用DFM接口
2025-06-11 15:41:11.737 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:41:11.838 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:41:12.363 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:41:12.457 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:41:24.277 +08:00 [DBG] 调用DFM接口
2025-06-11 15:41:26.619 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:41:26.716 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:41:27.249 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:41:27.348 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:42:10.280 +08:00 [DBG] 调用DFM接口
2025-06-11 15:42:12.957 +08:00 [DBG] 调用DFM接口
2025-06-11 15:42:15.730 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:42:15.827 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:42:16.278 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:42:16.384 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:43:23.667 +08:00 [DBG] 调用DFM接口
2025-06-11 15:43:27.531 +08:00 [DBG] 调用DFM接口
2025-06-11 15:43:29.251 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:43:29.355 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:43:30.611 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:43:30.712 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:45:07.262 +08:00 [DBG] 调用DFM接口
2025-06-11 15:45:10.371 +08:00 [DBG] 调用DFM接口
2025-06-11 15:45:12.282 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:45:12.391 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:45:12.946 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:45:13.046 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:48:44.075 +08:00 [DBG] 调用DFM接口
2025-06-11 15:48:46.921 +08:00 [DBG] 调用DFM接口
2025-06-11 15:48:48.938 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:48:49.052 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:48:49.787 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:48:49.913 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:49:17.492 +08:00 [DBG] 调用DFM接口
2025-06-11 15:49:20.749 +08:00 [DBG] 调用DFM接口
2025-06-11 15:49:23.480 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:49:23.583 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:49:24.043 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:49:24.142 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:54:03.772 +08:00 [DBG] 调用DFM接口
2025-06-11 15:54:06.802 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:54:06.912 +08:00 [DBG] 直接返回[]数据[0]条
2025-06-11 15:54:10.860 +08:00 [DBG] 获取工厂配置[02308281-5121-6559-163e-0370f6000000][]
2025-06-11 15:54:10.965 +08:00 [DBG] 直接返回[]数据[0]条
