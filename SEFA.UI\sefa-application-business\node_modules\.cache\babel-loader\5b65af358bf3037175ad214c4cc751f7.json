{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetailForm.vue", "mtime": 1749177894578}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoCA,SACAA,wBADA,EAEAC,cAFA,QAGA,mCAHA;AAIA;AACA;EACAC;IACAC;EADA,CADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,cAJA;MAKAC;IALA;EAOA,CAZA;;EAaAC;IACA;EACA,CAfA;;EAgBAC,WACA,CAjBA;;EAkBAC;IACA,sBACA,CAFA;;IAIAC;MAEAZ;QACA;QACA;QACA;MACA,CAJA;IAKA,CAXA;;IAYAa;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CArBA;;IAsBAC;MACAf;QACA;MACA,CAFA;IAGA,CA1BA;;IA2BAgB;MACA;MACA;MACA;MACA;MACA;MACA,oBANA,CAOA;MACA;MACA;IACA,CArCA;;IAsCAC;MACA;IACA;;EAxCA;AAlBA", "names": ["getWeekScheduleBomDetail", "changeMaterial", "components", "MaterialTable", "data", "dialogForm", "dialogVisible", "formLoading", "currentRow", "matInfo", "created", "mounted", "methods", "submit", "show", "getDialogDetail", "setMaterial", "openMaterialTable"], "sourceRoot": "src/views/planManagement/weekPacking", "sources": ["bomDetailForm.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n        <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\">              \n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"替代物料代码\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div>\n                {{dialogForm.InsteadMaterialCode}}&nbsp; &nbsp; {{dialogForm.InsteadMaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getWeekScheduleBomDetail,\n    changeMaterial\n  } from \"@/api/planManagement/weekSchedule\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n      },\n      \n      submit() {\n\n        changeMaterial(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getWeekScheduleBomDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)        \n        this.dialogForm.InsteadMaterialId = val.ID\n        this.dialogForm.InsteadMaterialCode = val.Code\n        this.dialogForm.InsteadMaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}