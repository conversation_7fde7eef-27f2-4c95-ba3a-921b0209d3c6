namespace SEFA.PPM.Model.ViewModels.MKM;

public class BatchConsumeRequirementSummary
{
    // 批次消耗需求ID (GUID 格式)
    public string BatchConsumeRequirementId { get; set; }

    // 物料ID (GUID 格式)
    public string MaterialId { get; set; }

    // 物料编码
    public string MaterialCode { get; set; }

    // 物料描述
    public string MaterialDescription { get; set; }

    // 单位ID (GUID 格式)
    public string UnitId { get; set; }

    // 批次数量 (来自批次表)
    public string Number { get; set; }

    // 换算单位
    public string ChangeUnit { get; set; }

    // 称重数量
    public decimal? WeighingQty { get; set; }

    // 库存类型 (例如：正常库存、冻结库存等)
    public string InventoryType { get; set; }
    
    /// <summary>
    /// 批次ID
    /// </summary>
    public string BatchId { get; set; }
    
    /// <summary>
    /// 生产订单ID
    /// </summary>
    public string ProductionOrderId { get; set; }
    
    /// <summary>
    /// 生产订单编号
    /// </summary>
    public string ProductionOrderNo { get; set; }

    // 库存类型计数 (聚合结果)
    public int PrepareCount { get; set; }

    // 已准备重量合计 (聚合结果)
    public decimal? PreparedWeight { get; set; }
}