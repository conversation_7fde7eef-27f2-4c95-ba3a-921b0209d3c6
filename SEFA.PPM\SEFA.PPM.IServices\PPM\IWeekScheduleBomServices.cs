﻿using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPM.IServices
{	
	/// <summary>
	/// IWeekScheduleBomServices
	/// </summary>	
    public interface IWeekScheduleBomServices :IBaseServices<WeekScheduleBomEntity>
	{
		Task<PageModel<WeekScheduleBomEntity>> GetPageList(WeekScheduleBomRequestModel reqModel);

        Task<List<WeekScheduleBomEntity>> GetList(WeekScheduleBomRequestModel reqModel);

		Task<bool> SaveForm(WeekScheduleBomEntity entity);
    }
}