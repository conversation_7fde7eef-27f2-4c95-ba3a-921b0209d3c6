{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\bomDetailForm.vue", "mtime": 1749180160948}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bomDetailForm.vue"], "names": [], "mappings": ";AA8CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bomDetailForm.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n        <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\">\n              <!-- {{ dialogForm.MaterialCode }} - {{ dialogForm.MaterialName }} -->\n              <el-select placeholder=\"请选择新替代物料\" v-model=\"dialogForm.MaterialCode\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"替代物料代码\">\n              {{ dialogForm.InsteadMaterialCode }} - {{ dialogForm.InsteadMaterialName }}\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"12\">\n            <el-form-item label=\"新替代物料\">\n              <el-select placeholder=\"请选择新替代物料\" @change=\"materialChange\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in insteadMaterialList\" :key=\"item.MaterialId\" :label=\"item.MaterialName\" :value=\"item.MaterialCode\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"\">\n              \n            </el-form-item>\n          </el-col> -->\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <!-- <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table> -->\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getInsteadMaterialList,\n    getWeekScheduleBomDetail,\n    changeMaterial\n  } from \"@/api/planManagement/weekSchedule\";\n  //import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      // MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        currentRow: {},\n        insteadMaterialList:[],\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n      },\n      async initInsteadMaterialList(data) {\n        await getInsteadMaterialList(data).then(res => {\n              console.log(\"initInsteadMaterialList\")\n              console.log(res.response)\n              this.insteadMaterialList = res.response\n            });\n      },      \n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.dialogForm = data\n            this.initInsteadMaterialList(data)\n          }\n        })\n      },\n      materialChange(val) {\n            console.log(val);\n            this.insteadMaterialList.forEach(item => {\n                item.value = item.ID;\n                if(item.MaterialCode == val) {\n                    this.dialogForm.MaterialId  = \"\";\n                    this.dialogForm.MaterialCode  = item.MaterialCode;\n                    this.dialogForm.MaterialName  = item.MaterialName;\n                }\n            });\n          },\n      submit() {\n        console.log(\"bomDetailForm.submit\")\n        console.log(this.dialogForm)\n        this.$emit('saveForm',this.dialogForm)\n        this.dialogVisible = false\n      },\n      // setMaterial(val){\n      //   // console.log(\"setMaterial\")\n      //   // console.log(val)        \n      //   this.dialogForm.MaterialId = val.ID\n      //   this.dialogForm.MaterialCode = val.Code\n      //   this.dialogForm.MaterialName = val.NAME\n      //   this.$forceUpdate()\n      //   // this.matInfo = val        \n      //   // console.log(this.dialogForm.MaterialCode)\n      //   // console.log(this.dialogForm.MaterialName)\n      // },\n      // openMaterialTable(){\n      //   this.$refs['materialTable'].show()\n      // },\n    }\n  }\n  </script>"]}]}