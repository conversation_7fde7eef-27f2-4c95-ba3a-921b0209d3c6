{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749629020051}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox available-inventory-container\" :style=\"{ '--table-height': tableHeight + 'px' }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- 测试状态显示 - 测试完成后删除 -->\r\n                            <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                [{{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                                数据: {{ tableList.length }}行 |\r\n                                高度: {{ tableHeight }}px]\r\n                            </span>\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px;\">\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                size=\"small\"\r\n                                @click=\"toggleTableHeightMode()\"\r\n                                :title=\"useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\">\r\n                                {{ useViewportHeight ? '视口模式' : '行数模式' }}\r\n                            </el-button>\r\n                            <!-- 测试按钮组 - 测试完成后删除 -->\r\n                            <div class=\"test-buttons\">\r\n                                <el-button size=\"small\" type=\"success\" @click=\"addTestData()\" title=\"添加测试数据\">+数据</el-button>\r\n                                <el-button size=\"small\" type=\"warning\" @click=\"removeTestData()\" title=\"删除测试数据\">-数据</el-button>\r\n                                <el-button size=\"small\" type=\"info\" @click=\"showTestInfo()\" title=\"显示测试信息\">测试信息</el-button>\r\n                                <el-button size=\"small\" type=\"danger\" @click=\"forceSetTableHeight(300)\" title=\"强制设置300px\">300px</el-button>\r\n                                <el-button size=\"small\" type=\"danger\" @click=\"forceSetTableHeight(500)\" title=\"强制设置500px\">500px</el-button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px',\r\n                        transition: 'height 0.3s ease'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableHeightKey\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n            minTableHeight: 150, // 最小表格高度\r\n            maxTableHeight: 400, // 最大表格高度\r\n            rowHeight: 40, // 每行的高度\r\n            windowHeight: window.innerHeight, // 窗口高度\r\n            useViewportHeight: false, // 是否使用视口高度模式\r\n            tableHeight: 200, // 当前表格高度\r\n            tableHeightKey: 0, // 强制重新渲染的key\r\n            // 测试相关数据 - 测试完成后删除\r\n            testDataCounter: 0,\r\n            originalTableList: [] // 保存原始数据\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 添加窗口大小变化监听器\r\n        this.handleResize = () => {\r\n            this.windowHeight = window.innerHeight;\r\n            console.log('窗口大小变化:', this.windowHeight);\r\n            if (this.useViewportHeight) {\r\n                this.updateTableHeight();\r\n            }\r\n        };\r\n        window.addEventListener('resize', this.handleResize);\r\n\r\n        // 初始化日志\r\n        console.log('=== 表格自适应高度初始化 ===');\r\n        console.log('初始模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n        console.log('初始窗口高度:', this.windowHeight);\r\n\r\n        // 初始化表格高度\r\n        this.$nextTick(() => {\r\n            this.updateTableHeight();\r\n\r\n            // 延迟测试，确保页面完全加载\r\n            setTimeout(() => {\r\n                console.log('=== 页面加载完成，开始测试 ===');\r\n                this.testTableHeight();\r\n\r\n                // 自动测试切换功能\r\n                setTimeout(() => {\r\n                    console.log('=== 自动测试切换功能 ===');\r\n                    this.toggleTableHeightMode();\r\n                }, 2000);\r\n            }, 1000);\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n            // 数据加载完成后更新表格高度\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        },\r\n        // 切换表格高度模式\r\n        toggleTableHeightMode() {\r\n            console.log('=== 切换表格高度模式 ===');\r\n            console.log('切换前:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('当前表格高度:', this.tableHeight);\r\n\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n\r\n            console.log('切换后:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            // 立即重新计算表格高度\r\n            this.updateTableHeight();\r\n\r\n            // 显示切换成功消息\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n        // 更新表格高度\r\n        updateTableHeight() {\r\n            console.log('=== updateTableHeight 被调用 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('tableList.length:', this.tableList ? this.tableList.length : 'undefined');\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 基于视口高度的响应式计算\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 基于数据行数计算高度\r\n                const headerHeight = 40;\r\n                const dataHeight = headerHeight + (this.tableList.length * this.rowHeight);\r\n                newHeight = Math.min(Math.max(dataHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('行数模式计算:', {\r\n                    headerHeight,\r\n                    dataRows: this.tableList.length,\r\n                    rowHeight: this.rowHeight,\r\n                    dataHeight,\r\n                    newHeight\r\n                });\r\n            }\r\n\r\n            console.log('旧高度:', this.tableHeight, '新高度:', newHeight);\r\n\r\n            // 总是更新高度，即使值相同也要触发重新渲染\r\n            this.tableHeight = newHeight;\r\n            this.tableHeightKey++; // 强制重新渲染\r\n            console.log('高度已更新到:', this.tableHeight, 'Key:', this.tableHeightKey);\r\n\r\n            // 强制更新组件\r\n            this.$forceUpdate();\r\n\r\n            this.$nextTick(() => {\r\n                if (this.$refs.TopTabel) {\r\n                    console.log('调用表格 doLayout');\r\n                    this.$refs.TopTabel.doLayout();\r\n\r\n                    // 再次确保布局更新\r\n                    setTimeout(() => {\r\n                        if (this.$refs.TopTabel) {\r\n                            this.$refs.TopTabel.doLayout();\r\n                            console.log('表格布局更新完成');\r\n                        }\r\n                    }, 100);\r\n                } else {\r\n                    console.log('表格引用不存在');\r\n                }\r\n            });\r\n        },\r\n        // 手动设置表格高度参数\r\n        setTableHeightParams(minHeight, maxHeight, rowHeight) {\r\n            this.minTableHeight = minHeight || 150;\r\n            this.maxTableHeight = maxHeight || 400;\r\n            this.rowHeight = rowHeight || 40;\r\n        },\r\n        // 测试方法 - 可以在浏览器控制台调用\r\n        testTableHeight() {\r\n            console.log('=== 表格高度测试 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('表格数据行数:', this.tableList ? this.tableList.length : 0);\r\n            console.log('窗口高度:', this.windowHeight);\r\n            console.log('当前表格高度:', this.tableHeight);\r\n            console.log('最小高度:', this.minTableHeight);\r\n            console.log('最大高度:', this.maxTableHeight);\r\n            console.log('行高:', this.rowHeight);\r\n            console.log('表格Key:', this.tableHeightKey);\r\n\r\n            // 检查表格DOM元素\r\n            if (this.$refs.TopTabel) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                console.log('表格DOM高度:', tableEl.style.height);\r\n                console.log('表格实际高度:', tableEl.offsetHeight);\r\n                console.log('表格计算样式:', window.getComputedStyle(tableEl).height);\r\n            }\r\n\r\n            return this.tableHeight;\r\n        },\r\n\r\n        // 强制设置表格高度 - 测试用\r\n        forceSetTableHeight(height) {\r\n            console.log('强制设置表格高度:', height);\r\n            this.tableHeight = height;\r\n            this.tableHeightKey++;\r\n\r\n            this.$nextTick(() => {\r\n                if (this.$refs.TopTabel) {\r\n                    const tableEl = this.$refs.TopTabel.$el;\r\n                    tableEl.style.height = height + 'px';\r\n                    this.$refs.TopTabel.doLayout();\r\n                    console.log('强制设置后的DOM高度:', tableEl.style.height);\r\n                }\r\n            });\r\n        },\r\n\r\n        // === 以下为测试方法，测试完成后删除 ===\r\n\r\n        // 生成测试数据\r\n        generateTestData() {\r\n            const testData = {\r\n                ID: `TEST_${++this.testDataCounter}`,\r\n                SbSscc: `SSCC${String(this.testDataCounter).padStart(4, '0')}`,\r\n                LBatch: `BATCH${String(this.testDataCounter).padStart(3, '0')}`,\r\n                MaterialCode: `MAT${String(this.testDataCounter % 5 + 1).padStart(3, '0')}`,\r\n                MaterialName: `测试物料${this.testDataCounter}`,\r\n                InQuantity: Math.floor(Math.random() * 1000) + 100,\r\n                MaterialUnit1: 'kg',\r\n                LStatus: Math.floor(Math.random() * 3) + 1,\r\n                SbStatus: Math.floor(Math.random() * 3) + 1,\r\n                ExpirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\r\n                Location: `A${String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`\r\n            };\r\n            return testData;\r\n        },\r\n\r\n        // 添加测试数据\r\n        addTestData() {\r\n            // 第一次添加时保存原始数据\r\n            if (this.originalTableList.length === 0 && this.tableList.length > 0) {\r\n                this.originalTableList = [...this.tableList];\r\n            }\r\n\r\n            const newData = this.generateTestData();\r\n            this.tableList.push(newData);\r\n\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n\r\n            Message({\r\n                message: `已添加测试数据，当前共 ${this.tableList.length} 行`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n\r\n        // 删除测试数据\r\n        removeTestData() {\r\n            if (this.tableList.length > 0) {\r\n                // 优先删除测试数据\r\n                const testIndex = this.tableList.findIndex(item => item.ID && item.ID.startsWith('TEST_'));\r\n                if (testIndex !== -1) {\r\n                    this.tableList.splice(testIndex, 1);\r\n                } else {\r\n                    this.tableList.pop();\r\n                }\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: `已删除数据，当前共 ${this.tableList.length} 行`,\r\n                    type: 'warning',\r\n                    duration: 2000\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: '没有数据可删除',\r\n                    type: 'info',\r\n                    duration: 2000\r\n                });\r\n            }\r\n        },\r\n\r\n        // 显示测试信息\r\n        showTestInfo() {\r\n            const info = `\r\n=== 表格自适应高度测试信息 ===\r\n当前模式: ${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}\r\n数据行数: ${this.tableList.length}\r\n窗口高度: ${this.windowHeight}px\r\n表格高度: ${this.tableHeight}px\r\n最小高度: ${this.minTableHeight}px\r\n最大高度: ${this.maxTableHeight}px\r\n行高设置: ${this.rowHeight}px\r\n\r\n测试说明:\r\n1. 点击\"视口模式/行数模式\"按钮切换计算方式\r\n2. 点击\"+数据\"按钮添加测试数据观察高度变化\r\n3. 点击\"-数据\"按钮删除数据观察高度变化\r\n4. 调整浏览器窗口大小测试响应式效果\r\n5. 在视口模式下，表格高度为窗口高度的25%\r\n6. 在行数模式下，表格高度根据数据行数计算\r\n            `;\r\n\r\n            console.log(info);\r\n\r\n            this.$alert(info, '测试信息', {\r\n                confirmButtonText: '确定',\r\n                type: 'info'\r\n            });\r\n        },\r\n\r\n        // 恢复原始数据 (可在控制台调用)\r\n        restoreOriginalData() {\r\n            if (this.originalTableList.length > 0) {\r\n                this.tableList = [...this.originalTableList];\r\n                this.originalTableList = [];\r\n                this.testDataCounter = 0;\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: '已恢复原始数据',\r\n                    type: 'success'\r\n                });\r\n            }\r\n        }\r\n\r\n        // === 测试方法结束 ===\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 可用库存表格容器自适应样式\r\n    .available-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 450px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 使用CSS变量控制表格高度\r\n        .dynamic-height-table {\r\n            height: var(--table-height, 200px) !important;\r\n            transition: height 0.3s ease;\r\n            width: 100%;\r\n\r\n            // 确保表格内容区域也使用动态高度\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 200px) - 40px) !important;\r\n                overflow-y: auto;\r\n\r\n                // 自定义滚动条样式\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px;\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 300px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 250px;\r\n        }\r\n    }\r\n\r\n    /* 测试按钮样式 - 测试完成后删除 */\r\n    .test-buttons {\r\n        .el-button {\r\n            margin-left: 3px !important;\r\n            font-size: 11px;\r\n            padding: 5px 8px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}