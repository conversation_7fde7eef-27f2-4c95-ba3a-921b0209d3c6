{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749627596938}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings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file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox available-inventory-container\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.AvallableInventory') }}</div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; gap: 5px;\">\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n                            <el-button class=\"tablebtn\" size=\"small\" @click=\"toggleTableHeightMode()\" :title=\"useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'\">\r\n                                {{ useViewportHeight ? '行数模式' : '视口模式' }}\r\n                            </el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableList\" ref=\"TopTabel\" @row-click=\"GetCurrentRow\" highlight-current-row style=\"width: 100%\" :height=\"calculateTableHeight\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n            minTableHeight: 150, // 最小表格高度\r\n            maxTableHeight: 400, // 最大表格高度\r\n            rowHeight: 40, // 每行的高度\r\n            windowHeight: window.innerHeight, // 窗口高度\r\n            useViewportHeight: false // 是否使用视口高度模式\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 添加窗口大小变化监听器\r\n        this.handleResize = () => {\r\n            this.windowHeight = window.innerHeight;\r\n        };\r\n        window.addEventListener('resize', this.handleResize);\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        },\r\n        // 切换表格高度模式\r\n        toggleTableHeightMode() {\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n            this.$nextTick(() => {\r\n                // 强制重新计算表格高度\r\n                if (this.$refs.TopTabel) {\r\n                    this.$refs.TopTabel.doLayout();\r\n                }\r\n            });\r\n        },\r\n        // 手动设置表格高度参数\r\n        setTableHeightParams(minHeight, maxHeight, rowHeight) {\r\n            this.minTableHeight = minHeight || 150;\r\n            this.maxTableHeight = maxHeight || 400;\r\n            this.rowHeight = rowHeight || 40;\r\n        }\r\n    },\r\n    computed: {\r\n        // 计算表格高度\r\n        calculateTableHeight() {\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                return this.minTableHeight;\r\n            }\r\n\r\n            if (this.useViewportHeight) {\r\n                // 基于视口高度的响应式计算\r\n                const availableHeight = this.windowHeight * 0.25; // 使用25%的视口高度\r\n                return Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n            } else {\r\n                // 基于数据行数计算高度\r\n                const headerHeight = 40; // 表头高度\r\n                const calculatedHeight = headerHeight + (this.tableList.length * this.rowHeight);\r\n\r\n                // 限制在最小和最大高度之间\r\n                return Math.min(Math.max(calculatedHeight, this.minTableHeight), this.maxTableHeight);\r\n            }\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 可用库存表格容器自适应样式\r\n    .available-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 450px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        .el-table {\r\n            transition: height 0.3s ease;\r\n\r\n            .el-table__body-wrapper {\r\n                overflow-y: auto;\r\n\r\n                // 自定义滚动条样式\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 300px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 250px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}