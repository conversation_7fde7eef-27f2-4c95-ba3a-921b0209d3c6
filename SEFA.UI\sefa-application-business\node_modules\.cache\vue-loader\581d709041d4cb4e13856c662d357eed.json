{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749626560536}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAq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file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox\" style=\"height: 32%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.AvallableInventory') }}</div>\r\n                        <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"margin-left: 5px; width: 140px; position: absolute; right: 10px\" @click=\"PrintAvallable()\">\r\n                            {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableList\" ref=\"TopTabel\" @row-click=\"GetCurrentRow\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        }\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n}\r\n</style>\r\n"]}]}