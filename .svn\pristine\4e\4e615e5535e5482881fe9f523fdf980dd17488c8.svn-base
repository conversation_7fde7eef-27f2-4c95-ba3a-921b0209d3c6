﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SEFA.PPM.Model.ViewModels.MKM.View
{
    public class PartialBagModel
    {

        public string IS_FC { get; set; }
        public string ChangeUnit { get; set; }

        public string PrintId { get; set; }
        public string IsType { get; set; }
        /// <summary>
        /// Desc:
        /// Default:子批次
        /// Nullable:True
        /// </summary>
        public string subID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:库房实际重量
        /// Nullable:True
        /// </summary>
        public string actualValue { get; set; }
        /// <summary>
        /// Desc:
        /// Default:物料ID
        /// Nullable:True
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:输入数量
        /// Nullable:True
        /// </summary>
        public decimal inputBagWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:单包重量
        /// Nullable:True
        /// </summary>
        public int bagWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:目标重量
        /// Nullable:True
        /// </summary>
        public decimal targetWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:实际重量
        /// Nullable:True
        /// </summary>
        public decimal actualWeight { get; set; }
        /// <summary>
        /// Desc:
        /// Default:容器ID
        /// Nullable:True
        /// </summary>
        public string containerID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:产线工单ID
        /// Nullable:True
        /// </summary>
        public string proOrderID { get; set; }
        /// <summary>
        /// 批次消耗需求ID
        /// </summary>
        public string batchConsumeRequirementId { get; set; }
        /// <summary>
        /// Desc:
        /// Default:批次ID
        /// Nullable:True
        /// </summary>
        public string batchID { get; set; }
        /// <summary>
        /// Desc:
        /// Default:批次ID
        /// Nullable:True
        /// </summary>
        public string equpmentID { get; set; }
    }
}
