﻿using AutoMapper;
using Magicodes.ExporterAndImporter.Excel;
using Magicodes.ExporterAndImporter.Excel.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.DFM.Model.ViewModels;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SqlSugar;
using static SEFA.PPM.Model.PPMConstant;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class WeekScheduleController : BaseApiController
    {
        /// <summary>
        /// WeekSchedule
        /// </summary>
        private readonly IWeekScheduleServices _weekScheduleServices;
        private readonly IWeekScheduleBomServices _weekScheduleBomServices;
        private readonly IProductionOrderServices _productionOrderServices;
        //private readonly IMaterialServices _materialServices;
        private readonly IMapper _mapper;

        public WeekScheduleController(IWeekScheduleServices WeekScheduleServices,
            IWeekScheduleBomServices weekScheduleBomServices,
            IProductionOrderServices productionOrderServices,
            //IMaterialServices materialServices,
            IMapper mapper)
        {
            _weekScheduleServices = WeekScheduleServices;
            _weekScheduleBomServices = weekScheduleBomServices;
            _productionOrderServices = productionOrderServices;
            //_materialServices = materialServices;
            _mapper = mapper;
        }

        #region 查询、添加、修改、删除
        [HttpPost]
        public async Task<MessageModel<List<WeekScheduleEntity>>> GetList([FromBody] WeekScheduleRequestModel reqModel)
        {
            var data = await _weekScheduleServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<WeekScheduleEntity>>> GetPageList([FromBody] WeekScheduleRequestModel reqModel)
        {
            var data = await _weekScheduleServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<WeekScheduleEntity>> GetEntity(string id)
        {
            var data = await _weekScheduleServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<WeekScheduleBomEntity>>> GetBomList([FromBody] WeekScheduleBomRequestModel reqModel)
        {
            var data = await _weekScheduleBomServices.GetList(reqModel);
            data = data.OrderBy(m => m.SegmentName).ThenBy(m => m.Sort).ToList();
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<WeekScheduleBomEntity>> GetBomEntity(string id)
        {
            var data = await _weekScheduleBomServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<List<ProductionOrderRequestModel>>> GetProductionOrderList([FromBody] ProductionOrderRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<ProductionOrderEntity, MaterialEntity>()
                .And((p, m) => p.ParentId == reqModel.ParentId)
                             .ToExpression();
            var data = await _productionOrderServices.QueryTwoTable<ProductionOrderEntity, MaterialEntity, ProductionOrderRequestModel>(
                (p, m) => new object[]
                {
                    JoinType.Left, p.MaterialId == m.ID,
                },
                (p, m) => new ProductionOrderRequestModel()
                {
                    ID = p.ID,
                    SegmentCode = p.SegmentCode,
                    LineCode = p.LineCode,
                    ProductionOrderNo = p.ProductionOrderNo,
                    MesOrderCode = p.MesOrderCode,
                    ParentId = p.ParentId,
                    PlanQty = p.PlanQty,
                    MaterialVersionId = p.MaterialVersionId,
                    MaterialId = p.MaterialId,
                    MaterialCode = m.Code,
                    MaterialName = m.NAME,
                    PlanDate = p.PlanDate,
                    PrepareShiftid = p.PrepareShiftid,
                    PlanStartTime = p.PlanStartTime,
                    PlanEndTime = p.PlanEndTime,
                    StartTime = p.StartTime,
                    EndTime = p.EndTime,
                    BomVersion = p.BomVersion,
                    PoStatus = int.Parse(p.PoStatus),
                    ReleaseStatus = int.Parse(p.ReleaseStatus),
                    ProduceStatus = int.Parse(p.ProduceStatus),
                    Remark = p.Remark,
                    Deleted = p.Deleted,
                    Type = p.Type,
                    SapOrderType = p.SapOrderType,
                    SapFlag = p.SapFlag
                },
                whereExpression);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<ProductionOrderEntity>> GetProductionOrderEntity(string id)
        {
            var data = await _productionOrderServices.QueryById(id);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取批次拆分信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<WeekScheduleRequestModel>> GetSplitBatchInfo([FromBody] WeekScheduleRequestModel request)
        {
            var data = await _weekScheduleServices.GetSplitBatchInfo(request);
            if (data.Succeed)
            {
                return Success(data.Data, "获取成功");
            }
            else
            {
                return Failed(data.Data, $"获取失败，{data.Error.Text}");
            }
        }

        /// <summary>
        /// 获取批次拆分信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<WeekScheduleRequestModel>> GetAddBatchInfo([FromBody] ProductionOrderRequestModel request)
        {
            var data = await _weekScheduleServices.GetAddBatchInfo(request);
            if (data.Succeed)
            {
                return Success(data.Data, "获取成功");
            }
            else
            {
                return Failed(data.Data, $"获取失败，{data.Error.Text}");
            }
        }

        [HttpPost]
        public async Task<MessageModel<List<ReplaceMaterialEntity>>> GetInsteadMaterialList([FromBody] WeekScheduleBomRequestModel reqModel)
        {
            var data = await _weekScheduleServices.GetInsteadMaterialList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 新增周计划
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] WeekScheduleEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrWhiteSpace(request.ID))
            {
                var ret = await _weekScheduleServices.SaveForm(request);
                if (ret.Succeed)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed($"添加失败，{ret.Error.Text}");
                }
            }
            else
            {
                var ret = await _weekScheduleServices.UpdateForm(request);
                if (ret.Succeed)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed($"添加失败，{ret.Error.Text}");
                }
            }

        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            var list = await _weekScheduleServices.FindList(m => ids.Contains(m.ID) && m.Status == WeekSchedule_Status.NotSplit.ToString());
            if (list.Count > 0)
            {
                return Failed($"无法删除，计划已拆分批次。{string.Join("|", list.Select(m => m.OrderNo))}");
            }

            var deleteBom = await _weekScheduleBomServices.Delete(m => ids.Contains(m.WeekScheduleId));
            if (!deleteBom)
            {
                return Failed("删除周计划Bom失败");
            }

            data.success = await _weekScheduleServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
        #endregion

        #region 数据导入、导出
        /// <summary>
        /// 数据导入
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Consumes("multipart/form-data")]
        [HttpPost]
        public async Task<MessageModel<string>> ImportData([FromForm] FileImportDto input)
        {
            if (input.File == null)
            {
                return Failed("", "请选择文件导入");
            }

            var stream = input.File.OpenReadStream();
            // 检查文件是否存在
            if (stream == null)
            {
                return Failed("", "未找到文件,请重新上传");
            }
            var result = await _weekScheduleServices.ImportData(stream);
            return result.Succeed ? Success("", "导入成功") : Failed("", "导入失败：" + result.Error.Text);
        }

        /// <summary>
        /// 数据导出
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<ActionResult> ExportData([FromBody] WeekScheduleRequestModel reqModel)
        {
            ExcelExporter exporter = new ExcelExporter();
            var query = await _weekScheduleServices.GetList(reqModel);

            var data = _mapper.Map<List<WeekScheduleExcelDto>>(query);

            var result = await exporter.ExportAsByteArray<WeekScheduleExcelDto>(data);
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: $"周计划-{DateTime.Now.ToString("yyyy-MM-dd HH-mm-ss")}");
        }

        /// <summary>
        /// 下载模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActionResult> DownLoadTemplate()
        {
            ExcelImporter Importer = new ExcelImporter();
            var result = await Importer.GenerateTemplateBytes<WeekScheduleExcelDto>();
            var fs = new MemoryStream(result);
            return new XlsxFileResult(stream: fs, fileDownloadName: "周计划导入模板");
        }

        #endregion

        #region 替换物料
        [HttpPost]
        public async Task<MessageModel<string>> ChangeMaterial([FromBody] WeekScheduleBomRequestModel reqModel)
        {
            var result = await _weekScheduleServices.ChangeMaterial(reqModel);
            return result.Succeed ? Success("", "批次拆分成功") : Failed("", "批次拆分失败：" + result.Error.Text);
        }
        #endregion

        #region 拆分批次
        [HttpPost]
        public async Task<MessageModel<string>> SplitBatch([FromBody] WeekScheduleRequestModel wsModel)
        {
            var result = await _weekScheduleServices.SplitBatch(wsModel);
            return result.Succeed ? Success("", "批次拆分成功") : Failed("", "批次拆分失败：" + result.Error.Text);
        }
        #endregion

        #region 新增批次工单
        [HttpPost]
        public async Task<MessageModel<string>> AddBatch([FromBody] WeekScheduleRequestModel wsModel)
        {
            var result = await _weekScheduleServices.AddBatch(wsModel);
            return result.Succeed ? Success("", "新增批次工单成功") : Failed("", "新增批次工单失败：" + result.Error.Text);
        }
        #endregion

        #region 批次工单下发DCS
        [HttpPost]
        public async Task<MessageModel<string>> DownloadDCS([FromBody] ProductionOrderModel reqModel)
        {
            var result = await _weekScheduleServices.DcsDownload(reqModel);
            return result.Succeed ? Success("", "批次拆分成功") : Failed("", "批次拆分失败：" + result.Error.Text);
        }
        #endregion
    }
    //public class WeekScheduleRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}