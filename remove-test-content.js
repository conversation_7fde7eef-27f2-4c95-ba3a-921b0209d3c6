// 删除测试内容的脚本
// 测试完成后运行此脚本来清理测试代码

const fs = require('fs');
const path = require('path');

const filePath = 'SEFA.UI/sefa-application-business/src/views/Inventory/buildpalletsStart/index.vue';

function removeTestContent() {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        
        // 删除测试按钮
        content = content.replace(/<!-- 测试按钮组 - 测试完成后删除 -->[\s\S]*?<\/div>/g, '');
        
        // 删除测试状态显示
        content = content.replace(/<!-- 测试状态显示 - 测试完成后删除 -->[\s\S]*?<\/span>/g, '');
        
        // 删除测试数据属性
        content = content.replace(/\/\/ 测试相关数据 - 测试完成后删除[\s\S]*?originalTableList: \[\] \/\/ 保存原始数据/g, '');
        
        // 删除测试方法
        content = content.replace(/\/\/ === 以下为测试方法，测试完成后删除 ===[\s\S]*?\/\/ === 测试方法结束 ===/g, '');
        
        // 删除测试样式
        content = content.replace(/\/\* 测试按钮样式 - 测试完成后删除 \*\/[\s\S]*?}/g, '');
        
        // 清理多余的空行
        content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
        
        fs.writeFileSync(filePath, content, 'utf8');
        console.log('✅ 测试内容已成功删除');
        
    } catch (error) {
        console.error('❌ 删除测试内容时出错:', error.message);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    removeTestContent();
}

module.exports = { removeTestContent };
