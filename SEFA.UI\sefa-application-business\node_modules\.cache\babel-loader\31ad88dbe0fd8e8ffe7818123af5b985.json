{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekPacking\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekPacking\\index.vue", "mtime": 1749177894525}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA6FA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,aADA;EAEAC;IACAC,YADA;IAEAC,UAFA;IAGAC;EAHA,CAFA;;EAOAC;IACA;MACAC;QACAC,YADA;QAEAC;MAFA,CADA;MAKAC,QALA;MAMAC,eANA;MAOAC,sCAPA;MAQAC,aARA;MASAC,cATA;MAUAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,EAYA;QAAAF;QAAAC;QAAAC;MAAA,CAZA,EAaA;QAAAF;QAAAC;QAAAC;MAAA,CAbA,EAcA;QAAAF;QAAAC;QAAAC;MAAA,CAdA,EAeA;QAAAF;QAAAC;QAAAC;MAAA,CAfA,EAgBA;QAAAF;QAAAC;QAAAC;MAAA,CAhBA,EAiBA;QAAAF;QAAAC;QAAAC;MAAA,CAjBA,EAkBA;QAAAF;QAAAC;QAAAC;MAAA,CAlBA,EAmBA;QAAAF;QAAAC;QAAAC;MAAA,CAnBA,EAoBA;QAAAF;QAAAC;QAAAC;MAAA,CApBA,EAqBA;QAAAF;QAAAC;QAAAC;MAAA,CArBA,CAVA;MAiCAC,QAjCA;MAkCAC;QACAnB,WADA;QAEAoB,sBAFA;QAGAC,yCAHA;QAGA;QACA;QACAC,iDALA,CAKA;;MALA;IAlCA;EA0CA,CAlDA;;EAmDAC;IACA;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CA5DA;;EA6DAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;IACA,CANA;;IAOAC;MACA;MACA;IACA,CAVA;;IAWAC;MACA;MACA;IACA,CAdA;;IAeAC;MACA;MACA;IACA,CAlBA;;IAmBAC;MACA;IACA,CArBA;;IAsBAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CApCA;;IAsCAC;MACAC;QACAF;QACA;QACA;MACA,CAJA;IAKA;;EA5CA;AA7DA", "names": ["name", "components", "UploadButton", "FormDialog", "BomDetail", "data", "searchForm", "pageIndex", "pageSize", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "loading", "tableOption", "code", "width", "align", "mainH", "buttonOption", "serveIp", "uploadUrl", "DownLoadUrl", "mounted", "window", "methods", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "bomDialog", "delRow", "title", "message", "confirmText", "cancelText", "then", "delWeekSchedule", "catch", "err", "console", "getTableData", "getWeekScheduleList"], "sourceRoot": "src/views/planManagement/weekSchedule", "sources": ["index.vue"], "sourcesContent": ["<!--\n * @Descripttion: (周计划/PPM_B_WEEK_SCHEDULE)\n * @version: (1.0)\n * @Author: (SECI)\n * @Date: (2025-04-10)\n * @LastEditors: (SECI)\n * @LastEditTime: (2025-04-10)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n            \t\t\t\t    \n        <el-form-item label=\"工厂\" class=\"mb-2\">\n          <!--el-select v-model=\"searchForm.factory\" placeholder=\"请选择工厂\">\n            <el-option v-for=\"item in  sys_classify_type \" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n          </!--el-select-->\n          <el-input clearable v-model=\"searchForm.factory\" placeholder=\"请输入工厂\" />\n        </el-form-item>\n        <el-form-item label=\"车间\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.workshop\" placeholder=\"请输入车间\" />\n        </el-form-item>\n        <el-form-item label=\"产线\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.lineCode\" placeholder=\"请输入产线\" />\n        </el-form-item>\n        <el-form-item label=\"物料代码\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.materialCode\" placeholder=\"请输入物料代码\" />\n        </el-form-item>\n        <el-form-item label=\"计划编号\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.orderNo\" placeholder=\"请输入计划编号\" />\n        </el-form-item>\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n            {{ $t('GLOBAL._XZ') }}\n          </el-button>\n        </el-form-item>\n        <upload-button :option=\"buttonOption\" :searchForm=\"searchForm\" ref=\"uploadButton\"></upload-button>\n\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"bomDialog(scope.row)\">{{ $t('GLOBAL._BOM') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>          \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n    <BomDetail ref=\"bomDetail\" />\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport FormDialog from './form-dialog'\nimport BomDetail from './bomDetail.vue'\nimport {delWeekSchedule, getWeekScheduleList} from \"@/api/planManagement/weekSchedule\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\nimport UploadButton from \"@/components/UploadButton.vue\";\n\nexport default {\n  name: 'index',\n  components: {\n    UploadButton,\n    FormDialog,\n    BomDetail\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('WeekSchedule.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n        {code: 'Factory', width: 100, align: 'center'},\n        {code: 'Workshop', width: 150, align: 'center'},\n        {code: 'Category', width: 150, align: 'center'},\n        {code: 'DesignCode', width: 150, align: 'center'},\n        {code: 'FinishWorkday', width: 180, align: 'center'},\n        {code: 'LineCode', width: 150, align: 'center'},\n        {code: 'FinishShift', width: 100, align: 'center'},\n        {code: 'MaterialCode', width: 100, align: 'center'},\n        {code: 'MaterialName', width: 180, align: 'center'},\n        {code: 'OrderNo', width: 150, align: 'center'},\n        {code: 'Output', width: 100, align: 'center'},\n        {code: 'PackSize', width: 150, align: 'center'},\n        {code: 'PlanQuantity', width: 100, align: 'center'},\n        {code: 'Remark', width: 150, align: 'center'},\n        {code: 'SapOrderNo', width: 150, align: 'center'},\n        {code: 'StartWorkday', width: 150, align: 'center'},\n        {code: 'StartShift', width: 100, align: 'center'},\n        {code: 'Status', width: 100, align: 'center'},\n        {code: 'Type', width: 100, align: 'center'},\n        {code: 'Unit', width: 100, align: 'center'},\n        {code: 'WorkCenter', width: 100, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'周计划',\n        serveIp:'baseURL_PPM',\n        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入\n        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出\n        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      this.tableName = getTableHead(this.hansObj, this.tableOption)\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n    bomDialog(row) {\n      this.$refs.bomDetail.show(row)\n    },\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delWeekSchedule([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getWeekScheduleList(this.searchForm).then(res => {\n        console.log(res);\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}