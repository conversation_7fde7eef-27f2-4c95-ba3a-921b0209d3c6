﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
	///<summary>
	///
	///</summary>

	[SugarTable("PPM_B_PO_CONSUME_REQUIREMENT")]
	public class PoConsumeRequirementEntity : EntityBase
	{
		public PoConsumeRequirementEntity()
		{
		}
		/// <summary>
		/// Desc:订单ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
		public string ProductionOrderId { get; set; }
		/// <summary>
		/// Desc:订单工序需求ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "PO_SEGMENT_REQUIREMENT_ID")]
		public string PoSegmentRequirementId { get; set; }
		/// <summary>
		/// Desc:物料ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_ID")]
		public string MaterialId { get; set; }
		/// <summary>
		/// Desc:物料编码
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_CODE")]
		public string MaterialCode { get; set; }
		/// <summary>
		/// Desc:物料描述
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_DESCRIPTION")]
		public string MaterialDescription { get; set; }
		/// <summary>
		/// Desc:物料版本ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_VERSION_ID")]
		public string MaterialVersionId { get; set; }
		/// <summary>
		/// Desc:数量
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "QUANTITY")]
		public decimal? Quantity { get; set; }
		/// <summary>
		/// Desc:计量单位ID
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "UNIT_ID")]
		public string UnitId { get; set; }
		/// <summary>
		/// Desc:指定批次号
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "MATERIAL_LOT_NO")]
		public string MaterialLotNo { get; set; }
		/// <summary>
		/// Desc:存储仓库
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "STORAGE_BIN")]
		public string StorageBin { get; set; }
		/// <summary>
		/// Desc:存储仓库库位
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "STORAGE_LOCATION")]
		public string StorageLocation { get; set; }
		/// <summary>
		/// Desc:排序
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "SORT_ORDER")]
		public int SortOrder { get; set; }
		/// <summary>
		/// Desc:删除标识
		/// Default:
		/// Nullable:False
		/// </summary>
		[SugarColumn(ColumnName = "DELETED")]
		public int Deleted { get; set; }
        /// <summary>
        /// Desc:称量备料数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WEIGHING_QTY")]
        public decimal? WeighingQty { get; set; }
		/// <summary>
		/// Desc:预调值
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "AD_JUSTPERCENT_QUANTITY")]
		public decimal? AdjustPercentQuantity { get; set; }
		/// <summary>
		/// Desc:是否为千克，默认为空，为g写上单位g
		/// Default:
		/// Nullable:True
		/// </summary>
		[SugarColumn(ColumnName = "CHANGE_UNIT")]
		public string ChangeUnit { get; set; }

		
	}
}