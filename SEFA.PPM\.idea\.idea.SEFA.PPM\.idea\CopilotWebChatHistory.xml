<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotWebChatHistory">
    <option name="conversations">
      <list>
        <WebConversation>
          <option name="session">
            <ChatSession>
              <option name="messages">
                <list>
                  <UIChatMessage>
                    <option name="command" value="" />
                    <option name="content" value="根据SELECT&#10;DISTINCT&#10;&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG,&#10;-- DISTINCT&#10;--g2.Full_QTY,g3.P_QTY,&#10;&#9; EQUIPMENT_ID,&#10;&#9; Only_Id,&#10;&#9;PRODUCTION_ORDER_NO,&#10;&#9;EQUIPMENT_NAME,&#10;&#9; M_BATCH_NUMBER,&#10;--material&#10;&#9; M_NAME,&#10;&#9; M_CODE,&#10;--实际值(实际生产了多少)&#10;&#9; M_QUANTITY,&#10;-- SUM(CASE &#10;-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- &#9;ELSE 0&#10;-- END&#10;-- )  AS M_QUANTITY,&#10;&#9;M_QUANTITYUNIT,&#10;--TARGET_QUANTITY(目标值)&#10;&#9; M_QUANTITY_TOTAL,&#10;&#9;QUANTITY_TOTAL_UNIT,&#10;&#9;T_UINTID,&#10;--inventory quantity available库存数量&#10;&#9;IN_QUANTITY,&#10;&#9;MATERIAL_UNIT1,&#10;--BAG_SIZE（单包数量）&#10;ISNULL( FullBagWeight , 0 ) AS BAG_SIZE,&#10;--fullBag（整袋数量，已经合并成斜杠数据）&#10;CASE&#10;&#9;&#9;&#10;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (&#10;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;END AS FULL_PAGE,&#10;--实际值PARITIAL_PAGE （partial Bags）&#10;&#9;&#9;Tagp_s,--&#10;&#9;&#9;CASE--目标值 （partial）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) &#10;&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE,&#10;--单位&#10;&#9;&#9;&#9;Tagp_s_UNIT,&#10;&#9;&#9;&#9;Full_Finish,&#10;--BAG_S&#10;&#9;&#9;CASE&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) &#10;&#9;&#9;&#9;&#9;&#9;END AS BAG_S,&#10;--COMPLETE&#10;&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;MATERIAL_ID,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE,&#10;--最小比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE,&#10;--最大比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX,&#10;&#9;&#9;&#9;&#9;&#9;CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;ID,&#10;&#9;&#9;&#9;&#9;&#9;CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;SELECT&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id,&#10;&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish,&#10;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID,&#10;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID &#10;&#9;&#9;&#9;&#9;) t &#10;&#9;&#9;&#9;WHERE&#10;&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- SELECT&#10;--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG,&#10;-- -- DISTINCT&#10;-- --g2.Full_QTY,g3.P_QTY,&#10;--  '' as EQUIPMENT_ID,&#10;--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id,&#10;-- &#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;a2.EQUIPMENT_NAME,&#10;-- &#9;B.NUMBER AS M_BATCH_NUMBER,&#10;-- --material&#10;-- &#9;c.NAME AS M_NAME,&#10;-- &#9;c.CODE AS M_CODE,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY,&#10;--  &#10;-- -- SUM(CASE &#10;-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- -- &#9;ELSE 0&#10;-- -- END&#10;-- -- )  AS M_QUANTITY,&#10;-- &#9;  a11.NAME AS M_QUANTITYUNIT,&#10;-- --TARGET_QUANTITY(目标值)&#10;-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;-- &#9;&#10;-- &#9;a11.ID AS T_UINTID,&#10;-- --inventory quantity available库存数量&#10;-- &#9;0 as IN_QUANTITY,&#10;-- &#9;a11.NAME as MATERIAL_UNIT1,&#10;-- --BAG_SIZE（单包数量）&#10;-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE,&#10;--  &#10;-- &#9;&#10;-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）&#10;--   CASE&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE &#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;END AS FULL_PAGE,&#10;-- --实际值PARITIAL_PAGE （partial Bags）&#10;-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       &#10;-- &#10;-- &#9;CASE--目标值 （partial）&#10;-- &#9;&#9;&#10;-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;'0' ELSE CAST (&#10;-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;END AS PARITIAL_PAGE,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish,&#10;-- --BAG_S&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) &#10;-- &#9;&#9;&#9;&#9;END AS BAG_S,&#10;-- --COMPLETE&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;-- &#10;-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= &#10;-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= &#10;-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;THEN&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;-- --完成状态前端处理&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;END AS CONSUMED_STATES,&#10;-- &#9;c.ID AS MATERIAL_ID,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE,&#10;-- --最小比例 1 1%&#10;-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN,&#10;-- --最大值&#10;-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE,&#10;-- --最大比例 1 1%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;FROM&#10;-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'&#10;-- &#9;&#9;&#10;--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID&#10;--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
                    <option name="cost" value="0" />
                    <option name="createdAt" value="1749192933324" />
                    <option name="localReferences">
                      <list>
                        <LocalReferences>
                          <option name="isRequestStage" value="true" />
                          <option name="range">
                            <RefRange />
                          </option>
                          <option name="uri" value="{&quot;path&quot;:&quot;file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs&quot;}" />
                        </LocalReferences>
                      </list>
                    </option>
                    <option name="parser">
                      <SimpleMarkdownStreamParser>
                        <option name="columns">
                          <list>
                            <option value="7" />
                            <option value="7" />
                            <option value="37" />
                            <option value="10" />
                            <option value="22" />
                            <option value="14" />
                            <option value="9" />
                            <option value="20" />
                            <option value="15" />
                            <option value="16" />
                            <option value="9" />
                            <option value="8" />
                            <option value="8" />
                            <option value="13" />
                            <option value="12" />
                            <option value="11" />
                            <option value="51" />
                            <option value="9" />
                            <option value="5" />
                            <option value="19" />
                            <option value="15" />
                            <option value="21" />
                            <option value="18" />
                            <option value="20" />
                            <option value="9" />
                            <option value="33" />
                            <option value="12" />
                            <option value="15" />
                            <option value="15" />
                            <option value="39" />
                            <option value="24" />
                            <option value="3" />
                            <option value="1" />
                            <option value="28" />
                            <option value="28" />
                            <option value="134" />
                            <option value="19" />
                            <option value="83" />
                            <option value="4" />
                            <option value="18" />
                            <option value="32" />
                            <option value="10" />
                            <option value="20" />
                            <option value="2" />
                            <option value="29" />
                            <option value="29" />
                            <option value="116" />
                            <option value="24" />
                            <option value="3" />
                            <option value="14" />
                            <option value="14" />
                            <option value="6" />
                            <option value="5" />
                            <option value="3" />
                            <option value="30" />
                            <option value="30" />
                            <option value="73" />
                            <option value="17" />
                            <option value="9" />
                            <option value="149" />
                            <option value="145" />
                            <option value="4" />
                            <option value="73" />
                            <option value="12" />
                            <option value="119" />
                            <option value="8" />
                            <option value="28" />
                            <option value="6" />
                            <option value="73" />
                            <option value="12" />
                            <option value="118" />
                            <option value="8" />
                            <option value="28" />
                            <option value="11" />
                            <option value="18" />
                            <option value="27" />
                            <option value="9" />
                            <option value="19" />
                            <option value="16" />
                            <option value="6" />
                            <option value="4" />
                            <option value="124" />
                            <option value="10" />
                            <option value="86" />
                            <option value="4" />
                            <option value="124" />
                            <option value="10" />
                            <option value="86" />
                            <option value="14" />
                            <option value="13" />
                            <option value="24" />
                            <option value="7" />
                            <option value="15" />
                            <option value="17" />
                            <option value="15" />
                            <option value="17" />
                            <option value="20" />
                            <option value="7" />
                            <option value="5" />
                            <option value="10" />
                            <option value="24" />
                            <option value="67" />
                            <option value="27" />
                            <option value="23" />
                            <option value="32" />
                            <option value="22" />
                            <option value="22" />
                            <option value="78" />
                            <option value="32" />
                            <option value="40" />
                            <option value="37" />
                            <option value="24" />
                            <option value="22" />
                            <option value="32" />
                            <option value="5" />
                            <option value="17" />
                            <option value="20" />
                            <option value="41" />
                            <option value="29" />
                            <option value="48" />
                            <option value="14" />
                            <option value="5" />
                            <option value="37" />
                            <option value="18" />
                            <option value="27" />
                            <option value="24" />
                            <option value="20" />
                            <option value="4" />
                            <option value="15" />
                            <option value="32" />
                            <option value="9" />
                            <option value="17" />
                            <option value="19" />
                            <option value="17" />
                            <option value="19" />
                            <option value="22" />
                            <option value="107" />
                            <option value="107" />
                            <option value="85" />
                            <option value="79" />
                            <option value="81" />
                            <option value="7" />
                            <option value="82" />
                            <option value="83" />
                            <option value="57" />
                            <option value="75" />
                            <option value="73" />
                            <option value="48" />
                            <option value="56" />
                            <option value="62" />
                            <option value="37" />
                            <option value="22" />
                            <option value="62" />
                            <option value="26" />
                            <option value="20" />
                            <option value="30" />
                            <option value="55" />
                            <option value="116" />
                            <option value="169" />
                            <option value="7" />
                            <option value="7" />
                            <option value="41" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="-1" />
                            <option value="8" />
                            <option value="46" />
                            <option value="13" />
                            <option value="25" />
                            <option value="22" />
                            <option value="58" />
                            <option value="25" />
                            <option value="21" />
                            <option value="30" />
                            <option value="12" />
                            <option value="20" />
                            <option value="20" />
                            <option value="3" />
                            <option value="17" />
                            <option value="73" />
                            <option value="3" />
                            <option value="14" />
                            <option value="54" />
                            <option value="12" />
                            <option value="8" />
                            <option value="22" />
                            <option value="32" />
                            <option value="24" />
                            <option value="38" />
                            <option value="35" />
                            <option value="3" />
                            <option value="22" />
                            <option value="36" />
                            <option value="20" />
                            <option value="30" />
                            <option value="18" />
                            <option value="47" />
                            <option value="3" />
                            <option value="3" />
                            <option value="28" />
                            <option value="12" />
                            <option value="37" />
                            <option value="36" />
                            <option value="25" />
                            <option value="32" />
                            <option value="79" />
                            <option value="10" />
                            <option value="23" />
                            <option value="24" />
                            <option value="94" />
                            <option value="9" />
                            <option value="23" />
                            <option value="35" />
                            <option value="46" />
                            <option value="2" />
                            <option value="22" />
                            <option value="4" />
                            <option value="35" />
                            <option value="34" />
                            <option value="20" />
                            <option value="107" />
                            <option value="7" />
                            <option value="25" />
                            <option value="8" />
                            <option value="28" />
                            <option value="46" />
                            <option value="9" />
                            <option value="7" />
                            <option value="5" />
                            <option value="36" />
                            <option value="35" />
                            <option value="82" />
                            <option value="19" />
                            <option value="12" />
                            <option value="7" />
                            <option value="9" />
                            <option value="144" />
                            <option value="148" />
                            <option value="2" />
                            <option value="70" />
                            <option value="148" />
                            <option value="71" />
                            <option value="146" />
                            <option value="8" />
                            <option value="4" />
                            <option value="23" />
                            <option value="33" />
                            <option value="12" />
                            <option value="7" />
                            <option value="10" />
                            <option value="37" />
                            <option value="23" />
                            <option value="26" />
                            <option value="23" />
                            <option value="9" />
                            <option value="7" />
                            <option value="116" />
                            <option value="13" />
                            <option value="75" />
                            <option value="7" />
                            <option value="116" />
                            <option value="13" />
                            <option value="82" />
                            <option value="2" />
                            <option value="25" />
                            <option value="2" />
                            <option value="20" />
                            <option value="37" />
                            <option value="14" />
                            <option value="22" />
                            <option value="24" />
                            <option value="22" />
                            <option value="24" />
                            <option value="27" />
                            <option value="12" />
                            <option value="80" />
                            <option value="88" />
                            <option value="62" />
                            <option value="80" />
                            <option value="78" />
                            <option value="53" />
                            <option value="60" />
                            <option value="72" />
                            <option value="47" />
                            <option value="32" />
                            <option value="4" />
                            <option value="76" />
                            <option value="116" />
                            <option value="103" />
                            <option value="65" />
                            <option value="31" />
                            <option value="69" />
                            <option value="2" />
                            <option value="124" />
                            <option value="124" />
                            <option value="111" />
                            <option value="118" />
                            <option value="121" />
                            <option value="69" />
                            <option value="45" />
                            <option value="97" />
                            <option value="38" />
                            <option value="61" />
                            <option value="159" />
                            <option value="17" />
                            <option value="30" />
                            <option value="26" />
                            <option value="27" />
                            <option value="18" />
                            <option value="16" />
                            <option value="16" />
                            <option value="21" />
                            <option value="18" />
                            <option value="21" />
                            <option value="24" />
                            <option value="18" />
                            <option value="16" />
                            <option value="21" />
                            <option value="29" />
                            <option value="17" />
                            <option value="20" />
                            <option value="21" />
                            <option value="22" />
                            <option value="24" />
                            <option value="22" />
                            <option value="24" />
                            <option value="27" />
                            <option value="24" />
                            <option value="27" />
                            <option value="27" />
                            <option value="29" />
                            <option value="22" />
                            <option value="27" />
                            <option value="15" />
                          </list>
                        </option>
                        <option name="content" value="根据SELECT&#10;DISTINCT&#10;&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG,&#10;-- DISTINCT&#10;--g2.Full_QTY,g3.P_QTY,&#10;&#9; EQUIPMENT_ID,&#10;&#9; Only_Id,&#10;&#9;PRODUCTION_ORDER_NO,&#10;&#9;EQUIPMENT_NAME,&#10;&#9; M_BATCH_NUMBER,&#10;--material&#10;&#9; M_NAME,&#10;&#9; M_CODE,&#10;--实际值(实际生产了多少)&#10;&#9; M_QUANTITY,&#10;-- SUM(CASE &#10;-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- &#9;ELSE 0&#10;-- END&#10;-- )  AS M_QUANTITY,&#10;&#9;M_QUANTITYUNIT,&#10;--TARGET_QUANTITY(目标值)&#10;&#9; M_QUANTITY_TOTAL,&#10;&#9;QUANTITY_TOTAL_UNIT,&#10;&#9;T_UINTID,&#10;--inventory quantity available库存数量&#10;&#9;IN_QUANTITY,&#10;&#9;MATERIAL_UNIT1,&#10;--BAG_SIZE（单包数量）&#10;ISNULL( FullBagWeight , 0 ) AS BAG_SIZE,&#10;--fullBag（整袋数量，已经合并成斜杠数据）&#10;CASE&#10;&#9;&#9;&#10;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (&#10;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;END AS FULL_PAGE,&#10;--实际值PARITIAL_PAGE （partial Bags）&#10;&#9;&#9;Tagp_s,--&#10;&#9;&#9;CASE--目标值 （partial）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) &#10;&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE,&#10;--单位&#10;&#9;&#9;&#9;Tagp_s_UNIT,&#10;&#9;&#9;&#9;Full_Finish,&#10;--BAG_S&#10;&#9;&#9;CASE&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) &#10;&#9;&#9;&#9;&#9;&#9;END AS BAG_S,&#10;--COMPLETE&#10;&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;MATERIAL_ID,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE,&#10;--最小比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE,&#10;--最大比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX,&#10;&#9;&#9;&#9;&#9;&#9;CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;ID,&#10;&#9;&#9;&#9;&#9;&#9;CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;SELECT&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id,&#10;&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish,&#10;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID,&#10;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID &#10;&#9;&#9;&#9;&#9;) t &#10;&#9;&#9;&#9;WHERE&#10;&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- SELECT&#10;--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG,&#10;-- -- DISTINCT&#10;-- --g2.Full_QTY,g3.P_QTY,&#10;--  '' as EQUIPMENT_ID,&#10;--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id,&#10;-- &#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;a2.EQUIPMENT_NAME,&#10;-- &#9;B.NUMBER AS M_BATCH_NUMBER,&#10;-- --material&#10;-- &#9;c.NAME AS M_NAME,&#10;-- &#9;c.CODE AS M_CODE,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY,&#10;--  &#10;-- -- SUM(CASE &#10;-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- -- &#9;ELSE 0&#10;-- -- END&#10;-- -- )  AS M_QUANTITY,&#10;-- &#9;  a11.NAME AS M_QUANTITYUNIT,&#10;-- --TARGET_QUANTITY(目标值)&#10;-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;-- &#9;&#10;-- &#9;a11.ID AS T_UINTID,&#10;-- --inventory quantity available库存数量&#10;-- &#9;0 as IN_QUANTITY,&#10;-- &#9;a11.NAME as MATERIAL_UNIT1,&#10;-- --BAG_SIZE（单包数量）&#10;-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE,&#10;--  &#10;-- &#9;&#10;-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）&#10;--   CASE&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE &#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;END AS FULL_PAGE,&#10;-- --实际值PARITIAL_PAGE （partial Bags）&#10;-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       &#10;-- &#10;-- &#9;CASE--目标值 （partial）&#10;-- &#9;&#9;&#10;-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;'0' ELSE CAST (&#10;-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;END AS PARITIAL_PAGE,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish,&#10;-- --BAG_S&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) &#10;-- &#9;&#9;&#9;&#9;END AS BAG_S,&#10;-- --COMPLETE&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;-- &#10;-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= &#10;-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= &#10;-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;THEN&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;-- --完成状态前端处理&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;END AS CONSUMED_STATES,&#10;-- &#9;c.ID AS MATERIAL_ID,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE,&#10;-- --最小比例 1 1%&#10;-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN,&#10;-- --最大值&#10;-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE,&#10;-- --最大比例 1 1%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;FROM&#10;-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'&#10;-- &#9;&#9;&#10;--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID&#10;--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
                        <option name="nodes">
                          <list>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="根据SELECT" />
                              <option name="contentId" value="-255371375" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="DISTINCT" />
                              <option name="contentId" value="1071324924" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG," />
                              <option name="contentId" value="849320344" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- DISTINCT" />
                              <option name="contentId" value="-444248708" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--g2.Full_QTY,g3.P_QTY," />
                              <option name="contentId" value="-1686820412" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; EQUIPMENT_ID," />
                              <option name="contentId" value="-1779972023" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; Only_Id," />
                              <option name="contentId" value="-1481771627" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;PRODUCTION_ORDER_NO," />
                              <option name="contentId" value="1212107645" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;EQUIPMENT_NAME," />
                              <option name="contentId" value="-1589069369" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; M_BATCH_NUMBER," />
                              <option name="contentId" value="268711765" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--material" />
                              <option name="contentId" value="314821319" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; M_NAME," />
                              <option name="contentId" value="-1034709128" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; M_CODE," />
                              <option name="contentId" value="-1044459434" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--实际值(实际生产了多少)" />
                              <option name="contentId" value="-1374403122" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; M_QUANTITY," />
                              <option name="contentId" value="680976888" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- SUM(CASE " />
                              <option name="contentId" value="-790617293" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY" />
                              <option name="contentId" value="-198961698" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;ELSE 0" />
                              <option name="contentId" value="1396848082" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- END" />
                              <option name="contentId" value="1330892347" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- )  AS M_QUANTITY," />
                              <option name="contentId" value="1028040778" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;M_QUANTITYUNIT," />
                              <option name="contentId" value="1178058210" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--TARGET_QUANTITY(目标值)" />
                              <option name="contentId" value="973227509" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9; M_QUANTITY_TOTAL," />
                              <option name="contentId" value="-1763125389" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;QUANTITY_TOTAL_UNIT," />
                              <option name="contentId" value="-1472227806" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;T_UINTID," />
                              <option name="contentId" value="-619777533" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--inventory quantity available库存数量" />
                              <option name="contentId" value="-968128356" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;IN_QUANTITY," />
                              <option name="contentId" value="-118550288" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;MATERIAL_UNIT1," />
                              <option name="contentId" value="-1314573938" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--BAG_SIZE（单包数量）" />
                              <option name="contentId" value="-782442550" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="ISNULL( FullBagWeight , 0 ) AS BAG_SIZE," />
                              <option name="contentId" value="1406227705" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--fullBag（整袋数量，已经合并成斜杠数据）" />
                              <option name="contentId" value="-1373017751" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="CASE" />
                              <option name="contentId" value="2061104" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;" />
                              <option name="contentId" value="288" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;WHEN FullBagWeight IS NULL " />
                              <option name="contentId" value="-356512744" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;OR FullBagWeight = '0' THEN" />
                              <option name="contentId" value="967245934" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (" />
                              <option name="contentId" value="-2139640800" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;VARCHAR ( 200 )," />
                              <option name="contentId" value="1811085676" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) " />
                              <option name="contentId" value="-1527732104" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;) " />
                              <option name="contentId" value="8589760" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;END AS FULL_PAGE," />
                              <option name="contentId" value="-1720653130" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--实际值PARITIAL_PAGE （partial Bags）" />
                              <option name="contentId" value="225691974" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;Tagp_s,--" />
                              <option name="contentId" value="-493488446" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;CASE--目标值 （partial）" />
                              <option name="contentId" value="1135994211" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;" />
                              <option name="contentId" value="8937" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;WHEN FullBagWeight IS NULL " />
                              <option name="contentId" value="-254667601" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;OR FullBagWeight = '0' THEN" />
                              <option name="contentId" value="1069091077" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) " />
                              <option name="contentId" value="1410725030" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE," />
                              <option name="contentId" value="411742479" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--单位" />
                              <option name="contentId" value="2065464" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;Tagp_s_UNIT," />
                              <option name="contentId" value="-1955060036" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;Full_Finish," />
                              <option name="contentId" value="-66991694" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--BAG_S" />
                              <option name="contentId" value="-1660735460" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;CASE" />
                              <option name="contentId" value="268035152" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="277056" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL " />
                              <option name="contentId" value="-1392435464" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN" />
                              <option name="contentId" value="-68676786" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) " />
                              <option name="contentId" value="580648565" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;END AS BAG_S," />
                              <option name="contentId" value="-493171376" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--COMPLETE" />
                              <option name="contentId" value="198936281" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY " />
                              <option name="contentId" value="-10781505" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN" />
                              <option name="contentId" value="-1992167466" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="8588745" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (" />
                              <option name="contentId" value="894866905" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(" />
                              <option name="contentId" value="-7873225" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY )," />
                              <option name="contentId" value="-651351784" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 " />
                              <option name="contentId" value="-917946279" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) " />
                              <option name="contentId" value="-510328645" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;) " />
                              <option name="contentId" value="-336149344" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (" />
                              <option name="contentId" value="-69265590" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(" />
                              <option name="contentId" value="-7873225" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY )," />
                              <option name="contentId" value="-1800935740" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 " />
                              <option name="contentId" value="-917946279" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) " />
                              <option name="contentId" value="-510328645" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;) THEN" />
                              <option name="contentId" value="422152980" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' " />
                              <option name="contentId" value="1539945685" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES," />
                              <option name="contentId" value="1796836548" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--完成状态前端处理" />
                              <option name="contentId" value="2068067955" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;CONSUMED_STATES," />
                              <option name="contentId" value="-541136045" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;MATERIAL_ID," />
                              <option name="contentId" value="1280334754" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--做业务处理" />
                              <option name="contentId" value="709967683" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--最小值" />
                              <option name="contentId" value="68989805" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE," />
                              <option name="contentId" value="-1665792507" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--最小比例 1 1%" />
                              <option name="contentId" value="1869159197" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN," />
                              <option name="contentId" value="1946041835" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--最大值" />
                              <option name="contentId" value="68966741" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE," />
                              <option name="contentId" value="-2085083225" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--最大比例 1 1%" />
                              <option name="contentId" value="-1996573947" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX," />
                              <option name="contentId" value="-1180982329" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CON_SUMED," />
                              <option name="contentId" value="-2009218712" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;BATCH_ID," />
                              <option name="contentId" value="-686198749" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID," />
                              <option name="contentId" value="544335139" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;ID," />
                              <option name="contentId" value="-1830663160" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CREATEDATE," />
                              <option name="contentId" value="312729945" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CREATEUSERID," />
                              <option name="contentId" value="-1704650975" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;MODIFYDATE," />
                              <option name="contentId" value="-284872613" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;MODIFYUSERID," />
                              <option name="contentId" value="-475091549" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP " />
                              <option name="contentId" value="1124779836" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;FROM" />
                              <option name="contentId" value="-1828836886" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;(" />
                              <option name="contentId" value="266251135" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;SELECT" />
                              <option name="contentId" value="496761861" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID," />
                              <option name="contentId" value="-1609841518" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id," />
                              <option name="contentId" value="-730843265" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO," />
                              <option name="contentId" value="1559823836" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME," />
                              <option name="contentId" value="2069915731" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER," />
                              <option name="contentId" value="-1999933015" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME," />
                              <option name="contentId" value="-1772810133" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE," />
                              <option name="contentId" value="-1327987545" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY," />
                              <option name="contentId" value="543036745" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT," />
                              <option name="contentId" value="-688529211" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL," />
                              <option name="contentId" value="1908801074" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT," />
                              <option name="contentId" value="1717750047" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID," />
                              <option name="contentId" value="1799176022" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY," />
                              <option name="contentId" value="-720774075" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1," />
                              <option name="contentId" value="1113805937" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="266251104" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY," />
                              <option name="contentId" value="1327264195" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY," />
                              <option name="contentId" value="1718140386" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s," />
                              <option name="contentId" value="1383844441" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT," />
                              <option name="contentId" value="627430361" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish," />
                              <option name="contentId" value="415119108" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY," />
                              <option name="contentId" value="-56368377" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="266251104" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN" />
                              <option name="contentId" value="-647777560" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' " />
                              <option name="contentId" value="1539945685" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES," />
                              <option name="contentId" value="542294131" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID," />
                              <option name="contentId" value="1725092406" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED," />
                              <option name="contentId" value="248628246" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="8588745" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;a.BATCH_ID," />
                              <option name="contentId" value="1798030614" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID," />
                              <option name="contentId" value="-1373528620" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.ID," />
                              <option name="contentId" value="1695451328" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.CREATEDATE," />
                              <option name="contentId" value="-1811683823" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID," />
                              <option name="contentId" value="1138150873" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE," />
                              <option name="contentId" value="1885680915" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID," />
                              <option name="contentId" value="-1927256997" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP," />
                              <option name="contentId" value="-1425476208" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent," />
                              <option name="contentId" value="-1222484295" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent," />
                              <option name="contentId" value="548322965" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh," />
                              <option name="contentId" value="-1158113069" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight," />
                              <option name="contentId" value="-990127551" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge " />
                              <option name="contentId" value="83008839" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;FROM" />
                              <option name="contentId" value="-1828836886" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID" />
                              <option name="contentId" value="1051270383" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID" />
                              <option name="contentId" value="-1914592795" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID" />
                              <option name="contentId" value="-2068785572" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单" />
                              <option name="contentId" value="2126995506" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID" />
                              <option name="contentId" value="-998272726" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID" />
                              <option name="contentId" value="-1805038571" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID " />
                              <option name="contentId" value="1702535555" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID " />
                              <option name="contentId" value="20002543" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID " />
                              <option name="contentId" value="-42306783" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0" />
                              <option name="contentId" value="-1902058409" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID " />
                              <option name="contentId" value="1267907259" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID " />
                              <option name="contentId" value="634066342" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 " />
                              <option name="contentId" value="-524882807" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' " />
                              <option name="contentId" value="848743097" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID" />
                              <option name="contentId" value="1201957198" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾" />
                              <option name="contentId" value="814016827" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID " />
                              <option name="contentId" value="671551847" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;) t " />
                              <option name="contentId" value="-1829747773" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;WHERE" />
                              <option name="contentId" value="-1756755074" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY" />
                              <option name="contentId" value="-2064888359" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- SELECT" />
                              <option name="contentId" value="-2131363972" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG," />
                              <option name="contentId" value="2076949007" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- DISTINCT" />
                              <option name="contentId" value="2092863228" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --g2.Full_QTY,g3.P_QTY," />
                              <option name="contentId" value="-1420285116" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  '' as EQUIPMENT_ID," />
                              <option name="contentId" value="-687592878" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id," />
                              <option name="contentId" value="907634075" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;f.PRODUCTION_ORDER_NO," />
                              <option name="contentId" value="1598326597" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;a2.EQUIPMENT_NAME," />
                              <option name="contentId" value="-2089875908" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;B.NUMBER AS M_BATCH_NUMBER," />
                              <option name="contentId" value="297101792" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --material" />
                              <option name="contentId" value="-157525689" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;c.NAME AS M_NAME," />
                              <option name="contentId" value="309760354" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;c.CODE AS M_CODE," />
                              <option name="contentId" value="754582942" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;" />
                              <option name="contentId" value="1384841" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  --实际值(实际生产了多少)" />
                              <option name="contentId" value="-683798674" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY," />
                              <option name="contentId" value="-298846053" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  " />
                              <option name="contentId" value="1384864" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- SUM(CASE " />
                              <option name="contentId" value="550441395" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY" />
                              <option name="contentId" value="1981973598" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;ELSE 0" />
                              <option name="contentId" value="924501074" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- END" />
                              <option name="contentId" value="1052220603" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- )  AS M_QUANTITY," />
                              <option name="contentId" value="-2137057590" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;  a11.NAME AS M_QUANTITYUNIT," />
                              <option name="contentId" value="-369853636" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --TARGET_QUANTITY(目标值)" />
                              <option name="contentId" value="150541429" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL," />
                              <option name="contentId" value="669765481" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT," />
                              <option name="contentId" value="-1764861112" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;" />
                              <option name="contentId" value="1384841" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;a11.ID AS T_UINTID," />
                              <option name="contentId" value="1694654093" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --inventory quantity available库存数量" />
                              <option name="contentId" value="-2063558884" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;0 as IN_QUANTITY," />
                              <option name="contentId" value="-934160068" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;a11.NAME as MATERIAL_UNIT1," />
                              <option name="contentId" value="1925533864" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --BAG_SIZE（单包数量）" />
                              <option name="contentId" value="-1691316662" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE," />
                              <option name="contentId" value="-75810066" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--  " />
                              <option name="contentId" value="1384864" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;" />
                              <option name="contentId" value="1384841" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）" />
                              <option name="contentId" value="-2135366656" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--   CASE&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="627779568" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL " />
                              <option name="contentId" value="-1672732956" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN" />
                              <option name="contentId" value="906161834" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE " />
                              <option name="contentId" value="1319152489" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 )," />
                              <option name="contentId" value="1749803742" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) " />
                              <option name="contentId" value="-495667537" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;) +" />
                              <option name="contentId" value="948698635" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (" />
                              <option name="contentId" value="2067863022" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 )," />
                              <option name="contentId" value="1727920396" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) " />
                              <option name="contentId" value="1800421279" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;) " />
                              <option name="contentId" value="-385038816" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;END AS FULL_PAGE," />
                              <option name="contentId" value="1463252758" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --实际值PARITIAL_PAGE （partial Bags）" />
                              <option name="contentId" value="883092166" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       " />
                              <option name="contentId" value="-1304298123" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- " />
                              <option name="contentId" value="44672" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;CASE--目标值 （partial）" />
                              <option name="contentId" value="363704922" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;" />
                              <option name="contentId" value="42930080" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL " />
                              <option name="contentId" value="-405677052" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN" />
                              <option name="contentId" value="1916865930" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;'0' ELSE CAST (" />
                              <option name="contentId" value="1932797955" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) " />
                              <option name="contentId" value="-1954204886" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;) " />
                              <option name="contentId" value="-970230976" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;END AS PARITIAL_PAGE," />
                              <option name="contentId" value="-1498613841" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;--单位" />
                              <option name="contentId" value="-10632232" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;a11.NAME AS Tagp_s_UNIT," />
                              <option name="contentId" value="-230852583" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish," />
                              <option name="contentId" value="253327570" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --BAG_S" />
                              <option name="contentId" value="-1709624932" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;CASE" />
                              <option name="contentId" value="-968447943" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;" />
                              <option name="contentId" value="1330832489" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL " />
                              <option name="contentId" value="-1519014373" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN" />
                              <option name="contentId" value="2019499155" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) " />
                              <option name="contentId" value="786329263" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;END AS BAG_S," />
                              <option name="contentId" value="1780218809" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --COMPLETE" />
                              <option name="contentId" value="-273410727" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;CASE" />
                              <option name="contentId" value="-968447943" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="-385039831" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY " />
                              <option name="contentId" value="1650950607" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN" />
                              <option name="contentId" value="1568407126" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- " />
                              <option name="contentId" value="44672" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= " />
                              <option name="contentId" value="428346377" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))" />
                              <option name="contentId" value="-1970694267" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= " />
                              <option name="contentId" value="500147115" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))" />
                              <option name="contentId" value="2004698095" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;THEN" />
                              <option name="contentId" value="-10123843" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;" />
                              <option name="contentId" value="42930080" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' " />
                              <option name="contentId" value="428884277" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES," />
                              <option name="contentId" value="-2068633171" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --完成状态前端处理" />
                              <option name="contentId" value="1595720947" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;CASE" />
                              <option name="contentId" value="-968447943" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="948667136" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN" />
                              <option name="contentId" value="-2031398070" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' " />
                              <option name="contentId" value="428884277" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;END AS CONSUMED_STATES," />
                              <option name="contentId" value="-616139597" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;c.ID AS MATERIAL_ID," />
                              <option name="contentId" value="-1235909130" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --做业务处理" />
                              <option name="contentId" value="661078211" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --最小值" />
                              <option name="contentId" value="-909830931" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE," />
                              <option name="contentId" value="-1799135704" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --最小比例 1 1%" />
                              <option name="contentId" value="111303837" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN," />
                              <option name="contentId" value="1252923272" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --最大值" />
                              <option name="contentId" value="-909853995" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE," />
                              <option name="contentId" value="1948179227" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- --最大比例 1 1%" />
                              <option name="contentId" value="540537989" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX," />
                              <option name="contentId" value="734512686" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- " />
                              <option name="contentId" value="44672" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED," />
                              <option name="contentId" value="1965471862" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- " />
                              <option name="contentId" value="44672" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID," />
                              <option name="contentId" value="-840461130" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID," />
                              <option name="contentId" value="-1983184332" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID," />
                              <option name="contentId" value="910594144" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE," />
                              <option name="contentId" value="923422129" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID," />
                              <option name="contentId" value="1054985593" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE," />
                              <option name="contentId" value="325819571" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID," />
                              <option name="contentId" value="-2010422277" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP " />
                              <option name="contentId" value="-806202908" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;FROM" />
                              <option name="contentId" value="1137940874" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID" />
                              <option name="contentId" value="587757926" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID" />
                              <option name="contentId" value="-1994415739" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID" />
                              <option name="contentId" value="2132745724" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单" />
                              <option name="contentId" value="-984678446" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID" />
                              <option name="contentId" value="-1448437558" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID" />
                              <option name="contentId" value="-1736576907" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID" />
                              <option name="contentId" value="1181079005" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID " />
                              <option name="contentId" value="509028746" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' " />
                              <option name="contentId" value="-1419435835" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'" />
                              <option name="contentId" value="569810491" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;" />
                              <option name="contentId" value="42930080" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID" />
                              <option name="contentId" value="-758980020" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0" />
                              <option name="contentId" value="-727659557" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 " />
                              <option name="contentId" value="2146551238" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null" />
                              <option name="contentId" value="1527436309" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID" />
                              <option name="contentId" value="286178203" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null" />
                              <option name="contentId" value="1450175375" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- " />
                              <option name="contentId" value="44672" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID" />
                              <option name="contentId" value="312684656" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID" />
                              <option name="contentId" value="856904379" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID " />
                              <option name="contentId" value="521617554" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID " />
                              <option name="contentId" value="1332397764" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾" />
                              <option name="contentId" value="689790363" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID " />
                              <option name="contentId" value="-1899957007" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID" />
                              <option name="contentId" value="1604254796" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY " />
                              <option name="contentId" value="-1319147140" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL " />
                              <option name="contentId" value="-999417685" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID" />
                              <option name="contentId" value="-924138589" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'" />
                              <option name="contentId" value="-186616809" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY " />
                              <option name="contentId" value="-815601784" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO," />
                              <option name="contentId" value="1379595356" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID," />
                              <option name="contentId" value="-73352100" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME," />
                              <option name="contentId" value="-1002717476" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER," />
                              <option name="contentId" value="-476486578" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME," />
                              <option name="contentId" value="544662381" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE," />
                              <option name="contentId" value="534912075" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY," />
                              <option name="contentId" value="1522115340" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY," />
                              <option name="contentId" value="-1265082786" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1," />
                              <option name="contentId" value="911007875" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY," />
                              <option name="contentId" value="-267183111" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME," />
                              <option name="contentId" value="-822975221" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID," />
                              <option name="contentId" value="-1171809541" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY," />
                              <option name="contentId" value="128449506" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1," />
                              <option name="contentId" value="-1964200772" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID," />
                              <option name="contentId" value="381412485" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID," />
                              <option name="contentId" value="-840461130" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;" />
                              <option name="contentId" value="-660667319" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE," />
                              <option name="contentId" value="923422129" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID," />
                              <option name="contentId" value="1054985593" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE," />
                              <option name="contentId" value="325819571" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID," />
                              <option name="contentId" value="-2010422277" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP," />
                              <option name="contentId" value="-806202896" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY," />
                              <option name="contentId" value="-756684643" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE," />
                              <option name="contentId" value="724037358" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE," />
                              <option name="contentId" value="-864310544" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE," />
                              <option name="contentId" value="-1131869264" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE," />
                              <option name="contentId" value="-1373741258" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE," />
                              <option name="contentId" value="2077347055" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id," />
                              <option name="contentId" value="-1318100826" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
                              <option name="contentId" value="-972357351" />
                              <option name="indentSize" value="0" />
                              <option name="settled" value="false" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                          </list>
                        </option>
                        <option name="parserState">
                          <ParserState>
                            <option name="line" value="344" />
                            <option name="column" value="31" />
                            <option name="offset" value="12394" />
                            <option name="moveCursor" value="true" />
                          </ParserState>
                        </option>
                      </SimpleMarkdownStreamParser>
                    </option>
                    <option name="role" value="user" />
                    <option name="selectedRanges">
                      <list />
                    </option>
                    <option name="uri" value="" />
                    <option name="variables">
                      <list />
                    </option>
                  </UIChatMessage>
                  <UIChatMessage>
                    <option name="actions">
                      <list />
                    </option>
                    <option name="codebaseReferences">
                      <list />
                    </option>
                    <option name="codebaseStatus" value="init" />
                    <option name="command" value="" />
                    <option name="content" value="&lt;think&gt;&#10;根据用户提供的SQL查询和实体类代码，我们需要为实体类`BBatchDetailIiViewEntity`中的字段添加注释。注释内容应基于SQL查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - MaxPvalue&#10;   - SetMax&#10;   - ConSumed&#10;   - BatchId&#10;   - ProductionOrderId&#10;&#10; 在SQL查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  1. MAX_PVALUE: 最大值（根据公式计算得出）&#10;  2. SET_MAX: 最大比例（来自物料属性PreweighToleranceMaxPercent）&#10;  3. CON_SUMED: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  4. BATCH_ID: 批次ID&#10;  5. PRODUCTION_ORDER_ID: 生产订单ID&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - MaxPvalue (MAX_PVALUE): &#10;      计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - SetMax (SET_MAX):&#10;      来源: 物料的PreweighToleranceMaxPercent属性&#10;      描述: 预称重最大容忍百分比（例如，1表示1%）&#10;&#10;  - ConSumed (CON_SUMED):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为CONSUMED_STATES的列，它根据条件返回'OK'或''，表示消耗状态。但实体类中的ConSumed对应的是查询中的CON_SUMED列，而该列在查询中被固定设置为空字符串（'' AS CON_SUMED）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由CONSUMED_STATES列表示的。然而，在实体类中，我们并没有看到CONSUMED_STATES字段，所以这里我们按照查询中CON_SUMED列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的t表中，CON_SUMED列是直接来自a表的吗？实际上，在子查询中，CON_SUMED列被设置为固定空字符串（'' AS CON_SUMED）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，ConSumed字段的类型是string，并且不可为空（Nullable:False）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - BatchId (BATCH_ID): 批次ID&#10;  - ProductionOrderId (PRODUCTION_ORDER_ID): 生产订单ID&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/think&gt;&#10;&#10;根据提供的SQL查询和实体类代码，我将为`BBatchDetailIiViewEntity`实体类添加注释说明。以下是带详细注释的实体类代码：&#10;&#10;```csharp&#10;/// &lt;summary&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/summary&gt;&#10;public class BBatchDetailIiViewEntity&#10;{&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;    /// 说明：基于物料属性&quot;PreweighToleranceMaxPercent&quot;计算的目标值上限&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;MAX_PVALUE&quot;)]&#10;    public decimal? MaxPvalue { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;PreweighToleranceMaxPercent&quot;&#10;    /// 说明：允许的最大偏差百分比（如1表示1%）&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;SET_MAX&quot;)]&#10;    public int? SetMax { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;OK&quot;：已消耗&#10;    /// 来源：PPM_B_PO_CONSUME_ACTUAL消耗记录存在性检查&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;CON_SUMED&quot;)]&#10;    public string ConSumed { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：PPM_B_BATCH&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;BATCH_ID&quot;)]&#10;    public string BatchId { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 生产订单ID&#10;    /// 关联表：PPM_B_PRODUCTION_ORDER&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;PRODUCTION_ORDER_ID&quot;)]&#10;    public string ProductionOrderId { get; set; }&#10;}&#10;```&#10;&#10;### 关键字段说明：&#10;1. **MaxPvalue**  &#10;   - 表示预称重允许的最大值&#10;   - 计算逻辑：`目标值 × (1 + 最大容忍百分比)`&#10;   - 用于判断实际产量是否超出允许范围&#10;&#10;2. **SetMax**  &#10;   - 来自物料属性表`PreweighToleranceMaxPercent`字段&#10;   - 控制生产允许的正向偏差范围&#10;   - 示例：值=1 表示允许超过目标值1%&#10;&#10;3. **ConSumed**  &#10;   - 消耗状态标记位&#10;   - 通过检查`PPM_B_PO_CONSUME_ACTUAL`表中是否存在消耗记录确定&#10;   - 业务逻辑：存在消耗记录=已消耗(&quot;OK&quot;)，无记录=未消耗(空字符串)&#10;&#10;4. **BatchId**  &#10;   - 批次系统的核心标识符&#10;   - 关联批次主表`PPM_B_BATCH`&#10;&#10;5. **ProductionOrderId**  &#10;   - 生产订单系统的核心标识符&#10;   - 关联工单主表`PPM_B_PRODUCTION_ORDER`&#10;&#10;&gt; 注意：所有字段映射均基于SQL查询结果集的列名，通过`[SugarColumn]`特性实现ORM映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。" />
                    <option name="cost" value="57810" />
                    <option name="createdAt" value="1749192991182" />
                    <option name="followup">
                      <list />
                    </option>
                    <option name="id" value="01974405ffcb7aafbdee9068b2657329" />
                    <option name="knowledgeReferences">
                      <list />
                    </option>
                    <option name="localReferences">
                      <list>
                        <LocalReferences>
                          <option name="isRequestStage" value="true" />
                          <option name="range">
                            <RefRange />
                          </option>
                          <option name="uri" value="{&quot;path&quot;:&quot;file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs&quot;}" />
                        </LocalReferences>
                      </list>
                    </option>
                    <option name="parser">
                      <SimpleMarkdownStreamParser>
                        <option name="columns">
                          <list>
                            <option value="6" />
                            <option value="83" />
                            <option value="-1" />
                            <option value="10" />
                            <option value="13" />
                            <option value="10" />
                            <option value="12" />
                            <option value="11" />
                            <option value="21" />
                            <option value="-1" />
                            <option value="33" />
                            <option value="-1" />
                            <option value="29" />
                            <option value="52" />
                            <option value="46" />
                            <option value="18" />
                            <option value="31" />
                            <option value="-1" />
                            <option value="43" />
                            <option value="-1" />
                            <option value="21" />
                            <option value="-1" />
                            <option value="27" />
                            <option value="67" />
                            <option value="34" />
                            <option value="-1" />
                            <option value="20" />
                            <option value="41" />
                            <option value="29" />
                            <option value="-1" />
                            <option value="24" />
                            <option value="286" />
                            <option value="-1" />
                            <option value="105" />
                            <option value="-1" />
                            <option value="105" />
                            <option value="-1" />
                            <option value="27" />
                            <option value="50" />
                            <option value="-1" />
                            <option value="18" />
                            <option value="-1" />
                            <option value="46" />
                            <option value="-1" />
                            <option value="7" />
                            <option value="-1" />
                            <option value="7" />
                            <option value="-1" />
                            <option value="70" />
                            <option value="-1" />
                            <option value="8" />
                            <option value="12" />
                            <option value="15" />
                            <option value="13" />
                            <option value="36" />
                            <option value="0" />
                            <option value="16" />
                            <option value="15" />
                            <option value="69" />
                            <option value="53" />
                            <option value="17" />
                            <option value="43" />
                            <option value="42" />
                            <option value="-1" />
                            <option value="16" />
                            <option value="17" />
                            <option value="43" />
                            <option value="28" />
                            <option value="17" />
                            <option value="40" />
                            <option value="35" />
                            <option value="-1" />
                            <option value="16" />
                            <option value="13" />
                            <option value="11" />
                            <option value="19" />
                            <option value="19" />
                            <option value="42" />
                            <option value="17" />
                            <option value="42" />
                            <option value="39" />
                            <option value="-1" />
                            <option value="16" />
                            <option value="14" />
                            <option value="22" />
                            <option value="17" />
                            <option value="41" />
                            <option value="38" />
                            <option value="-1" />
                            <option value="16" />
                            <option value="13" />
                            <option value="33" />
                            <option value="17" />
                            <option value="52" />
                            <option value="48" />
                            <option value="0" />
                            <option value="2" />
                            <option value="-1" />
                            <option value="10" />
                            <option value="17" />
                            <option value="15" />
                            <option value="30" />
                            <option value="20" />
                            <option value="-1" />
                            <option value="14" />
                            <option value="42" />
                            <option value="17" />
                            <option value="22" />
                            <option value="-1" />
                            <option value="16" />
                            <option value="11" />
                            <option value="45" />
                            <option value="39" />
                            <option value="-1" />
                            <option value="15" />
                            <option value="14" />
                            <option value="23" />
                            <option value="-1" />
                            <option value="25" />
                            <option value="16" />
                            <option value="34" />
                            <option value="-1" />
                            <option value="86" />
                          </list>
                        </option>
                        <option name="content" value="&lt;think&gt;&#10;根据用户提供的SQL查询和实体类代码，我们需要为实体类`BBatchDetailIiViewEntity`中的字段添加注释。注释内容应基于SQL查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - MaxPvalue&#10;   - SetMax&#10;   - ConSumed&#10;   - BatchId&#10;   - ProductionOrderId&#10;&#10; 在SQL查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  1. MAX_PVALUE: 最大值（根据公式计算得出）&#10;  2. SET_MAX: 最大比例（来自物料属性PreweighToleranceMaxPercent）&#10;  3. CON_SUMED: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  4. BATCH_ID: 批次ID&#10;  5. PRODUCTION_ORDER_ID: 生产订单ID&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - MaxPvalue (MAX_PVALUE): &#10;      计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - SetMax (SET_MAX):&#10;      来源: 物料的PreweighToleranceMaxPercent属性&#10;      描述: 预称重最大容忍百分比（例如，1表示1%）&#10;&#10;  - ConSumed (CON_SUMED):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为CONSUMED_STATES的列，它根据条件返回'OK'或''，表示消耗状态。但实体类中的ConSumed对应的是查询中的CON_SUMED列，而该列在查询中被固定设置为空字符串（'' AS CON_SUMED）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由CONSUMED_STATES列表示的。然而，在实体类中，我们并没有看到CONSUMED_STATES字段，所以这里我们按照查询中CON_SUMED列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的t表中，CON_SUMED列是直接来自a表的吗？实际上，在子查询中，CON_SUMED列被设置为固定空字符串（'' AS CON_SUMED）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，ConSumed字段的类型是string，并且不可为空（Nullable:False）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - BatchId (BATCH_ID): 批次ID&#10;  - ProductionOrderId (PRODUCTION_ORDER_ID): 生产订单ID&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/think&gt;&#10;&#10;根据提供的SQL查询和实体类代码，我将为`BBatchDetailIiViewEntity`实体类添加注释说明。以下是带详细注释的实体类代码：&#10;&#10;```csharp&#10;/// &lt;summary&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/summary&gt;&#10;public class BBatchDetailIiViewEntity&#10;{&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;    /// 说明：基于物料属性&quot;PreweighToleranceMaxPercent&quot;计算的目标值上限&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;MAX_PVALUE&quot;)]&#10;    public decimal? MaxPvalue { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;PreweighToleranceMaxPercent&quot;&#10;    /// 说明：允许的最大偏差百分比（如1表示1%）&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;SET_MAX&quot;)]&#10;    public int? SetMax { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;OK&quot;：已消耗&#10;    /// 来源：PPM_B_PO_CONSUME_ACTUAL消耗记录存在性检查&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;CON_SUMED&quot;)]&#10;    public string ConSumed { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：PPM_B_BATCH&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;BATCH_ID&quot;)]&#10;    public string BatchId { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 生产订单ID&#10;    /// 关联表：PPM_B_PRODUCTION_ORDER&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;PRODUCTION_ORDER_ID&quot;)]&#10;    public string ProductionOrderId { get; set; }&#10;}&#10;```&#10;&#10;### 关键字段说明：&#10;1. **MaxPvalue**  &#10;   - 表示预称重允许的最大值&#10;   - 计算逻辑：`目标值 × (1 + 最大容忍百分比)`&#10;   - 用于判断实际产量是否超出允许范围&#10;&#10;2. **SetMax**  &#10;   - 来自物料属性表`PreweighToleranceMaxPercent`字段&#10;   - 控制生产允许的正向偏差范围&#10;   - 示例：值=1 表示允许超过目标值1%&#10;&#10;3. **ConSumed**  &#10;   - 消耗状态标记位&#10;   - 通过检查`PPM_B_PO_CONSUME_ACTUAL`表中是否存在消耗记录确定&#10;   - 业务逻辑：存在消耗记录=已消耗(&quot;OK&quot;)，无记录=未消耗(空字符串)&#10;&#10;4. **BatchId**  &#10;   - 批次系统的核心标识符&#10;   - 关联批次主表`PPM_B_BATCH`&#10;&#10;5. **ProductionOrderId**  &#10;   - 生产订单系统的核心标识符&#10;   - 关联工单主表`PPM_B_PRODUCTION_ORDER`&#10;&#10;&gt; 注意：所有字段映射均基于SQL查询结果集的列名，通过`[SugarColumn]`特性实现ORM映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。&#10;" />
                        <option name="nodes">
                          <list>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&lt;think&gt;&#10;根据用户提供的SQL查询和实体类代码，我们需要为实体类`BBatchDetailIiViewEntity`中的字段添加注释。注释内容应基于SQL查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - MaxPvalue&#10;   - SetMax&#10;   - ConSumed&#10;   - BatchId&#10;   - ProductionOrderId&#10;&#10; 在SQL查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  1. MAX_PVALUE: 最大值（根据公式计算得出）&#10;  2. SET_MAX: 最大比例（来自物料属性PreweighToleranceMaxPercent）&#10;  3. CON_SUMED: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  4. BATCH_ID: 批次ID&#10;  5. PRODUCTION_ORDER_ID: 生产订单ID&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - MaxPvalue (MAX_PVALUE): &#10;      计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - SetMax (SET_MAX):&#10;      来源: 物料的PreweighToleranceMaxPercent属性&#10;      描述: 预称重最大容忍百分比（例如，1表示1%）&#10;&#10;  - ConSumed (CON_SUMED):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为CONSUMED_STATES的列，它根据条件返回'OK'或''，表示消耗状态。但实体类中的ConSumed对应的是查询中的CON_SUMED列，而该列在查询中被固定设置为空字符串（'' AS CON_SUMED）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由CONSUMED_STATES列表示的。然而，在实体类中，我们并没有看到CONSUMED_STATES字段，所以这里我们按照查询中CON_SUMED列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的t表中，CON_SUMED列是直接来自a表的吗？实际上，在子查询中，CON_SUMED列被设置为固定空字符串（'' AS CON_SUMED）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，ConSumed字段的类型是string，并且不可为空（Nullable:False）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - BatchId (BATCH_ID): 批次ID&#10;  - ProductionOrderId (PRODUCTION_ORDER_ID): 生产订单ID&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/think&gt;" />
                              <option name="contentId" value="-1964332012" />
                              <option name="settled" value="true" />
                              <option name="type" value="reasoning" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="根据提供的SQL查询和实体类代码，我将为`BBatchDetailIiViewEntity`实体类添加注释说明。以下是带详细注释的实体类代码：" />
                              <option name="contentId" value="-182517447" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="/// &lt;summary&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/summary&gt;&#10;public class BBatchDetailIiViewEntity&#10;{&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;    /// 说明：基于物料属性&quot;PreweighToleranceMaxPercent&quot;计算的目标值上限&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;MAX_PVALUE&quot;)]&#10;    public decimal? MaxPvalue { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;PreweighToleranceMaxPercent&quot;&#10;    /// 说明：允许的最大偏差百分比（如1表示1%）&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;SET_MAX&quot;)]&#10;    public int? SetMax { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;OK&quot;：已消耗&#10;    /// 来源：PPM_B_PO_CONSUME_ACTUAL消耗记录存在性检查&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;CON_SUMED&quot;)]&#10;    public string ConSumed { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：PPM_B_BATCH&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;BATCH_ID&quot;)]&#10;    public string BatchId { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 生产订单ID&#10;    /// 关联表：PPM_B_PRODUCTION_ORDER&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;PRODUCTION_ORDER_ID&quot;)]&#10;    public string ProductionOrderId { get; set; }&#10;}&#10;" />
                              <option name="contentId" value="-1998371479" />
                              <option name="expectEnding" value="```" />
                              <option name="indentSize" value="0" />
                              <option name="language" value="csharp" />
                              <option name="settled" value="true" />
                              <option name="type" value="code-block" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value=" 关键字段说明：" />
                              <option name="contentId" value="1662701671" />
                              <option name="level" value="3" />
                              <option name="settled" value="true" />
                              <option name="type" value="heading" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="1. **MaxPvalue**  " />
                              <option name="contentId" value="-1187490078" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="表示预称重允许的最大值" />
                              <option name="contentId" value="1160760343" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="计算逻辑：`目标值 × (1 + 最大容忍百分比)`" />
                              <option name="contentId" value="580441364" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="用于判断实际产量是否超出允许范围" />
                              <option name="contentId" value="199554674" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="2. **SetMax**  " />
                              <option name="contentId" value="-32722266" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="来自物料属性表`PreweighToleranceMaxPercent`字段" />
                              <option name="contentId" value="-2129509793" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="控制生产允许的正向偏差范围" />
                              <option name="contentId" value="-1191155724" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="示例：值=1 表示允许超过目标值1%" />
                              <option name="contentId" value="-1658130925" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="3. **ConSumed**  " />
                              <option name="contentId" value="1403126477" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="消耗状态标记位" />
                              <option name="contentId" value="852563402" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="通过检查`PPM_B_PO_CONSUME_ACTUAL`表中是否存在消耗记录确定" />
                              <option name="contentId" value="532801560" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="业务逻辑：存在消耗记录=已消耗(&quot;OK&quot;)，无记录=未消耗(空字符串)" />
                              <option name="contentId" value="-183253873" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="4. **BatchId**  " />
                              <option name="contentId" value="-453318609" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="批次系统的核心标识符" />
                              <option name="contentId" value="-1018100220" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="关联批次主表`PPM_B_BATCH`" />
                              <option name="contentId" value="-758104865" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="5. **ProductionOrderId**  " />
                              <option name="contentId" value="-859359895" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="生产订单系统的核心标识符" />
                              <option name="contentId" value="1215261335" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="关联工单主表`PPM_B_PRODUCTION_ORDER`" />
                              <option name="contentId" value="172964743" />
                              <option name="indentSize" value="3" />
                              <option name="settled" value="true" />
                              <option name="startSymbol" value="-" />
                              <option name="type" value="list-item" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="&gt; 注意：所有字段映射均基于SQL查询结果集的列名，通过`[SugarColumn]`特性实现ORM映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。" />
                              <option name="contentId" value="1494319492" />
                              <option name="settled" value="true" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="" />
                              <option name="contentId" value="0" />
                              <option name="settled" value="false" />
                              <option name="type" value="undetermined" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                          </list>
                        </option>
                        <option name="parserState">
                          <ParserState>
                            <option name="line" value="123" />
                            <option name="column" value="-1" />
                            <option name="offset" value="3214" />
                            <option name="moveCursor" value="true" />
                            <option name="tagBuffer" value="&lt;" />
                          </ParserState>
                        </option>
                      </SimpleMarkdownStreamParser>
                    </option>
                    <option name="references">
                      <list />
                    </option>
                    <option name="role" value="assistant" />
                    <option name="topic" value="" />
                  </UIChatMessage>
                  <UIChatMessage>
                    <option name="command" value="" />
                    <option name="content" value="如果PreparedWeight为空则改成0" />
                    <option name="cost" value="0" />
                    <option name="createdAt" value="1749203410554" />
                    <option name="localReferences">
                      <list>
                        <LocalReferences>
                          <option name="isRequestStage" value="true" />
                          <option name="range">
                            <RefRange />
                          </option>
                          <option name="uri" value="{&quot;path&quot;:&quot;file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/ViewModels/MKM/BatchConsumeRequirementSummary.cs&quot;}" />
                        </LocalReferences>
                      </list>
                    </option>
                    <option name="parser">
                      <SimpleMarkdownStreamParser>
                        <option name="content" value="如果PreparedWeight为空则改成0" />
                        <option name="nodes">
                          <list>
                            <MarkdownNode>
                              <option name="children">
                                <list />
                              </option>
                              <option name="content" value="如果PreparedWeight为空则改成0" />
                              <option name="contentId" value="1552849071" />
                              <option name="settled" value="false" />
                              <option name="type" value="line" />
                              <option name="xmlData">
                                <XmlNode />
                              </option>
                            </MarkdownNode>
                          </list>
                        </option>
                        <option name="parserState">
                          <ParserState>
                            <option name="column" value="21" />
                            <option name="offset" value="22" />
                            <option name="moveCursor" value="true" />
                          </ParserState>
                        </option>
                      </SimpleMarkdownStreamParser>
                    </option>
                    <option name="role" value="user" />
                    <option name="selectedRanges">
                      <list />
                    </option>
                    <option name="uri" value="" />
                    <option name="variables">
                      <list />
                    </option>
                  </UIChatMessage>
                </list>
              </option>
              <option name="messagesV3">
                <list>
                  <ChatMessageV3>
                    <option name="role" value="user" />
                    <option name="content" value="根据SELECT&#10;DISTINCT&#10;&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG,&#10;-- DISTINCT&#10;--g2.Full_QTY,g3.P_QTY,&#10;&#9; EQUIPMENT_ID,&#10;&#9; Only_Id,&#10;&#9;PRODUCTION_ORDER_NO,&#10;&#9;EQUIPMENT_NAME,&#10;&#9; M_BATCH_NUMBER,&#10;--material&#10;&#9; M_NAME,&#10;&#9; M_CODE,&#10;--实际值(实际生产了多少)&#10;&#9; M_QUANTITY,&#10;-- SUM(CASE &#10;-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- &#9;ELSE 0&#10;-- END&#10;-- )  AS M_QUANTITY,&#10;&#9;M_QUANTITYUNIT,&#10;--TARGET_QUANTITY(目标值)&#10;&#9; M_QUANTITY_TOTAL,&#10;&#9;QUANTITY_TOTAL_UNIT,&#10;&#9;T_UINTID,&#10;--inventory quantity available库存数量&#10;&#9;IN_QUANTITY,&#10;&#9;MATERIAL_UNIT1,&#10;--BAG_SIZE（单包数量）&#10;ISNULL( FullBagWeight , 0 ) AS BAG_SIZE,&#10;--fullBag（整袋数量，已经合并成斜杠数据）&#10;CASE&#10;&#9;&#9;&#10;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (&#10;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;END AS FULL_PAGE,&#10;--实际值PARITIAL_PAGE （partial Bags）&#10;&#9;&#9;Tagp_s,--&#10;&#9;&#9;CASE--目标值 （partial）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) &#10;&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE,&#10;--单位&#10;&#9;&#9;&#9;Tagp_s_UNIT,&#10;&#9;&#9;&#9;Full_Finish,&#10;--BAG_S&#10;&#9;&#9;CASE&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) &#10;&#9;&#9;&#9;&#9;&#9;END AS BAG_S,&#10;--COMPLETE&#10;&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;MATERIAL_ID,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE,&#10;--最小比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE,&#10;--最大比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX,&#10;&#9;&#9;&#9;&#9;&#9;CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;ID,&#10;&#9;&#9;&#9;&#9;&#9;CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;SELECT&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id,&#10;&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish,&#10;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID,&#10;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID &#10;&#9;&#9;&#9;&#9;) t &#10;&#9;&#9;&#9;WHERE&#10;&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- SELECT&#10;--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG,&#10;-- -- DISTINCT&#10;-- --g2.Full_QTY,g3.P_QTY,&#10;--  '' as EQUIPMENT_ID,&#10;--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id,&#10;-- &#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;a2.EQUIPMENT_NAME,&#10;-- &#9;B.NUMBER AS M_BATCH_NUMBER,&#10;-- --material&#10;-- &#9;c.NAME AS M_NAME,&#10;-- &#9;c.CODE AS M_CODE,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY,&#10;--  &#10;-- -- SUM(CASE &#10;-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- -- &#9;ELSE 0&#10;-- -- END&#10;-- -- )  AS M_QUANTITY,&#10;-- &#9;  a11.NAME AS M_QUANTITYUNIT,&#10;-- --TARGET_QUANTITY(目标值)&#10;-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;-- &#9;&#10;-- &#9;a11.ID AS T_UINTID,&#10;-- --inventory quantity available库存数量&#10;-- &#9;0 as IN_QUANTITY,&#10;-- &#9;a11.NAME as MATERIAL_UNIT1,&#10;-- --BAG_SIZE（单包数量）&#10;-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE,&#10;--  &#10;-- &#9;&#10;-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）&#10;--   CASE&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE &#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;END AS FULL_PAGE,&#10;-- --实际值PARITIAL_PAGE （partial Bags）&#10;-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       &#10;-- &#10;-- &#9;CASE--目标值 （partial）&#10;-- &#9;&#9;&#10;-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;'0' ELSE CAST (&#10;-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;END AS PARITIAL_PAGE,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish,&#10;-- --BAG_S&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) &#10;-- &#9;&#9;&#9;&#9;END AS BAG_S,&#10;-- --COMPLETE&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;-- &#10;-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= &#10;-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= &#10;-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;THEN&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;-- --完成状态前端处理&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;END AS CONSUMED_STATES,&#10;-- &#9;c.ID AS MATERIAL_ID,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE,&#10;-- --最小比例 1 1%&#10;-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN,&#10;-- --最大值&#10;-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE,&#10;-- --最大比例 1 1%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;FROM&#10;-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'&#10;-- &#9;&#9;&#10;--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID&#10;--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
                    <option name="extra">
                      <ChatMessageExtra>
                        <option name="agent" value="default" />
                        <option name="cmd" value="default" />
                        <option name="code" value="           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;NZC_KEZOFV&quot;)]&#10;        kfyorx wvxrnzo? NzcKezofv { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;HVG_NZC&quot;)]&#10;        kfyorx rmg? HvgNzc { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;XLM_HFNVW&quot;)]&#10;        kfyorx hgirmt XlmHfnvw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;YZGXS_RW&quot;)]&#10;        kfyorx hgirmt YzgxsRw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;KILWFXGRLM_LIWVI_RW&quot;)]&#10;        kfyorx hgirmt KilwfxgrlmLiwviRw { tvg; hvg; }&#10;&#10;    }&#10;}" />
                        <option name="path" value="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Model\Models\MKM\BBatchDetailIiViewEntity.cs" />
                      </ChatMessageExtra>
                    </option>
                  </ChatMessageV3>
                  <ChatMessageV3>
                    <option name="role" value="assistant" />
                    <option name="content" value="&lt;think&gt;&#10;根据用户提供的SQL查询和实体类代码，我们需要为实体类`BBatchDetailIiViewEntity`中的字段添加注释。注释内容应基于SQL查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - MaxPvalue&#10;   - SetMax&#10;   - ConSumed&#10;   - BatchId&#10;   - ProductionOrderId&#10;&#10; 在SQL查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  1. MAX_PVALUE: 最大值（根据公式计算得出）&#10;  2. SET_MAX: 最大比例（来自物料属性PreweighToleranceMaxPercent）&#10;  3. CON_SUMED: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  4. BATCH_ID: 批次ID&#10;  5. PRODUCTION_ORDER_ID: 生产订单ID&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - MaxPvalue (MAX_PVALUE): &#10;      计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - SetMax (SET_MAX):&#10;      来源: 物料的PreweighToleranceMaxPercent属性&#10;      描述: 预称重最大容忍百分比（例如，1表示1%）&#10;&#10;  - ConSumed (CON_SUMED):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为CONSUMED_STATES的列，它根据条件返回'OK'或''，表示消耗状态。但实体类中的ConSumed对应的是查询中的CON_SUMED列，而该列在查询中被固定设置为空字符串（'' AS CON_SUMED）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由CONSUMED_STATES列表示的。然而，在实体类中，我们并没有看到CONSUMED_STATES字段，所以这里我们按照查询中CON_SUMED列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的t表中，CON_SUMED列是直接来自a表的吗？实际上，在子查询中，CON_SUMED列被设置为固定空字符串（'' AS CON_SUMED）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，ConSumed字段的类型是string，并且不可为空（Nullable:False）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - BatchId (BATCH_ID): 批次ID&#10;  - ProductionOrderId (PRODUCTION_ORDER_ID): 生产订单ID&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/think&gt;&#10;&#10;根据提供的SQL查询和实体类代码，我将为`BBatchDetailIiViewEntity`实体类添加注释说明。以下是带详细注释的实体类代码：&#10;&#10;```csharp&#10;/// &lt;summary&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/summary&gt;&#10;public class BBatchDetailIiViewEntity&#10;{&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (1 + (PreweighToleranceMaxPercent / 100)) * WEIGHING_QTY&#10;    /// 说明：基于物料属性&quot;PreweighToleranceMaxPercent&quot;计算的目标值上限&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;MAX_PVALUE&quot;)]&#10;    public decimal? MaxPvalue { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;PreweighToleranceMaxPercent&quot;&#10;    /// 说明：允许的最大偏差百分比（如1表示1%）&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;SET_MAX&quot;)]&#10;    public int? SetMax { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;OK&quot;：已消耗&#10;    /// 来源：PPM_B_PO_CONSUME_ACTUAL消耗记录存在性检查&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;CON_SUMED&quot;)]&#10;    public string ConSumed { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：PPM_B_BATCH&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;BATCH_ID&quot;)]&#10;    public string BatchId { get; set; }&#10;&#10;    /// &lt;summary&gt;&#10;    /// 生产订单ID&#10;    /// 关联表：PPM_B_PRODUCTION_ORDER&#10;    /// &lt;/summary&gt;&#10;    [SugarColumn(ColumnName = &quot;PRODUCTION_ORDER_ID&quot;)]&#10;    public string ProductionOrderId { get; set; }&#10;}&#10;```&#10;&#10;### 关键字段说明：&#10;1. **MaxPvalue**  &#10;   - 表示预称重允许的最大值&#10;   - 计算逻辑：`目标值 × (1 + 最大容忍百分比)`&#10;   - 用于判断实际产量是否超出允许范围&#10;&#10;2. **SetMax**  &#10;   - 来自物料属性表`PreweighToleranceMaxPercent`字段&#10;   - 控制生产允许的正向偏差范围&#10;   - 示例：值=1 表示允许超过目标值1%&#10;&#10;3. **ConSumed**  &#10;   - 消耗状态标记位&#10;   - 通过检查`PPM_B_PO_CONSUME_ACTUAL`表中是否存在消耗记录确定&#10;   - 业务逻辑：存在消耗记录=已消耗(&quot;OK&quot;)，无记录=未消耗(空字符串)&#10;&#10;4. **BatchId**  &#10;   - 批次系统的核心标识符&#10;   - 关联批次主表`PPM_B_BATCH`&#10;&#10;5. **ProductionOrderId**  &#10;   - 生产订单系统的核心标识符&#10;   - 关联工单主表`PPM_B_PRODUCTION_ORDER`&#10;&#10;&gt; 注意：所有字段映射均基于SQL查询结果集的列名，通过`[SugarColumn]`特性实现ORM映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。" />
                    <option name="extra">
                      <ChatMessageExtra />
                    </option>
                  </ChatMessageV3>
                </list>
              </option>
              <option name="sessionId" value="019742dfaa507aafbdee853c4887710c" />
            </ChatSession>
          </option>
          <option name="conversationId" value="019742dfaa507aafbdee853c4887710c" />
          <option name="conversation">
            <Conversation>
              <option name="createTime" value="1749173641774" />
              <option name="id" value="019742dfaa507aafbdee853c4887710c" />
              <option name="title" value="根据SELECT&#10;DISTINCT&#10;&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG,&#10;-- DISTINCT&#10;--g2.Full_QTY,g3.P_QTY,&#10;&#9; EQUIPMENT_ID,&#10;&#9; Only_Id,&#10;&#9;PRODUCTION_ORDER_NO,&#10;&#9;EQUIPMENT_NAME,&#10;&#9; M_BATCH_NUMBER,&#10;--material&#10;&#9; M_NAME,&#10;&#9; M_CODE,&#10;--实际值(实际生产了多少)&#10;&#9; M_QUANTITY,&#10;-- SUM(CASE &#10;-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- &#9;ELSE 0&#10;-- END&#10;-- )  AS M_QUANTITY,&#10;&#9;M_QUANTITYUNIT,&#10;--TARGET_QUANTITY(目标值)&#10;&#9; M_QUANTITY_TOTAL,&#10;&#9;QUANTITY_TOTAL_UNIT,&#10;&#9;T_UINTID,&#10;--inventory quantity available库存数量&#10;&#9;IN_QUANTITY,&#10;&#9;MATERIAL_UNIT1,&#10;--BAG_SIZE（单包数量）&#10;ISNULL( FullBagWeight , 0 ) AS BAG_SIZE,&#10;--fullBag（整袋数量，已经合并成斜杠数据）&#10;CASE&#10;&#9;&#9;&#10;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (&#10;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;END AS FULL_PAGE,&#10;--实际值PARITIAL_PAGE （partial Bags）&#10;&#9;&#9;Tagp_s,--&#10;&#9;&#9;CASE--目标值 （partial）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) &#10;&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE,&#10;--单位&#10;&#9;&#9;&#9;Tagp_s_UNIT,&#10;&#9;&#9;&#9;Full_Finish,&#10;--BAG_S&#10;&#9;&#9;CASE&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) &#10;&#9;&#9;&#9;&#9;&#9;END AS BAG_S,&#10;--COMPLETE&#10;&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;MATERIAL_ID,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE,&#10;--最小比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE,&#10;--最大比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX,&#10;&#9;&#9;&#9;&#9;&#9;CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;ID,&#10;&#9;&#9;&#9;&#9;&#9;CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;SELECT&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id,&#10;&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish,&#10;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID,&#10;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID &#10;&#9;&#9;&#9;&#9;) t &#10;&#9;&#9;&#9;WHERE&#10;&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- SELECT&#10;--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG,&#10;-- -- DISTINCT&#10;-- --g2.Full_QTY,g3.P_QTY,&#10;--  '' as EQUIPMENT_ID,&#10;--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id,&#10;-- &#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;a2.EQUIPMENT_NAME,&#10;-- &#9;B.NUMBER AS M_BATCH_NUMBER,&#10;-- --material&#10;-- &#9;c.NAME AS M_NAME,&#10;-- &#9;c.CODE AS M_CODE,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY,&#10;--  &#10;-- -- SUM(CASE &#10;-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- -- &#9;ELSE 0&#10;-- -- END&#10;-- -- )  AS M_QUANTITY,&#10;-- &#9;  a11.NAME AS M_QUANTITYUNIT,&#10;-- --TARGET_QUANTITY(目标值)&#10;-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;-- &#9;&#10;-- &#9;a11.ID AS T_UINTID,&#10;-- --inventory quantity available库存数量&#10;-- &#9;0 as IN_QUANTITY,&#10;-- &#9;a11.NAME as MATERIAL_UNIT1,&#10;-- --BAG_SIZE（单包数量）&#10;-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE,&#10;--  &#10;-- &#9;&#10;-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）&#10;--   CASE&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE &#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;END AS FULL_PAGE,&#10;-- --实际值PARITIAL_PAGE （partial Bags）&#10;-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       &#10;-- &#10;-- &#9;CASE--目标值 （partial）&#10;-- &#9;&#9;&#10;-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;'0' ELSE CAST (&#10;-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;END AS PARITIAL_PAGE,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish,&#10;-- --BAG_S&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) &#10;-- &#9;&#9;&#9;&#9;END AS BAG_S,&#10;-- --COMPLETE&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;-- &#10;-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= &#10;-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= &#10;-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;THEN&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;-- --完成状态前端处理&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;END AS CONSUMED_STATES,&#10;-- &#9;c.ID AS MATERIAL_ID,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE,&#10;-- --最小比例 1 1%&#10;-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN,&#10;-- --最大值&#10;-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE,&#10;-- --最大比例 1 1%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;FROM&#10;-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'&#10;-- &#9;&#9;&#10;--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID&#10;--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
              <option name="turns">
                <list>
                  <Turn>
                    <option name="agent" value="" />
                    <option name="answer" value="&lt;gsrmp&gt;&#10;根据用户提供的HJO查询和实体类代码，我们需要为实体类`YYzgxsWvgzroRrErvdVmgrgb`中的字段添加注释。注释内容应基于HJO查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - NzcKezofv&#10;   - HvgNzc&#10;   - XlmHfnvw&#10;   - YzgxsRw&#10;   - KilwfxgrlmLiwviRw&#10;&#10; 在HJO查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  8. NZC_KEZOFV: 最大值（根据公式计算得出）&#10;  7. HVG_NZC: 最大比例（来自物料属性KivdvrtsGlovizmxvNzcKvixvmg）&#10;  6. XLM_HFNVW: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  5. YZGXS_RW: 批次RW&#10;  4. KILWFXGRLM_LIWVI_RW: 生产订单RW&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - NzcKezofv (NZC_KEZOFV): &#10;      计算公式: (8 + (KivdvrtsGlovizmxvNzcKvixvmg / 899)) * DVRTSRMT_JGB&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - HvgNzc (HVG_NZC):&#10;      来源: 物料的KivdvrtsGlovizmxvNzcKvixvmg属性&#10;      描述: 预称重最大容忍百分比（例如，8表示8%）&#10;&#10;  - XlmHfnvw (XLM_HFNVW):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为XLMHFNVW_HGZGVH的列，它根据条件返回'LP'或''，表示消耗状态。但实体类中的XlmHfnvw对应的是查询中的XLM_HFNVW列，而该列在查询中被固定设置为空字符串（'' ZH XLM_HFNVW）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由XLMHFNVW_HGZGVH列表示的。然而，在实体类中，我们并没有看到XLMHFNVW_HGZGVH字段，所以这里我们按照查询中XLM_HFNVW列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的g表中，XLM_HFNVW列是直接来自z表的吗？实际上，在子查询中，XLM_HFNVW列被设置为固定空字符串（'' ZH XLM_HFNVW）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，XlmHfnvw字段的类型是hgirmt，并且不可为空（Mfoozyov:Uzohv）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - YzgxsRw (YZGXS_RW): 批次RW&#10;  - KilwfxgrlmLiwviRw (KILWFXGRLM_LIWVI_RW): 生产订单RW&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/gsrmp&gt;&#10;&#10;根据提供的HJO查询和实体类代码，我将为`YYzgxsWvgzroRrErvdVmgrgb`实体类添加注释说明。以下是带详细注释的实体类代码：&#10;&#10;```xhszik&#10;/// &lt;hfnnzib&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/hfnnzib&gt;&#10;kfyorx xozhh YYzgxsWvgzroRrErvdVmgrgb&#10;{&#10;    /// &lt;hfnnzib&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (8 + (KivdvrtsGlovizmxvNzcKvixvmg / 899)) * DVRTSRMT_JGB&#10;    /// 说明：基于物料属性&quot;KivdvrtsGlovizmxvNzcKvixvmg&quot;计算的目标值上限&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;NZC_KEZOFV&quot;)]&#10;    kfyorx wvxrnzo? NzcKezofv { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;KivdvrtsGlovizmxvNzcKvixvmg&quot;&#10;    /// 说明：允许的最大偏差百分比（如8表示8%）&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;HVG_NZC&quot;)]&#10;    kfyorx rmg? HvgNzc { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;LP&quot;：已消耗&#10;    /// 来源：KKN_Y_KL_XLMHFNV_ZXGFZO消耗记录存在性检查&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;XLM_HFNVW&quot;)]&#10;    kfyorx hgirmt XlmHfnvw { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：KKN_Y_YZGXS&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;YZGXS_RW&quot;)]&#10;    kfyorx hgirmt YzgxsRw { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 生产订单RW&#10;    /// 关联表：KKN_Y_KILWFXGRLM_LIWVI&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;KILWFXGRLM_LIWVI_RW&quot;)]&#10;    kfyorx hgirmt KilwfxgrlmLiwviRw { tvg; hvg; }&#10;}&#10;```&#10;&#10;### 关键字段说明：&#10;8. **NzcKezofv**  &#10;   - 表示预称重允许的最大值&#10;   - 计算逻辑：`目标值 × (8 + 最大容忍百分比)`&#10;   - 用于判断实际产量是否超出允许范围&#10;&#10;7. **HvgNzc**  &#10;   - 来自物料属性表`KivdvrtsGlovizmxvNzcKvixvmg`字段&#10;   - 控制生产允许的正向偏差范围&#10;   - 示例：值=8 表示允许超过目标值8%&#10;&#10;6. **XlmHfnvw**  &#10;   - 消耗状态标记位&#10;   - 通过检查`KKN_Y_KL_XLMHFNV_ZXGFZO`表中是否存在消耗记录确定&#10;   - 业务逻辑：存在消耗记录=已消耗(&quot;LP&quot;)，无记录=未消耗(空字符串)&#10;&#10;5. **YzgxsRw**  &#10;   - 批次系统的核心标识符&#10;   - 关联批次主表`KKN_Y_YZGXS`&#10;&#10;4. **KilwfxgrlmLiwviRw**  &#10;   - 生产订单系统的核心标识符&#10;   - 关联工单主表`KKN_Y_KILWFXGRLM_LIWVI`&#10;&#10;&gt; 注意：所有字段映射均基于HJO查询结果集的列名，通过`[HftziXlofnm]`特性实现LIN映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。" />
                    <option name="chatResource" value="user" />
                    <option name="codeReferences">
                      <list>
                        <CodeReferencesBean>
                          <option name="content" value="           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;NZC_KEZOFV&quot;)]&#10;        kfyorx wvxrnzo? NzcKezofv { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;HVG_NZC&quot;)]&#10;        kfyorx rmg? HvgNzc { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;XLM_HFNVW&quot;)]&#10;        kfyorx hgirmt XlmHfnvw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;YZGXS_RW&quot;)]&#10;        kfyorx hgirmt YzgxsRw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;KILWFXGRLM_LIWVI_RW&quot;)]&#10;        kfyorx hgirmt KilwfxgrlmLiwviRw { tvg; hvg; }&#10;&#10;    }&#10;}" />
                          <option name="end_line" value="244" />
                          <option name="filename" value="BBatchDetailIiViewEntity.cs" />
                          <option name="language" value="C#" />
                          <option name="range">
                            <Range>
                              <option name="end">
                                <Position>
                                  <option name="character" value="1" />
                                  <option name="line" value="244" />
                                </Position>
                              </option>
                              <option name="start">
                                <Position>
                                  <option name="line" value="208" />
                                </Position>
                              </option>
                            </Range>
                          </option>
                          <option name="start_line" value="208" />
                          <option name="type" value="editor" />
                          <option name="url" value="file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs" />
                        </CodeReferencesBean>
                      </list>
                    </option>
                    <option name="command" value="" />
                    <option name="conversationId" value="019742dfaa507aafbdee853c4887710c" />
                    <option name="historyDocumentMap">
                      <map>
                        <entry key="file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs" value="using System;&#10;using System.Linq;&#10;using System.Text;&#10;using SqlSugar;&#10;&#10;using SEFA.Base.Model.BASE;&#10;namespace SEFA.MKM.Model.Models&#10;{&#10;    ///&lt;summary&gt;&#10;    ///第二层批次下物料信息表(包括跳转界面)&#10;    ///&lt;/summary&gt;&#10;    &#10;    [SugarTable(&quot;V_PPM_B_BATCH_DETAIL_II_VIEW&quot;)] &#10;    public class BBatchDetailIiViewEntity : EntityBase&#10;    {&#10;        public BBatchDetailIiViewEntity()&#10;        {&#10;        }&#10;        /// &lt;summary&gt;&#10;        /// Desc:&#10;        /// Default:&#10;        /// Nullable:False&#10;        /// &lt;/summary&gt;&#10;        [SugarColumn(ColumnName = &quot;IS_PG&quot;)]&#10;        public string IsPg { get; set; }&#10;        /// &lt;summary&gt;&#10;        /// Desc:&#10;        /// Default:&#10;        /// Nullable:True&#10;        /// &lt;/summary&gt;&#10;        [SugarColumn(ColumnName=&quot;EQUIPMENT_ID&quot;)]&#10;        public string EquipmentId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Only_Id&quot;)]&#10;        public string OnlyId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PRODUCTION_ORDER_NO&quot;)]&#10;        public string ProductionOrderNo { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;EQUIPMENT_NAME&quot;)]&#10;        public string EquipmentName { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_BATCH_NUMBER&quot;)]&#10;        public string MBatchNumber { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_NAME&quot;)]&#10;        public string MName { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_CODE&quot;)]&#10;        public string MCode { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITY&quot;)]&#10;        public decimal? MQuantity { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITYUNIT&quot;)]&#10;        public string MQuantityunit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITY_TOTAL&quot;)]&#10;        public decimal MQuantityTotal { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;QUANTITY_TOTAL_UNIT&quot;)]&#10;        public string QuantityTotalUnit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;T_UINTID&quot;)]&#10;        public string TUintid { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;IN_QUANTITY&quot;)]&#10;        public decimal? InQuantity { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MATERIAL_UNIT1&quot;)]&#10;        public string MaterialUnit1 { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BAG_SIZE&quot;)]&#10;        public string BagSize { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;FULL_PAGE&quot;)]&#10;        public string FullPage { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Tagp_s&quot;)]&#10;        public decimal? TagpS { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PARITIAL_PAGE&quot;)]&#10;        public string ParitialPage { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Tagp_s_UNIT&quot;)]&#10;        public string TagpSUnit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Full_Finish&quot;)]&#10;        public int FullFinish { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BAG_S&quot;)]&#10;        public int? BagS { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;COMPLETE_STATES&quot;)]&#10;        public string CompleteStates { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;CONSUMED_STATES&quot;)]&#10;        public string ConsumedStates { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MATERIAL_ID&quot;)]&#10;        public string MaterialId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MIN_PVALUE&quot;)]&#10;        public decimal? MinPvalue { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;SET_MIN&quot;)]&#10;        public int? SetMin { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MAX_PVALUE&quot;)]&#10;        public decimal? MaxPvalue { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;SET_MAX&quot;)]&#10;        public int? SetMax { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;CON_SUMED&quot;)]&#10;        public string ConSumed { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BATCH_ID&quot;)]&#10;        public string BatchId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PRODUCTION_ORDER_ID&quot;)]&#10;        public string ProductionOrderId { get; set; }&#10;&#10;    }&#10;}" />
                      </map>
                    </option>
                    <option name="messageId" value="01974405ffcb7aafbdee9068b2657329" />
                    <option name="question" value="根据HVOVXG&#10;WRHGRMXG&#10;&#9;RHMFOO( RhUfooYztNvitv, 9 ) ZH RH_KT,&#10;-- WRHGRMXG&#10;--t7.Ufoo_JGB,t6.K_JGB,&#10;&#9; VJFRKNVMG_RW,&#10;&#9; Lmob_Rw,&#10;&#9;KILWFXGRLM_LIWVI_ML,&#10;&#9;VJFRKNVMG_MZNV,&#10;&#9; N_YZGXS_MFNYVI,&#10;--nzgvirzo&#10;&#9; N_MZNV,&#10;&#9; N_XLWV,&#10;--实际值(实际生产了多少)&#10;&#9; N_JFZMGRGB,&#10;-- HFN(XZHV &#10;-- &#9;DSVM RMEVMGLIB_GBKV rh mlg mfoo GSVM RM_JFZMGRGB&#10;-- &#9;VOHV 9&#10;-- VMW&#10;-- )  ZH N_JFZMGRGB,&#10;&#9;N_JFZMGRGBFMRG,&#10;--GZITVG_JFZMGRGB(目标值)&#10;&#9; N_JFZMGRGB_GLGZO,&#10;&#9;JFZMGRGB_GLGZO_FMRG,&#10;&#9;G_FRMGRW,&#10;--rmevmglib jfzmgrgb zezrozyov库存数量&#10;&#9;RM_JFZMGRGB,&#10;&#9;NZGVIRZO_FMRG8,&#10;--YZT_HRAV（单包数量）&#10;RHMFOO( UfooYztDvrtsg , 9 ) ZH YZT_HRAV,&#10;--ufooYzt（整袋数量，已经合并成斜杠数据）&#10;XZHV&#10;&#9;&#9;&#10;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;'9/Rmurmrgb' VOHV XLMEVIG ( EZIXSZI ( 799 ), XLMEVIG ( RMG, RHMFOO( Ufoo_JGB, 9 ) ) / RHMFOO( UfooYztDvrtsg, 9 ) ) + '/' + XLMEVIG (&#10;&#9;&#9;&#9;&#9;EZIXSZI ( 799 ),&#10;&#9;&#9;&#9;&#9;( XLMEVIG ( RMG, DVRTSRMT_JGB ) / XLMEVIG ( RMG, RHMFOO( UfooYztDvrtsg, 9 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;VMW ZH UFOO_KZTV,&#10;--实际值KZIRGRZO_KZTV （kzigrzo Yzth）&#10;&#9;&#9;Gztk_h,--&#10;&#9;&#9;XZHV--目标值 （kzigrzo）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;&#9;'9' VOHV XZHG ( ( DVRTSRMT_JGB % XZHG ( RHMFOO( UfooYztDvrtsg, 9 ) ZH WVXRNZO ( 89, 7 ) ) ) ZH EZIXSZI ( 799 ) ) &#10;&#9;&#9;&#9;&#9;VMW ZH KZIRGRZO_KZTV,&#10;--单位&#10;&#9;&#9;&#9;Gztk_h_FMRG,&#10;&#9;&#9;&#9;Ufoo_Urmrhs,&#10;--YZT_H&#10;&#9;&#9;XZHV&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;&#9;&#9;'9' VOHV XZHG ( (DVRTSRMT_JGB/ RHMFOO( UfooYztDvrtsg, 9 ) ) ZH RMG ) &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH YZT_H,&#10;--XLNKOVGV&#10;&#9;&#9;&#9;&#9;XZHV-- &#9;&#9;DSVM ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= ( 8- ( XLZOVHXV ( w7.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB &#10;-- &#9;&#9;ZMW ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= ( 8+ ( XLZOVHXV ( w6.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;DSVM ( ( XLZOVHXV (Ufoo_JGB, 9 ) + XLZOVHXV (K_JGB, 9 ) ) ) &gt;= XZHG (&#10;&#9;&#9;&#9;&#9;&#9;&#9;RHMFOO(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 8.99- ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 80, 3 ) ) / 899.99 ) ) * ( DVRTSRMT_JGB ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;9 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) ZH WVXRNZO ( 80, 3 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;ZMW ( ( XLZOVHXV ( Ufoo_JGB, 9 ) + XLZOVHXV (K_JGB, 9 ) ) ) &lt;= XZHG (&#10;&#9;&#9;&#9;&#9;&#9;&#9;RHMFOO(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 8.99+ ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 80, 3 ) ) / 899.99 ) ) * (DVRTSRMT_JGB ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;9 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) ZH WVXRNZO ( 80, 3 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH XLNKOVGV_HGZGVH,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;XLMHFNVW_HGZGVH,&#10;&#9;&#9;&#9;&#9;&#9;NZGVIRZO_RW,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 8- ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) / 899.99 ) ) * DVRTSRMT_JGB ZH NRM_KEZOFV,&#10;--最小比例 8 8%&#10;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) ZH HVG_NRM,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 8+ ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) / 899.99 ) ) * DVRTSRMT_JGB ZH NZC_KEZOFV,&#10;--最大比例 8 8%&#10;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) ZH HVG_NZC,&#10;&#9;&#9;&#9;&#9;&#9;XLM_HFNVW,&#10;&#9;&#9;&#9;&#9;&#9;YZGXS_RW,&#10;&#9;&#9;&#9;&#9;&#9;KILWFXGRLM_LIWVI_RW,&#10;&#9;&#9;&#9;&#9;&#9;RW,&#10;&#9;&#9;&#9;&#9;&#9;XIVZGVWZGV,&#10;&#9;&#9;&#9;&#9;&#9;XIVZGVFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;NLWRUBWZGV,&#10;&#9;&#9;&#9;&#9;&#9;NLWRUBFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;FKWZGVGRNVHGZNK &#10;&#9;&#9;&#9;&#9;UILN&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;HVOVXG&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' ZH VJFRKNVMG_RW,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.RW + '/' + u.RW + '/' + x.XLWV+ '/' + z.YZGXS_RW ZH Lmob_Rw,&#10;&#9;&#9;&#9;&#9;&#9;&#9;u.KILWFXGRLM_LIWVI_ML,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z7.VJFRKNVMG_MZNV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;Y.MFNYVI ZH N_YZGXS_MFNYVI,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.MZNV ZH N_MZNV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.XLWV ZH N_XLWV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( XLZOVHXV ( t7.Ufoo_JGB, 9 ) + XLZOVHXV ( t6.K_JGB, 9 ) ) ZH N_JFZMGRGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH N_JFZMGRGBFMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB ZH N_JFZMGRGB_GLGZO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH JFZMGRGB_GLGZO_FMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.RW ZH G_FRMGRW,&#10;&#9;&#9;&#9;&#9;&#9;&#9;9 ZH RM_JFZMGRGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH NZGVIRZO_FMRG8,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;t7.Ufoo_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;XLZOVHXV ( t6.K_JGB , 9 ) ZH Gztk_h,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH Gztk_h_FMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;XLZOVHXV ( t7.Ufoo_JGB, 9 ) ZH Ufoo_Urmrhs,&#10;&#9;&#9;&#9;&#9;&#9;&#9;t6.K_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;XZHV &#9;DSVM c7.RW RH MLG MFOO GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH XLMHFNVW_HGZGVH,&#10;&#9;&#9;&#9;&#9;&#9;x.RW ZH NZGVIRZO_RW,&#10;&#9;&#9;&#9;&#9;&#9;'' ZH XLM_HFNVW,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;&#9;&#9;&#9;&#9;&#9;u.RW ZH KILWFXGRLM_LIWVI_RW,&#10;&#9;&#9;&#9;&#9;&#9;u.RW,&#10;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'KivdvrtsGlovizmxvNzcKvixvmg', X.RW ) ZH KivdvrtsGlovizmxvNzcKvixvmg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'KivdvrtsGlovizmxvNrmKvixvmg', X.RW ) ZH KivdvrtsGlovizmxvNrmKvixvmg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'IvjfrivhKivDvrts', X.RW ) ZH IvjfrivhKivDvrts,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'UfooYztDvrtsg', X.RW ) ZH UfooYztDvrtsg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'RhUfooYztNvitv', X.RW ) ZH RhUfooYztNvitv &#10;&#9;&#9;&#9;&#9;UILN&#10;&#9;&#9;&#9;&#9;&#9;KKN_Y_YZGXS_XLMHFNV_IVJFRIVNVMG z --OVUG QLRM WUN_N_FMRG z4 lm z4.RW=z.FMRG_RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_IVJFRIVNVMG z8 LM z.KL_XLMHFNV_IVJFRIVNVMG_RW= z8.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_FMRGNZMZTV z88 LM z8.FMRG_RW = z88.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KILWFXGRLM_LIWVI u LM z8.KILWFXGRLM_LIWVI_RW= u.RW --工单&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_KGN_HVTNVMGORHG z7 LM z8.KL_HVTNVMG_IVJFRIVNVMG_RW= z7.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_YZGXS y LM z.YZGXS_RW = y.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_NZGVIRZO x LM z8.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_U_ERVD t7 LM t7.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t7.KILWFXGRLM_YZGXS_RW= y.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t7.Ufoo_JGB&gt; 9&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_K_ERVD t6 LM t6.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.YZGXS_RW= y.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.K_JGB&gt; 9 &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.XLMGZRMVI_RW &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.XLMGZRMVI_RW RH MLG MFOO --这里需要判断部分包是否存在容器RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM NPN_Y_NZGVIRZO_OLG c8 LM c8.NZGVIRZO_RW = x.RW --xlmhfnvw：查找KKN_Y_KL_XLMHFNV_ZXGFZO中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM ( HVOVXG KL_XLMHFNV_IVJFRIVNVMG_RW ZH RW, HFN ( JFZMGRGB ) ZH JFZMGRGB UILN KKN_Y_KL_XLMHFNV_ZXGFZO TILFK YB KL_XLMHFNV_IVJFRIVNVMG_RW ) c7 LM c7.RW= z.RW &#10;&#9;&#9;&#9;&#9;) g &#10;&#9;&#9;&#9;DSVIV&#10;&#9;&#9;&#9;&#9;IvjfrivhKivDvrts = '8' -- &#9;&#9;&#9;&#9;TILFK YB&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- HVOVXG&#10;--  RHMFOO( w5CC.KILKVIGB_EZOFV , 9 ) ZH RH_KT,&#10;-- -- WRHGRMXG&#10;-- --t7.Ufoo_JGB,t6.K_JGB,&#10;--  '' zh VJFRKNVMG_RW,&#10;--   x.RW +'/'+ u.RW +'/'+x.XLWV+'/'+z.YZGXS_RW zh Lmob_Rw,&#10;-- &#9;u.KILWFXGRLM_LIWVI_ML,&#10;-- &#9;z7.VJFRKNVMG_MZNV,&#10;-- &#9;Y.MFNYVI ZH N_YZGXS_MFNYVI,&#10;-- --nzgvirzo&#10;-- &#9;x.MZNV ZH N_MZNV,&#10;-- &#9;x.XLWV ZH N_XLWV,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 ))  zh N_JFZMGRGB,&#10;--  &#10;-- -- HFN(XZHV &#10;-- -- &#9;DSVM RMEVMGLIB_GBKV rh mlg mfoo GSVM RM_JFZMGRGB&#10;-- -- &#9;VOHV 9&#10;-- -- VMW&#10;-- -- )  ZH N_JFZMGRGB,&#10;-- &#9;  z88.MZNV ZH N_JFZMGRGBFMRG,&#10;-- --GZITVG_JFZMGRGB(目标值)&#10;-- &#9;z.DVRTSRMT_JGB ZH N_JFZMGRGB_GLGZO,&#10;-- &#9;z88.MZNV ZH JFZMGRGB_GLGZO_FMRG,&#10;-- &#9;&#10;-- &#9;z88.RW ZH G_FRMGRW,&#10;-- --rmevmglib jfzmgrgb zezrozyov库存数量&#10;-- &#9;9 zh RM_JFZMGRGB,&#10;-- &#9;z88.MZNV zh NZGVIRZO_FMRG8,&#10;-- --YZT_HRAV（单包数量）&#10;-- &#9;RHMFOO( w5.KILKVIGB_EZOFV , 9 ) ZH YZT_HRAV,&#10;--  &#10;-- &#9;&#10;-- &#9;--ufooYzt（整袋数量，已经合并成斜杠数据）&#10;--   XZHV&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;&#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;&#9;&#9;'9/Rmurmrgb' VOHV &#10;-- &#9;&#9;&#9;&#9;&#9;XLMEVIG (EZIXSZI ( 799 ),&#10;-- &#9;&#9;&#9;&#9;&#9;XLMEVIG(RMG, RHMFOO( t7.Ufoo_JGB, 9 ))/ RHMFOO( w5.KILKVIGB_EZOFV , 9 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + XLMEVIG (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;EZIXSZI ( 799 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( XLMEVIG ( RMG, z.DVRTSRMT_JGB ) / XLMEVIG ( RMG, RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;VMW ZH UFOO_KZTV,&#10;-- --实际值KZIRGRZO_KZTV （kzigrzo Yzth）&#10;-- XLZOVHXV( t6.K_JGB , 9 ) ZH Gztk_h,--       &#10;-- &#10;-- &#9;XZHV--目标值 （kzigrzo）&#10;-- &#9;&#9;&#10;-- &#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;'9' VOHV XZHG (&#10;-- &#9;&#9;&#9;&#9;( z.DVRTSRMT_JGB % XZHG ( RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 7 ) ) ) ZH EZIXSZI ( 799 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;VMW ZH KZIRGRZO_KZTV,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;z88.MZNV ZH Gztk_h_FMRG,&#10;-- &#9;&#9;XLZOVHXV( t7.Ufoo_JGB, 9 ) zh Ufoo_Urmrhs,&#10;-- --YZT_H&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;&#9;'9' VOHV XZHG ( ( z.DVRTSRMT_JGB/ RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ) ZH RMG ) &#10;-- &#9;&#9;&#9;&#9;VMW ZH YZT_H,&#10;-- --XLNKOVGV&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;DSVM ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= ( 8- ( XLZOVHXV ( w7.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB &#10;-- -- &#9;&#9;ZMW ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= ( 8+ ( XLZOVHXV ( w6.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB GSVM&#10;-- &#10;-- &#9;DSVM (( XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= &#10;-- &#9;&#9;XZHG (RHMFOO(&#9;( 8.99- ( XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 80, 3 ) )  / 899.99 ) ) *( z.DVRTSRMT_JGB ) ,9) ZH WVXRNZO ( 80, 3 ))&#10;-- &#9;&#9;ZMW (( XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= &#10;-- &#9;&#9;XZHG (RHMFOO(( 8.99+ ( XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO (80, 3 ) )  / 899.99 ) ) * ( z.DVRTSRMT_JGB ),9) ZH WVXRNZO ( 80, 3 ))&#10;-- &#9;&#9;GSVM&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;VMW ZH XLNKOVGV_HGZGVH,&#10;-- --完成状态前端处理&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;DSVM c7.RW RH MLG MFOO GSVM&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;-- &#9;VMW ZH XLMHFNVW_HGZGVH,&#10;-- &#9;x.RW ZH NZGVIRZO_RW,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 8- ( XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  / 899.99 ) ) * z.DVRTSRMT_JGB ZH NRM_KEZOFV,&#10;-- --最小比例 8 8%&#10;-- XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  ZH HVG_NRM,&#10;-- --最大值&#10;-- &#9;( 8+ ( XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  / 899.99 ) ) * z.DVRTSRMT_JGB ZH NZC_KEZOFV,&#10;-- --最大比例 8 8%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  ZH HVG_NZC,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' ZH XLM_HFNVW,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW ZH KILWFXGRLM_LIWVI_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;UILN&#10;-- KKN_Y_YZGXS_XLMHFNV_IVJFRIVNVMG z --OVUG QLRM WUN_N_FMRG z4 lm z4.RW=z.FMRG_RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_IVJFRIVNVMG z8 LM z.KL_XLMHFNV_IVJFRIVNVMG_RW= z8.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_FMRGNZMZTV z88 LM z8.FMRG_RW = z88.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KILWFXGRLM_LIWVI u LM z8.KILWFXGRLM_LIWVI_RW= u.RW --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_KGN_HVTNVMGORHG z7 LM z8.KL_HVTNVMG_IVJFRIVNVMG_RW= z7.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_YZGXS y LM z.YZGXS_RW = y.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_NZGVIRZO x LM z8.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w8 LM w8.NZGVIRZO_RW= x.RW &#10;-- &#9;&#9;&#9;&#9;ZMW w8.KILKVIGB_XLWV= 'IvjfrivhKivDvrts' &#10;-- &#9;&#9;&#9;&#9;ZMW w8.KILKVIGB_EZOFV= '8'&#10;-- &#9;&#9;&#10;--            --   OVUG QLRM E_NPN_RMEVMG_PX_ERVD t8 LM t8.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_U_ERVD t7 LM t7.NZGVIRZO_RW = x.RW zmw t7.KILWFXGRLM_YZGXS_RW=y.RW zmw t7.Ufoo_JGB&gt;9&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_K_ERVD t6 LM t6.NZGVIRZO_RW = x.RW zmw t6.YZGXS_RW=y.RW zmw t6.K_JGB&gt;9 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;zmw t6.XLMGZRMVI_RW &lt;&gt;'' zmw t6.XLMGZRMVI_RW rh mlg mfoo&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;zmw t6.XLMGZRMVI_RW !='' zmw t6.XLMGZRMVI_RW rh mlg mfoo&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w7 LM w7.KILKVIGB_XLWV= 'KivdvrtsGlovizmxvNzcKvixvmg' zmw w7.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w6 LM w6.KILKVIGB_XLWV= 'KivdvrtsGlovizmxvNrmKvixvmg' zmw w6.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w5 LM w5.KILKVIGB_XLWV= 'UfooYztDvrtsg' zmw w5.NZGVIRZO_RW = x.RW &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w5CC LM w5CC.KILKVIGB_XLWV= 'RhUfooYztNvitv' zmw w5CC.NZGVIRZO_RW = x.RW &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM NPN_Y_NZGVIRZO_OLG c8 LM c8.NZGVIRZO_RW = x.RW --xlmhfnvw：查找KKN_Y_KL_XLMHFNV_ZXGFZO中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_ZXGFZO c7 LM c7.OLG_RW= c8.RW &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;ZMW u.RW = c7.KILWFXGRLM_LIWVI_RW&#10;--               OVUG QLRM (HVOVXG KL_XLMHFNV_IVJFRIVNVMG_RW zh RW , HFN ( JFZMGRGB ) ZH JFZMGRGB &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;UILN KKN_Y_KL_XLMHFNV_ZXGFZO &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;TILFK YB KL_XLMHFNV_IVJFRIVNVMG_RW) c7 LM c7.RW=z.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--OVUG QLRM KKN_Y_KL_KILWFXVW_ZXGFZO i8 LM i8.KILWFXGRLM_LIWVI_RW = u.RW --OVUG QLRM WUN_N_VJFRKNVMG h lm i8.VJFRKNVMG_RW= h.RW zmw h.[OVEVO]='nzxsrmv'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;TILFK YB &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;u.KILWFXGRLM_LIWVI_ML,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;t8.VJFRKNVMG_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z7.VJFRKNVMG_MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;Y.MFNYVI,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.XLWV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;t7.Ufoo_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;t6.K_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--v.N_FMRG8,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z88.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--t8.PX_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--t8.NZGVIRZO_FMRG8,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--c8.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--v.N_JFZMGRGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w7.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w5.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w5CC.KILKVIGB_EZOFV,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;UFOO_KZTV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w6.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c7.rw,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.rw;  视图内容  给这个实体进行备注" />
                    <option name="rating" value="UNRATED" />
                    <option name="referenceBeans">
                      <list />
                    </option>
                    <option name="status" value="SUCCESS" />
                    <option name="variables">
                      <list />
                    </option>
                  </Turn>
                </list>
              </option>
              <option name="updateTime" value="1749192989177" />
            </Conversation>
          </option>
        </WebConversation>
      </list>
    </option>
    <option name="currentConversation" />
  </component>
</project>