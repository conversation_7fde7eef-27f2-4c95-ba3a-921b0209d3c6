﻿using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Common.Common;
using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IWeekScheduleServices
    /// </summary>	
    public interface IWeekScheduleServices : IBaseServices<WeekScheduleEntity>
    {
        Task<PageModel<WeekScheduleEntity>> GetPageList(WeekScheduleRequestModel reqModel);

        Task<List<WeekScheduleEntity>> GetList(WeekScheduleRequestModel reqModel);

        Task<ResultBool> SaveForm(WeekScheduleEntity entity);

        Task<ResultBool> UpdateForm(WeekScheduleEntity entity);

        Task<List<ReplaceMaterialEntity>> GetInsteadMaterialList(WeekScheduleBomRequestModel reqModel);

        Task<ResultData<WeekScheduleRequestModel>> GetSplitBatchInfo(WeekScheduleRequestModel wsModel);

        Task<ResultData<WeekScheduleRequestModel>> GetAddBatchInfo(ProductionOrderRequestModel wsModel);

        Task<ResultBool> ChangeMaterial(WeekScheduleBomRequestModel reqModel);

        Task<ResultBool> SplitBatch(WeekScheduleRequestModel wsModel);

        Task<ResultBool> AddBatch(WeekScheduleRequestModel wsModel);

        Task<ResultBool> DcsDownload([FromBody] ProductionOrderModel reqModel);

        Task<ResultString> ImportData(Stream stream);
    }
}