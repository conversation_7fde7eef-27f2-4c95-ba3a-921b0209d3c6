{"format": 1, "restore": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Api\\SEFA.PPM.Api.csproj": {}}, "projects": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Api\\SEFA.PPM.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Api\\SEFA.PPM.Api.csproj", "projectName": "SEFA.PPM.Api", "projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Api\\SEFA.PPM.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Api\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj": {"projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Abp": {"target": "Package", "version": "[7.3.0, )"}, "AspNetCoreRateLimit": {"target": "Package", "version": "[3.0.5, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[8.1.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Autofac.Extras.DynamicProxy": {"target": "Package", "version": "[5.0.0, )"}, "Castle.Core": {"target": "Package", "version": "[5.0.0, )"}, "Com.Ctrip.Framework.Apollo.Configuration": {"target": "Package", "version": "[2.7.0, )"}, "Confluent.Kafka": {"target": "Package", "version": "[1.7.0, )"}, "Consul": {"target": "Package", "version": "[*******, )"}, "InitQ": {"target": "Package", "version": "[*******, )"}, "Magicodes.IE.Core": {"target": "Package", "version": "[2.6.4, )"}, "Magicodes.IE.Excel.Abp": {"target": "Package", "version": "[2.6.4, )"}, "Magicodes.IE.Excel.AspNetCore": {"target": "Package", "version": "[2.6.4, )"}, "MicroKnights.Log4NetAdoNetAppender": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[5.0.9, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.Hosting.WindowsServices": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[6.0.7, )"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore": {"target": "Package", "version": "[6.1.0, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[6.21.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.9.10, )"}, "MiniProfiler.AspNetCore.Mvc": {"target": "Package", "version": "[4.2.1, )"}, "MiniProfiler.Shared": {"target": "Package", "version": "[4.2.22, )"}, "Minio": {"target": "Package", "version": "[5.0.0, )"}, "Panda.DynamicWebApi": {"target": "Package", "version": "[1.2.1, )"}, "Polly": {"target": "Package", "version": "[7.2.2, )"}, "Polly.Extensions.Http": {"target": "Package", "version": "[3.0.0, )"}, "RestSharp": {"target": "Package", "version": "[108.0.1, )"}, "SapNwRfc": {"target": "Package", "version": "[1.4.0, )"}, "Serilog": {"target": "Package", "version": "[2.10.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[6.0.1, )"}, "Serilog.Sinks.Async": {"target": "Package", "version": "[1.5.0, )"}, "Serilog.Sinks.Elasticsearch": {"target": "Package", "version": "[8.4.1, )"}, "SkyAPM.Agent.AspNetCore": {"target": "Package", "version": "[1.3.0, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.48, )"}, "Swashbuckle.AspNetCore.Filters": {"target": "Package", "version": "[6.0.1, )"}, "Swashbuckle.AspNetCore.Newtonsoft": {"target": "Package", "version": "[6.4.0, )"}, "Swashbuckle.AspNetCore.SwaggerGen": {"target": "Package", "version": "[6.4.0, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.4.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[6.21.0, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Federation": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.10.*, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[5.0.0, )"}, "WebApiClient.JIT": {"target": "Package", "version": "[1.1.4, )"}, "log4mongo-netcore": {"target": "Package", "version": "[3.2.0, )"}, "log4net": {"target": "Package", "version": "[2.0.14, )"}, "nacos-sdk-csharp": {"target": "Package", "version": "[1.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}, "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj", "projectName": "SEFA.PPM.IServices", "projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj": {"projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}, "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj", "projectName": "SEFA.PPM.Model", "projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Abp": {"target": "Package", "version": "[7.3.0, )"}, "AutoMapper": {"target": "Package", "version": "[10.1.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[8.1.0, )"}, "InfluxDB.Client": {"target": "Package", "version": "[4.0.0, )"}, "Magicodes.IE.Excel": {"target": "Package", "version": "[2.6.4, )"}, "Magicodes.IE.Excel.AspNetCore": {"target": "Package", "version": "[2.6.4, )"}, "MiniExcel": {"target": "Package", "version": "[1.33.0, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.120, )"}, "sqlSugarCore": {"target": "Package", "version": "[5.1.4.151, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}, "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj", "projectName": "SEFA.PPM.Repository", "projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj": {"projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Model\\SEFA.PPM.Model.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"MiniProfiler.AspNetCore.Mvc": {"target": "Package", "version": "[4.2.1, )"}, "MongoDB.Bson": {"target": "Package", "version": "[2.10.0, )"}, "MongoDB.Driver.Core": {"target": "Package", "version": "[2.10.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}, "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj", "projectName": "SEFA.PPM.Services", "projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj": {"projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj"}, "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj": {"projectPath": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"EasyModbusTCP.NETCore": {"target": "Package", "version": "[1.0.0, )"}, "InfluxDB.Client": {"target": "Package", "version": "[4.0.0, )"}, "InfluxDB.Client.Linq": {"target": "Package", "version": "[4.0.0, )"}, "MiniExcel": {"target": "Package", "version": "[1.33.0, )"}, "NPOI": {"target": "Package", "version": "[2.7.0, )"}, "System.ServiceModel.Duplex": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Federation": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Http": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.NetTcp": {"target": "Package", "version": "[4.10.*, )"}, "System.ServiceModel.Security": {"target": "Package", "version": "[4.10.*, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.33, 6.0.33]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.33, 6.0.33]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}}}