{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue?vue&type=style&index=0&id=6d6f7003&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\Inventory\\buildpalletsStart\\index.vue", "mtime": 1749630426874}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAmwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/Inventory/buildpalletsStart", "sourcesContent": ["<template>\r\n    <div class=\"usemystyle buildpalletsStart\">\r\n        <div class=\"InventorySearchBox\" style=\"margin-bottom: 0\">\r\n            <div class=\"searchbox\">\r\n                <el-button style=\"margin-left: 5px; width: 160px\" size=\"small\" icon=\"el-icon-back\" @click=\"back()\">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>\r\n                <el-button style=\"margin-left: 5px\" size=\"small\" icon=\"el-icon-refresh\" @click=\"refresh()\">{{ this.$t('Inventory.refresh') }}</el-button>\r\n                <div class=\"searchtipbox\">\r\n                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}\r\n                </div>\r\n                <el-button class=\"tablebtn\" @click=\"GetAddPallet\" v-if=\"this.SelectList.length == 0 && way == 'Batch'\" size=\"small\" style=\"margin-left: 5px; width: 120px\" icon=\"el-icon-plus\">\r\n                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}\r\n                </el-button>\r\n                <el-button class=\"tablebtn\" @click=\"openKeyDown()\" size=\"small\" style=\"margin-left: 5px\">\r\n                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}\r\n                </el-button>\r\n                <div class=\"searchtipbox\" style=\"background: #fff; color: red\">计划备注：{{ detailobj.Remark }}</div>\r\n                <div class=\"rightsearchbox\" style=\"position: absolute; right: 10px; display: flex\">\r\n                    <el-button style=\"margin-left: 5px; width: 100px\" size=\"small\" icon=\"el-icon-back\" :disabled=\"!MaterialList[MaterialNow - 1]\" @click=\"ChangeMaterial(-1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.Previous') }}\r\n                    </el-button>\r\n                    <el-button style=\"margin-left: 0px; width: 130px\" size=\"small\" icon=\"el-icon-right\" :disabled=\"!MaterialList[MaterialNow + 1]\" @click=\"ChangeMaterial(+1)\">\r\n                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}\r\n                    </el-button>\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <!-- <div class=\"searchboxTitle\" v-if=\"way == 'Material'\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->\r\n                <div class=\"searchboxTitle\">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>\r\n                <div class=\"searchboxTitle\">\r\n                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/\r\n                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}\r\n                    <!-- /{{ detailobj.PrepStatuscount }}  -->\r\n                    <!-- -{{ detailobj.EquipmentName }} -->\r\n                </div>\r\n            </div>\r\n            <div class=\"searchbox\">\r\n                <div class=\"searchboxColorTitle\" v-if=\"way == 'Batch'\" :style=\"{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}\r\n                </div>\r\n\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantity\r\n                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))\r\n                            : detailobj.MQuantity\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{\r\n                        way == 'Material'\r\n                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0\r\n                                ? detailobj.MQuantityTotal\r\n                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))\r\n                            : detailobj.MQuantityTotal\r\n                    }}\r\n                </div>\r\n                <div class=\"searchboxColorTitle\" :style=\"{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }\">\r\n                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"tableboxheightall\">\r\n            <div class=\"tablebox available-inventory-container\" :style=\"{\r\n                '--table-height': tableHeight + 'px',\r\n                '--dynamic-table-height': tableHeight + 'px'\r\n            }\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">\r\n                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            <!-- 测试状态显示 - 测试完成后删除 -->\r\n                            <span style=\"font-size: 12px; color: #666; margin-left: 10px;\">\r\n                                [{{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                                数据: {{ tableList.length }}行 |\r\n                                高度: {{ tableHeight }}px]\r\n                            </span>\r\n                        </div>\r\n                        <div style=\"position: absolute; right: 10px; display: flex; flex-wrap: wrap; gap: 5px; align-items: center; z-index: 10;\">\r\n                            <!-- 打印按钮 -->\r\n                            <el-button v-if=\"way == 'Material'\" class=\"tablebtn\" size=\"small\" style=\"width: 140px;\" @click=\"PrintAvallable()\">\r\n                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}\r\n                            </el-button>\r\n\r\n                            <!-- 模式切换按钮 - 始终显示 -->\r\n                            <el-button\r\n                                class=\"tablebtn\"\r\n                                size=\"small\"\r\n                                @click=\"toggleTableHeightMode()\"\r\n                                :title=\"useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'\"\r\n                                :type=\"useViewportHeight ? 'primary' : 'default'\"\r\n                                style=\"min-width: 80px; background: #fff; border: 1px solid #ddd;\">\r\n                                {{ useViewportHeight ? '视口模式' : '行数模式' }}\r\n                            </el-button>\r\n\r\n                            <!-- 测试按钮组 - 测试完成后删除 -->\r\n                            <el-button size=\"mini\" type=\"success\" @click=\"addTestData()\" title=\"添加测试数据\" style=\"background: #67c23a; color: white; border: none;\">+数据</el-button>\r\n                            <el-button size=\"mini\" type=\"warning\" @click=\"removeTestData()\" title=\"删除测试数据\" style=\"background: #e6a23c; color: white; border: none;\">-数据</el-button>\r\n                            <el-button size=\"mini\" type=\"info\" @click=\"showTestInfo()\" title=\"显示测试信息\" style=\"background: #909399; color: white; border: none;\">信息</el-button>\r\n                            <el-button size=\"mini\" type=\"danger\" @click=\"forceSetTableHeight(300)\" title=\"强制设置300px\" style=\"background: #f56c6c; color: white; border: none;\">300px</el-button>\r\n                            <el-button size=\"mini\" type=\"danger\" @click=\"forceSetTableHeight(500)\" title=\"强制设置500px\" style=\"background: #f56c6c; color: white; border: none;\">500px</el-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 测试控制面板 - 测试完成后删除 -->\r\n                <div style=\"background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px; padding: 10px; margin-bottom: 10px;\">\r\n                    <div style=\"display: flex; align-items: center; gap: 10px; flex-wrap: wrap;\">\r\n                        <span style=\"font-weight: bold; color: #0369a1;\">🧪 表格高度测试:</span>\r\n\r\n                        <el-button\r\n                            size=\"small\"\r\n                            @click=\"toggleTableHeightMode()\"\r\n                            :type=\"useViewportHeight ? 'primary' : 'default'\">\r\n                            {{ useViewportHeight ? '🖥️ 视口模式' : '📏 行数模式' }}\r\n                        </el-button>\r\n\r\n                        <el-button size=\"small\" type=\"success\" @click=\"addTestData()\">➕ 添加数据</el-button>\r\n                        <el-button size=\"small\" type=\"warning\" @click=\"removeTestData()\">➖ 删除数据</el-button>\r\n                        <el-button size=\"small\" type=\"info\" @click=\"showTestInfo()\">详细信息</el-button>\r\n                        <el-button size=\"small\" type=\"primary\" @click=\"recalculateHeight()\">重新计算</el-button>\r\n                        <el-button size=\"small\" type=\"danger\" @click=\"ultimateForceHeight(200)\">� 终极200px</el-button>\r\n                        <el-button size=\"small\" type=\"danger\" @click=\"ultimateForceHeight(400)\">� 终极400px</el-button>\r\n\r\n                        <span style=\"color: #64748b; font-size: 12px;\">\r\n                            当前: {{ useViewportHeight ? '视口模式' : '行数模式' }} |\r\n                            数据: {{ tableList.length }}行 |\r\n                            高度: {{ tableHeight }}px\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n\r\n                <el-table\r\n                    :data=\"tableList\"\r\n                    ref=\"TopTabel\"\r\n                    @row-click=\"GetCurrentRow\"\r\n                    highlight-current-row\r\n                    :class=\"['dynamic-table', `height-${Math.floor(tableHeight)}`]\"\r\n                    :style=\"{\r\n                        width: '100%',\r\n                        height: tableHeight + 'px !important',\r\n                        maxHeight: tableHeight + 'px !important',\r\n                        minHeight: tableHeight + 'px !important'\r\n                    }\"\r\n                    :height=\"tableHeight\"\r\n                    :max-height=\"tableHeight\"\r\n                    :key=\"tableHeightKey\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in header\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'BatchStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SSCCStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }\">\r\n                                    {{ scope.row.ExpirationDate }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div>\r\n            <!-- <div class=\"tablebox\" style=\"height: 32%\" v-if=\"way == 'Batch'\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.POInventory') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-table :data=\"tableListBatchPO\" ref=\"TopBatchTabel\" @row-click=\"GetCurrentRow2\" highlight-current-row style=\"width: 100%\" height=\"200\">\r\n                    <el-table-column\r\n                        v-for=\"(item, index) in headerBatchPO\"\r\n                        :fixed=\"item.fixed ? item.fixed : false\"\r\n                        :key=\"index\"\r\n                        :align=\"item.align\"\r\n                        :prop=\"item.prop ? item.prop : item.value\"\r\n                        :label=\"$t(`$vuetify.dataTable.${tableId}.${item.value}`)\"\r\n                        :width=\"item.width\"\r\n                    >\r\n                        <template slot-scope=\"scope\">\r\n                            <span v-if=\"scope.column.property == 'SSCC/Batch'\">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>\r\n                            <span v-else-if=\"scope.column.property == 'Material'\">\r\n                                <div>{{ scope.row.MaterialCode }}</div>\r\n                                <div style=\"color: #808080\">{{ scope.row.MaterialName }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'SbStatus'\">\r\n                                <div :class=\"'statusbox status' + scope.row.SbStatus\">\r\n                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'LStatus'\">\r\n                                <div :class=\"'statusbox batchstatus' + scope.row.LStatus\">\r\n                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}\r\n                                </div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'ExpirationDate'\">\r\n                                <div class=\"statusbox\" :style=\"{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }\">{{ scope.row.ExpirationDate }}</div>\r\n                            </span>\r\n                            <span v-else-if=\"scope.column.property == 'Quantity'\">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>\r\n                            <span v-else>{{ scope.row[item.prop] }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n            </div> -->\r\n            <div class=\"tablebox\" style=\"height: 21%\">\r\n                <div class=\"InventorySearchBox\">\r\n                    <div class=\"searchbox\">\r\n                        <div class=\"searchboxTitle\" style=\"font-size: 16px\">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>\r\n                    </div>\r\n                </div>\r\n                <el-tabs v-model=\"activeName\" type=\"border-card\">\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullBag')\" name=\"FullBag\">\r\n                        <FullBag ref=\"FullBag\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.PartialBag')\" name=\"PartialBag\">\r\n                        <PartialBag ref=\"PartialBag\" @getRefresh=\"refresh()\" @getRowBySscc=\"getRowBySscc\"></PartialBag>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"this.$t('MaterialPreparationBuild.FullAmount')\" name=\"FullAmount\">\r\n                        <FullAmount ref=\"FullAmount\" @getRefresh=\"refresh()\" @getRowSSCC=\"GetSSCC\" @getRowBySscc=\"getRowBySscc\"></FullAmount>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"this.SelectList.length != 0 && way == 'Batch'\">\r\n                <BatchPallets ref=\"BatchPallets\"></BatchPallets>\r\n            </div>\r\n            <div class=\"tablebox\" style=\"height: 600px\" v-if=\"way == 'Material'\">\r\n                <POInventory ref=\"POInventory\"></POInventory>\r\n            </div>\r\n        </div>\r\n        <el-dialog :title=\"$t('Inventory.Print')\" id=\"Printdialog\" :visible.sync=\"PrintModel\" width=\"500px\">\r\n            <div class=\"dialogdetailbox\" style=\"margin: 10px 0\">\r\n                <div class=\"dialogdetailsinglelabel\">{{ $t('Inventory.selectprinter') }}</div>\r\n                <div class=\"dialogdetailsinglevalue\" style=\"width: auto\">\r\n                    <el-select disabled clearable v-model=\"PrintId\" filterable>\r\n                        <el-option v-for=\"item in printeroption\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\"></el-option>\r\n                    </el-select>\r\n                </div>\r\n            </div>\r\n            <span slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button class=\"tablebtn\" icon=\"el-icon-orange\" @click=\"getPrint()\">\r\n                    {{ $t('Inventory.Print') }}\r\n                </el-button>\r\n                <el-button @click=\"PrintModel = false\" icon=\"el-icon-circle-close\">{{ $t('GLOBAL._QX') }}</el-button>\r\n            </span>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n<script>\r\nimport '@/views/Inventory/mystyle.scss';\r\nimport { Message, MessageBox } from 'element-ui';\r\nimport { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';\r\nimport {\r\n    GetPageListNewMaterialPreDown,\r\n    GetPageListByMaterial,\r\n    GetPageListByBatchIDSByID,\r\n    GetPageListByMaterialII,\r\n    GetPageListByBatchIDSII,\r\n    GetPageListMaterialPreTop,\r\n    GetPageListNewMaterialPreTop,\r\n    GetConSelectList,\r\n    FirstAddPallet,\r\n    GetPageListByBatchIDS,\r\n    MygetSSCC\r\n} from '@/api/Inventory/MaterialPreparation.js';\r\nimport { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';\r\nimport { Empty } from 'ant-design-vue';\r\n\r\nexport default {\r\n    components: {\r\n        PartialBag: () => import('./components/PartialBag'),\r\n        FullAmount: () => import('./components/FullAmount'),\r\n        FullBag: () => import('./components/FullBag'),\r\n        POInventory: () => import('./components/POInventory'),\r\n        BatchPallets: () => import('./components/BatchPallets')\r\n    },\r\n    data() {\r\n        return {\r\n            PrintId: '',\r\n            printeroption: [],\r\n            PrintModel: false,\r\n            isExpirationDate: false,\r\n            tableId: 'INV_CLZB',\r\n            activeName: 'PartialBag',\r\n            OnlyFullAmount: false,\r\n            Hidecompleted: false,\r\n            detailobj: {},\r\n            room: '',\r\n            tableList: [],\r\n            tableListBatchPO: [],\r\n            SelectList: [],\r\n            headerBatchPO: POInventoryPalletsColumn,\r\n            header: AvallableInventoryColumn,\r\n            way: '',\r\n            listId: '',\r\n            nowChooseRow: {},\r\n            MaterialList: [],\r\n            MaterialNow: null,\r\n            UseType: '',\r\n            clblFlag: false,\r\n            keepKeyDown: false,\r\n            minTableHeight: 150, // 最小表格高度\r\n            maxTableHeight: 400, // 最大表格高度\r\n            rowHeight: 48, // 每行的高度（调整为更准确的值）\r\n            windowHeight: window.innerHeight, // 窗口高度\r\n            useViewportHeight: false, // 是否使用视口高度模式\r\n            tableHeight: 200, // 当前表格高度\r\n            tableHeightKey: 0, // 强制重新渲染的key\r\n            isResizing: false, // 防止resize死循环\r\n            // 测试相关数据 - 测试完成后删除\r\n            testDataCounter: 0,\r\n            originalTableList: [] // 保存原始数据\r\n        };\r\n    },\r\n    mounted() {\r\n        console.log(this.$route)\r\n        console.log(this.$route.path)\r\n        let mykey = window.sessionStorage.getItem('MaterialPreparation');\r\n        if (mykey == 'clbl') {\r\n            this.way = 'Material';\r\n        } else {\r\n            this.way = 'Batch';\r\n        }\r\n        this.detailobj = JSON.parse(this.$route.query.query);\r\n        console.log(this.detailobj, 123);\r\n        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n        console.log(this.clblFlag);\r\n        this.UseType = this.$route.query.UseType;\r\n        this.listId = JSON.parse(this.$route.query.List);\r\n        this.room = window.sessionStorage.getItem('room');\r\n        this.getprintList();\r\n        // this.way = this.$route.query.way;\r\n        this.GetMaterialList();\r\n        this.getSelectList();\r\n        this.getTopData();\r\n        if (this.way == 'Batch') {\r\n            this.getPOTabelData();\r\n            this.activeName = 'FullBag';\r\n        } else {\r\n            this.activeName = 'PartialBag';\r\n        }\r\n\r\n        // 添加窗口大小变化监听器\r\n        this.isResizing = false; // 防止死循环标志\r\n        this.handleResize = () => {\r\n            if (this.isResizing) {\r\n                console.log('跳过resize事件，防止死循环');\r\n                return;\r\n            }\r\n            this.windowHeight = window.innerHeight;\r\n            console.log('窗口大小变化:', this.windowHeight);\r\n            if (this.useViewportHeight) {\r\n                this.updateTableHeight();\r\n            }\r\n        };\r\n        window.addEventListener('resize', this.handleResize);\r\n\r\n        // 初始化日志\r\n        console.log('=== 表格自适应高度初始化 ===');\r\n        console.log('初始模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n        console.log('初始窗口高度:', this.windowHeight);\r\n        console.log('way变量值:', this.way);\r\n        console.log('tableList长度:', this.tableList ? this.tableList.length : 'undefined');\r\n\r\n        // 初始化表格高度\r\n        this.$nextTick(() => {\r\n            this.updateTableHeight();\r\n            console.log('=== 页面初始化完成 ===');\r\n        });\r\n    },\r\n    beforeMount() {\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n    },\r\n    beforeDestroy() {\r\n        // 清理事件监听器\r\n        window.removeEventListener('keyup', this.getKeyDown);\r\n        if (this.handleResize) {\r\n            window.removeEventListener('resize', this.handleResize);\r\n        }\r\n    },\r\n    methods: {\r\n        openKeyDown() {\r\n            console.log(2);\r\n            if (this.keepKeyDown == false) {\r\n                window.addEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = true;\r\n            } else {\r\n                window.removeEventListener('keyup', this.getKeyDown);\r\n                this.keepKeyDown = false;\r\n            }\r\n        },\r\n        getKeyDown(event) {\r\n            if (this.keepKeyDown) {\r\n                let code = event.keyCode;\r\n                console.log(code);\r\n                switch (code) {\r\n                    case 37:\r\n                        if (this.MaterialList[this.MaterialNow - 1]) {\r\n                            this.ChangeMaterial(-1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 39:\r\n                        if (this.MaterialList[this.MaterialNow + 1]) {\r\n                            this.ChangeMaterial(+1);\r\n                        } else {\r\n                            Message({\r\n                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,\r\n                                type: 'warning'\r\n                            });\r\n                        }\r\n                        break;\r\n                    case 32:\r\n                        if (this.activeName == 'FullBag') {\r\n                            console.log('FullBag');\r\n                            if (this.$refs.FullBag.getbtnStatus()) {\r\n                                this.$refs.FullBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'PartialBag') {\r\n                            console.log('PartialBag');\r\n                            if (this.$refs.PartialBag.getbtnStatus()) {\r\n                                this.$refs.PartialBag.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        if (this.activeName == 'FullAmount') {\r\n                            console.log('FullAmount');\r\n                            if (this.$refs.FullAmount.getbtnStatus()) {\r\n                                this.$refs.FullAmount.Transfer();\r\n                            } else {\r\n                                Message({\r\n                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,\r\n                                    type: 'warning'\r\n                                });\r\n                            }\r\n                        }\r\n                        break;\r\n                }\r\n                return false;\r\n            }\r\n        },\r\n        async getPrint() {\r\n            let ids = this.tableList.map(item => {\r\n                return item.ID;\r\n            });\r\n\r\n            // let params = {\r\n            //     Ids: ids,\r\n            //     equmentid: this.room,\r\n            //     PrintId: this.PrintId\r\n            // };\r\n\r\n            let params = {\r\n                printId: this.PrintId,\r\n                EquipmentId: this.room,\r\n                BagSiZe: this.detailobj.BagSize,\r\n                MCode: this.detailobj.MCode,\r\n                ids: ids\r\n            };\r\n\r\n            let res = await PrintPreparaLabelKY(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.PrintModel = false;\r\n        },\r\n        async getprintList() {\r\n            let params = {\r\n                equipmentId: this.room\r\n            };\r\n            let res2 = await GetPrinit8(params);\r\n            res2.response.forEach(item => {\r\n                item.value = item.ID;\r\n                item.label = item.Code;\r\n                item.ItemName = item.Code;\r\n                item.ItemValue = item.ID;\r\n            });\r\n            this.printeroption = res2.response;\r\n            if (this.$refs.POInventory) {\r\n                this.$refs.POInventory.printeroption = this.printeroption;\r\n            }\r\n        },\r\n        PrintAvallable() {\r\n            this.PrintId = window.sessionStorage.getItem('PrintId');\r\n            this.PrintModel = true;\r\n        },\r\n        async getPOTabelData() {\r\n            let params = {\r\n                BatchId: this.detailobj.BatchId,\r\n                ProOrderid: this.detailobj.ProductionOrderId,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            let res = await GetPageListNewMaterialPreDown(params);\r\n            this.tableListBatchPO = res.response.data;\r\n        },\r\n        GetSSCC(key) {\r\n            let flag = this.tableList.some(item => {\r\n                return item.SbSscc == key;\r\n            });\r\n            if (flag == true) {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = true;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = true;\r\n                }\r\n            } else {\r\n                if (this.$refs.FullBag) {\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.PartialBag) {\r\n                    this.$refs.PartialBag.ssccFlag = false;\r\n                }\r\n                if (this.$refs.FullAmount) {\r\n                    this.$refs.FullAmount.ssccFlag = false;\r\n                }\r\n            }\r\n        },\r\n        EmptySscc() {\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n        },\r\n        ChangeMaterial(num) {\r\n            if (this.way == 'Batch' && !this.OnlyFullAmount) {\r\n                this.activeName = 'FullBag';\r\n            }\r\n            if (this.way == 'Material') {\r\n                this.activeName = 'PartialBag';\r\n            }\r\n            if (this.OnlyFullAmount) {\r\n                this.activeName = 'FullAmount';\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.sscc = '';\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.sscc = '';\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.sscc = '';\r\n            }\r\n            let now = this.MaterialNow + num;\r\n            this.detailobj = this.MaterialList[now];\r\n            console.log(this.detailobj);\r\n            this.MaterialNow = now;\r\n            this.refresh();\r\n        },\r\n        async GetMaterialList() {\r\n            let res;\r\n            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        pageIndex: 1,\r\n                        EquipmentId: this.room,\r\n                        pageSize: 1000\r\n                    };\r\n                    // alert('aa');\r\n                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        MaterialId: this.listId,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            } else {\r\n                if (this.UseType == 'Batch') {\r\n                    let data = {\r\n                        ID: this.listId,\r\n                        EquipmentId: this.room,\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样\r\n                } else {\r\n                    let ProIds = window.sessionStorage.getItem('ProIds');\r\n                    let data = {\r\n                        MaterialId: this.listId,\r\n                        EqumentId: this.room,\r\n                        ProId: JSON.parse(ProIds),\r\n                        pageIndex: 1,\r\n                        pageSize: 1000\r\n                    };\r\n                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样\r\n                }\r\n            }\r\n\r\n            let response = res.response;\r\n            this.MaterialList = response.data;\r\n            this.MaterialList.forEach((item, index) => {\r\n                if (item.OnlyId == this.detailobj.OnlyId) {\r\n                    this.MaterialNow = index;\r\n                }\r\n            });\r\n            this.detailobj = this.MaterialList[this.MaterialNow];\r\n            this.detailobj.isGUnit = false;\r\n            if (this.detailobj.ChangeUnit) {\r\n                if (this.detailobj.ChangeUnit == 'g') {\r\n                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;\r\n                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;\r\n                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;\r\n                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;\r\n                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;\r\n                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;\r\n                    this.detailobj.isGUnit = true;\r\n                }\r\n            }\r\n            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.getDetailobj();\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n            if (this.$refs.FullBag) {\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                this.$refs.FullBag.Bags = 0;\r\n            }\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.getTabelData();\r\n                this.$refs.BatchPallets.getBatchPalletsStatus();\r\n            }\r\n        },\r\n        async getRowBySscc(val) {\r\n            let params = {\r\n                MCode: this.detailobj.MCode,\r\n                SSCC: val\r\n            };\r\n            let res = await MygetSSCC(params);\r\n            if (res.response.data == null) {\r\n                Message({\r\n                    message: `该追溯码不存在`,\r\n                    type: 'error'\r\n                });\r\n            } else {\r\n                let data = res.response.data[0];\r\n                if (data.Remark == 'ky') {\r\n                    this.$refs.TopTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow(item);\r\n                        }\r\n                    });\r\n                } else {\r\n                    this.$refs.TopBatchTabel.tableData.forEach(item => {\r\n                        if (item.ID == data.ID) {\r\n                            this.$refs.TopBatchTabel.setCurrentRow(item);\r\n                            this.GetCurrentRow2(item);\r\n                        }\r\n                    });\r\n                }\r\n            }\r\n        },\r\n        GetCurrentRow(val) {\r\n            console.log(val, 2);\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            // this.$refs.TopTabel.setCurrentRow();\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            if (this.$refs.TopBatchTabel) {\r\n                this.$refs.TopBatchTabel.setCurrentRow();\r\n            }\r\n            this.OnlyFullAmount = false;\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.BatchPallets) {\r\n                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;\r\n            }\r\n            if (this.$refs.PartialBag) {\r\n                this.$refs.PartialBag.ssccFlag = true;\r\n                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            //判断整袋转移\r\n            if (this.$refs.FullBag) {\r\n                let InQuantity = this.nowChooseRow.InQuantity;\r\n                if (this.detailobj.ChangeUnit) {\r\n                    if (this.detailobj.ChangeUnit == 'g') {\r\n                        InQuantity = InQuantity * 1000;\r\n                    }\r\n                }\r\n                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;\r\n                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];\r\n                let key = num * this.detailobj.BagSize;\r\n                if (num == 0) {\r\n                    //左右相等就禁止转移，并且包数为0\r\n                    this.$refs.FullBag.ssccFlag = false;\r\n                    this.$refs.FullBag.Bags = 0;\r\n                } else {\r\n                    //不相等就判断选中数量跟差值乘以单包数量\r\n                    this.$refs.FullBag.ssccFlag = true;\r\n                    if (InQuantity >= key) {\r\n                        this.$refs.FullBag.Bags = Math.floor(num);\r\n                    } else {\r\n                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);\r\n                    }\r\n                }\r\n\r\n                this.$refs.FullBag.InQuantity = InQuantity;\r\n                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;\r\n            }\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity();\r\n            }\r\n        },\r\n        GetCurrentRow2(val) {\r\n            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);\r\n            this.$refs.TopTabel.setCurrentRow();\r\n            this.activeName = 'FullAmount';\r\n            this.OnlyFullAmount = true;\r\n            // this.$refs.TopBatchTabel.setCurrentRow();\r\n            this.nowChooseRow = val;\r\n            if (this.$refs.FullAmount) {\r\n                this.$refs.FullAmount.ssccFlag = true;\r\n                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;\r\n                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;\r\n                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);\r\n            }\r\n        },\r\n        async GetAddPallet() {\r\n            let params = {\r\n                TareWeight: this.detailobj.MQuantityTotal,\r\n                UomID: this.detailobj.TUintid,\r\n                ProBatchID: this.detailobj.BatchId,\r\n                EquipMentID: this.room,\r\n                MaterialId: this.detailobj.MaterialId,\r\n                ProRequestID: this.detailobj.ProductionOrderId\r\n            };\r\n            let res = await FirstAddPallet(params);\r\n            Message({\r\n                message: res.msg,\r\n                type: 'success'\r\n            });\r\n            this.refresh();\r\n        },\r\n        refresh() {\r\n            this.getTopData();\r\n            this.GetMaterialList();\r\n            if (this.way == 'Batch') {\r\n                this.getPOTabelData();\r\n                this.getSelectList();\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getSelectList() {\r\n            let params = {\r\n                proOrderID: this.detailobj.ProductionOrderId,\r\n                batchID: this.detailobj.BatchId\r\n            };\r\n            let res = await GetConSelectList(params);\r\n            console.log(res, 123123);\r\n            this.SelectList = res.response;\r\n            if (this.way == 'Batch') {\r\n                if (this.SelectList.length != 0) {\r\n                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);\r\n                    if (this.$refs.BatchPallets) {\r\n                        this.$refs.BatchPallets.BatchPalletsOption = res.response;\r\n                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;\r\n                    }\r\n                }\r\n            } else {\r\n                this.$refs.POInventory.getTabelData();\r\n            }\r\n        },\r\n        async getTopData() {\r\n            let res;\r\n            let params = {\r\n                MaterialId: this.detailobj.MaterialId,\r\n                EquipmentId: this.room,\r\n                pageIndex: 1,\r\n                pageSize: 1000\r\n            };\r\n            if (this.way == 'Batch') {\r\n                res = await GetPageListMaterialPreTop(params);\r\n            } else {\r\n                res = await GetPageListNewMaterialPreTop(params);\r\n            }\r\n            this.tableList = res.response.data;\r\n            // 数据加载完成后更新表格高度\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n        },\r\n        back(val) {\r\n            this.$router.go(-1);\r\n        },\r\n        isDateInThePast(dateString) {\r\n            const givenDate = new Date(dateString);\r\n            const now = new Date();\r\n            return givenDate < now;\r\n        },\r\n        // 切换表格高度模式\r\n        toggleTableHeightMode() {\r\n            console.log('=== 切换表格高度模式 ===');\r\n            console.log('切换前:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('当前表格高度:', this.tableHeight);\r\n\r\n            this.useViewportHeight = !this.useViewportHeight;\r\n\r\n            console.log('切换后:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n\r\n            // 立即重新计算表格高度\r\n            this.updateTableHeight();\r\n\r\n            // 显示切换成功消息\r\n            Message({\r\n                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n        // 更新表格高度\r\n        updateTableHeight() {\r\n            if (this.isResizing) {\r\n                console.log('跳过updateTableHeight，正在resize中');\r\n                return;\r\n            }\r\n\r\n            console.log('=== updateTableHeight 被调用 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('tableList.length:', this.tableList ? this.tableList.length : 'undefined');\r\n\r\n            let newHeight;\r\n\r\n            if (!this.tableList || this.tableList.length === 0) {\r\n                newHeight = this.minTableHeight;\r\n                console.log('使用最小高度:', newHeight);\r\n            } else if (this.useViewportHeight) {\r\n                // 基于视口高度的响应式计算\r\n                const availableHeight = this.windowHeight * 0.25;\r\n                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);\r\n                console.log('视口模式计算:', {\r\n                    windowHeight: this.windowHeight,\r\n                    availableHeight,\r\n                    newHeight\r\n                });\r\n            } else {\r\n                // 基于数据行数计算高度 - 智能计算\r\n                newHeight = this.calculateSmartTableHeight();\r\n\r\n                console.log('行数模式计算结果:', {\r\n                    dataRows: this.tableList.length,\r\n                    rowHeight: this.rowHeight,\r\n                    calculatedHeight: newHeight\r\n                });\r\n            }\r\n\r\n            console.log('旧高度:', this.tableHeight, '新高度:', newHeight);\r\n\r\n            // 只有高度真的改变时才更新\r\n            if (Math.abs(this.tableHeight - newHeight) > 1) {\r\n                this.tableHeight = newHeight;\r\n                this.tableHeightKey++; // 强制重新渲染\r\n                console.log('高度已更新到:', this.tableHeight, 'Key:', this.tableHeightKey);\r\n\r\n                this.$nextTick(() => {\r\n                    this.forceTableHeightDOM(newHeight);\r\n                });\r\n            } else {\r\n                console.log('高度无变化，跳过更新');\r\n            }\r\n        },\r\n\r\n        // 智能计算表格高度\r\n        calculateSmartTableHeight() {\r\n            // 基础计算\r\n            const headerHeight = 40; // 表头高度\r\n            const borderPadding = 4; // 边框和内边距\r\n            const extraSpace = 12; // 额外空间，确保完整显示\r\n\r\n            // 如果表格已经渲染，尝试检测实际尺寸\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                const headerEl = tableEl.querySelector('.el-table__header');\r\n                const firstRow = tableEl.querySelector('.el-table__body tr');\r\n\r\n                let actualHeaderHeight = headerHeight;\r\n                let actualRowHeight = this.rowHeight;\r\n\r\n                if (headerEl) {\r\n                    actualHeaderHeight = headerEl.offsetHeight;\r\n                    console.log('检测到的实际表头高度:', actualHeaderHeight);\r\n                }\r\n\r\n                if (firstRow) {\r\n                    actualRowHeight = firstRow.offsetHeight;\r\n                    console.log('检测到的实际行高:', actualRowHeight);\r\n                    // 更新行高配置\r\n                    if (actualRowHeight > 0 && Math.abs(actualRowHeight - this.rowHeight) > 2) {\r\n                        this.rowHeight = actualRowHeight;\r\n                    }\r\n                }\r\n\r\n                const calculatedHeight = actualHeaderHeight + (this.tableList.length * actualRowHeight) + borderPadding + extraSpace;\r\n\r\n                console.log('智能计算详情:', {\r\n                    actualHeaderHeight,\r\n                    actualRowHeight,\r\n                    dataRows: this.tableList.length,\r\n                    borderPadding,\r\n                    extraSpace,\r\n                    calculatedHeight\r\n                });\r\n\r\n                return Math.min(Math.max(calculatedHeight, this.minTableHeight), this.maxTableHeight);\r\n            }\r\n\r\n            // 回退到基础计算\r\n            const basicHeight = headerHeight + (this.tableList.length * this.rowHeight) + borderPadding + extraSpace;\r\n            console.log('使用基础计算:', basicHeight);\r\n            return Math.min(Math.max(basicHeight, this.minTableHeight), this.maxTableHeight);\r\n        },\r\n\r\n        // 检测实际行高\r\n        detectActualRowHeight() {\r\n            if (this.$refs.TopTabel && this.tableList.length > 0) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                const firstRow = tableEl.querySelector('.el-table__body tr');\r\n                if (firstRow) {\r\n                    const actualRowHeight = firstRow.offsetHeight;\r\n                    console.log('检测到的实际行高:', actualRowHeight);\r\n                    if (actualRowHeight > 0 && Math.abs(actualRowHeight - this.rowHeight) > 5) {\r\n                        console.log('更新行高从', this.rowHeight, '到', actualRowHeight);\r\n                        this.rowHeight = actualRowHeight;\r\n                        return actualRowHeight;\r\n                    }\r\n                }\r\n            }\r\n            return this.rowHeight;\r\n        },\r\n\r\n        // 强制设置DOM高度\r\n        forceTableHeightDOM(height) {\r\n            console.log('=== 强制设置DOM高度 ===', height);\r\n\r\n            if (this.$refs.TopTabel) {\r\n                const tableComponent = this.$refs.TopTabel;\r\n                const tableEl = tableComponent.$el;\r\n\r\n                console.log('表格组件:', tableComponent);\r\n                console.log('表格DOM元素:', tableEl);\r\n\r\n                // 检测实际行高\r\n                this.detectActualRowHeight();\r\n\r\n                // 方法1: 直接设置表格元素样式\r\n                tableEl.style.height = height + 'px';\r\n                tableEl.style.maxHeight = height + 'px';\r\n                tableEl.style.minHeight = height + 'px';\r\n\r\n                // 方法2: 设置表格内部容器\r\n                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                if (bodyWrapper) {\r\n                    bodyWrapper.style.maxHeight = (height - 40) + 'px';\r\n                    console.log('设置body wrapper高度:', (height - 40) + 'px');\r\n                }\r\n\r\n                // 方法3: 调用Element UI的内部方法\r\n                if (tableComponent.doLayout) {\r\n                    tableComponent.doLayout();\r\n                }\r\n\r\n                // 验证设置结果\r\n                setTimeout(() => {\r\n                    console.log('DOM验证 - 表格高度:', tableEl.offsetHeight);\r\n                    console.log('DOM验证 - 样式高度:', tableEl.style.height);\r\n                    console.log('DOM验证 - 计算样式:', window.getComputedStyle(tableEl).height);\r\n\r\n                    if (bodyWrapper) {\r\n                        console.log('DOM验证 - body wrapper高度:', bodyWrapper.offsetHeight);\r\n                        console.log('DOM验证 - body wrapper样式:', bodyWrapper.style.maxHeight);\r\n                    }\r\n\r\n                    // 检查是否有滚动条\r\n                    const hasVerticalScrollbar = bodyWrapper && bodyWrapper.scrollHeight > bodyWrapper.clientHeight;\r\n                    console.log('是否有垂直滚动条:', hasVerticalScrollbar);\r\n                    if (hasVerticalScrollbar) {\r\n                        console.log('内容高度:', bodyWrapper.scrollHeight, '可见高度:', bodyWrapper.clientHeight);\r\n                    }\r\n                }, 100);\r\n\r\n            } else {\r\n                console.log('表格引用不存在');\r\n            }\r\n        },\r\n        // 手动设置表格高度参数\r\n        setTableHeightParams(minHeight, maxHeight, rowHeight) {\r\n            this.minTableHeight = minHeight || 150;\r\n            this.maxTableHeight = maxHeight || 400;\r\n            this.rowHeight = rowHeight || 40;\r\n        },\r\n        // 测试方法 - 可以在浏览器控制台调用\r\n        testTableHeight() {\r\n            console.log('=== 表格高度测试 ===');\r\n            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');\r\n            console.log('表格数据行数:', this.tableList ? this.tableList.length : 0);\r\n            console.log('窗口高度:', this.windowHeight);\r\n            console.log('当前表格高度:', this.tableHeight);\r\n            console.log('最小高度:', this.minTableHeight);\r\n            console.log('最大高度:', this.maxTableHeight);\r\n            console.log('行高:', this.rowHeight);\r\n            console.log('表格Key:', this.tableHeightKey);\r\n\r\n            // 检查表格DOM元素\r\n            if (this.$refs.TopTabel) {\r\n                const tableEl = this.$refs.TopTabel.$el;\r\n                console.log('表格DOM高度:', tableEl.style.height);\r\n                console.log('表格实际高度:', tableEl.offsetHeight);\r\n                console.log('表格计算样式:', window.getComputedStyle(tableEl).height);\r\n            }\r\n\r\n            return this.tableHeight;\r\n        },\r\n\r\n        // 重新计算高度\r\n        recalculateHeight() {\r\n            console.log('=== 重新计算高度 ===');\r\n            // 强制重新检测行高\r\n            this.detectActualRowHeight();\r\n            // 重新计算并应用高度\r\n            this.updateTableHeight();\r\n        },\r\n\r\n        // 强制设置表格高度 - 测试用\r\n        forceSetTableHeight(height) {\r\n            console.log('=== 强制设置表格高度 ===', height);\r\n            this.tableHeight = height;\r\n            this.tableHeightKey++;\r\n\r\n            this.$nextTick(() => {\r\n                this.forceTableHeightDOM(height);\r\n            });\r\n        },\r\n\r\n        // 终极DOM操作方法\r\n        ultimateForceHeight(height) {\r\n            console.log('=== 终极强制高度设置 ===', height);\r\n\r\n            // 1. 更新Vue数据\r\n            this.tableHeight = height;\r\n            this.tableHeightKey++;\r\n\r\n            // 2. 强制更新组件\r\n            this.$forceUpdate();\r\n\r\n            // 3. 等待DOM更新后操作\r\n            this.$nextTick(() => {\r\n                if (this.$refs.TopTabel) {\r\n                    const tableComponent = this.$refs.TopTabel;\r\n                    const tableEl = tableComponent.$el;\r\n\r\n                    // 4. 设置所有可能的高度属性\r\n                    tableEl.style.setProperty('height', height + 'px', 'important');\r\n                    tableEl.style.setProperty('max-height', height + 'px', 'important');\r\n                    tableEl.style.setProperty('min-height', height + 'px', 'important');\r\n\r\n                    // 5. 设置CSS变量\r\n                    tableEl.style.setProperty('--table-height', height + 'px');\r\n                    tableEl.style.setProperty('--dynamic-table-height', height + 'px');\r\n\r\n                    // 6. 设置父容器CSS变量\r\n                    const container = tableEl.closest('.available-inventory-container');\r\n                    if (container) {\r\n                        container.style.setProperty('--table-height', height + 'px');\r\n                        container.style.setProperty('--dynamic-table-height', height + 'px');\r\n                    }\r\n\r\n                    // 7. 设置内部元素\r\n                    const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');\r\n                    if (bodyWrapper) {\r\n                        bodyWrapper.style.setProperty('max-height', (height - 40) + 'px', 'important');\r\n                    }\r\n\r\n                    // 8. 调用Element UI方法\r\n                    if (tableComponent.doLayout) {\r\n                        tableComponent.doLayout();\r\n                    }\r\n\r\n                    // 9. 触发resize事件（防止死循环）\r\n                    setTimeout(() => {\r\n                        this.isResizing = true;\r\n                        window.dispatchEvent(new Event('resize'));\r\n                        setTimeout(() => {\r\n                            this.isResizing = false;\r\n                        }, 100);\r\n\r\n                        // 10. 最终验证\r\n                        console.log('=== 最终验证结果 ===');\r\n                        console.log('表格offsetHeight:', tableEl.offsetHeight);\r\n                        console.log('表格style.height:', tableEl.style.height);\r\n                        console.log('表格计算样式:', window.getComputedStyle(tableEl).height);\r\n\r\n                        if (bodyWrapper) {\r\n                            console.log('body wrapper offsetHeight:', bodyWrapper.offsetHeight);\r\n                            console.log('body wrapper style:', bodyWrapper.style.maxHeight);\r\n                        }\r\n                    }, 200);\r\n                }\r\n            });\r\n        },\r\n\r\n        // === 以下为测试方法，测试完成后删除 ===\r\n\r\n        // 生成测试数据\r\n        generateTestData() {\r\n            const testData = {\r\n                ID: `TEST_${++this.testDataCounter}`,\r\n                SbSscc: `SSCC${String(this.testDataCounter).padStart(4, '0')}`,\r\n                LBatch: `BATCH${String(this.testDataCounter).padStart(3, '0')}`,\r\n                MaterialCode: `MAT${String(this.testDataCounter % 5 + 1).padStart(3, '0')}`,\r\n                MaterialName: `测试物料${this.testDataCounter}`,\r\n                InQuantity: Math.floor(Math.random() * 1000) + 100,\r\n                MaterialUnit1: 'kg',\r\n                LStatus: Math.floor(Math.random() * 3) + 1,\r\n                SbStatus: Math.floor(Math.random() * 3) + 1,\r\n                ExpirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\r\n                Location: `A${String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`\r\n            };\r\n            return testData;\r\n        },\r\n\r\n        // 添加测试数据\r\n        addTestData() {\r\n            // 第一次添加时保存原始数据\r\n            if (this.originalTableList.length === 0 && this.tableList.length > 0) {\r\n                this.originalTableList = [...this.tableList];\r\n            }\r\n\r\n            const newData = this.generateTestData();\r\n            this.tableList.push(newData);\r\n\r\n            this.$nextTick(() => {\r\n                this.updateTableHeight();\r\n            });\r\n\r\n            Message({\r\n                message: `已添加测试数据，当前共 ${this.tableList.length} 行`,\r\n                type: 'success',\r\n                duration: 2000\r\n            });\r\n        },\r\n\r\n        // 删除测试数据\r\n        removeTestData() {\r\n            if (this.tableList.length > 0) {\r\n                // 优先删除测试数据\r\n                const testIndex = this.tableList.findIndex(item => item.ID && item.ID.startsWith('TEST_'));\r\n                if (testIndex !== -1) {\r\n                    this.tableList.splice(testIndex, 1);\r\n                } else {\r\n                    this.tableList.pop();\r\n                }\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: `已删除数据，当前共 ${this.tableList.length} 行`,\r\n                    type: 'warning',\r\n                    duration: 2000\r\n                });\r\n            } else {\r\n                Message({\r\n                    message: '没有数据可删除',\r\n                    type: 'info',\r\n                    duration: 2000\r\n                });\r\n            }\r\n        },\r\n\r\n        // 显示测试信息\r\n        showTestInfo() {\r\n            const info = `\r\n=== 表格自适应高度测试信息 ===\r\n当前模式: ${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}\r\n数据行数: ${this.tableList.length}\r\n窗口高度: ${this.windowHeight}px\r\n表格高度: ${this.tableHeight}px\r\n最小高度: ${this.minTableHeight}px\r\n最大高度: ${this.maxTableHeight}px\r\n行高设置: ${this.rowHeight}px\r\n\r\n测试说明:\r\n1. 点击\"视口模式/行数模式\"按钮切换计算方式\r\n2. 点击\"+数据\"按钮添加测试数据观察高度变化\r\n3. 点击\"-数据\"按钮删除数据观察高度变化\r\n4. 调整浏览器窗口大小测试响应式效果\r\n5. 在视口模式下，表格高度为窗口高度的25%\r\n6. 在行数模式下，表格高度根据数据行数计算\r\n            `;\r\n\r\n            console.log(info);\r\n\r\n            this.$alert(info, '测试信息', {\r\n                confirmButtonText: '确定',\r\n                type: 'info'\r\n            });\r\n        },\r\n\r\n        // 恢复原始数据 (可在控制台调用)\r\n        restoreOriginalData() {\r\n            if (this.originalTableList.length > 0) {\r\n                this.tableList = [...this.originalTableList];\r\n                this.originalTableList = [];\r\n                this.testDataCounter = 0;\r\n\r\n                this.$nextTick(() => {\r\n                    this.updateTableHeight();\r\n                });\r\n\r\n                Message({\r\n                    message: '已恢复原始数据',\r\n                    type: 'success'\r\n                });\r\n            }\r\n        }\r\n\r\n        // === 测试方法结束 ===\r\n    }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.buildpalletsStart {\r\n    .InventorySearchBox {\r\n        margin-bottom: 0px;\r\n    }\r\n    .tablebox {\r\n        margin-top: 10px;\r\n    }\r\n    .tableboxheightall {\r\n        overflow-y: auto;\r\n        max-height: 87%;\r\n    }\r\n    .searchtipbox {\r\n        margin: 0 5px;\r\n        background: #90ffa2;\r\n        height: 30px;\r\n        padding: 0 2vh;\r\n        display: flex;\r\n        margin-bottom: 0.5vh;\r\n        align-items: center;\r\n        justify-content: center;\r\n        color: black;\r\n        font-size: 16px;\r\n    }\r\n\r\n    .expandbox {\r\n        background: #f5f5f5;\r\n        padding: 10px;\r\n    }\r\n    .el-tabs--border-card {\r\n        border: 0;\r\n        box-shadow: none;\r\n    }\r\n\r\n    // 可用库存表格容器自适应样式\r\n    .available-inventory-container {\r\n        min-height: 200px;\r\n        max-height: 450px;\r\n        height: auto !important;\r\n        transition: height 0.3s ease;\r\n\r\n        // 动态表格样式 - 强制覆盖Element UI默认样式\r\n        .dynamic-table {\r\n            transition: height 0.3s ease !important;\r\n            width: 100% !important;\r\n\r\n            // 强制设置表格高度\r\n            &.el-table {\r\n                height: var(--table-height, 200px) !important;\r\n                max-height: var(--table-height, 200px) !important;\r\n                min-height: var(--table-height, 200px) !important;\r\n            }\r\n\r\n            // 确保表格内容区域也使用动态高度\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--table-height, 200px) - 40px) !important;\r\n                overflow-y: auto !important;\r\n\r\n                // 自定义滚动条样式\r\n                &::-webkit-scrollbar {\r\n                    width: 6px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-track {\r\n                    background: #f1f1f1;\r\n                    border-radius: 3px;\r\n                }\r\n\r\n                &::-webkit-scrollbar-thumb {\r\n                    background: #c1c1c1;\r\n                    border-radius: 3px;\r\n\r\n                    &:hover {\r\n                        background: #a8a8a8;\r\n                    }\r\n                }\r\n            }\r\n\r\n            // 表头固定高度\r\n            .el-table__header-wrapper {\r\n                height: 40px !important;\r\n                min-height: 40px !important;\r\n                max-height: 40px !important;\r\n            }\r\n\r\n            // 固定列样式\r\n            .el-table__fixed,\r\n            .el-table__fixed-right {\r\n                height: var(--table-height, 200px) !important;\r\n            }\r\n        }\r\n\r\n        // 响应式设计\r\n        @media (max-height: 768px) {\r\n            max-height: 300px;\r\n        }\r\n\r\n        @media (max-height: 600px) {\r\n            max-height: 250px;\r\n        }\r\n    }\r\n\r\n    // 全局强制样式 - 确保表格高度生效\r\n    .el-table {\r\n        &.dynamic-table {\r\n            height: var(--dynamic-table-height, 200px) !important;\r\n            max-height: var(--dynamic-table-height, 200px) !important;\r\n\r\n            .el-table__body-wrapper {\r\n                max-height: calc(var(--dynamic-table-height, 200px) - 40px) !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    /* 测试按钮样式 - 测试完成后删除 */\r\n    .test-buttons {\r\n        .el-button {\r\n            margin-left: 3px !important;\r\n            font-size: 11px;\r\n            padding: 5px 8px;\r\n        }\r\n    }\r\n\r\n    /* 确保按钮容器可见 */\r\n    .searchbox {\r\n        position: relative;\r\n\r\n        > div:last-child {\r\n            z-index: 100 !important;\r\n            background: rgba(255, 255, 255, 0.95);\r\n            border-radius: 4px;\r\n            padding: 5px;\r\n        }\r\n    }\r\n}\r\n</style>\r\n"]}]}