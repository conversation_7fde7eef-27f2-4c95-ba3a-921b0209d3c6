<template>
    <el-dialog :title="$t('GLOBAL._PCGDCF')" :visible.sync="dialogVisible" width="1200px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

          <el-col :lg="8">
            <el-form-item label="产线" prop="LineCode">
              <div>
                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="物料" prop="MaterialCode">
              <div>
                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="计划数量" prop="PlanQuantity">
              <el-input v-model="dialogForm.PlanQuantity" placeholder="请输计划数量" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="单位" prop="Unit">
              <el-input v-model="dialogForm.Unit" placeholder="请输入单位" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="开始工作日" prop="StartWorkday">
              <el-input v-model="dialogForm.StartWorkday" placeholder="请输入开始工作日" disabled />
            </el-form-item>
          </el-col>
          
          <el-col :lg="8">
            <el-form-item label="结束工作日" prop="FinishWorkday">
              <el-input v-model="dialogForm.FinishWorkday" placeholder="请输入结束工作日" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="首批时长" prop="FirstLotPeriod">
              <el-input v-model="dialogForm.FirstLotPeriod" placeholder="请输入首批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="中间批时长" prop="MiddleLotPeriod">
              <el-input v-model="dialogForm.MiddleLotPeriod" placeholder="请输入中间批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="末批时长" prop="LastLotPeriod">
              <el-input v-model="dialogForm.LastLotPeriod" placeholder="请输入末批时长" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="配方版本" prop="BomVersion">
              <el-input v-model="dialogForm.BomVersion" placeholder="请输入配方版本" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="每批数量" prop="LotQuantity">
              <el-input v-model="dialogForm.LotQuantity" placeholder="请输入每批数量" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="批次总数" prop="LotCount">
              <el-input v-model="dialogForm.LotCount" placeholder="请输入批次总数" disabled />
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="对应关系" prop="StandardLotType">
              <el-select v-model="dialogForm.StandardLotType" placeholder="请选择对应关系" clearable style="width:100%">
                <el-option v-for="item in standardPeriodTypeOptions" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :lg="8">
            <el-form-item label="从第几批返工" prop="ReworkLot">
              <el-input v-model="dialogForm.ReworkLot" placeholder="请输入从第几批返工" />
            </el-form-item>
          </el-col>

          <!-- <el-col :lg="24">
            <el-form-item label="" > -->
              <el-table class="mt-3"
                :height="200"
                border
                :data="tableData"
                style="width: 100%">
                <el-table-column prop="operation" width="100" :label="$t('GLOBAL._ACTIONS')" align="center">
                  <template slot-scope="scope">
                    <div class="combination">
                      <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._TD') }}</el-button>
                    </div>                        
                  </template>
                </el-table-column>
                <el-table-column v-for="(item) in tableName"
                                :default-sort="{prop: 'date', order: 'descending'}"
                                :key="item.ID"
                                :prop="item.field"
                                :label="item.label"
                                :width="item.width"
                                :align="item.alignType"
                                sortable
                                show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    {{ scope.row[item.field] }}
                  </template>
                </el-table-column>
              </el-table>
            <!-- </el-form-item>
          </el-col> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">拆分</el-button>
      </div>      
      <BomDetailForm @saveForm="setBomRowData" ref="formDialog" />
    </el-dialog>
  </template>

<script>
  import {
    //getLineList,
    getSplitBatchInfo,
    splitBatch
  } from "@/api/planManagement/weekSchedule";
  import BomDetailForm from './bomDetailForm'
  import {getTableHead} from "@/util/dataDictionary.js";
  export default {
    components:{
      BomDetailForm
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        // factoryOptions: [],
        // workshopOptions: [],
        // lineOptions: [],        
        // categoryOptions: [],
        // shiftOptions: [],
        // typeOptions: [],
        standardPeriodTypeOptions: [],
        tableName : [],
        hansObjDrawer: this.$t('WeekFormulation.bomDetail'),
        tableOption: [
          {code: 'SegmentName', width: 100, align: 'center'},  
          {code: 'Sort', width: 80, align: 'center'},
          {code: 'MaterialCode', width: 150, align: 'left'},
          {code: 'MaterialName', width: 180, align: 'left'},
          {code: 'MaterialType', width: 100, align: 'left'},
          {code: 'InsteadMaterialCode', width: 150, align: 'left'},
          {code: 'InsteadMaterialName', width: 180, align: 'left'},
          {code: 'Unit', width: 100, align: 'center'},
          {code: 'StandardQuantity', width: 100, align: 'left'},
          {code: 'PlanQuantity', width: 100, align: 'left'},
          {code: 'Remark', width: 180, align: 'left'},
        ],
        tableData : [],
        currentRow: {},
        currentBomRow: {},
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
      //this.getLineList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType');
      },
      // async getLineList() {
      //   const { response } = await getLineList({
      //    //areaCode: 'PackingArea'
      //    areaCode: 'Formulation'
      //   })
      //   console.log(response)
      //   this.lineOptions = response
      // },
      submit() {
        splitBatch(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.initTableHead()
        this.$nextTick(_ => {
          if(data.ID){
            //console.log("show")
            this.getDialogInfo(data)
          }
        })
      },
      initTableHead() {
        this.tableName = getTableHead(this.hansObjDrawer, this.tableOption)
      },
      getDialogInfo(data){
        //console.log("getDialogInfo")
        getSplitBatchInfo(data).then(res => {
          this.dialogForm = res.response
          this.tableData = res.response.wsBomModels
        })
      },    
      showDialog(row) {
        this.currentBomRow = row
        this.$refs.formDialog.show(row)
      },
      async setBomRowData(val) {
        console.log("setBomRowData")
        console.log(val)
        this.currentBomRow.MaterialId = val.MaterialId
        this.currentBomRow.MaterialCode = val.MaterialCode
        this.currentBomRow.MaterialName = val.MaterialCode
        this.$forceUpdate()
        // const { response } = await getWeekScheduleBomList({
        //   WeekScheduleId: this.currentRow.ID,
        //   ...this.searchForm
        // })
        // this.tableData = response
      },
    }
  }
  </script>