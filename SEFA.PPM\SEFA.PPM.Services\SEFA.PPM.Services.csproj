﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<OutputPath>..\SEFA.PPM.Api\bin\Debug\</OutputPath>
	</PropertyGroup>
	
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<OutputPath>..\SEFA.PPM\bin\Release\</OutputPath>
	</PropertyGroup>
	
	<ItemGroup>
	  <Compile Remove="Helper\InfluxDBClient.cs" />
	  <Compile Remove="Helper\PLCHeartbeat.cs" />
	  <Compile Remove="Interface\AndonServices - 副本.cs" />
	  <Compile Remove="Interface\AndonServices_prod.cs" />
	  <Compile Remove="Interface\AndonServices_test.cs" />
	  <Compile Remove="Interface\Helper\InfluxDBClient.cs" />
	  <Compile Remove="Interface\SapPackOrderGetService_20241206.cs" />
	  <Compile Remove="Interface\SapPackOrderGetService_20241208.cs" />
	  <Compile Remove="Interface\SapPackOrderGetService_20241209.cs" />
	  <Compile Remove="Interface\SapPackOrderGetService_202412091.cs" />
	  <Compile Remove="Interface\SapPackOrderGetService_20241211.cs" />
	  <Compile Remove="PPM\View\BProductionOrderListViewServices - 复制.cs" />
	</ItemGroup>
	
	<ItemGroup>
	  <None Remove="MM_STOCK_QUERY.wsdl" />
	</ItemGroup>
	
	<ItemGroup>
	  <PackageReference Include="EasyModbusTCP.NETCore" Version="1.0.0" />
	  <PackageReference Include="InfluxDB.Client" Version="4.0.0" />
	  <PackageReference Include="InfluxDB.Client.Linq" Version="4.0.0" />
	  <PackageReference Include="MiniExcel" Version="1.33.0" />
	  <PackageReference Include="NPOI" Version="2.7.0" />
	  <PackageReference Include="System.ServiceModel.Duplex" Version="4.10.*" />
	  <PackageReference Include="System.ServiceModel.Federation" Version="4.10.*" />
	  <PackageReference Include="System.ServiceModel.Http" Version="4.10.*" />
	  <PackageReference Include="System.ServiceModel.NetTcp" Version="4.10.*" />
	  <PackageReference Include="System.ServiceModel.Security" Version="4.10.*" />
	</ItemGroup>
	 

	<ItemGroup>
		<ProjectReference Include="..\SEFA.PPM.IServices\SEFA.PPM.IServices.csproj" />
		<ProjectReference Include="..\SEFA.PPM.Repository\SEFA.PPM.Repository.csproj" />
	</ItemGroup>
	 

	<ItemGroup>
	  <Reference Include="LKK.Lib.Core">
	    <HintPath>..\..\common\LKK.Lib.Core.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Common">
	    <HintPath>..\..\common\SEFA.Base.Common.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.ESB">
	    <HintPath>..\..\common\SEFA.Base.ESB.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Extensions">
	    <HintPath>..\..\common\SEFA.Base.Extensions.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.InfluxDB">
	    <HintPath>..\..\common\SEFA.Base.InfluxDB.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.IServices">
	    <HintPath>..\..\common\SEFA.Base.IServices.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Model">
	    <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Repository">
	    <HintPath>..\..\common\SEFA.Base.Repository.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Services">
	    <HintPath>..\..\common\SEFA.Base.Services.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Model">
	    <HintPath>..\..\common\SEFA.DFM.Model.dll</HintPath>
	  </Reference>
	</ItemGroup>
	 

	<ItemGroup>
	  <Folder Include="Report\" />
	</ItemGroup>

</Project>
