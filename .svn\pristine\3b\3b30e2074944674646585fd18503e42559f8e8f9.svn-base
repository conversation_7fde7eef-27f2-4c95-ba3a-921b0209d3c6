﻿using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Model.Models.PTM;
using SEFA.PPM.Model.ViewModels.PTM;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.View;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.Models;
using Abp.Domain.Uow;
using SEFA.Base.Common.HttpRestSharp;
using SEFA.DFM.Model.Models;
using SEFA.PPM.IServices;
using SqlSugar;
using System.Linq;
using System;

namespace SEFA.MKM.IServices
{
    /// <summary>
    /// IMaterialPreparationViewServices
    /// </summary>	
    public interface IMaterialPreparationViewServices : IBaseServices<MaterialPreparationViewEntity>
    {

        #region 拼锅PDA

        /// <summary>
        /// 根据条码查询对应的容器编码是否存在
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        Task<MessageModel<string>> GetContainerPDA(string sscc);

        
        Task<PageModel<MaterialPreparationViewEntity>> GetPGTopPDA(MaterialPreparationViewRequestModel reqModel);

        /// <summary>
        /// 根据批次ID和eqpmentID获取数据源（底部数据源）（物料 ：MCode +MName  数量：MQuantity  /MQuantityTotal MQuantityunit 批次产量： returnData[0].Total）
        /// </summary>
        /// <param name="batchID">批次id</param>
        /// <param name="eqpmentID">拼锅返回数据</param>
        /// <returns></returns>
        Task<List<BBatchDetailIiViewEntityModel>> GetListByBatchIDPDA(string batchID, string eqpmentID);

        /// <summary>
        /// 判断当前数据所处位置（托盘/备料小标签+ invent.SubLotId;"/可用库存++ invent.SubLotId;）分号
        /// </summary>
        /// <param name="cName">容器名称</param>
        /// <param name="sscc">扫码的二维码</param>
        /// <param name="batchID">批次ID</param>
        /// <param name="proID">工单ID</param>
        /// <param name="equpmentID">ROOMID</param>
        /// <returns></returns>
        Task<MessageModel<string>> GetCodeTypeByPDA(string cName, string sscc, string batchID, string proID, string equpmentID);

        /// <summary>  
        /// 用来查询可用库存的数据(实体)：sscc:  SSCC 追溯码 Bag 包数  Size 单包重量  MUnit 单位 TargetWeight 目标重量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<InVentPDAModel> GetInventPDA(BBatchDetailIIModel model);

        /// <summary>
        /// 工单库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        Task<MessageModel<string>> MPreparationTransfer_FullAmountPDA(FullAmountModel reqModel);

        /// <summary>
        /// 拼锅-转移-选可用库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        Task<MessageModel<string>> MPreparationTransfer_FullBagPDA(FullBagModel reqModel);

        #endregion
        Task<List<SelectDataModel>> Get_SelectPro(MaterialPreparationViewRequestModel reqModel);
        Task<List<SelectDataModel>> Get_SelectMaterial(MaterialPreparationViewRequestModel reqModel);
        Task<PageModel<MaterialPreparationViewEntity>> Get_CLBLNew(MaterialPreparationViewRequestModel reqModel);
        Task<PageModel<MaterialPreparationViewEntity>> Get_YLLabelNew(MaterialPreparationViewRequestModel reqModel);

        Task<List<MEquipmentroomViewEntity>> GetRoomSelectList();

        Task<List<MEquipmentroomViewEntity>> GetRoomSelectListCLBL();
        Task<List<MEquipmentroomViewEntity>> GetRoomSelectListByName(string name);
        Task<PageModel<MaterialPreparationViewEntity>> GetPageList(MaterialPreparationViewRequestModel reqModel);
        Task<PageModel<MaterialPreparationViewEntity>> GetPageList_CLBL(MaterialPreparationViewRequestModel reqModel);

        Task<List<MaterialPreparationViewEntity>> GetList(MaterialPreparationViewRequestModel reqModel);

        Task<bool> SaveForm(MaterialPreparationViewEntity entity);

        Task<List<BBatchDetailViewEntity>> GetByProOrderID(string proOrderId);

        Task<List<BBatchDetailViewEntity>> GetByList(string proOrderId, string eqpmentid);

        Task<List<BclblDetailViewEntity>> GetByList_CLBL(string proOrderId, string eqpmentid);
        Task<List<BBatchDetailIiViewEntity>> GetListByBatchID(string batchID, string eqpmentID);
        Task<bool> IsFinish(string batchID, string eqpmentID);

        Task<List<BBdetailIiViewEntity>> GetListByBatchIDNew(string batchID);

        Task<List<ClblDiiViewEntity>> GetListByBatchID_CLBL(string batchID, string eqpmentID);
        
        Task<List<ClblDiiViewEntity>> GetPrepareationListByBatchID(string batchID, string eqpmentID);
        
        Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByBatchIDS(BBatchDetailIIModel model);
        Task<PageModel<ClblDiiViewEntity>> GetPageListByBatchIDS_CLBL(BBatchDetailIIModel model);

        Task<PageModel<InventObj>> GetInventState(BBatchDetailIIModel model);

        Task<PageModel<ClblDiiViewEntity>> GetPageListByBatchIDS_CLBLBYID(BBatchDetailIIModel model);
        Task<PageModel<BBdetailIiViewEntity>> GetPageListByBatchIDSII(BBatchDetailIIModel model);
        Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByMaterial(BBatchDModel model);
        Task<PageModel<ClblDiiViewEntity>> GetPageListByMaterial_CLBL(BBatchDModel model);
        Task<PageModel<BBdetailIiViewEntity>> GetPageListByMaterialII(BBatchDModel model);
        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListByBatchDetailMaterial(BBatchDetailMaterialViewRequestModel reqModel, bool isContainer, bool isHType);
        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListByBatchDetailMaterialPG(BBatchDetailMaterialViewRequestModel reqModel, bool isContainer, bool isHType);
        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial(BBatchDetailMaterialViewRequestModel reqModel, bool BatchId, bool ProOrderid, bool isType);

        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewMaterial(BBatchDetailMaterialViewRequestModel reqModel, bool ProOrderid, bool Batchid, bool isType);

        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial_CLBL(BBatchDetailMaterialViewRequestModel reqModel, bool BatchId, bool ProOrderid, bool isType);

        Task<PageModel<BBatchDetailMaterialViewEntity>> GetPageListNewByBatchDetailMaterial_CLBLTop(BBatchDetailMaterialViewRequestModel reqModel, bool BatchId, bool ProOrderid, bool isType);
        //Task<bool> AddPallet(int tareWeight, string uomID, string proBatchID, string proRequestID, string equipMentID, string materialId, string lotID, string subLotID);

        Task<MessageModel<string>> AddPallet(MPPallentAddModel plateAddModel);

        Task<List<ContainerEntity>> GetContainerBatchPalletList(string batchID, string stateClass);

        Task<MessageModel<string>> MPreparationTransfer_FullBag(FullBagModel reqModel);

        Task<List<DicMaterialPreselectViewEntity>> GetMarialPreSelect();

        Task<MessageModel<string>> MPreparationTransfer_PartialBag(PartialBagModel reqModel);

        Task<List<DicBatchPalletselectViewEntity>> GetContainerSelect(string batchID);

        Task<PageModel<BBatchDetailMcontainerViewEntity>> GetPageBatchDetailList(BBatchDetailMcontainerViewRequestModel reqModel);

        Task<decimal> GetContainerSelectQuantity(string batchID);

        Task<MessageModel<string>> PrintMINLable(MergeModel reqModel);
        Task<MessageModel<string>> MPreparationTransfer_Merge(MergeModel reqModel);

        Task<MessageModel<string>> MPreparationTransfer_FullAmount(FullAmountModel reqModel);

        Task<MessageModel<string>> MPreparationTransfer_NewFullAmount(FullAmountModel reqModel);
        Task<MessageModel<string>> MPreparationTransfer_Remove(RemoveBagModel reqModel);

        Task<MessageModel<string>> MPreparationNewTransfer_Remove(RemoveBagModel reqModel);

        Task<MessageModel<string>> MPreparationTransfer_CompletePallet(CompletePalletModel reqModel);
        Task<MessageModel<string>> MPreparationTransfer_OpenPallet(OpenPalletModel reqModel);

        Task<MessageModel<string>> MPreparationTransfer_DeletePallet(DeletePalletModel model);
        Task<MessageModel<string>> ReprintSavePG(MPPallentAddModel plateAddModel);
        Task<MessageModel<string>> ReprintSave(MPPallentAddModel plateAddModel);
        Task<List<BBatchDetailbymaterialViewEntity>> GetByProOrderID_NewMaterial(string[] proOrderId);

        Task<List<BBatchDetailIiViewEntity>> GetList_NewByMaterialID(string materialID);

        Task<List<MBatchriiViewEntity>> GetSegmentList(MBatchriiViewRequestModel reqModel);

        Task<List<MBatchriiViewEntity>> GetSegmentList_CLBL(MBatchriiViewRequestModel reqModel);

        Task<List<ContainerEntity>> GetContainerSelectList(string proOrderID, string batchID);

        Task<List<MContainerViewEntity>> GetConSelectList(string proOrderID, string batchID);
        //Task<MPreparationViewEntity> GetMPreparationII(PreparationIIModel reqModel);
        Task<PageModel<MPreparationViewEntity>> GetMPreparationII(PreparationIIModel model);

        Task<PageModel<MPreparationViewEntity>> GetMPreparationII_CLBL(PreparationIIModel model);

        Task<PageModel<ViewEntity>> GetMateriaConsumlList(BBatchDetailMaterialViewRequestModel reqModel);

        Task<PageModel<ViewEntity>> GetMateriaConsumlListPG(BBatchDetailMaterialViewRequestModel reqModel);

    }
}