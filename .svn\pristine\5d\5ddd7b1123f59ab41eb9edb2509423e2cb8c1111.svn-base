﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///标准时间批量
    ///</summary>
    
    [SugarTable("PPM_M_STANDARD_PERIOD_LOT")] 
    public class StandardPeriodLotEntity : EntityBase
    {
        public StandardPeriodLotEntity()
        {
        }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_CODE")]
        public string LineCode { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_NAME")]
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VER")]
        public string MaterialVer { get; set; }
           /// <summary>
           /// Desc:对应关系
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:第一批用时
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FIRST_LOT_PERIOD")]
        public decimal? FirstLotPeriod { get; set; }
           /// <summary>
           /// Desc:中间批用时
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MIDDLE_LOT_PERIOD")]
        public decimal? MiddleLotPeriod { get; set; }
           /// <summary>
           /// Desc:最后一批用时
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="LAST_LOT_PERIOD")]
        public decimal? LastLotPeriod { get; set; }
           /// <summary>
           /// Desc:批次量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLAN_QUANTITY")]
        public decimal? PlanQuantity { get; set; }
           /// <summary>
           /// Desc:最小成批量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MIN_LOT_QUANTITY")]
        public decimal? MinLotQuantity { get; set; }
           /// <summary>
           /// Desc:最大成批量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="MAX_LOT_QUANTITY")]
        public decimal? MaxLotQuantity { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}