<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格高度调试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .control-panel {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .table-container {
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .available-inventory-container {
            min-height: 200px;
            max-height: 450px;
            height: auto !important;
            transition: height 0.3s ease;
        }
        .dynamic-height-table {
            height: var(--table-height, 200px) !important;
            transition: height 0.3s ease;
            width: 100%;
        }
        .dynamic-height-table .el-table__body-wrapper {
            max-height: calc(var(--table-height, 200px) - 40px) !important;
            overflow-y: auto;
        }
        .dynamic-height-table .el-table__header-wrapper {
            height: 40px;
        }
        .status-info {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 5px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>表格高度调试工具</h1>
            
            <div class="control-panel">
                <h3>控制面板</h3>
                <el-row :gutter="20">
                    <el-col :span="4">
                        <el-button 
                            :type="useViewportHeight ? 'primary' : 'default'"
                            @click="toggleMode()">
                            {{ useViewportHeight ? '视口模式' : '行数模式' }}
                        </el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-button @click="addData()">添加数据</el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-button @click="removeData()">删除数据</el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-button @click="setHeight(300)">设置300px</el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-button @click="setHeight(500)">设置500px</el-button>
                    </el-col>
                    <el-col :span="4">
                        <el-button @click="debugInfo()">调试信息</el-button>
                    </el-col>
                </el-row>
            </div>

            <div class="table-container">
                <h3>表格测试 (当前高度: {{ tableHeight }}px)</h3>
                <div class="available-inventory-container" :style="{ '--table-height': tableHeight + 'px' }">
                    <el-table 
                        :data="tableList" 
                        class="dynamic-height-table"
                        :key="tableKey"
                        border>
                        <el-table-column prop="id" label="ID" width="80"></el-table-column>
                        <el-table-column prop="name" label="名称" width="120"></el-table-column>
                        <el-table-column prop="value" label="值" width="100"></el-table-column>
                        <el-table-column prop="status" label="状态" width="80"></el-table-column>
                        <el-table-column prop="description" label="描述"></el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="status-info">
                <h3>实时状态</h3>
                <p><strong>模式:</strong> {{ useViewportHeight ? '视口高度模式' : '数据行数模式' }}</p>
                <p><strong>数据行数:</strong> {{ tableList.length }}</p>
                <p><strong>窗口高度:</strong> {{ windowHeight }}px</p>
                <p><strong>计算高度:</strong> {{ tableHeight }}px</p>
                <p><strong>CSS变量:</strong> {{ tableHeight }}px</p>
                <p><strong>表格Key:</strong> {{ tableKey }}</p>
                <p><strong>最小高度:</strong> {{ minHeight }}px</p>
                <p><strong>最大高度:</strong> {{ maxHeight }}px</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableList: [],
                    useViewportHeight: false,
                    tableHeight: 200,
                    tableKey: 0,
                    minHeight: 150,
                    maxHeight: 400,
                    rowHeight: 40,
                    windowHeight: window.innerHeight,
                    counter: 0
                };
            },
            mounted() {
                // 初始化数据
                this.generateData(3);
                this.updateHeight();

                // 监听窗口变化
                window.addEventListener('resize', () => {
                    this.windowHeight = window.innerHeight;
                    if (this.useViewportHeight) {
                        this.updateHeight();
                    }
                });
            },
            methods: {
                toggleMode() {
                    this.useViewportHeight = !this.useViewportHeight;
                    console.log('切换到:', this.useViewportHeight ? '视口模式' : '行数模式');
                    this.updateHeight();
                },
                updateHeight() {
                    let newHeight;
                    
                    if (this.tableList.length === 0) {
                        newHeight = this.minHeight;
                    } else if (this.useViewportHeight) {
                        const availableHeight = this.windowHeight * 0.25;
                        newHeight = Math.min(Math.max(availableHeight, this.minHeight), this.maxHeight);
                    } else {
                        const headerHeight = 40;
                        const dataHeight = headerHeight + (this.tableList.length * this.rowHeight);
                        newHeight = Math.min(Math.max(dataHeight, this.minHeight), this.maxHeight);
                    }
                    
                    console.log('更新高度:', this.tableHeight, '->', newHeight);
                    this.tableHeight = newHeight;
                    this.tableKey++;
                },
                generateData(count) {
                    for (let i = 0; i < count; i++) {
                        this.tableList.push({
                            id: ++this.counter,
                            name: `项目${this.counter}`,
                            value: Math.floor(Math.random() * 1000),
                            status: ['正常', '警告', '错误'][Math.floor(Math.random() * 3)],
                            description: `这是第${this.counter}个测试项目的描述信息`
                        });
                    }
                },
                addData() {
                    this.generateData(1);
                    this.updateHeight();
                },
                removeData() {
                    if (this.tableList.length > 0) {
                        this.tableList.pop();
                        this.updateHeight();
                    }
                },
                setHeight(height) {
                    console.log('强制设置高度:', height);
                    this.tableHeight = height;
                    this.tableKey++;
                },
                debugInfo() {
                    console.log('=== 调试信息 ===');
                    console.log('模式:', this.useViewportHeight ? '视口模式' : '行数模式');
                    console.log('数据行数:', this.tableList.length);
                    console.log('窗口高度:', this.windowHeight);
                    console.log('表格高度:', this.tableHeight);
                    console.log('表格Key:', this.tableKey);
                    
                    const container = document.querySelector('.available-inventory-container');
                    if (container) {
                        console.log('CSS变量值:', getComputedStyle(container).getPropertyValue('--table-height'));
                    }
                    
                    const table = document.querySelector('.dynamic-height-table');
                    if (table) {
                        console.log('表格实际高度:', table.offsetHeight);
                        console.log('表格样式高度:', getComputedStyle(table).height);
                    }
                }
            }
        });
    </script>
</body>
</html>
