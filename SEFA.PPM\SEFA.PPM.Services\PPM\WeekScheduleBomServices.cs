﻿
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{
    public class WeekScheduleBomServices : BaseServices<WeekScheduleBomEntity>, IWeekScheduleBomServices
    {
        private readonly IBaseRepository<WeekScheduleBomEntity> _dal;
        public WeekScheduleBomServices(IBaseRepository<WeekScheduleBomEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<WeekScheduleBomEntity>> GetList(WeekScheduleBomRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<WeekScheduleBomEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.WeekScheduleId), (x) => x.WeekScheduleId == reqModel.WeekScheduleId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentId), (x) => x.SegmentId == reqModel.SegmentId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentMaterialId), (x) => x.SegmentMaterialId == reqModel.SegmentMaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentMaterialStepId), (x) => x.SegmentMaterialStepId == reqModel.SegmentMaterialStepId)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), (x) => x.MaterialId == reqModel.MaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), (x) => x.MaterialCode == reqModel.MaterialCode)
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<WeekScheduleBomEntity>> GetPageList(WeekScheduleBomRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<WeekScheduleBomEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.WeekScheduleId), (x) => x.WeekScheduleId == reqModel.WeekScheduleId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentId), (x) => x.SegmentId == reqModel.SegmentId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentMaterialId), (x) => x.SegmentMaterialId == reqModel.SegmentMaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.SegmentMaterialStepId), (x) => x.SegmentMaterialStepId == reqModel.SegmentMaterialStepId)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialId), (x) => x.MaterialId == reqModel.MaterialId)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), (x) => x.MaterialCode == reqModel.MaterialCode)
                .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(WeekScheduleBomEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}