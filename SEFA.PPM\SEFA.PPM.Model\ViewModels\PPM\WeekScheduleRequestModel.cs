﻿using SEFA.Base.Model;
using System;
using System.Collections.Generic;

namespace SEFA.PPM.Model.ViewModels
{
    public class WeekScheduleRequestModel : RequestPageModelBase
    {
        public WeekScheduleRequestModel()
        {
        }

        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:工厂
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Factory { get; set; }
        /// <summary>
        /// Desc:车间
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Workshop { get; set; }
        /// <summary>
        /// Desc:产线ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineId { get; set; }
        /// <summary>
        /// Desc:产线代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:计划分类
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Category { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVer { get; set; }
        /// <summary>
        /// Desc:包装规格ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string PackSizeId { get; set; }
        /// <summary>
        /// Desc:包装规格
        /// Default:
        /// Nullable:True
        /// </summary>
        public string PackSize { get; set; }
        /// <summary>
        /// Desc:设计代码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string DesignCode { get; set; }
        /// <summary>
        /// Desc:配方ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string DesignId { get; set; }
        /// <summary>
        /// Desc:配方版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string DesignVer { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Unit { get; set; }
        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:输出
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Output { get; set; }
        /// <summary>
        /// Desc:开始工作日
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? StartWorkday { get; set; }
        /// <summary>
        /// Desc:开始工作日历ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StartCalendarId { get; set; }
        /// <summary>
        /// Desc:开始班次
        /// Default:
        /// Nullable:False
        /// </summary>
        public string StartShift { get; set; }
        /// <summary>
        /// Desc:开始班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StartShiftId { get; set; }
        /// <summary>
        /// Desc:结束工作日
        /// Default:
        /// Nullable:False
        /// </summary>
        public DateTime? FinishWorkday { get; set; }
        /// <summary>
        /// Desc:结束工作日历ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string FinishCalendarId { get; set; }
        /// <summary>
        /// Desc:结束班次
        /// Default:
        /// Nullable:False
        /// </summary>
        public string FinishShift { get; set; }
        /// <summary>
        /// Desc:结束班次ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string FinishShiftId { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? PlanQuantity { get; set; }
        /// <summary>
        /// Desc:返工物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReworkMaterialId { get; set; }
        /// <summary>
        /// Desc:返工物料代码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReworkMaterialCode { get; set; }
        /// <summary>
        /// Desc:返工物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReworkMaterialName { get; set; }
        /// <summary>
        /// Desc:返工物料版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ReworkMaterialVer { get; set; }
        /// <summary>
        /// Desc:返工物料数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ReworkQuantity { get; set; }
        /// <summary>
        /// Desc:计划编号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string OrderNo { get; set; }
        /// <summary>
        /// Desc:计划类型
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:计划状态
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// Desc:SAP工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapOrderNo { get; set; }
        /// <summary>
        /// Desc:SAP标识
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapFlag { get; set; }
        /// <summary>
        /// Desc:SAP状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SapStatus { get; set; }
        /// <summary>
        /// Desc:SAP报工数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? SapFeedbackQuantity { get; set; }
        /// <summary>
        /// Desc:工作中心
        /// Default:
        /// Nullable:True
        /// </summary>
        public string WorkCenter { get; set; }
        /// <summary>
        /// Desc:实际开始工作日
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? ActualStartWorkday { get; set; }
        /// <summary>
        /// Desc:实际开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? ActualStartTime { get; set; }
        /// <summary>
        /// Desc:实际开始班次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ActualStartShift { get; set; }
        /// <summary>
        /// Desc:实际结束工作日
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? ActualFinishWorkday { get; set; }
        /// <summary>
        /// Desc:实际结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? ActualFinishTime { get; set; }
        /// <summary>
        /// Desc:实际结束班次
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ActualFinishShift { get; set; }
        /// <summary>
        /// Desc:实际数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ActualQuantity { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// Desc:标准批量类型
        /// Default:
        /// Nullable:True
        /// </summary>
        public string StandardLotType { get; set; }
        /// <summary>
        /// Desc:首批次时长
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? FirstLotPeriod { get; set; }
        /// <summary>
        /// Desc:中间批次时长
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? MiddleLotPeriod { get; set; }
        /// <summary>
        /// Desc:末批次时长
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? LastLotPeriod { get; set; }
        /// <summary>
        /// Desc:每批数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? LotQuantity { get; set; }
        /// <summary>
        /// Desc:批次数量
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? LotCount { get; set; }
        /// <summary>
        /// Desc:Bom版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BomVersion { get; set; }
        /// <summary>
        /// Desc:返工批次
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? ReworkLot { get; set; }

        /// <summary>
        /// Desc:最小成批量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? MinLotQuantity { get; set; }
        /// <summary>
        /// Desc:最大成批量
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? MaxLotQuantity { get; set; }

        public List<WeekScheduleBomRequestModel> wsBomModels = new List<WeekScheduleBomRequestModel>();
    }
}