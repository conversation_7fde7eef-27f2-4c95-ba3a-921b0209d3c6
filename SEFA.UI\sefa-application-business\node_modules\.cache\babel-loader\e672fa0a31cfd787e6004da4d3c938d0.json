{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\index.vue", "mtime": 1749177894465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAmFA;AACA;AACA;AACA;AACA;AAEA;EACAA,aADA;EAEAC;IACAC,UADA;IAEAC;EAFA,CAFA;;EAMAC;IACA;MACAC;QACAC,YADA;QAEAC;MAFA,CADA;MAKAC,QALA;MAMAC,eANA;MAOAC,sCAPA;MAQAC,aARA;MASAC,cATA;MAUAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,EAYA;QAAAF;QAAAC;QAAAC;MAAA,CAZA,EAaA;QAAAF;QAAAC;QAAAC;MAAA,CAbA,EAcA;QAAAF;QAAAC;QAAAC;QAAAC;MAAA,CAdA,EAeA;QAAAH;QAAAC;QAAAC;MAAA,CAfA,EAgBA;QAAAF;QAAAC;QAAAC;MAAA,CAhBA,EAiBA;QAAAF;QAAAC;QAAAC;MAAA,CAjBA,EAkBA;QAAAF;QAAAC;QAAAC;MAAA,CAlBA,EAmBA;QAAAF;QAAAC;QAAAC;MAAA,CAnBA,EAoBA;QAAAF;QAAAC;QAAAC;MAAA,CApBA,EAqBA;QAAAF;QAAAC;QAAAC;MAAA,CArBA,CAVA;MAiCAE,QAjCA;MAkCAC;QACAnB,aADA;QAEAoB,sBAFA;QAGAC,yCAHA;QAGA;QACA;QACAC,iDALA,CAKA;;MALA;IAlCA;EA0CA,CAjDA;;EAkDAC;IACA;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CA3DA;;EA4DAC;IACAC;MACA;IACA,CAHA;;IAIAC;MACA;IACA,CANA;;IAOAC;MACA;MACA;IACA,CAVA;;IAWAC;MACA;MACA;IACA,CAdA;;IAeAC;MACA;MACA;IACA,CAlBA;;IAmBAC;MACA;IACA,CArBA;;IAsBAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CApCA;;IAsCAC;MACAC;QACA;QACA;QACA;MACA,CAJA;IAKA;;EA5CA;AA5DA", "names": ["name", "components", "FormDialog", "POList", "data", "searchForm", "pageIndex", "pageSize", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "loading", "tableOption", "code", "width", "align", "format", "mainH", "buttonOption", "serveIp", "uploadUrl", "DownLoadUrl", "mounted", "window", "methods", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "poListDialog", "delRow", "title", "message", "confirmText", "cancelText", "then", "delWeekSchedule", "catch", "err", "console", "getTableData", "getWeekScheduleList"], "sourceRoot": "src/views/planManagement/weekFormulation", "sources": ["index.vue"], "sourcesContent": ["<!--\n * @Descripttion: (配置工单批次管理)\n * @version: (1.0)\n * @Author: (SECI)\n * @Date: (2025-05-22)\n * @LastEditors: (SECI)\n * @LastEditTime: (2025-05-22)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n        <el-form-item label=\"计划编号\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.OrderNo\" placeholder=\"请输入计划编号\" />\n        </el-form-item>\n        <el-form-item label=\"产品名称\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.MaterialName\" placeholder=\"请输入产品名称\" />\n        </el-form-item>\n        <el-form-item label=\"产线\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.lineCode\" placeholder=\"请输入产线\" />\n        </el-form-item>\n        <el-form-item label=\"计划时间\" class=\"mb-2\">\n          <el-date-picker v-model=\"searchForm.StartWorkday\" type=\"datetime\" placeholder=\"选择开始时间\"></el-date-picker>\n          ~\n          <el-date-picker v-model=\"searchForm.FinishWorkday\" type=\"datetime\" placeholder=\"选择结束时间\"></el-date-picker>\n        </el-form-item>\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        \n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <div class=\"combination\">\n              <i class=\"el-icon-document\" @click=\"poListDialog(scope.row)\"></i>&nbsp;&nbsp;\n              <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._PCGDCF') }}</el-button>\n            </div>                        \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n    <POList ref=\"poList\" />\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport FormDialog from './form-dialog'\nimport POList from './poListDetail.vue'\nimport {delWeekSchedule, getWeekScheduleList} from \"@/api/planManagement/weekSchedule\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\nexport default {\n  name: 'index',\n  components: {\n    FormDialog,\n    POList\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('WeekSchedule.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n        {code: 'Factory', width: 100, align: 'center'},\n        {code: 'Workshop', width: 150, align: 'center'},\n        {code: 'Category', width: 150, align: 'center'},\n        {code: 'DesignCode', width: 150, align: 'center'},       \n        {code: 'LineCode', width: 150, align: 'center'},       \n        {code: 'MaterialCode', width: 100, align: 'center'},\n        {code: 'MaterialName', width: 180, align: 'center'},\n        {code: 'OrderNo', width: 180, align: 'center'},\n        {code: 'Output', width: 100, align: 'center'},\n        {code: 'PackSize', width: 150, align: 'center'},\n        {code: 'PlanQuantity', width: 100, align: 'center'},\n        {code: 'Remark', width: 150, align: 'center'},\n        {code: 'SapOrderNo', width: 150, align: 'center'},\n        {code: 'StartWorkday', width: 150, align: 'center', format: 'yyyy-MM-dd'},\n        {code: 'StartShift', width: 100, align: 'center'},\n        {code: 'FinishWorkday', width: 180, align: 'center'},\n        {code: 'FinishShift', width: 100, align: 'center'},\n        {code: 'Status', width: 100, align: 'center'},\n        {code: 'Type', width: 100, align: 'center'},\n        {code: 'Unit', width: 100, align: 'center'},\n        {code: 'WorkCenter', width: 100, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'配置周计划',\n        serveIp:'baseURL_PPM',\n        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入\n        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出\n        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      this.tableName = getTableHead(this.hansObj, this.tableOption)\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n    poListDialog(row) {\n      this.$refs.poList.show(row)\n    },\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delWeekSchedule([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getWeekScheduleList(this.searchForm).then(res => {\n        //console.log(res);\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}