<!--
 * @Descripttion: (批次DCS下发/PPM_B_BATCH_DCS)
 * @version: (1.0)
 * @Author: (admin)
 * @Date: (2025-05-14)
 * @LastEditors: (admin)
 * @LastEditTime: (2025-05-14)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
      
      <el-form-item label="产线" prop="LineName">
        <el-input v-model="searchForm.LineName" placeholder="请输入产线名称" />
      </el-form-item>

      <el-form-item label="工单号" prop="PoNo">
        <el-input v-model="searchForm.PoNo" placeholder="请输入工单号" />
      </el-form-item>
    				    
      <el-form-item label="批次号" prop="BatchtNo">
        <el-input v-model="searchForm.BatchtNo" placeholder="请输入批次号" />
      </el-form-item>
    				    
      <el-form-item label="物料代码" prop="MaterialCode">
        <el-input v-model="searchForm.MaterialCode" placeholder="请输入物料代码" />
      </el-form-item>

        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>
          
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20, 50, 100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import FormDialog from './form-dialog'
import {
    delBatchDcs, getBatchDcsList
} from "@/api/planManagement/batchDcs";
import {getTableHead} from "@/util/dataDictionary.js";

//import { batchDcsColumn } from '@/columns/planManagement/batchDcs.js';


export default {
  name: 'index.vue',
  components: {
    //UploadButton,
    FormDialog,
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('BatchDcs.table'),
      tableName: [],
      loading: false,
      tableOption: [
      {code: 'LineName', width: 130, align: 'center'},
      {code: 'PoNo', width: 180, align: 'center'},
      {code: 'BatchtNo', width: 130, align: 'center'},
      {code: 'MaterialCode', width: 180, align: 'center'},
      {code: 'MaterialName', width: 180, align: 'center'},
      {code: 'Unit', width: 130, align: 'center'},
      {code: 'StandardQuantity', width: 130, align: 'center'},
      {code: 'PlanQuantity', width: 130, align: 'center'},
      {code: 'Status', width: 130, align: 'center'},
      {code: 'SendData', width: 200, align: 'center'},
      {code: 'ResponseData', width: 200, align: 'center'},
      ],
      mainH: 0,
      buttonOption:{
        name:'批次DCS下发',
        serveIp:'baseURL_PPM',
        // uploadUrl:'/api/BatchDcs/ImportData', //导入
        // exportUrl:'/api/BatchDcs/ExportData', //导出
        // DownLoadUrl:'/api/BatchDcs/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      for (let key in this.hansObj) {
        this.tableName = getTableHead(this.hansObj, this.tableOption)
      }
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },

    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delBatchDcs([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData(data) {
      getBatchDcsList(this.searchForm).then(res => {
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}

//<!-- 移到到src/local/en.json和zh-Hans.json -->
//"BatchDcs": {
//    "table": {
//        "poNo": "poNo",
//        "batchtNo": "batchtNo",
//        "materialCode": "materialCode",
//        "unit": "unit",
//        "standardQuantity": "standardQuantity",
//        "planQuantity": "planQuantity",
//        "status": "status",
//        "sendData": "sendData",
//        "responseData": "responseData",
//        "remark": "remark",
//        "createdate": "createdate",
//        "createuserid": "createuserid",
//        "modifydate": "modifydate",
//        "modifyuserid": "modifyuserid",
//    }
//},
</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>