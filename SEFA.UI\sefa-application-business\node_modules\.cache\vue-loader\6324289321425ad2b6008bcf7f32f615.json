{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\form-dialog.vue", "mtime": 1749177894387}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AAyFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/planManagement/standardPeriodLot", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"700px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n       \n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线\" prop=\"LindCode\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.LindCode\" placeholder=\"请选择\" @change=\"setFormLineName\">\n                    <el-option v-for=\"(item, index) in lineOptions\" :key=\"index\" :label=\"item.EquipmentName\"\n                        :value=\"item.EquipmentCode\">\n                    </el-option>\n                </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div v-if=\"dialogForm&&dialogForm.MaterialCode\">\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>          \n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"对应关系\" prop=\"Type\">\n              <el-select v-model=\"dialogForm.Type\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in typeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"第一批用时\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入第一批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"中间批用时\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最后一批用时\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入最后一批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"批次量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入批次量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最小成批量\" prop=\"MinLotQuantity\">\n              <el-input v-model=\"dialogForm.MinLotQuantity\" placeholder=\"请输入最小成批量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最大成批量\" prop=\"MaxLotQuantity\">\n              <el-input v-model=\"dialogForm.MaxLotQuantity\" placeholder=\"请输入最大成批量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              <el-input v-model=\"dialogForm.Remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\n          @click=\"submit()\">确定\n        </el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    getStandardPeriodLotDetail,\n    saveStandardPeriodLotForm,\n    getLineList\n  } from \"@/api/planManagement/standardPeriodLot\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        lineOptions: [],\n        currentRow: {},\n      }\n    },\n    mounted() {\n    },\n    created() {\n      this.initDictList();\n      this.getLineList();\n    },\n    methods: {\n      submit() {\n        saveStandardPeriodLotForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      async initDictList(){\n        this.typeOptions = await this.$getDataDictionary('StandardPeriodType')    \n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n\n      },\n      getDialogDetail(id){\n        getStandardPeriodLotDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      async getLineList() {\n        const { response } = await getLineList({\n         //areaCode: 'PackingArea'\n         areaCode: 'Formulation'\n        })\n        console.log(response)\n        this.lineOptions = response\n      },\n      setFormLineName(EquipmentCode) {\n        //console.log(EquipmentCode);\n        //console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n        //console.log(this.dialogForm);\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)\n        this.dialogForm.MaterialId = val.ID\n        this.dialogForm.MaterialCode = val.Code\n        this.dialogForm.MaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}