<template>
    <v-navigation-drawer v-model="showDrawer" app class="app-drawer" :mini-variant.sync="mini" mini-variant-width="48"
        :width="drawerWidth">
        <v-toolbar color="primary darken-1" height="40" dark>
            <img :src="computeLogo" height="36" alt="$t('SZGC')" />
        </v-toolbar>
        <!-- <app-switcher /> -->
        <vue-perfect-scrollbar class="app-drawer__scrollbar">
            <div class="app-drawer__inner">
                <nav-list :items="computeMenu" :mini="mini" />
            </div>
        </vue-perfect-scrollbar>
        <!-- 注销-菜单-折叠 -->
        <!-- <template #append>
            <div class="grey lighten-3">
                <template v-if="mini">
                    <v-btn block width="48" height="48" icon tile class="mx-auto" @click="handleDrawerCollapse">
                        <v-icon>mdi-arrow-collapse-right</v-icon>
                    </v-btn>
                </template>
<template v-else>
                    <v-btn right block height="48" icon tile @click="handleDrawerCollapse">
                        <v-icon>mdi-arrow-collapse-left</v-icon>
                    </v-btn>
                </template>
</div>
</template> -->
    </v-navigation-drawer>
</template>
<script>
// import { protectedRoute as routes } from '@/router/config';
import VuePerfectScrollbar from 'vue-perfect-scrollbar';
// import AppSwitcher from './AppSwitcher'
import NavList from '@/components/nav/NavList';
export default {
    name: 'AppDrawer',
    components: {
        VuePerfectScrollbar,
        // AppSwitcher,
        NavList
    },
    props: {},
    data() {
        return {
            mini: false,
            showDrawer: true,
            drawerWidth: 256,
            scrollSettings: {
                maxScrollbarLength: 160
            }
        };
    },

    computed: {
        computeLogo() {
            return '/static/schneider_Chinese_LIO_White_RGB.png';
        },
        computeMenu() {
            // return this.filterRouteItem(routes[0].children);
            return this.filterRouteItem(this.$store.getters.getMenuList);
        },
        computeHeight() {
            return {
                height: this.height || ''
            };
        }
    },
    created() { },
    methods: {
        filterRouteItem(routes) {
            return routes
                .filter(item => item.Enable == '1' && !item.isHide)
                .filter(item => ['admin', 'user'].includes(item.Code)) //  菜单有效判断
                .map(item => {
                    // return {
                    //     title: this.$t(item.meta.title),
                    //     icon: item.meta.icon,
                    //     path: item.path,
                    //     isNew: item.meta.isNew || false,
                    //     children: item.children ? this.filterRouteItem(item.children) : []
                    // };
                    return {
                        MEMU_TYPE: item.MEMU_TYPE,
                        title: this.$store.getters.getLocale == 'cn' ? item.CnName : (this.$t(item.CnCode) || item.CnName),
                        icon: item.Icon,
                        path: item.Route,
                        children: item.children ? this.filterRouteItem(item.children) : []
                    };
                });
        },
        handleDrawerCollapse() {
            this.mini = !this.mini;
        },
        toggleDrawer() {
            this.showDrawer = !this.showDrawer;
        }
    }
};
</script>

<style lang="scss" scoped>
.app-drawer {
    &__srollbar {
        max-height: calc(100vh - 48px - 20px - 44px);
        overflow: auto;
    }

    &__inner {
        height: calc(100vh - 48px - 20px - 44px);
        overflow: auto;
    }
}
</style>
