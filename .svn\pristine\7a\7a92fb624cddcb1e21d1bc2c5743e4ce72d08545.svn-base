﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("PPM_B_BATCH_CONSUME_REQUIREMENT")]
    public class BatchConsumeRequirementEntity : EntityBase
    {
        public BatchConsumeRequirementEntity()
        {
        }

        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_ID")]
        public string BatchId { get; set; }

        /// <summary>
        /// Desc:订单工序消耗需求ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PO_CONSUME_REQUIREMENT_ID")]
        public string PoConsumeRequirementId { get; set; }

        /// <summary>
        /// Desc:数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY")]
        public decimal Quantity { get; set; }

        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "DELETED")]
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:投料状态（0 未投料，1，投料中 2，已投料）
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "FEED_STATES")]
        public int? FeedStates { get; set; }

        /// <summary>
        /// Desc:称量备料数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "WEIGHING_QTY")]
        public decimal? WeighingQty { get; set; }

        /// <summary>
        /// Desc:是否为千克，默认为空，为g写上单位g
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "CHANGE_UNIT")]
        public string ChangeUnit { get; set; }
    }
}