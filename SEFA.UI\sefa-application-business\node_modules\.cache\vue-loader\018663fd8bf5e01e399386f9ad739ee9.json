{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\index.vue", "mtime": 1749177894394}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AA+EA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/planManagement/standardPeriodLot", "sourcesContent": ["<!--\n * @Descripttion: (标准时间批量/PPM_M_STANDARD_PERIOD_LOT)\n * @version: (1.0)\n * @Author: (admin)\n * @Date: (2025-05-14)\n * @LastEditors: (admin)\n * @LastEditTime: (2025-05-14)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n            \t\t\t\t    \n      <el-form-item label=\"产线代码\" prop=\"lineCode\">\n        <el-input v-model=\"searchForm.lineCode\" placeholder=\"请输入产线代码\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"物料代码\" prop=\"materialCode\">\n        <el-input v-model=\"searchForm.materialCode\" placeholder=\"请输入物料代码\" />\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n            {{ $t('GLOBAL._XZ') }}\n          </el-button>\n        </el-form-item>\n              </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n                        <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n          \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delStandardPeriodLot, getStandardPeriodLotList\n} from \"@/api/planManagement/standardPeriodLot\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\n//import { standardPeriodLotColumn } from '@/columns/pPM/standardPeriodLot.js';\n//import UploadButton from \"@/components/UploadButton.vue\";\n\nexport default {\n  name: 'index.vue',\n  components: {\n    //UploadButton,\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('StandardPeriodLot.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n      {code: 'LineCode', width: 130, align: 'center'},\n      {code: 'MaterialCode', width: 130, align: 'center'},\n      {code: 'MaterialName', width: 130, align: 'center'},\n      {code: 'Type', width: 130, align: 'center'},\n      {code: 'FirstLotPeriod', width: 130, align: 'center'},\n      {code: 'MiddleLotPeriod', width: 130, align: 'center'},\n      {code: 'LastLotPeriod', width: 130, align: 'center'},\n      {code: 'PlanQuantity', width: 130, align: 'center'},\n      {code: 'MinLotQuantity', width: 130, align: 'center'},\n      {code: 'MaxLotQuantity', width: 130, align: 'center'},\n      {code: 'Remark', width: 130, align: 'center'},\n      {code: 'CreateDate', width: 130, align: 'center'},\n      {code: 'CreateUserId', width: 130, align: 'center'},\n      {code: 'ModifyDate', width: 130, align: 'center'},\n      {code: 'ModifyUserId', width: 130, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'标准时间批量',\n        serveIp:'baseURL_PPM',\n        // uploadUrl:'/api/StandardPeriodLot/ImportData', //导入\n        // exportUrl:'/api/StandardPeriodLot/ExportData', //导出\n        // DownLoadUrl:'/api/StandardPeriodLot/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      for (let key in this.hansObj) {\n        this.tableName = getTableHead(this.hansObj, this.tableOption)\n      }\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delStandardPeriodLot([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getStandardPeriodLotList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"StandardPeriodLot\": {\n//    \"table\": {\n//        \"lineCode\": \"lineCode\",\n//        \"materialCode\": \"materialCode\",\n//        \"materialName\": \"materialName\",\n//        \"type\": \"type\",\n//        \"firstLotPeriod\": \"firstLotPeriod\",\n//        \"middleLotPeriod\": \"middleLotPeriod\",\n//        \"lastLotPeriod\": \"lastLotPeriod\",\n//        \"planQuantity\": \"planQuantity\",\n//        \"minLotQuantity\": \"minLotQuantity\",\n//        \"maxLotQuantity\": \"maxLotQuantity\",\n//        \"remark\": \"remark\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}