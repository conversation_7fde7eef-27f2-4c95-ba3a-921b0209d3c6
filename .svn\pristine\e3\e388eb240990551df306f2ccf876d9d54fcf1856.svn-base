﻿using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// BatchDcsRepository
	/// </summary>
    public class BatchDcsRepository : BaseRepository<BatchDcsEntity>, IBatchDcsRepository
    {
        public BatchDcsRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}