﻿"restore":{"projectUniqueName":"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj","projectName":"SEFA.PPM.Services","projectPath":"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\SEFA.PPM.Services.csproj","outputPath":"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Services\\obj\\","projectStyle":"PackageReference","originalTargetFrameworks":["net6.0"],"sources":{"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net6.0":{"targetAlias":"net6.0","projectReferences":{"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj":{"projectPath":"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.IServices\\SEFA.PPM.IServices.csproj"},"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj":{"projectPath":"C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.PPM\\SEFA.PPM.Repository\\SEFA.PPM.Repository.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net6.0":{"targetAlias":"net6.0","dependencies":{"EasyModbusTCP.NETCore":{"target":"Package","version":"[1.0.0, )"},"InfluxDB.Client":{"target":"Package","version":"[4.0.0, )"},"InfluxDB.Client.Linq":{"target":"Package","version":"[4.0.0, )"},"MiniExcel":{"target":"Package","version":"[1.33.0, )"},"NPOI":{"target":"Package","version":"[2.7.0, )"},"System.ServiceModel.Duplex":{"target":"Package","version":"[4.10.*, )"},"System.ServiceModel.Federation":{"target":"Package","version":"[4.10.*, )"},"System.ServiceModel.Http":{"target":"Package","version":"[4.10.*, )"},"System.ServiceModel.NetTcp":{"target":"Package","version":"[4.10.*, )"},"System.ServiceModel.Security":{"target":"Package","version":"[4.10.*, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.AspNetCore.App.Ref","version":"[6.0.33, 6.0.33]"},{"name":"Microsoft.NETCore.App.Ref","version":"[6.0.33, 6.0.33]"},{"name":"Microsoft.WindowsDesktop.App.Ref","version":"[6.0.33, 6.0.33]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Users\\<USER>\\.dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}