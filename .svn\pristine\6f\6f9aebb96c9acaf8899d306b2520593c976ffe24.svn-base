<template>
    <el-dialog :title="dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')" :visible.sync="dialogVisible" width="800px"
      :close-on-click-modal="false" :modal-append-to-body="false" :close-on-press-escape="false"
      @close="dialogVisible = false">
      <el-form ref="dialogForm" :model="dialogForm" label-width="130px">

        <el-col :lg="12">
            <el-form-item label="物料代码">              
              <div>
                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}
              </div>
            </el-form-item>
          </el-col>

          <el-col :lg="12">
            <el-form-item label="替代物料代码">
              <div>
                <el-button icon="el-icon-plus" type="text" @click="openMaterialTable">{{ $t('GLOBAL._CX') }}</el-button>
              </div>
              <div>
                {{dialogForm.InsteadMaterialCode}}&nbsp; &nbsp; {{dialogForm.InsteadMaterialName}}
              </div>
            </el-form-item>
          </el-col>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button v-loading="formLoading" :disabled="formLoading" element-loading-spinner="el-icon-loading" size="small" @click="submit()">确定</el-button>
      </div>
      <material-table :is-id="false" ref="materialTable" @saveForm="setMaterial"></material-table>
    </el-dialog>
  </template>
  

<script>
  import {
    getWeekScheduleBomDetail,
    changeMaterial
  } from "@/api/planManagement/weekSchedule";
  import MaterialTable from '@/components/MaterialTable.vue';
  export default {
    components:{
      MaterialTable
    },
    data() {
      return {
        dialogForm: {},
        dialogVisible: false,
        formLoading: false,
        currentRow: {},
        matInfo:{}
      }
    },
    created() {
      this.initDictList();
    },
    mounted() {
    },
    methods: {
      async initDictList(){
      },
      
      submit() {

        changeMaterial(this.dialogForm).then(res=>{
          this.$message.success(res.msg)
          this.$emit('saveForm')
          this.dialogVisible = false
        })
      },
      show(data) {
        this.dialogForm = {}
        this.currentRow = data
        this.dialogVisible = true
        this.$nextTick(_ => {
          if(data.ID){
            this.getDialogDetail(data.ID)
          }
        })
      },
      getDialogDetail(id){
        getWeekScheduleBomDetail(id).then(res => {
          this.dialogForm = res.response
        })
      },
      setMaterial(val){
        // console.log("setMaterial")
        // console.log(val)        
        this.dialogForm.InsteadMaterialId = val.ID
        this.dialogForm.InsteadMaterialCode = val.Code
        this.dialogForm.InsteadMaterialName = val.NAME
        this.$forceUpdate()
        // this.matInfo = val        
        // console.log(this.dialogForm.MaterialCode)
        // console.log(this.dialogForm.MaterialName)
      },
      openMaterialTable(){
        this.$refs['materialTable'].show()
      }
    }
  }
  </script>