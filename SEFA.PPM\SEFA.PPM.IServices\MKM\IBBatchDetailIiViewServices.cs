﻿using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using System.Threading.Tasks;
using System.Collections.Generic;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Model.Models;

namespace SEFA.MKM.IServices
{
    /// <summary>
    /// IBBatchDetailIiViewServices
    /// </summary>	
    public interface IBBatchDetailIiViewServices : IBaseServices<BBatchDetailIiViewEntity>
    {
        Task<PageModel<BBatchDetailIiViewEntity>> GetPageList(BBatchDetailIiViewRequestModel reqModel);

        Task<List<BBatchDetailIiViewEntity>> GetList(BBatchDetailIiViewRequestModel reqModel);

        Task<List<BBatchDetailIiViewEntity>> GetListByBatchID(string batchID);

        Task<PageModel<BBatchDetailIiViewEntity>> GetPageListByBatchIDS(string[] batchIDS, int pageIndex, int pageSize);

        Task<List<BBatchDetailIiViewEntity>> GetBatchPreparationDetailList(string batchID, string eqpmentID);

        Task<bool> SaveForm(BBatchDetailIiViewEntity entity);
    }
}