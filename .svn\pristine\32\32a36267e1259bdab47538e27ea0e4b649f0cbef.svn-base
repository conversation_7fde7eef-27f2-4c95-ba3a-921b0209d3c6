﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SEFA.Base;
using SEFA.Base.Common.Common;
using SEFA.Base.Common.HttpContextUser;
using SEFA.Base.IRepository.Base;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.DFM.Model.Models;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;
using static SEFA.PPM.Model.PPMConstant;

namespace SEFA.PPM.Services
{
    public class WeekScheduleServices : BaseServices<WeekScheduleEntity>, IWeekScheduleServices
    {
        private readonly IBaseRepository<WeekScheduleEntity> _dal;
        private readonly IBaseRepository<WeekScheduleBomEntity> _weekScheduleBom;
        private readonly IBaseRepository<DataItemDetailEntity> _dataItem;
        private readonly IBaseRepository<DFM.Model.Models.CalendarEntity> _calendar;
        private readonly IBaseRepository<DFM.Model.Models.UnitmanageEntity> _unit;
        private readonly IBaseRepository<DFM.Model.Models.EquipmentEntity> _equipment;
        private readonly IBaseRepository<DFM.Model.Models.MaterialEntity> _material;
        private readonly IBaseRepository<DFM.Model.Models.MaterialVersionEntity> _materialVersion;
        private readonly IBaseRepository<DFM.Model.Models.SapSegmentEntity> _sapSegment;
        private readonly IBaseRepository<DFM.Model.Models.SapSegmentMaterialEntity> _sapSegmentMaterial;
        private readonly IBaseRepository<DFM.Model.Models.SapSegmentMaterialStepEntity> _sapSegmentMaterialStep;
        private readonly IBaseRepository<ProductionOrderEntity> _productionOrder;
        private readonly IBaseRepository<PoSegmentRequirementEntity> _poSegmentRequirement;
        private readonly IBaseRepository<PoConsumeRequirementEntity> _poConsumeRequirement;
        private readonly IBaseRepository<PoProducedRequirementEntity> _poProducedRequirement;
        private readonly IBaseRepository<SEFA.PPM.Model.Models.BatchEntity> _batch;
        private readonly IBaseRepository<BatchConsumeRequirementEntity> _batchConsumeRequirement;
        private readonly IBaseRepository<BatchProducedRequirementEntity> _batchProducedRequirement;
        private readonly IBaseRepository<StandardPeriodLotEntity> _standardPeriodLot;
        private readonly IBaseRepository<ReplaceMaterialEntity> _replaceMaterial;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IUser _user;

        /// <summary>
        /// 默认为0，表示正常，1表示CIP，2表示返工，3表示休息日
        /// </summary>
        private XSSFColor Color_Normal = new XSSFColor(new byte[] { 238, 236, 225 });
        private XSSFColor Color_CIP = new XSSFColor(new byte[] { 0, 0, 0 });
        private XSSFColor Color_Rework = new XSSFColor(new byte[] { 146, 208, 80 });
        private XSSFColor Color_Holiday = new XSSFColor(new byte[] { 192, 80, 77 });

        public WeekScheduleServices(IBaseRepository<WeekScheduleEntity> dal,
            IBaseRepository<WeekScheduleBomEntity> weekScheduleBom,
            IBaseRepository<DataItemDetailEntity> dataItem,
            IBaseRepository<DFM.Model.Models.UnitmanageEntity> unit,
            IBaseRepository<DFM.Model.Models.CalendarEntity> calendar,
            IBaseRepository<DFM.Model.Models.MaterialEntity> material,
            IBaseRepository<DFM.Model.Models.MaterialVersionEntity> materialVersion,
            IBaseRepository<DFM.Model.Models.EquipmentEntity> equipment,
            IBaseRepository<DFM.Model.Models.SapSegmentEntity> sapSegment,
            IBaseRepository<DFM.Model.Models.SapSegmentMaterialEntity> sapSegmentMaterial,
            IBaseRepository<DFM.Model.Models.SapSegmentMaterialStepEntity> sapSegmentMaterialStep,
            IBaseRepository<ProductionOrderEntity> productionOrder,
            IBaseRepository<PoSegmentRequirementEntity> poSegmentRequirement,
            IBaseRepository<PoConsumeRequirementEntity> poConsumeRequirement,
            IBaseRepository<PoProducedRequirementEntity> poProducedRequirement,
            IBaseRepository<SEFA.PPM.Model.Models.BatchEntity> batch,
            IBaseRepository<BatchConsumeRequirementEntity> batchConsumeRequirement,
            IBaseRepository<BatchProducedRequirementEntity> batchProducedRequirement,
            IBaseRepository<StandardPeriodLotEntity> standardPeriodLot,
            IBaseRepository<ReplaceMaterialEntity> replaceMaterial,
        IUnitOfWork unitOfWork, IMapper mapper, IUser user)
        {
            this._dal = dal;
            _weekScheduleBom = weekScheduleBom;
            base.BaseDal = dal;
            _dataItem = dataItem;
            _unit = unit;
            _calendar = calendar;
            _equipment = equipment;
            _material = material;
            _materialVersion = materialVersion;
            _sapSegment = sapSegment;
            _sapSegmentMaterial = sapSegmentMaterial;
            _sapSegmentMaterialStep = sapSegmentMaterialStep;
            _productionOrder = productionOrder;
            _poSegmentRequirement = poSegmentRequirement;
            _poConsumeRequirement = poConsumeRequirement;
            _poProducedRequirement = poProducedRequirement;
            _batch = batch;
            _batchConsumeRequirement = batchConsumeRequirement;
            _batchProducedRequirement = batchProducedRequirement;
            _standardPeriodLot = standardPeriodLot;
            _replaceMaterial = replaceMaterial;
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _user = user;
        }

        #region 获取数据、保存
        public async Task<List<WeekScheduleEntity>> GetList(WeekScheduleRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<WeekScheduleEntity>()
                .AndIF(!string.IsNullOrWhiteSpace(reqModel.Category), a => a.Category == reqModel.Category)
                .AndIF(!string.IsNullOrWhiteSpace(reqModel.OrderNo), a => a.OrderNo.Contains(reqModel.OrderNo))
                .AndIF(!string.IsNullOrWhiteSpace(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode))
                .AndIF(!string.IsNullOrWhiteSpace(reqModel.MaterialName), a => a.MaterialName.Contains(reqModel.MaterialName))
                .AndIF(!string.IsNullOrWhiteSpace(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
                .AndIF(reqModel.StartWorkday != null && reqModel.FinishWorkday == null, a => a.StartWorkday >= reqModel.StartWorkday)
                .AndIF(reqModel.StartWorkday == null && reqModel.FinishWorkday != null, a => a.FinishWorkday <= reqModel.FinishWorkday)
                .AndIF(reqModel.StartWorkday != null && reqModel.FinishWorkday != null, a => a.StartWorkday >= reqModel.StartWorkday
                                                                                          && a.StartWorkday <= reqModel.FinishWorkday
                                                                                          && a.FinishWorkday >= reqModel.FinishWorkday
                                                                                          && a.FinishWorkday <= reqModel.FinishWorkday)
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<WeekScheduleEntity>> GetPageList(WeekScheduleRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<WeekScheduleEntity>()
               .AndIF(!string.IsNullOrWhiteSpace(reqModel.Factory), a => a.Factory == reqModel.Factory)
               .AndIF(!string.IsNullOrWhiteSpace(reqModel.Category), a => a.Category == reqModel.Category)
               .AndIF(!string.IsNullOrWhiteSpace(reqModel.OrderNo), a => a.OrderNo.Contains(reqModel.OrderNo))
               .AndIF(!string.IsNullOrWhiteSpace(reqModel.MaterialCode), a => a.MaterialCode.Contains(reqModel.MaterialCode))
               .AndIF(!string.IsNullOrWhiteSpace(reqModel.LineCode), a => a.LineCode.Contains(reqModel.LineCode))
               .AndIF(reqModel.StartWorkday != null && reqModel.FinishWorkday == null, a => a.StartWorkday >= reqModel.StartWorkday)
               .AndIF(reqModel.StartWorkday == null && reqModel.FinishWorkday != null, a => a.FinishWorkday <= reqModel.FinishWorkday)
               .AndIF(reqModel.StartWorkday != null && reqModel.FinishWorkday != null, a => a.StartWorkday >= reqModel.StartWorkday
                                                                                         && a.StartWorkday <= reqModel.FinishWorkday
                                                                                         && a.FinishWorkday >= reqModel.FinishWorkday
                                                                                         && a.FinishWorkday <= reqModel.FinishWorkday)
               .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<List<ReplaceMaterialEntity>> GetInsteadMaterialList(WeekScheduleBomRequestModel reqModel)
        {
            var scheduleEntity = await _dal.QueryById(reqModel.WeekScheduleId);
            if (scheduleEntity == null)
                return new List<ReplaceMaterialEntity>();

            var whereExpression = Expressionable.Create<ReplaceMaterialEntity>()
                .And((x) => x.ProductionCode == scheduleEntity.MaterialCode)
                .And(x => x.Deleted == 0 && x.Enabled == 1)
                .ToExpression();
            var allData = await _replaceMaterial.FindList(whereExpression);

            var sourceMaterialList = allData.Where(m => m.MaterialCode == reqModel.MaterialCode || m.MaterialCode == reqModel.InsteadMaterialCode).ToList();

            var data = allData.Where(x => sourceMaterialList.Select(i => i.GroupCode).Contains(x.GroupCode)).ToList();
            return data;
        }

        /// <summary>
        /// 新增计划
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<ResultBool> SaveForm(WeekScheduleEntity entity)
        {
            var result = new ResultBool();

            _unitOfWork.BeginTran();

            var unitList = await _unit.FindList(x => x.Deleted == 0);
            var equipmentList = await _equipment.FindList(x => x.Deleted == 0);
            var itemList = await _dataItem.FindList(x => x.Deleted == 0);

            string workshop = entity.Workshop;              //车间
            string line = entity.LineCode;                  //线别
            var lineEntity = equipmentList.FirstOrDefault(m => m.EquipmentCode == line && m.Deleted == 0);
            if (lineEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found line code {line}");
                return result;
            }
            string lineId = lineEntity.ID;                  //线别ID
            string productCode = entity.MaterialCode;       //产品代码
            var productEntity = (await _material.FindEntity(m => m.Code == productCode && m.Deleted == 0));
            if (productEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found product code {productCode}");
                return result;
            }
            string productId = productEntity.ID;            //产品代码
            string productName = productEntity.Description; //产品名称
            var unitEntity = unitList.FirstOrDefault(m => m.ID == productEntity.Unit && m.Deleted == 0);
            if (unitEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found unit {productEntity.Unit}");
                return result;
            }

            var matVer = await _materialVersion.FindEntity(m => m.MaterialId == productId && m.MaterialVersionNumber == "0001" && m.Deleted == 0);
            if (matVer == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found material 【{productCode}】 version 【0001】");
                return result;
            }

            entity.OrderNo = GetPlanOrderNo(line);
            entity.Workshop = workshop;
            entity.Category = workshop.ToLower().Contains("formulation") ? "Formulation" : "Packing";
            entity.LineId = lineId;
            entity.MaterialId = productId;
            entity.MaterialVer = matVer.ID;
            entity.UnitId = unitEntity.ID;
            entity.Status = WeekSchedule_Status.NotSplit.ToString();
            entity.CreateCustomGuid(_user.UserName);

            var weekScheduleListAdd = new List<WeekScheduleEntity>();
            weekScheduleListAdd.Add(entity);

            var add = await _dal.Add(weekScheduleListAdd);
            //添加成功，则新增周计划BOM
            if (add == weekScheduleListAdd.Count)
            {
                #region 产品
                //获取产品的工序物料清单
                var segmentList = await _sapSegment.FindList(m => m.Deleted == 0);
                var segmentMaterialList = await _sapSegmentMaterial.FindList(m => weekScheduleListAdd.Select(m => m.MaterialVer).ToArray().Contains(m.MaterialVersionId));
                var segmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => segmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));
                var weekScheduleBomList = new List<WeekScheduleBomEntity>();
                foreach (var item in weekScheduleListAdd)
                {
                    var verId = item.MaterialVer;
                    var segmentMaterial = segmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == item.MaterialVer);
                    if (segmentMaterial == null)
                        continue;
                    var segmentMaterialStepes = segmentMaterialStepList.Where(m => m.SapSegmentMaterialId == segmentMaterial.ID);
                    var materialList = await _material.FindList(a => segmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                    var materialVersionList = await _materialVersion.FindList(a => materialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                    foreach (var step in segmentMaterialStepes)
                    {
                        var material = materialList.FirstOrDefault(m => m.ID == step.MaterialId);
                        var materialVersion = materialVersionList.FirstOrDefault(m => m.MaterialId == material.ID);
                        var unitId = material?.Unit;
                        var unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                        var bom = new WeekScheduleBomEntity()
                        {
                            WeekScheduleId = item.ID,
                            SegmentId = segmentMaterial.SapSegmentId,
                            SegmentName = segmentList.FirstOrDefault(m => m.ID == segmentMaterial.SapSegmentId).SegmentName,
                            SegmentMaterialId = segmentMaterial.ID,
                            SegmentMaterialStepId = step.ID,
                            MaterialId = step.MaterialId,
                            MaterialCode = material.Code,
                            MaterialName = material.NAME,
                            MaterialVer = materialVersion.ID,
                            MaterialType = material.Type,//原材料、半成品
                            Sort = step.SortOrder,
                            //ParentId = step.ParentId,
                            //InsteadMaterialId = step.InsteadMaterialId,
                            //InsteadMaterialCode = step.InsteadMaterialCode,
                            //InsteadMaterialName = step.InsteadMaterialName,
                            //InsteadMaterialVer = step.InsteadMaterialVer,
                            //InsteadMaterialGroup = step.InsteadMaterialGroup,
                            //InsteadRate = step.InsteadRate,
                            Unit = unit,
                            UnitId = unitId,
                            StandardQuantity = step.ParentQuantity,
                            PlanQuantity = step.Quantity,
                            ActualQuantity = item.PlanQuantity / step.ParentQuantity * step.Quantity,
                            //SapOrderNo = item.SapOrderNo,
                            //SapFeedbackQuantity = step.SapFeedbackQuantity,
                            //SapFeedbackDate = step.SapFeedbackDate,
                            //Remark = step.Remark,
                        };
                        bom.CreateCustomGuid(_user.UserName);
                        weekScheduleBomList.Add(bom);

                        #region 半成品
                        //半成品物料，添加到半成品物料版本集合中，用于处理半成品
                        if (material.Type == "ZSFG")
                        {
                            //获取半成品的工序物料清单
                            var semiSegmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == materialVersion.ID);
                            var semiSegmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => semiSegmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));

                            var semiSegmentMaterial = semiSegmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == materialVersion.ID);
                            if (semiSegmentMaterial == null)
                                continue;
                            var semiSegmentMaterialStepes = semiSegmentMaterialStepList.Where(m => m.SapSegmentMaterialId == semiSegmentMaterial.ID);
                            var semiMaterialList = await _material.FindList(a => semiSegmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                            var semiMaterialVersionList = await _materialVersion.FindList(a => semiMaterialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                            foreach (var s in semiSegmentMaterialStepes)
                            {
                                var semiMaterial = semiMaterialList.FirstOrDefault(m => m.ID == s.MaterialId);
                                var semiMaterialVersion = semiMaterialVersionList.FirstOrDefault(m => m.MaterialId == semiMaterial.ID);
                                unitId = semiMaterial?.Unit;
                                unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                var semiBom = new WeekScheduleBomEntity()
                                {
                                    WeekScheduleId = item.ID,
                                    SegmentId = semiSegmentMaterial.SapSegmentId,
                                    SegmentName = segmentList.FirstOrDefault(m => m.ID == segmentMaterial.SapSegmentId).SegmentName,
                                    SegmentMaterialId = semiSegmentMaterial.ID,
                                    SegmentMaterialStepId = s.ID,
                                    MaterialId = s.MaterialId,
                                    MaterialCode = semiMaterial.Code,
                                    MaterialName = semiMaterial.NAME,
                                    MaterialVer = semiMaterialVersion.ID,
                                    MaterialType = semiMaterial.Type,//原材料、半成品
                                    Sort = s.SortOrder,
                                    ParentId = bom.ID,
                                    //InsteadMaterialId = step.InsteadMaterialId,
                                    //InsteadMaterialCode = step.InsteadMaterialCode,
                                    //InsteadMaterialName = step.InsteadMaterialName,
                                    //InsteadMaterialVer = step.InsteadMaterialVer,
                                    //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                    //InsteadRate = step.InsteadRate,
                                    Unit = unit,
                                    UnitId = unitId,
                                    StandardQuantity = s.ParentQuantity,
                                    PlanQuantity = s.Quantity,
                                    ActualQuantity = item.PlanQuantity / s.ParentQuantity * s.Quantity,
                                    //SapOrderNo = item.SapOrderNo,
                                    //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                    //SapFeedbackDate = step.SapFeedbackDate,
                                    //Remark = step.Remark,
                                };
                                semiBom.CreateCustomGuid(_user.UserName);
                                weekScheduleBomList.Add(semiBom);
                            }
                        }
                        #endregion
                    }
                }
                #endregion

                if (weekScheduleBomList.Count > 0 && await _weekScheduleBom.Add(weekScheduleBomList) != weekScheduleBomList.Count)
                {
                    _unitOfWork.RollbackTran();
                    result.AddError($"Add WeekScheduleBom Error");
                    return result;
                }

                #region 包装计划时，新增ProductOrder和Batch数据
                var productionOrderList = new List<ProductionOrderEntity>();
                var poSegmentRequirementList = new List<PoSegmentRequirementEntity>();
                var poConsumeRequirementList = new List<PoConsumeRequirementEntity>();
                var poProducedRequirementList = new List<PoProducedRequirementEntity>();
                var batchList = new List<SEFA.PPM.Model.Models.BatchEntity>();
                var batchConsumeRequirementList = new List<BatchConsumeRequirementEntity>();
                var batchProducedRequirementList = new List<BatchProducedRequirementEntity>();

                //数据分类是包装计划 &&  计划类型是正常计划 || 重工计划
                var patckingList = weekScheduleListAdd.Where(m => m.Category == "Packing" && (m.Type == WeekSchedule_Type.Normal.ToString() || m.Type == WeekSchedule_Type.Rework.ToString())).ToList();
                foreach (var item in patckingList)
                {
                    #region ProductOrder
                    var productOrder = new ProductionOrderEntity()
                    {
                        ProductionOrderNo = item.OrderNo + "_01",
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        Type = "WorkOrder",
                        ParentId = item.ID,
                        PlanDate = item.StartWorkday,
                        PrepareShiftid = item.StartShift,
                        PlanQty = (decimal)item.PlanQuantity,
                        PlanStartTime = item.StartWorkday,
                        PlanEndTime = item.FinishWorkday,
                        PoStatus = "2",//已释放
                        SegmentCode = item.Workshop,
                        LineCode = item.LineCode,
                        MesOrderCode = item.OrderNo + "_01",
                        SapOrderType = "ZXH2",
                        Sequence = 1,
                        SapStatus = "C",
                        HasThroat = 0,
                        SapFlag = 0,
                        OrderType = "C",
                    };
                    productOrder.CreateCustomGuid(_user.UserName);
                    productionOrderList.Add(productOrder);
                    //await _productionOrder.Add(productOrder);
                    #endregion

                    #region POSegmentRequirement
                    var bomList = weekScheduleBomList.Where(m => m.WeekScheduleId == item.ID).ToList();
                    var poSegmentIdList = bomList.Select(m => m.SegmentId).Distinct().ToList();
                    foreach (var segmentId in poSegmentIdList)
                    {
                        #region PoSegmentRequirement
                        var poSegmentRequirement = new PoSegmentRequirementEntity()
                        {
                            ProductionOrderId = productOrder.ID,
                            SegmentId = segmentId,
                            Deleted = 0
                        };
                        poSegmentRequirement.CreateCustomGuid(_user.UserName);
                        //await _poSegmentRequirement.Add(poSegmentRequirement);
                        poSegmentRequirementList.Add(poSegmentRequirement);
                        #endregion

                        var bomConsumeList = bomList.Where(m => m.SegmentId == segmentId).ToList();
                        foreach (var consumeMaterial in bomConsumeList)
                        {
                            #region PoConsumeRequirement
                            var poConsumeRequirement = new PoConsumeRequirementEntity()
                            {
                                ProductionOrderId = productOrder.ID,
                                PoSegmentRequirementId = poSegmentRequirement.ID,
                                MaterialId = consumeMaterial.MaterialId,
                                MaterialVersionId = consumeMaterial.MaterialVer,
                                Quantity = consumeMaterial.PlanQuantity,
                                UnitId = consumeMaterial.UnitId,
                                SortOrder = (int)consumeMaterial.Sort,
                                Deleted = 0,
                                WeighingQty = consumeMaterial.PlanQuantity,
                            };
                            poConsumeRequirement.CreateCustomGuid(_user.UserName);
                            //wait _poConsumeRequirement.Add(poConsumeRequirement);
                            poConsumeRequirementList.Add(poConsumeRequirement);
                            #endregion
                        }
                    }
                    #endregion

                    #region poProducedRequirement
                    var poProducedRequirement = new PoProducedRequirementEntity()
                    {
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        Quantity = (decimal)item.PlanQuantity,
                        UnitId = item.UnitId,
                        SortNo = 1,
                        Deleted = 0
                    };
                    poProducedRequirement.CreateCustomGuid(_user.UserName);
                    //await _poProducedRequirement.Add(poProducedRequirement);
                    poProducedRequirementList.Add(poProducedRequirement);
                    #endregion

                    #region Batch
                    var batch = new SEFA.PPM.Model.Models.BatchEntity()
                    {
                        LineId = item.LineId,
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                        PoProducedRequirementId = poProducedRequirement.ID,
                        Number = "1",
                        BatchCode = productOrder.ProductionOrderNo + "_01",
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        MaterialCode = item.MaterialCode,
                        MaterialDescription = item.MaterialName,
                        TargetQuantity = item.PlanQuantity,
                        Status = "1",
                        PrepStatus = "1",
                        Deleted = 0
                    };
                    batch.CreateCustomGuid(_user.UserName);
                    //await _batch.Add(batch);
                    batchList.Add(batch);
                    #endregion

                    #region batchconsumeRequirement
                    foreach (var consume in poConsumeRequirementList.Where(m => m.ProductionOrderId == productOrder.ID))
                    {
                        var batchConsumeRequirement = new BatchConsumeRequirementEntity()
                        {
                            BatchId = batch.ID,
                            PoConsumeRequirementId = consume.ID,
                            Quantity = (decimal)consume.Quantity,
                            WeighingQty = (decimal)consume.WeighingQty,
                            Deleted = 0
                        };
                        batchConsumeRequirement.CreateCustomGuid(_user.UserName);
                        //await _batchConsumeRequirement.Add(batchConsumeRequirement);
                        batchConsumeRequirementList.Add(batchConsumeRequirement);
                    }
                    #endregion

                    #region BatchProducedRequirement
                    var batchProducedRequirement = new BatchProducedRequirementEntity()
                    {
                        BatchId = batch.ID,
                        PoProducedRequirementId = poProducedRequirement.ID,
                        Quantity = (decimal)poProducedRequirement.Quantity,
                        Deleted = 0
                    };
                    batchProducedRequirement.CreateCustomGuid(_user.UserName);
                    //await _batchProducedRequirement.Add(batchProducedRequirement);
                    batchProducedRequirementList.Add(batchProducedRequirement);
                    #endregion
                }

                if (productionOrderList.Count > 0)
                    await _productionOrder.Add(productionOrderList);
                if (poSegmentRequirementList.Count > 0)
                    await _poSegmentRequirement.Add(poSegmentRequirementList);
                if (poConsumeRequirementList.Count > 0)
                    await _poConsumeRequirement.Add(poConsumeRequirementList);
                if (poProducedRequirementList.Count > 0)
                    await _poProducedRequirement.Add(poProducedRequirementList);
                if (batchList.Count > 0)
                    await _batch.Add(batchList);
                if (batchConsumeRequirementList.Count > 0)
                    await _batchConsumeRequirement.Add(batchConsumeRequirementList);
                if (batchProducedRequirementList.Count > 0)
                    await _batchProducedRequirement.Add(batchProducedRequirementList);
                #endregion
            }
            else
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Add Week Schedule Error");
                return result;
            }

            _unitOfWork.CommitTran();
            return result;

        }

        /// <summary>
        /// 更新计划
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public async Task<ResultBool> UpdateForm(WeekScheduleEntity entity)
        {
            var result = new ResultBool();

            _unitOfWork.BeginTran();

            var unitList = await _unit.FindList(x => x.Deleted == 0);
            var equipmentList = await _equipment.FindList(x => x.Deleted == 0);
            var itemList = await _dataItem.FindList(x => x.Deleted == 0);

            string workshop = entity.Workshop;              //车间
            string line = entity.LineCode;                  //线别
            var lineEntity = equipmentList.FirstOrDefault(m => m.EquipmentCode == line && m.Deleted == 0);
            if (lineEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found line code {line}");
                return result;
            }
            string lineId = lineEntity.ID;                  //线别ID
            string productCode = entity.MaterialCode;       //产品代码
            var productEntity = (await _material.FindEntity(m => m.Code == productCode && m.Deleted == 0));
            if (productEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found product code {productCode}");
                return result;
            }
            string productId = productEntity.ID;            //产品代码
            string productName = productEntity.Description; //产品名称
            var unitEntity = unitList.FirstOrDefault(m => m.ID == productEntity.Unit && m.Deleted == 0);
            if (unitEntity == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found unit {productEntity.Unit}");
                return result;
            }

            var matVer = await _materialVersion.FindEntity(m => m.MaterialId == productId && m.MaterialVersionNumber == "0001" && m.Deleted == 0);
            if (matVer == null)
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Not found material 【{productCode}】 version 【0001】");
                return result;
            }

            //entity.OrderNo = GetPlanOrderNo(line);
            entity.Workshop = workshop;
            entity.Category = workshop.ToLower().Contains("formulation") ? "Formulation" : "Packing";
            entity.LineId = lineId;
            entity.MaterialId = productId;
            entity.MaterialVer = matVer.ID;
            entity.UnitId = unitEntity.ID;
            entity.Status = WeekSchedule_Status.NotSplit.ToString();
            entity.Modify(entity.ID, _user.UserName);

            var weekScheduleListUpdate = new List<WeekScheduleEntity>();
            weekScheduleListUpdate.Add(entity);

            var update = await _dal.Update(weekScheduleListUpdate);

            //添加成功，则新增周计划BOM
            if (update)
            {
                //删除周计划BOM
                var del = await _weekScheduleBom.Delete(x => x.WeekScheduleId == entity.ID);

                #region 产品
                //获取产品的工序物料清单
                var segmentList = await _sapSegment.FindList(m => m.Deleted == 0);
                var segmentMaterialList = await _sapSegmentMaterial.FindList(m => weekScheduleListUpdate.Select(m => m.MaterialVer).ToArray().Contains(m.MaterialVersionId));
                var segmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => segmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));
                var weekScheduleBomList = new List<WeekScheduleBomEntity>();
                foreach (var item in weekScheduleListUpdate)
                {
                    var verId = item.MaterialVer;
                    var segmentMaterial = segmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == item.MaterialVer);
                    if (segmentMaterial == null)
                        continue;
                    var segmentMaterialStepes = segmentMaterialStepList.Where(m => m.SapSegmentMaterialId == segmentMaterial.ID);
                    var materialList = await _material.FindList(a => segmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                    var materialVersionList = await _materialVersion.FindList(a => materialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                    foreach (var step in segmentMaterialStepes)
                    {
                        var material = materialList.FirstOrDefault(m => m.ID == step.MaterialId);
                        var materialVersion = materialVersionList.FirstOrDefault(m => m.MaterialId == material.ID);
                        var unitId = material?.Unit;
                        var unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                        var bom = new WeekScheduleBomEntity()
                        {
                            WeekScheduleId = item.ID,
                            SegmentId = segmentMaterial.SapSegmentId,
                            SegmentName = segmentList.FirstOrDefault(m => m.ID == segmentMaterial.SapSegmentId).SegmentName,
                            SegmentMaterialId = segmentMaterial.ID,
                            SegmentMaterialStepId = step.ID,
                            MaterialId = step.MaterialId,
                            MaterialCode = material.Code,
                            MaterialName = material.NAME,
                            MaterialVer = materialVersion.ID,
                            MaterialType = material.Type,//原材料、半成品
                            Sort = step.SortOrder,
                            //ParentId = step.ParentId,
                            //InsteadMaterialId = step.InsteadMaterialId,
                            //InsteadMaterialCode = step.InsteadMaterialCode,
                            //InsteadMaterialName = step.InsteadMaterialName,
                            //InsteadMaterialVer = step.InsteadMaterialVer,
                            //InsteadMaterialGroup = step.InsteadMaterialGroup,
                            //InsteadRate = step.InsteadRate,
                            Unit = unit,
                            UnitId = unitId,
                            StandardQuantity = step.ParentQuantity,
                            PlanQuantity = step.Quantity,
                            ActualQuantity = item.PlanQuantity / step.ParentQuantity * step.Quantity,
                            //SapOrderNo = item.SapOrderNo,
                            //SapFeedbackQuantity = step.SapFeedbackQuantity,
                            //SapFeedbackDate = step.SapFeedbackDate,
                            //Remark = step.Remark,
                        };
                        bom.CreateCustomGuid(_user.UserName);
                        weekScheduleBomList.Add(bom);

                        #region 半成品
                        //半成品物料，添加到半成品物料版本集合中，用于处理半成品
                        if (material.Type == "ZSFG")
                        {
                            //获取半成品的工序物料清单
                            var semiSegmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == materialVersion.ID);
                            var semiSegmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => semiSegmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));

                            var semiSegmentMaterial = semiSegmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == materialVersion.ID);
                            if (semiSegmentMaterial == null)
                                continue;
                            var semiSegmentMaterialStepes = semiSegmentMaterialStepList.Where(m => m.SapSegmentMaterialId == semiSegmentMaterial.ID);
                            var semiMaterialList = await _material.FindList(a => semiSegmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                            var semiMaterialVersionList = await _materialVersion.FindList(a => semiMaterialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                            foreach (var s in semiSegmentMaterialStepes)
                            {
                                var semiMaterial = semiMaterialList.FirstOrDefault(m => m.ID == s.MaterialId);
                                var semiMaterialVersion = semiMaterialVersionList.FirstOrDefault(m => m.MaterialId == semiMaterial.ID);
                                unitId = semiMaterial?.Unit;
                                unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                var semiBom = new WeekScheduleBomEntity()
                                {
                                    WeekScheduleId = item.ID,
                                    SegmentId = semiSegmentMaterial.SapSegmentId,
                                    SegmentName = segmentList.FirstOrDefault(m => m.ID == semiSegmentMaterial.SapSegmentId).SegmentName,
                                    SegmentMaterialId = semiSegmentMaterial.ID,
                                    SegmentMaterialStepId = s.ID,
                                    MaterialId = s.MaterialId,
                                    MaterialCode = semiMaterial.Code,
                                    MaterialName = semiMaterial.NAME,
                                    MaterialVer = semiMaterialVersion.ID,
                                    MaterialType = semiMaterial.Type,//原材料、半成品
                                    Sort = s.SortOrder,
                                    ParentId = bom.ID,
                                    //InsteadMaterialId = step.InsteadMaterialId,
                                    //InsteadMaterialCode = step.InsteadMaterialCode,
                                    //InsteadMaterialName = step.InsteadMaterialName,
                                    //InsteadMaterialVer = step.InsteadMaterialVer,
                                    //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                    //InsteadRate = step.InsteadRate,
                                    Unit = unit,
                                    UnitId = unitId,
                                    StandardQuantity = s.ParentQuantity,
                                    PlanQuantity = s.Quantity,
                                    ActualQuantity = item.PlanQuantity / s.ParentQuantity * s.Quantity,
                                    //SapOrderNo = item.SapOrderNo,
                                    //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                    //SapFeedbackDate = step.SapFeedbackDate,
                                    //Remark = step.Remark,
                                };
                                semiBom.CreateCustomGuid(_user.UserName);
                                weekScheduleBomList.Add(semiBom);
                            }
                        }
                        #endregion
                    }
                }
                #endregion

                if (weekScheduleBomList.Count > 0 && await _weekScheduleBom.Add(weekScheduleBomList) != weekScheduleBomList.Count)
                {
                    _unitOfWork.RollbackTran();
                    result.AddError($"Add WeekScheduleBom Error");
                    return result;
                }

                #region 包装计划时，新增ProductOrder和Batch数据
                var productionOrderList = new List<ProductionOrderEntity>();
                var poSegmentRequirementList = new List<PoSegmentRequirementEntity>();
                var poConsumeRequirementList = new List<PoConsumeRequirementEntity>();
                var poProducedRequirementList = new List<PoProducedRequirementEntity>();
                var batchList = new List<SEFA.PPM.Model.Models.BatchEntity>();
                var batchConsumeRequirementList = new List<BatchConsumeRequirementEntity>();
                var batchProducedRequirementList = new List<BatchProducedRequirementEntity>();

                //数据分类是包装计划 &&  计划类型是正常计划 || 重工计划
                var patckingList = weekScheduleListUpdate.Where(m => m.Category == "Packing" && (m.Type == WeekSchedule_Type.Normal.ToString() || m.Type == WeekSchedule_Type.Rework.ToString())).ToList();
                foreach (var item in patckingList)
                {
                    #region ProductOrder
                    var productOrder = new ProductionOrderEntity()
                    {
                        ProductionOrderNo = item.OrderNo + "_01",
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        Type = "WorkOrder",
                        ParentId = item.ID,
                        PlanDate = item.StartWorkday,
                        PrepareShiftid = item.StartShift,
                        PlanQty = (decimal)item.PlanQuantity,
                        PlanStartTime = item.StartWorkday,
                        PlanEndTime = item.FinishWorkday,
                        PoStatus = "2",//已释放
                        SegmentCode = item.Workshop,
                        LineCode = item.LineCode,
                        MesOrderCode = item.OrderNo + "_01",
                        SapOrderType = "ZXH2",
                        Sequence = 1,
                        SapStatus = "C",
                        HasThroat = 0,
                        SapFlag = 0,
                        OrderType = "C",
                    };
                    productOrder.CreateCustomGuid(_user.UserName);
                    productionOrderList.Add(productOrder);
                    //await _productionOrder.Add(productOrder);
                    #endregion

                    #region POSegmentRequirement
                    var bomList = weekScheduleBomList.Where(m => m.WeekScheduleId == item.ID).ToList();
                    var poSegmentIdList = bomList.Select(m => m.SegmentId).Distinct().ToList();
                    foreach (var segmentId in poSegmentIdList)
                    {
                        #region PoSegmentRequirement
                        var poSegmentRequirement = new PoSegmentRequirementEntity()
                        {
                            ProductionOrderId = productOrder.ID,
                            SegmentId = segmentId,
                            Deleted = 0
                        };
                        poSegmentRequirement.CreateCustomGuid(_user.UserName);
                        //await _poSegmentRequirement.Add(poSegmentRequirement);
                        poSegmentRequirementList.Add(poSegmentRequirement);
                        #endregion

                        var bomConsumeList = bomList.Where(m => m.SegmentId == segmentId).ToList();
                        foreach (var consumeMaterial in bomConsumeList)
                        {
                            #region PoConsumeRequirement
                            var poConsumeRequirement = new PoConsumeRequirementEntity()
                            {
                                ProductionOrderId = productOrder.ID,
                                PoSegmentRequirementId = poSegmentRequirement.ID,
                                MaterialId = consumeMaterial.MaterialId,
                                MaterialVersionId = consumeMaterial.MaterialVer,
                                Quantity = consumeMaterial.PlanQuantity,
                                UnitId = consumeMaterial.UnitId,
                                SortOrder = (int)consumeMaterial.Sort,
                                Deleted = 0,
                                WeighingQty = consumeMaterial.PlanQuantity,
                            };
                            poConsumeRequirement.CreateCustomGuid(_user.UserName);
                            //wait _poConsumeRequirement.Add(poConsumeRequirement);
                            poConsumeRequirementList.Add(poConsumeRequirement);
                            #endregion
                        }
                    }
                    #endregion

                    #region poProducedRequirement
                    var poProducedRequirement = new PoProducedRequirementEntity()
                    {
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        Quantity = (decimal)item.PlanQuantity,
                        UnitId = item.UnitId,
                        SortNo = 1,
                        Deleted = 0
                    };
                    poProducedRequirement.CreateCustomGuid(_user.UserName);
                    //await _poProducedRequirement.Add(poProducedRequirement);
                    poProducedRequirementList.Add(poProducedRequirement);
                    #endregion

                    #region Batch
                    var batch = new SEFA.PPM.Model.Models.BatchEntity()
                    {
                        LineId = item.LineId,
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                        PoProducedRequirementId = poProducedRequirement.ID,
                        Number = "1",
                        BatchCode = productOrder.ProductionOrderNo + "_01",
                        MaterialId = item.MaterialId,
                        MaterialVersionId = item.MaterialVer,
                        MaterialCode = item.MaterialCode,
                        MaterialDescription = item.MaterialName,
                        TargetQuantity = item.PlanQuantity,
                        Status = "1",
                        PrepStatus = "1",
                        Deleted = 0
                    };
                    batch.CreateCustomGuid(_user.UserName);
                    //await _batch.Add(batch);
                    batchList.Add(batch);
                    #endregion

                    #region batchconsumeRequirement
                    foreach (var consume in poConsumeRequirementList.Where(m => m.ProductionOrderId == productOrder.ID))
                    {
                        var batchConsumeRequirement = new BatchConsumeRequirementEntity()
                        {
                            BatchId = batch.ID,
                            PoConsumeRequirementId = consume.ID,
                            Quantity = (decimal)consume.Quantity,
                            WeighingQty = (decimal)consume.WeighingQty,
                            Deleted = 0
                        };
                        batchConsumeRequirement.CreateCustomGuid(_user.UserName);
                        //await _batchConsumeRequirement.Add(batchConsumeRequirement);
                        batchConsumeRequirementList.Add(batchConsumeRequirement);
                    }
                    #endregion

                    #region BatchProducedRequirement
                    var batchProducedRequirement = new BatchProducedRequirementEntity()
                    {
                        BatchId = batch.ID,
                        PoProducedRequirementId = poProducedRequirement.ID,
                        Quantity = (decimal)poProducedRequirement.Quantity,
                        Deleted = 0
                    };
                    batchProducedRequirement.CreateCustomGuid(_user.UserName);
                    //await _batchProducedRequirement.Add(batchProducedRequirement);
                    batchProducedRequirementList.Add(batchProducedRequirement);
                    #endregion
                }

                if (productionOrderList.Count > 0)
                    await _productionOrder.Add(productionOrderList);
                if (poSegmentRequirementList.Count > 0)
                    await _poSegmentRequirement.Add(poSegmentRequirementList);
                if (poConsumeRequirementList.Count > 0)
                    await _poConsumeRequirement.Add(poConsumeRequirementList);
                if (poProducedRequirementList.Count > 0)
                    await _poProducedRequirement.Add(poProducedRequirementList);
                if (batchList.Count > 0)
                    await _batch.Add(batchList);
                if (batchConsumeRequirementList.Count > 0)
                    await _batchConsumeRequirement.Add(batchConsumeRequirementList);
                if (batchProducedRequirementList.Count > 0)
                    await _batchProducedRequirement.Add(batchProducedRequirementList);
                #endregion
            }
            else
            {
                _unitOfWork.RollbackTran();
                result.AddError($"Add Week Schedule Error");
                return result;
            }

            _unitOfWork.CommitTran();
            return result;

        }
        #endregion

        #region 替换物料
        public async Task<ResultBool> ChangeMaterial(WeekScheduleBomRequestModel reqModel)
        {
            var result = new ResultBool();
            var insteadMaterial = await _material.FindEntity(x => x.Deleted == 0 && x.ID == reqModel.InsteadMaterialId);
            if (insteadMaterial == null)
            {
                result.AddError($"Not found material {reqModel.InsteadMaterialId}");
                return result;
            }

            var materialVer = await _materialVersion.FindEntity(x => x.Deleted == 0 && x.MaterialId == reqModel.InsteadMaterialId && x.MaterialVersionNumber == "0001");
            if (materialVer == null)
            {
                result.AddError($"Not found material version {reqModel.InsteadMaterialId}");
                return result;
            }

            var bom = await _weekScheduleBom.FindEntity(m => m.ID == reqModel.ID);
            if (bom == null)
            {
                result.AddError($"Not found bom {reqModel.ID}");
                return result;
            }

            var weekSchedule = await _dal.FindEntity(m => m.ID == bom.WeekScheduleId);
            if (weekSchedule == null)
            {
                result.AddError($"Not found week schedule {bom.WeekScheduleId}");
                return result;
            }

            if (weekSchedule.Status != WeekSchedule_Status.NotSplit.ToString())
            {
                result.AddError($"The week schedule {weekSchedule.OrderNo} has been split, can't change material");
                return result;
            }

            if (string.IsNullOrWhiteSpace(bom.InsteadMaterialCode))
            {
                bom.InsteadMaterialId = bom.MaterialId;
                bom.InsteadMaterialCode = bom.MaterialCode;
                bom.InsteadMaterialName = bom.MaterialName;
                bom.InsteadMaterialVer = bom.MaterialVer;
            }
            bom.MaterialId = insteadMaterial.ID;
            bom.MaterialCode = insteadMaterial.Code;
            bom.MaterialName = insteadMaterial.NAME;
            bom.MaterialVer = materialVer.ID;
            bom.Modify(bom.ID, _user.UserName);
            if (!await _weekScheduleBom.Update(bom))
            {
                result.AddError($"BOM update failed");
                return result;
            }

            return result;
        }
        #endregion

        #region 获取批次拆分信息
        public async Task<ResultData<WeekScheduleRequestModel>> GetSplitBatchInfo(WeekScheduleRequestModel wsModel)
        {
            var result = new ResultData<WeekScheduleRequestModel>();
            var where = Expressionable.Create<StandardPeriodLotEntity>()
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.LineCode), a => a.LineCode == wsModel.LineCode)
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.MaterialCode), a => a.MaterialCode == wsModel.MaterialCode)
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.StandardLotType), a => a.Type == wsModel.StandardLotType)
                .ToExpression();
            var standardPeriodLot = await _standardPeriodLot.FindEntity(where);
            if (standardPeriodLot == null)
            {
                result.AddError($"Not found standard period lot; Line:{wsModel.LineCode} MaterialCode:{wsModel.MaterialCode} Type:{wsModel.StandardLotType}");
                return result;
            }
            wsModel.FirstLotPeriod = standardPeriodLot.FirstLotPeriod;
            wsModel.MiddleLotPeriod = standardPeriodLot.MiddleLotPeriod;
            wsModel.LastLotPeriod = standardPeriodLot.LastLotPeriod;
            wsModel.LotQuantity = standardPeriodLot.PlanQuantity;
            wsModel.MaxLotQuantity = standardPeriodLot.MaxLotQuantity;
            wsModel.MinLotQuantity = standardPeriodLot.MinLotQuantity;
            //计算批次数量
            var count = (int)Math.Round((decimal)wsModel.PlanQuantity / (decimal)standardPeriodLot.PlanQuantity);
            var mod = (decimal)wsModel.PlanQuantity % (decimal)standardPeriodLot.PlanQuantity;
            if (mod > 0)
                count += 1;
            wsModel.LotCount = count;

            var weekSchedule = await _dal.FindEntity(m => m.ID == wsModel.ID);
            if (weekSchedule == null)
            {
                result.AddError($"Not found week schedule {wsModel.OrderNo}");
                return result;
            }

            var weekScheduleBom = await _weekScheduleBom.FindList(m => m.WeekScheduleId == wsModel.ID);
            wsModel.wsBomModels = _mapper.Map<List<WeekScheduleBomRequestModel>>(weekScheduleBom);

            result.Data = wsModel;

            return result;
        }
        #endregion

        #region 获取新增批次工单信息
        public async Task<ResultData<WeekScheduleRequestModel>> GetAddBatchInfo(ProductionOrderRequestModel wsModel)
        {
            var result = new ResultData<WeekScheduleRequestModel>();
            var data = new WeekScheduleRequestModel();

            var where = Expressionable.Create<StandardPeriodLotEntity>()
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.LineCode), a => a.LineCode == wsModel.LineCode)
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.MaterialCode), a => a.MaterialCode == wsModel.MaterialCode)
                .AndIF(!string.IsNullOrWhiteSpace(wsModel.StandardLotType), a => a.Type == wsModel.StandardLotType)
                .ToExpression();
            var standardPeriodLot = await _standardPeriodLot.FindEntity(where);
            if (standardPeriodLot == null)
            {
                result.AddError($"Not found standard period lot; Line:{wsModel.LineCode} MaterialCode:{wsModel.MaterialCode} Type:{wsModel.StandardLotType}");
                return result;
            }
            data.FirstLotPeriod = standardPeriodLot.FirstLotPeriod;
            data.MiddleLotPeriod = standardPeriodLot.MiddleLotPeriod;
            data.LastLotPeriod = standardPeriodLot.LastLotPeriod;
            data.LotQuantity = standardPeriodLot.PlanQuantity;
            data.MaxLotQuantity = standardPeriodLot.MaxLotQuantity;
            data.MinLotQuantity = standardPeriodLot.MinLotQuantity;

            var weekSchedule = await _dal.FindEntity(m => m.ID == wsModel.ParentId);
            if (weekSchedule == null)
            {
                result.AddError($"Not found week schedule {wsModel.ProductionOrderNo}");
                return result;
            }

            //计算批次数量
            var count = (int)Math.Round((decimal)weekSchedule.PlanQuantity / (decimal)standardPeriodLot.PlanQuantity);
            var mod = (decimal)weekSchedule.PlanQuantity % (decimal)standardPeriodLot.PlanQuantity;
            if (mod > 0)
                count += 1;
            data.LotCount = count;


            var weekScheduleBom = await _weekScheduleBom.FindList(m => m.WeekScheduleId == wsModel.ID);
            data.wsBomModels = _mapper.Map<List<WeekScheduleBomRequestModel>>(weekScheduleBom);

            result.Data = data;

            return result;
        }
        #endregion

        #region 批次拆分
        public async Task<ResultBool> SplitBatch(WeekScheduleRequestModel wsModel)
        {
            var result = new ResultBool();
            var wsBomModels = wsModel.wsBomModels;
            var weekSchedule = await _dal.FindEntity(m => m.ID == wsModel.ID);
            if (weekSchedule == null)
            {
                result.AddError($"Not found week schedule {wsModel.OrderNo}");
                return result;
            }

            #region ProductOrder
            var productOrder = new ProductionOrderEntity()
            {
                ProductionOrderNo = weekSchedule.OrderNo + "_01",
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                Type = "WorkOrder",
                ParentId = weekSchedule.ID,
                PlanDate = weekSchedule.StartWorkday,
                PrepareShiftid = weekSchedule.StartShift,
                PlanQty = (decimal)weekSchedule.PlanQuantity,
                PlanStartTime = weekSchedule.StartWorkday,
                PlanEndTime = weekSchedule.FinishWorkday,
                PoStatus = "2",//已释放
                SegmentCode = weekSchedule.Workshop,
                LineCode = weekSchedule.LineCode,
                MesOrderCode = weekSchedule.OrderNo + "_01",
                SapOrderType = "ZXH2",
                Sequence = 1,
                SapStatus = "C",
                HasThroat = 0,
                SapFlag = 0,
                OrderType = "C",
            };
            productOrder.CreateCustomGuid(_user.UserName);
            await _productionOrder.Add(productOrder);
            #endregion

            #region POSegmentRequirement
            var bomList = await _weekScheduleBom.FindList(m => m.WeekScheduleId == wsModel.ID);
            var poSegmentIdList = bomList.Select(m => m.SegmentId).Distinct().ToList();
            var poConsumeRequirementList = new List<PoConsumeRequirementEntity>();
            foreach (var segmentId in poSegmentIdList)
            {
                #region PoSegmentRequirement
                var poSegmentRequirement = new PoSegmentRequirementEntity()
                {
                    ProductionOrderId = productOrder.ID,
                    SegmentId = segmentId,
                    Deleted = 0
                };
                poSegmentRequirement.CreateCustomGuid(_user.UserName);
                await _poSegmentRequirement.Add(poSegmentRequirement);
                #endregion

                var bomConsumeList = bomList.Where(m => m.SegmentId == segmentId).ToList();
                foreach (var consumeMaterial in bomConsumeList)
                {
                    #region PoConsumeRequirement
                    var poConsumeRequirement = new PoConsumeRequirementEntity()
                    {
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirement.ID,
                        MaterialId = consumeMaterial.MaterialId,
                        MaterialVersionId = consumeMaterial.MaterialVer,
                        Quantity = consumeMaterial.PlanQuantity,
                        UnitId = consumeMaterial.UnitId,
                        SortOrder = (int)consumeMaterial.Sort,
                        Deleted = 0,
                        WeighingQty = consumeMaterial.PlanQuantity,
                    };
                    poConsumeRequirement.CreateCustomGuid(_user.UserName);
                    poConsumeRequirementList.Add(poConsumeRequirement);
                    await _poConsumeRequirement.Add(poConsumeRequirement);
                    #endregion
                }
            }

            var segmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == weekSchedule.MaterialVer);
            var producedPoSegmentRequirement = new PoSegmentRequirementEntity()
            {
                ProductionOrderId = productOrder.ID,
                SegmentId = segmentMaterialList.FirstOrDefault().SapSegmentId,
                Deleted = 0
            };
            producedPoSegmentRequirement.CreateCustomGuid(_user.UserName);
            await _poSegmentRequirement.Add(producedPoSegmentRequirement);
            #endregion

            #region poProducedRequirement
            var poProducedRequirement = new PoProducedRequirementEntity()
            {
                ProductionOrderId = productOrder.ID,
                PoSegmentRequirementId = producedPoSegmentRequirement.ID,
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                Quantity = (decimal)weekSchedule.PlanQuantity,
                UnitId = weekSchedule.UnitId,
                SortNo = 1,
                Deleted = 0
            };
            poProducedRequirement.CreateCustomGuid(_user.UserName);
            await _poProducedRequirement.Add(poProducedRequirement);
            #endregion

            #region Batch
            var batch = new SEFA.PPM.Model.Models.BatchEntity()
            {
                LineId = weekSchedule.LineId,
                ProductionOrderId = productOrder.ID,
                PoSegmentRequirementId = producedPoSegmentRequirement.ID,
                PoProducedRequirementId = poProducedRequirement.ID,
                Number = "1",
                BatchCode = productOrder.ProductionOrderNo + "_01",
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                MaterialCode = weekSchedule.MaterialCode,
                MaterialDescription = weekSchedule.MaterialName,
                TargetQuantity = weekSchedule.PlanQuantity,
                Status = "1",
                PrepStatus = "1",
                Deleted = 0
            };
            batch.CreateCustomGuid(_user.UserName);
            await _batch.Add(batch);
            #endregion

            #region batchconsumeRequirement
            foreach (var consume in poConsumeRequirementList.Where(m => m.ProductionOrderId == productOrder.ID))
            {
                var batchConsumeRequirement = new BatchConsumeRequirementEntity()
                {
                    BatchId = batch.ID,
                    PoConsumeRequirementId = consume.ID,
                    Quantity = (decimal)consume.Quantity,
                    WeighingQty = (decimal)consume.WeighingQty,
                    Deleted = 0
                };
                batchConsumeRequirement.CreateCustomGuid(_user.UserName);
                await _batchConsumeRequirement.Add(batchConsumeRequirement);
            }
            #endregion

            #region BatchProducedRequirement
            var batchProducedRequirement = new BatchProducedRequirementEntity()
            {
                BatchId = batch.ID,
                PoProducedRequirementId = poProducedRequirement.ID,
                Quantity = (decimal)poProducedRequirement.Quantity,
                Deleted = 0
            };
            batchProducedRequirement.CreateCustomGuid(_user.UserName);
            await _batchProducedRequirement.Add(batchProducedRequirement);
            #endregion

            return result;
        }
        #endregion

        #region 新增批次工单
        public async Task<ResultBool> AddBatch(WeekScheduleRequestModel wsModel)
        {
            var result = new ResultBool();
            var wsBomModels = wsModel.wsBomModels;
            var weekSchedule = await _dal.FindEntity(m => m.ID == wsModel.ID);
            if (weekSchedule == null)
            {
                result.AddError($"Not found week schedule {wsModel.ID}");
                return result;
            }

            #region ProductOrder
            var productOrder = new ProductionOrderEntity()
            {
                ProductionOrderNo = weekSchedule.OrderNo + "_01",
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                Type = "WorkOrder",
                ParentId = weekSchedule.ID,
                PlanDate = weekSchedule.StartWorkday,
                PrepareShiftid = weekSchedule.StartShift,
                PlanQty = (decimal)weekSchedule.PlanQuantity,
                PlanStartTime = weekSchedule.StartWorkday,
                PlanEndTime = weekSchedule.FinishWorkday,
                PoStatus = "2",//已释放
                SegmentCode = weekSchedule.Workshop,
                LineCode = weekSchedule.LineCode,
                MesOrderCode = weekSchedule.OrderNo + "_01",
                SapOrderType = "ZXH2",
                Sequence = 1,
                SapStatus = "C",
                HasThroat = 0,
                SapFlag = 0,
                OrderType = "C",
            };
            productOrder.CreateCustomGuid(_user.UserName);
            await _productionOrder.Add(productOrder);
            #endregion

            #region POSegmentRequirement
            var bomList = await _weekScheduleBom.FindList(m => m.WeekScheduleId == wsModel.ID);
            var poSegmentIdList = bomList.Select(m => m.SegmentId).Distinct().ToList();
            var poConsumeRequirementList = new List<PoConsumeRequirementEntity>();
            foreach (var segmentId in poSegmentIdList)
            {
                #region PoSegmentRequirement
                var poSegmentRequirement = new PoSegmentRequirementEntity()
                {
                    ProductionOrderId = productOrder.ID,
                    SegmentId = segmentId,
                    Deleted = 0
                };
                poSegmentRequirement.CreateCustomGuid(_user.UserName);
                await _poSegmentRequirement.Add(poSegmentRequirement);
                #endregion

                var bomConsumeList = bomList.Where(m => m.SegmentId == segmentId).ToList();
                foreach (var consumeMaterial in bomConsumeList)
                {
                    #region PoConsumeRequirement
                    var poConsumeRequirement = new PoConsumeRequirementEntity()
                    {
                        ProductionOrderId = productOrder.ID,
                        PoSegmentRequirementId = poSegmentRequirement.ID,
                        MaterialId = consumeMaterial.MaterialId,
                        MaterialVersionId = consumeMaterial.MaterialVer,
                        Quantity = consumeMaterial.PlanQuantity,
                        UnitId = consumeMaterial.UnitId,
                        SortOrder = (int)consumeMaterial.Sort,
                        Deleted = 0,
                        WeighingQty = consumeMaterial.PlanQuantity,
                    };
                    poConsumeRequirement.CreateCustomGuid(_user.UserName);
                    poConsumeRequirementList.Add(poConsumeRequirement);
                    await _poConsumeRequirement.Add(poConsumeRequirement);
                    #endregion
                }
            }

            var segmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == weekSchedule.MaterialVer);
            var producedPoSegmentRequirement = new PoSegmentRequirementEntity()
            {
                ProductionOrderId = productOrder.ID,
                SegmentId = segmentMaterialList.FirstOrDefault().SapSegmentId,
                Deleted = 0
            };
            producedPoSegmentRequirement.CreateCustomGuid(_user.UserName);
            await _poSegmentRequirement.Add(producedPoSegmentRequirement);
            #endregion

            #region poProducedRequirement
            var poProducedRequirement = new PoProducedRequirementEntity()
            {
                ProductionOrderId = productOrder.ID,
                PoSegmentRequirementId = producedPoSegmentRequirement.ID,
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                Quantity = (decimal)weekSchedule.PlanQuantity,
                UnitId = weekSchedule.UnitId,
                SortNo = 1,
                Deleted = 0
            };
            poProducedRequirement.CreateCustomGuid(_user.UserName);
            await _poProducedRequirement.Add(poProducedRequirement);
            #endregion

            #region Batch
            var batch = new SEFA.PPM.Model.Models.BatchEntity()
            {
                LineId = weekSchedule.LineId,
                ProductionOrderId = productOrder.ID,
                PoSegmentRequirementId = producedPoSegmentRequirement.ID,
                PoProducedRequirementId = poProducedRequirement.ID,
                Number = "1",
                BatchCode = productOrder.ProductionOrderNo + "_01",
                MaterialId = weekSchedule.MaterialId,
                MaterialVersionId = weekSchedule.MaterialVer,
                MaterialCode = weekSchedule.MaterialCode,
                MaterialDescription = weekSchedule.MaterialName,
                TargetQuantity = weekSchedule.PlanQuantity,
                Status = "1",
                PrepStatus = "1",
                Deleted = 0
            };
            batch.CreateCustomGuid(_user.UserName);
            await _batch.Add(batch);
            #endregion

            #region batchconsumeRequirement
            foreach (var consume in poConsumeRequirementList.Where(m => m.ProductionOrderId == productOrder.ID))
            {
                var batchConsumeRequirement = new BatchConsumeRequirementEntity()
                {
                    BatchId = batch.ID,
                    PoConsumeRequirementId = consume.ID,
                    Quantity = (decimal)consume.Quantity,
                    WeighingQty = (decimal)consume.WeighingQty,
                    Deleted = 0
                };
                batchConsumeRequirement.CreateCustomGuid(_user.UserName);
                await _batchConsumeRequirement.Add(batchConsumeRequirement);
            }
            #endregion

            #region BatchProducedRequirement
            var batchProducedRequirement = new BatchProducedRequirementEntity()
            {
                BatchId = batch.ID,
                PoProducedRequirementId = poProducedRequirement.ID,
                Quantity = (decimal)poProducedRequirement.Quantity,
                Deleted = 0
            };
            batchProducedRequirement.CreateCustomGuid(_user.UserName);
            await _batchProducedRequirement.Add(batchProducedRequirement);
            #endregion

            return result;
        }
        #endregion

        #region DCS下载工单
        public async Task<ResultBool> DcsDownload([FromBody] ProductionOrderModel reqModel)
        {
            var result = new ResultBool();


            return result;
        }
        #endregion

        #region 导入数据
        /// <summary>
        /// 导入数据
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        public async Task<ResultString> ImportData(Stream stream)
        {
            ResultString result = new ResultString();
            XSSFWorkbook xssWorkbook = null;
            ISheet sheet;
            try
            {
                _unitOfWork.BeginTran();

                #region 初始化
                var unitList = await _unit.FindList(x => x.Deleted == 0);
                var equipmentList = await _equipment.FindList(x => x.Deleted == 0);
                var itemList = await _dataItem.FindList(x => x.Deleted == 0);
                stream.Position = 0;
                xssWorkbook = new XSSFWorkbook(stream);
                sheet = xssWorkbook.GetSheetAt(0);

                DateTime? firstDate = GetCell(sheet, 3, 9).DateCellValue;
                if (firstDate == null)
                {
                    _unitOfWork.RollbackTran();
                    result.AddError($"Not found first date,pls check it.");
                    return result;
                }

                DateTime? lastDate = GetCell(sheet, 3, sheet.GetRow(3).LastCellNum - 1).DateCellValue;
                if (firstDate == null)
                {
                    _unitOfWork.RollbackTran();
                    result.AddError($"Not found last date,pls check it.");
                    return result;
                }

                //获取已存在的数据
                var existAllPlanList = await _dal.FindList(m => m.StartWorkday >= firstDate && m.StartWorkday <= lastDate);
                existAllPlanList = existAllPlanList.OrderBy(m => m.LineCode).OrderBy(m => m.MaterialCode).OrderBy(m => m.Factory).OrderBy(m => m.StartWorkday).ToList();

                List<WeekScheduleEntity> weekScheduleList = new List<WeekScheduleEntity>();
                var weekScheduleListAdd = new List<WeekScheduleEntity>();
                var weekScheduleListUpdate = new List<WeekScheduleEntity>();
                var weekScheduleListDelete = new List<WeekScheduleEntity>();
                #endregion

                //从第7行开始读取数据
                for (int i = 6; i <= sheet.LastRowNum; i++)
                {
                    #region 读取数据
                    IRow row = sheet.GetRow(i);
                    //如果未取到行数据，则跳过
                    if (row == null) continue;

                    //如果是空白行，则跳过
                    if (row.Cells.All(d => d.CellType == CellType.Blank)) continue;

                    if (row.Cells.Skip(8).All(d => d.CellType == CellType.Blank)) continue;

                    string workshop = GetCell(row, 0).ToString();    //车间
                    string line = GetCell(row, 1).ToString();        //线别
                    var lineEntity = equipmentList.FirstOrDefault(m => m.EquipmentName == line && m.Deleted == 0);
                    if (lineEntity == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Not found line code {line}");
                        return result;
                    }
                    string lineId = lineEntity.ID;                   //线别ID
                    string plant = GetCell(row, 3).ToString();       //工厂代码
                    string productCode = GetCell(row, 2).ToString(); //产品代码
                    var productEntity = (await _material.FindEntity(m => m.Code == productCode && m.Plant == plant && m.Deleted == 0));
                    if (productEntity == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Not found product code {productCode}");
                        return result;
                    }
                    string productId = productEntity.ID;             //产品代码
                    string productName = productEntity.Description;  //产品名称
                    var matVer = await _materialVersion.FindEntity(m => m.MaterialId == productId && m.MaterialVersionNumber == "0001" && m.Deleted == 0);
                    if (matVer == null)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Not found material 【{productCode}】 version 【0001】");
                        return result;
                    }
                    string productVerId = matVer.ID;                 //产品版本
                    string packageSize = GetCell(row, 4).ToString(); //包装尺寸
                    string product = GetCell(row, 5).StringCellValue;     //产品名称
                    string designCode = GetCell(row, 6).StringCellValue;  //设计代码
                    string unit = GetCell(row, 7).ToString();        //单位
                    var unitEntity = unitList.FirstOrDefault(m => m.Name == unit && m.Deleted == 0);
                    //if (unitEntity == null)
                    //{
                    //    _unitOfWork.RollbackTran();
                    //    result.AddError($"Not found unit {unit}");
                    //    return result;
                    //}
                    string unitId = unitEntity?.ID;                  //单位
                    string output = GetCell(row, 8).ToString();      //产出量
                    double? qty = null;
                    DateTime? startDate = null;
                    DateTime? endDate = null;
                    string startShift = null;
                    string endShift = null;

                    //获取计划数据
                    var existRowPlanList = existAllPlanList?.FindAll(m => m.LineCode == line && m.MaterialCode == productCode && m.Factory == plant);

                    #endregion

                    //从J列开始读取数据
                    for (int j = 9; j < row.LastCellNum; j++)
                    {
                        var cell = GetCell(row, j);
                        if (cell == null)//如果单元格为空，则跳过
                            continue;

                        if (cell.CellStyle.FillForegroundColorColor == null)//如果单元格背景色为空，则跳过
                            continue;

                        var backgroundColor = ((XSSFColor)cell.CellStyle.FillForegroundColorColor);

                        //如果单元格颜色为正常工单
                        #region 正常工单处理                        
                        if (CompareColor(backgroundColor, Color_Normal))
                        {
                            //如果单元格不为空&&未设置数量，则获取单元格值
                            if (cell.IsNotEmptyOrNull() && qty == null)
                            {
                                qty = cell.NumericCellValue;
                                startDate = GetCell(sheet, 3, j).DateCellValue;
                                startShift = GetCell(sheet, 5, j).ToString();
                                endDate = GetCell(sheet, 3, j).DateCellValue;
                                endShift = GetCell(sheet, 5, j).ToString();
                            }

                            //查找当前工单结束时间
                            while (j < row.LastCellNum)
                            {
                                cell = GetCell(row, ++j);
                                backgroundColor = ((XSSFColor)cell.CellStyle.FillForegroundColorColor);
                                if ((CompareColor(backgroundColor, Color_Normal) || CompareColor(backgroundColor, Color_Holiday)) && !cell.IsNotEmptyOrNull() && qty != null)
                                {
                                    if (CompareColor(backgroundColor, Color_Normal))
                                    {
                                        endDate = GetCell(sheet, 3, j).DateCellValue;
                                        endShift = GetCell(sheet, 5, j).ToString();
                                    }
                                }
                                else
                                {
                                    j--;
                                    break;
                                }
                            }

                            WeekScheduleEntity entity = new WeekScheduleEntity()
                            {
                                OrderNo = GetPlanOrderNo(line),
                                Factory = plant,
                                Workshop = workshop,
                                Category = workshop.ToLower().Contains("formulation") ? "Formulation" : "Packing",
                                LineId = lineId,
                                LineCode = line,
                                MaterialId = productId,
                                MaterialCode = productCode,
                                MaterialName = product,
                                MaterialVer = productVerId,
                                PackSizeId = null,
                                PackSize = packageSize,
                                DesignCode = designCode,
                                UnitId = unitId,
                                Unit = unit,
                                Output = output,
                                StartWorkday = (DateTime)startDate,
                                StartShift = startShift,
                                StartCalendarId = null,
                                StartShiftId = null,
                                FinishWorkday = (DateTime)endDate,
                                FinishShift = endShift,
                                FinishCalendarId = null,
                                FinishShiftId = null,
                                PlanQuantity = (decimal?)qty,
                                Type = WeekSchedule_Type.Normal.ToString(),
                                Status = WeekSchedule_Status.NotSplit.ToString(),
                            };
                            entity.CreateCustomGuid(_user.UserName);
                            weekScheduleList.Add(entity);
                            qty = null;
                            continue;
                        }
                        #endregion
                        //如果单元格颜色为CIP
                        #region CIP工单处理                        
                        else if (CompareColor(backgroundColor, Color_CIP))
                        {
                            //如果单元格不为空&&未设置数量，则获取单元格值
                            if (!cell.IsNotEmptyOrNull() && qty == null)
                            {
                                qty = cell.NumericCellValue;
                                startDate = GetCell(sheet, 3, j).DateCellValue;
                                startShift = GetCell(sheet, 5, j).ToString();
                                endDate = GetCell(sheet, 3, j).DateCellValue;
                                endShift = GetCell(sheet, 5, j).ToString();
                            }

                            //查找当前工单结束时间
                            while (j < row.LastCellNum)
                            {
                                cell = GetCell(row, ++j);
                                backgroundColor = ((XSSFColor)cell.CellStyle.FillForegroundColorColor);
                                if (CompareColor(backgroundColor, Color_CIP) || CompareColor(backgroundColor, Color_Holiday))
                                {
                                    if (CompareColor(backgroundColor, Color_CIP))
                                    {
                                        endDate = GetCell(sheet, 3, j).DateCellValue;
                                        endShift = GetCell(sheet, 5, j).ToString();
                                    }
                                }
                                else
                                {
                                    j--;
                                    break;
                                }
                            }

                            WeekScheduleEntity entity = new WeekScheduleEntity()
                            {
                                OrderNo = GetPlanOrderNo(line),
                                Factory = plant,
                                Workshop = workshop,
                                Category = workshop.ToLower().Contains("formulation") ? "Formulation" : "Packing",
                                LineId = lineId,
                                LineCode = line,
                                MaterialId = productId,
                                MaterialCode = productCode,
                                MaterialName = product,
                                MaterialVer = productVerId,
                                PackSizeId = null,
                                PackSize = packageSize,
                                DesignCode = designCode,
                                UnitId = unitId,
                                Unit = unit,
                                Output = output,
                                StartWorkday = (DateTime)startDate,
                                StartShift = startShift,
                                StartCalendarId = null,
                                StartShiftId = null,
                                FinishWorkday = (DateTime)endDate,
                                FinishShift = endShift,
                                FinishCalendarId = null,
                                FinishShiftId = null,
                                PlanQuantity = (decimal?)qty,
                                Type = WeekSchedule_Type.CIP.ToString(),
                                Status = WeekSchedule_Status.NotSplit.ToString(),
                            };
                            entity.CreateCustomGuid(_user.UserName);
                            weekScheduleList.Add(entity);
                            qty = null;
                            continue;
                        }
                        #endregion
                        //如果单元格颜色为节假日
                        #region 节假日处理
                        else if (CompareColor(backgroundColor, Color_Holiday))
                        {
                            continue;
                        }
                        #endregion
                        //如果单元格颜色为返工
                        #region 返工工单处理
                        else if (CompareColor(backgroundColor, Color_Rework))
                        {
                            //如果单元格不为空&&未设置数量，则获取单元格值
                            if (cell.IsNotEmptyOrNull() && qty == null)
                            {
                                qty = cell.NumericCellValue;
                                startDate = GetCell(sheet, 3, j).DateCellValue;
                                startShift = GetCell(sheet, 5, j).ToString();
                                endDate = GetCell(sheet, 3, j).DateCellValue;
                                endShift = GetCell(sheet, 5, j).ToString();
                            }

                            //查找当前工单结束时间
                            while (j < row.LastCellNum)
                            {
                                cell = GetCell(row, ++j);
                                backgroundColor = ((XSSFColor)cell.CellStyle.FillForegroundColorColor);
                                if ((CompareColor(backgroundColor, Color_Rework) || CompareColor(backgroundColor, Color_Holiday)) && !cell.IsNotEmptyOrNull() && qty != null)
                                {
                                    if (CompareColor(backgroundColor, Color_Rework))
                                    {
                                        endDate = GetCell(sheet, 3, j).DateCellValue;
                                        endShift = GetCell(sheet, 5, j).ToString();
                                    }
                                }
                                else
                                {
                                    j--;
                                    break;
                                }
                            }

                            WeekScheduleEntity entity = new WeekScheduleEntity()
                            {
                                OrderNo = GetPlanOrderNo(line),
                                Factory = plant,
                                Workshop = workshop,
                                Category = workshop.ToLower().Contains("formulation") ? "Formulation" : "Packing",
                                LineId = lineId,
                                LineCode = line,
                                MaterialId = productId,
                                MaterialCode = productCode,
                                MaterialName = product,
                                MaterialVer = productVerId,
                                PackSizeId = null,
                                PackSize = packageSize,
                                DesignCode = designCode,
                                UnitId = unitId,
                                Unit = unit,
                                Output = output,
                                StartWorkday = (DateTime)startDate,
                                StartShift = startShift,
                                StartCalendarId = null,
                                StartShiftId = null,
                                FinishWorkday = (DateTime)endDate,
                                FinishShift = endShift,
                                FinishCalendarId = null,
                                FinishShiftId = null,
                                PlanQuantity = (decimal?)qty,
                                Type = WeekSchedule_Type.Rework.ToString(),
                                Status = WeekSchedule_Status.NotSplit.ToString(),
                            };
                            entity.CreateCustomGuid(_user.UserName);
                            weekScheduleList.Add(entity);
                            qty = null;
                            continue;
                        }
                        #endregion
                    }
                }

                #region 判断如何数据处理                
                if (existAllPlanList.Count == 0)//第一次导入计划，新增数据
                {
                    weekScheduleListAdd.AddRange(weekScheduleList);
                }
                else
                {
                    weekScheduleList = weekScheduleList.OrderBy(m => m.LineCode).ThenBy(m => m.MaterialCode).ThenBy(m => m.Factory).ThenBy(m => m.StartWorkday).ToList();
                    var groupList = weekScheduleList.GroupBy(m => new { m.LineCode, m.MaterialCode, m.Factory }).ToList();
                    foreach (var item in groupList)
                    {
                        //获取同一行的计划
                        var oldPlanRowList = existAllPlanList.FindAll(m => m.LineCode == item.Key.LineCode && m.MaterialCode == item.Key.MaterialCode && m.Factory == item.Key.Factory).OrderBy(m => m.StartWorkday).ToList();
                        var newPlanRowList = weekScheduleList.FindAll(m => m.LineCode == item.Key.LineCode && m.MaterialCode == item.Key.MaterialCode && m.Factory == item.Key.Factory).OrderBy(m => m.StartWorkday).ToList();

                        //旧计划不存在，则新增
                        if (oldPlanRowList.Count == 0)
                        {
                            weekScheduleListAdd.AddRange(newPlanRowList);
                            continue;
                        }

                        foreach (var oldItem in oldPlanRowList)
                        {
                            var exist = newPlanRowList.Where(m => (oldItem.StartWorkday <= m.StartWorkday && oldItem.FinishWorkday >= m.StartWorkday)
                                                                || (oldItem.StartWorkday <= m.FinishWorkday && oldItem.FinishWorkday >= m.FinishWorkday)).OrderBy(m => m.StartWorkday);
                            //旧计划在新计划中不存在，则删除旧计划
                            if (exist.Count() == 0)
                            {
                                weekScheduleListDelete.Add(oldItem);
                            }
                            //旧计划在新计划中存在，则更新旧计划
                            else if (exist.Count() == 1)
                            {
                                oldItem.PackSize = exist.First().PackSize;
                                oldItem.DesignCode = exist.First().DesignCode;
                                oldItem.Unit = exist.First().Unit;
                                oldItem.UnitId = exist.First().UnitId;
                                oldItem.Output = exist.First().Output;
                                oldItem.PlanQuantity = exist.First().PlanQuantity;
                                oldItem.StartWorkday = exist.First().StartWorkday;
                                oldItem.StartShift = exist.First().StartShift;
                                oldItem.StartShiftId = exist.First().StartShiftId;
                                oldItem.FinishWorkday = exist.First().FinishWorkday;
                                oldItem.FinishShift = exist.First().FinishShift;
                                oldItem.FinishShiftId = exist.First().FinishShiftId;
                                oldItem.Type = exist.First().Type;
                                oldItem.Status = exist.First().Status;
                                oldItem.Modify(oldItem.ID, _user.UserName);
                                weekScheduleListUpdate.Add(oldItem);
                            }
                            //处理多条计划
                            else if (exist.Count() > 1)
                            {
                                //修改第一个旧计划
                                oldItem.PackSize = exist.First().PackSize;
                                oldItem.DesignCode = exist.First().DesignCode;
                                oldItem.Unit = exist.First().Unit;
                                oldItem.UnitId = exist.First().UnitId;
                                oldItem.Output = exist.First().Output;
                                oldItem.PlanQuantity = exist.First().PlanQuantity;
                                oldItem.StartWorkday = exist.First().StartWorkday;
                                oldItem.StartShift = exist.First().StartShift;
                                oldItem.StartShiftId = exist.First().StartShiftId;
                                oldItem.FinishWorkday = exist.First().FinishWorkday;
                                oldItem.FinishShift = exist.First().FinishShift;
                                oldItem.FinishShiftId = exist.First().FinishShiftId;
                                oldItem.Type = exist.First().Type;
                                oldItem.Status = exist.First().Status;
                                oldItem.Modify(oldItem.ID, _user.UserName);
                                weekScheduleListUpdate.Add(oldItem);

                                //新增多条计划
                                exist.Skip(1).ToList().ForEach(m => weekScheduleList.Add(m));
                            }
                        }
                    }
                }
                #endregion

                #region 新增、更新、删除周计划和周计划BOM
                #region 新增周计划
                if (weekScheduleListAdd.Count > 0)
                {
                    var add = await _dal.Add(weekScheduleListAdd);
                    //添加成功，则新增周计划BOM
                    if (add == weekScheduleListAdd.Count)
                    {
                        #region 产品
                        //获取产品的工序物料清单
                        var segmentList = await _sapSegment.FindList(m => m.Deleted == 0);
                        var segmentMaterialList = await _sapSegmentMaterial.FindList(m => weekScheduleListAdd.Select(m => m.MaterialVer).ToArray().Contains(m.MaterialVersionId));
                        var segmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => segmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));
                        var weekScheduleBomList = new List<WeekScheduleBomEntity>();
                        foreach (var item in weekScheduleListAdd)
                        {
                            var verId = item.MaterialVer;
                            var segmentMaterial = segmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == item.MaterialVer);
                            if (segmentMaterial == null)
                                continue;
                            var segmentMaterialStepes = segmentMaterialStepList.Where(m => m.SapSegmentMaterialId == segmentMaterial.ID);
                            var materialList = await _material.FindList(a => segmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                            var materialVersionList = await _materialVersion.FindList(a => materialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                            foreach (var step in segmentMaterialStepes)
                            {
                                var material = materialList.FirstOrDefault(m => m.ID == step.MaterialId);
                                var materialVersion = materialVersionList.FirstOrDefault(m => m.MaterialId == material.ID);
                                var unitId = material?.Unit;
                                var unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                var bom = new WeekScheduleBomEntity()
                                {
                                    WeekScheduleId = item.ID,
                                    SegmentId = segmentMaterial.SapSegmentId,
                                    SegmentName = segmentList.FirstOrDefault(m => m.ID == segmentMaterial.SapSegmentId).SegmentName,
                                    SegmentMaterialId = segmentMaterial.ID,
                                    SegmentMaterialStepId = step.ID,
                                    MaterialId = step.MaterialId,
                                    MaterialCode = material.Code,
                                    MaterialName = material.NAME,
                                    MaterialVer = materialVersion.ID,
                                    MaterialType = material.Type,//原材料、半成品
                                    Sort = step.SortOrder,
                                    //ParentId = step.ParentId,
                                    //InsteadMaterialId = step.InsteadMaterialId,
                                    //InsteadMaterialCode = step.InsteadMaterialCode,
                                    //InsteadMaterialName = step.InsteadMaterialName,
                                    //InsteadMaterialVer = step.InsteadMaterialVer,
                                    //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                    //InsteadRate = step.InsteadRate,
                                    Unit = unit,
                                    UnitId = unitId,
                                    StandardQuantity = step.ParentQuantity,
                                    PlanQuantity = step.Quantity,
                                    ActualQuantity = item.PlanQuantity / step.ParentQuantity * step.Quantity,
                                    //SapOrderNo = item.SapOrderNo,
                                    //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                    //SapFeedbackDate = step.SapFeedbackDate,
                                    //Remark = step.Remark,
                                };
                                bom.CreateCustomGuid(_user.UserName);
                                weekScheduleBomList.Add(bom);

                                #region 半成品
                                //半成品物料，添加到半成品物料版本集合中，用于处理半成品
                                if (material.Type == "ZSFG")
                                {
                                    //获取半成品的工序物料清单
                                    var semiSegmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == materialVersion.ID);
                                    var semiSegmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => semiSegmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));

                                    var semiSegmentMaterial = semiSegmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == materialVersion.ID);
                                    if (semiSegmentMaterial == null)
                                        continue;
                                    var semiSegmentMaterialStepes = semiSegmentMaterialStepList.Where(m => m.SapSegmentMaterialId == semiSegmentMaterial.ID);
                                    var semiMaterialList = await _material.FindList(a => semiSegmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                                    var semiMaterialVersionList = await _materialVersion.FindList(a => semiMaterialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                                    foreach (var s in semiSegmentMaterialStepes)
                                    {
                                        var semiMaterial = semiMaterialList.FirstOrDefault(m => m.ID == s.MaterialId);
                                        var semiMaterialVersion = semiMaterialVersionList.FirstOrDefault(m => m.MaterialId == semiMaterial.ID);
                                        unitId = semiMaterial?.Unit;
                                        unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                        var semiBom = new WeekScheduleBomEntity()
                                        {
                                            WeekScheduleId = item.ID,
                                            SegmentId = semiSegmentMaterial.SapSegmentId,
                                            SegmentName = segmentList.FirstOrDefault(m => m.ID == semiSegmentMaterial.SapSegmentId).SegmentName,
                                            SegmentMaterialId = semiSegmentMaterial.ID,
                                            SegmentMaterialStepId = s.ID,
                                            MaterialId = s.MaterialId,
                                            MaterialCode = semiMaterial.Code,
                                            MaterialName = semiMaterial.NAME,
                                            MaterialVer = semiMaterialVersion.ID,
                                            MaterialType = semiMaterial.Type,//原材料、半成品
                                            Sort = s.SortOrder,
                                            ParentId = bom.ID,
                                            //InsteadMaterialId = step.InsteadMaterialId,
                                            //InsteadMaterialCode = step.InsteadMaterialCode,
                                            //InsteadMaterialName = step.InsteadMaterialName,
                                            //InsteadMaterialVer = step.InsteadMaterialVer,
                                            //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                            //InsteadRate = step.InsteadRate,
                                            Unit = unit,
                                            UnitId = unitId,
                                            StandardQuantity = s.ParentQuantity,
                                            PlanQuantity = s.Quantity,
                                            ActualQuantity = item.PlanQuantity / s.ParentQuantity * s.Quantity,
                                            //SapOrderNo = item.SapOrderNo,
                                            //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                            //SapFeedbackDate = step.SapFeedbackDate,
                                            //Remark = step.Remark,
                                        };
                                        semiBom.CreateCustomGuid(_user.UserName);
                                        weekScheduleBomList.Add(semiBom);
                                    }
                                }
                                #endregion
                            }
                        }
                        #endregion                        

                        if (weekScheduleBomList.Count > 0 && await _weekScheduleBom.Add(weekScheduleBomList) != weekScheduleBomList.Count)
                        {
                            _unitOfWork.RollbackTran();
                            result.AddError($"Add WeekScheduleBom Error");
                            return result;
                        }

                        #region 包装计划时，新增ProductOrder和Batch数据
                        var productionOrderList = new List<ProductionOrderEntity>();
                        var poSegmentRequirementList = new List<PoSegmentRequirementEntity>();
                        var poConsumeRequirementList = new List<PoConsumeRequirementEntity>();
                        var poProducedRequirementList = new List<PoProducedRequirementEntity>();
                        var batchList = new List<SEFA.PPM.Model.Models.BatchEntity>();
                        var batchConsumeRequirementList = new List<BatchConsumeRequirementEntity>();
                        var batchProducedRequirementList = new List<BatchProducedRequirementEntity>();

                        //数据分类是包装计划 &&  计划类型是正常计划 || 重工计划
                        var patckingList = weekScheduleListAdd.Where(m => m.Category == "Packing" && (m.Type == WeekSchedule_Type.Normal.ToString() || m.Type == WeekSchedule_Type.Rework.ToString())).ToList();
                        foreach (var item in patckingList)
                        {
                            #region ProductOrder
                            var productOrder = new ProductionOrderEntity()
                            {
                                ProductionOrderNo = item.OrderNo + "_01",
                                MaterialId = item.MaterialId,
                                MaterialVersionId = item.MaterialVer,
                                Type = "WorkOrder",
                                ParentId = item.ID,
                                PlanDate = item.StartWorkday,
                                PrepareShiftid = item.StartShift,
                                PlanQty = (decimal)item.PlanQuantity,
                                PlanStartTime = item.StartWorkday,
                                PlanEndTime = item.FinishWorkday,
                                PoStatus = "2",//已释放
                                SegmentCode = item.Workshop,
                                LineCode = item.LineCode,
                                MesOrderCode = item.OrderNo + "_01",
                                SapOrderType = "ZXH2",
                                Sequence = 1,
                                SapStatus = "C",
                                HasThroat = 0,
                                SapFlag = 0,
                                OrderType = "C",
                            };
                            productOrder.CreateCustomGuid(_user.UserName);
                            productionOrderList.Add(productOrder);
                            //await _productionOrder.Add(productOrder);
                            #endregion

                            #region POSegmentRequirement
                            var bomList = weekScheduleBomList.Where(m => m.WeekScheduleId == item.ID).ToList();
                            var poSegmentIdList = bomList.Select(m => m.SegmentId).Distinct().ToList();
                            foreach (var segmentId in poSegmentIdList)
                            {
                                #region PoSegmentRequirement
                                var poSegmentRequirement = new PoSegmentRequirementEntity()
                                {
                                    ProductionOrderId = productOrder.ID,
                                    SegmentId = segmentId,
                                    Deleted = 0
                                };
                                poSegmentRequirement.CreateCustomGuid(_user.UserName);
                                //await _poSegmentRequirement.Add(poSegmentRequirement);
                                poSegmentRequirementList.Add(poSegmentRequirement);
                                #endregion

                                var bomConsumeList = bomList.Where(m => m.SegmentId == segmentId).ToList();
                                foreach (var consumeMaterial in bomConsumeList)
                                {
                                    #region PoConsumeRequirement
                                    var poConsumeRequirement = new PoConsumeRequirementEntity()
                                    {
                                        ProductionOrderId = productOrder.ID,
                                        PoSegmentRequirementId = poSegmentRequirement.ID,
                                        MaterialId = consumeMaterial.MaterialId,
                                        MaterialVersionId = consumeMaterial.MaterialVer,
                                        Quantity = consumeMaterial.PlanQuantity,
                                        UnitId = consumeMaterial.UnitId,
                                        SortOrder = (int)consumeMaterial.Sort,
                                        Deleted = 0,
                                        WeighingQty = consumeMaterial.PlanQuantity,
                                    };
                                    poConsumeRequirement.CreateCustomGuid(_user.UserName);
                                    //wait _poConsumeRequirement.Add(poConsumeRequirement);
                                    poConsumeRequirementList.Add(poConsumeRequirement);
                                    #endregion
                                }
                            }
                            #endregion

                            #region poProducedRequirement
                            var poProducedRequirement = new PoProducedRequirementEntity()
                            {
                                ProductionOrderId = productOrder.ID,
                                PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                                MaterialId = item.MaterialId,
                                MaterialVersionId = item.MaterialVer,
                                Quantity = (decimal)item.PlanQuantity,
                                UnitId = item.UnitId,
                                SortNo = 1,
                                Deleted = 0
                            };
                            poProducedRequirement.CreateCustomGuid(_user.UserName);
                            //await _poProducedRequirement.Add(poProducedRequirement);
                            poProducedRequirementList.Add(poProducedRequirement);
                            #endregion

                            #region Batch
                            var batch = new SEFA.PPM.Model.Models.BatchEntity()
                            {
                                LineId = item.LineId,
                                ProductionOrderId = productOrder.ID,
                                PoSegmentRequirementId = poSegmentRequirementList.FirstOrDefault(m => m.ProductionOrderId == productOrder.ID).ID,
                                PoProducedRequirementId = poProducedRequirement.ID,
                                Number = "1",
                                BatchCode = productOrder.ProductionOrderNo + "_01",
                                MaterialId = item.MaterialId,
                                MaterialVersionId = item.MaterialVer,
                                MaterialCode = item.MaterialCode,
                                MaterialDescription = item.MaterialName,
                                TargetQuantity = item.PlanQuantity,
                                Status = "1",
                                PrepStatus = "1",
                                Deleted = 0
                            };
                            batch.CreateCustomGuid(_user.UserName);
                            //await _batch.Add(batch);
                            batchList.Add(batch);
                            #endregion

                            #region batchconsumeRequirement
                            foreach (var consume in poConsumeRequirementList.Where(m => m.ProductionOrderId == productOrder.ID))
                            {
                                var batchConsumeRequirement = new BatchConsumeRequirementEntity()
                                {
                                    BatchId = batch.ID,
                                    PoConsumeRequirementId = consume.ID,
                                    Quantity = (decimal)consume.Quantity,
                                    WeighingQty = (decimal)consume.WeighingQty,
                                    Deleted = 0
                                };
                                batchConsumeRequirement.CreateCustomGuid(_user.UserName);
                                //await _batchConsumeRequirement.Add(batchConsumeRequirement);
                                batchConsumeRequirementList.Add(batchConsumeRequirement);
                            }
                            #endregion

                            #region BatchProducedRequirement
                            var batchProducedRequirement = new BatchProducedRequirementEntity()
                            {
                                BatchId = batch.ID,
                                PoProducedRequirementId = poProducedRequirement.ID,
                                Quantity = (decimal)poProducedRequirement.Quantity,
                                Deleted = 0
                            };
                            batchProducedRequirement.CreateCustomGuid(_user.UserName);
                            //await _batchProducedRequirement.Add(batchProducedRequirement);
                            batchProducedRequirementList.Add(batchProducedRequirement);
                            #endregion
                        }

                        if (productionOrderList.Count > 0)
                            await _productionOrder.Add(productionOrderList);
                        if (poSegmentRequirementList.Count > 0)
                            await _poSegmentRequirement.Add(poSegmentRequirementList);
                        if (poConsumeRequirementList.Count > 0)
                            await _poConsumeRequirement.Add(poConsumeRequirementList);
                        if (poProducedRequirementList.Count > 0)
                            await _poProducedRequirement.Add(poProducedRequirementList);
                        if (batchList.Count > 0)
                            await _batch.Add(batchList);
                        if (batchConsumeRequirementList.Count > 0)
                            await _batchConsumeRequirement.Add(batchConsumeRequirementList);
                        if (batchProducedRequirementList.Count > 0)
                            await _batchProducedRequirement.Add(batchProducedRequirementList);
                        #endregion
                    }
                    else
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Add Week Schedule Error");
                        return result;
                    }
                }
                #endregion

                #region 更新周计划
                if (weekScheduleListUpdate.Count > 0)
                {
                    var update = await _dal.Update(weekScheduleListUpdate);

                    //更新成功，则删除旧周计划BOM，再新增周计划BOM
                    if (update)
                    {
                        //删除旧周计划BOM
                        var deleteBom = await _weekScheduleBom.Delete(m => weekScheduleListUpdate.Select(m => m.ID).ToArray().Contains(m.WeekScheduleId));
                        if (!deleteBom)
                        {
                            _unitOfWork.RollbackTran();
                            result.AddError($"Delete WeekScheduleBom Error for update Bom");
                            return result;
                        }

                        //获取工序物料清单
                        var segmentList = await _sapSegment.FindList(m => m.Deleted == 0);
                        var segmentMaterialList = await _sapSegmentMaterial.FindList(m => weekScheduleListUpdate.Select(m => m.MaterialVer).ToArray().Contains(m.MaterialVersionId));
                        var segmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => weekScheduleListUpdate.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));
                        var weekScheduleBomList = new List<WeekScheduleBomEntity>();
                        foreach (var item in weekScheduleListUpdate)
                        {
                            var segmentMaterial = segmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == item.MaterialVer);
                            if (segmentMaterial == null)
                                continue;
                            var segmentMaterialStepes = segmentMaterialStepList.Where(m => m.SapSegmentMaterialId == segmentMaterial.ID);
                            var materialList = await _material.FindList(a => segmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                            var materialVersionList = await _materialVersion.FindList(a => materialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                            foreach (var step in segmentMaterialStepes)
                            {
                                var material = materialList.FirstOrDefault(m => m.ID == step.MaterialId);
                                var materialVersion = materialVersionList.FirstOrDefault(m => m.MaterialId == material.ID);
                                var unitId = material?.Unit;
                                var unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                var bom = new WeekScheduleBomEntity()
                                {
                                    WeekScheduleId = item.ID,
                                    SegmentId = segmentMaterial.SapSegmentId,
                                    SegmentName = segmentList.FirstOrDefault(m => m.ID == segmentMaterial.SapSegmentId).SegmentName,
                                    SegmentMaterialId = segmentMaterial.ID,
                                    SegmentMaterialStepId = step.ID,
                                    MaterialId = step.MaterialId,
                                    MaterialCode = material.Code,
                                    MaterialName = material.NAME,
                                    MaterialVer = materialVersion.ID,
                                    MaterialType = material.Type,//原材料、半成品
                                    Sort = step.SortOrder,
                                    //ParentId = step.ParentId,
                                    //InsteadMaterialId = step.InsteadMaterialId,
                                    //InsteadMaterialCode = step.InsteadMaterialCode,
                                    //InsteadMaterialName = step.InsteadMaterialName,
                                    //InsteadMaterialVer = step.InsteadMaterialVer,
                                    //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                    //InsteadRate = step.InsteadRate,
                                    Unit = unit,
                                    UnitId = unitId,
                                    StandardQuantity = step.ParentQuantity,
                                    PlanQuantity = step.Quantity,
                                    ActualQuantity = item.PlanQuantity / step.ParentQuantity * step.Quantity,
                                    //SapOrderNo = item.SapOrderNo,
                                    //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                    //SapFeedbackDate = step.SapFeedbackDate,
                                    //Remark = step.Remark,
                                };
                                bom.CreateCustomGuid(_user.UserName);
                                weekScheduleBomList.Add(bom);

                                #region 半成品
                                //半成品物料，添加到半成品物料版本集合中，用于处理半成品
                                if (material.Type == "ZSFG")
                                {
                                    //获取半成品的工序物料清单
                                    var semiSegmentMaterialList = await _sapSegmentMaterial.FindList(m => m.MaterialVersionId == materialVersion.ID);
                                    var semiSegmentMaterialStepList = await _sapSegmentMaterialStep.FindList(m => semiSegmentMaterialList.Select(m => m.ID).ToArray().Contains(m.SapSegmentMaterialId));

                                    var semiSegmentMaterial = semiSegmentMaterialList.FirstOrDefault(m => m.MaterialVersionId == materialVersion.ID);
                                    if (semiSegmentMaterial == null)
                                        continue;
                                    var semiSegmentMaterialStepes = semiSegmentMaterialStepList.Where(m => m.SapSegmentMaterialId == semiSegmentMaterial.ID);
                                    var semiMaterialList = await _material.FindList(a => semiSegmentMaterialStepes.Select(m => m.MaterialId).ToArray().Contains(a.ID));
                                    var semiMaterialVersionList = await _materialVersion.FindList(a => semiMaterialList.Select(m => m.ID).ToArray().Contains(a.MaterialId) && a.MaterialVersionNumber == "0001");
                                    foreach (var s in semiSegmentMaterialStepes)
                                    {
                                        var semiMaterial = semiMaterialList.FirstOrDefault(m => m.ID == s.MaterialId);
                                        var semiMaterialVersion = semiMaterialVersionList.FirstOrDefault(m => m.MaterialId == semiMaterial.ID);
                                        unitId = semiMaterial?.Unit;
                                        unit = unitList.FirstOrDefault(m => m.ID == unitId).Name;
                                        var semiBom = new WeekScheduleBomEntity()
                                        {
                                            WeekScheduleId = item.ID,
                                            SegmentId = semiSegmentMaterial.SapSegmentId,
                                            SegmentName = segmentList.FirstOrDefault(m => m.ID == semiSegmentMaterial.SapSegmentId).SegmentName,
                                            SegmentMaterialId = semiSegmentMaterial.ID,
                                            SegmentMaterialStepId = s.ID,
                                            MaterialId = s.MaterialId,
                                            MaterialCode = semiMaterial.Code,
                                            MaterialName = semiMaterial.NAME,
                                            MaterialVer = semiMaterialVersion.ID,
                                            MaterialType = semiMaterial.Type,//原材料、半成品
                                            Sort = s.SortOrder,
                                            ParentId = bom.ID,
                                            //InsteadMaterialId = step.InsteadMaterialId,
                                            //InsteadMaterialCode = step.InsteadMaterialCode,
                                            //InsteadMaterialName = step.InsteadMaterialName,
                                            //InsteadMaterialVer = step.InsteadMaterialVer,
                                            //InsteadMaterialGroup = step.InsteadMaterialGroup,
                                            //InsteadRate = step.InsteadRate,
                                            Unit = unit,
                                            UnitId = unitId,
                                            StandardQuantity = s.ParentQuantity,
                                            PlanQuantity = s.Quantity,
                                            ActualQuantity = item.PlanQuantity / s.ParentQuantity * s.Quantity,
                                            //SapOrderNo = item.SapOrderNo,
                                            //SapFeedbackQuantity = step.SapFeedbackQuantity,
                                            //SapFeedbackDate = step.SapFeedbackDate,
                                            //Remark = step.Remark,
                                        };
                                        semiBom.CreateCustomGuid(_user.UserName);
                                        weekScheduleBomList.Add(semiBom);
                                    }
                                }
                                #endregion
                            }
                        }

                        if (await _weekScheduleBom.Add(weekScheduleBomList) != weekScheduleBomList.Count)
                        {
                            _unitOfWork.RollbackTran();
                            result.AddError($"add WeekScheduleBom Error for updating Bom");
                            return result;
                        }
                    }

                    //更新包装相关的工单和批次数据
                    //TODO
                }
                #endregion

                #region 删除周计划
                if (weekScheduleListDelete.Count > 0)
                {
                    var deleteBom = await _weekScheduleBom.Delete(m => weekScheduleListDelete.Select(m => m.ID).ToArray().Contains(m.WeekScheduleId));
                    if (!deleteBom)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Delete WeekScheduleBom Error for deleting Bom");
                        return result;
                    }

                    var delete = await _dal.DeleteByIds(weekScheduleListDelete.Select(m => m.ID).ToArray());
                    if (!delete)
                    {
                        _unitOfWork.RollbackTran();
                        result.AddError($"Delete WeekSchedule Error");
                        return result;
                    }

                    //删除包装相关的工单和批次数据
                    var deletePackWeekScheduleList = weekScheduleListDelete.Where(m => m.Category == "Packing");
                    foreach (var ws in deletePackWeekScheduleList)
                    {
                        //删除工单
                        var deleteProdectionOrderList = await _productionOrder.FindList(m => m.ParentId == ws.ID);
                        await _productionOrder.Delete(a => deleteProdectionOrderList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除工单工序
                        var deletePoSegementRequirementList = await _poSegmentRequirement.FindList(m => deleteProdectionOrderList.Select(m => m.ID).ToArray().Contains(m.ProductionOrderId));
                        await _poSegmentRequirement.Delete(a => deletePoSegementRequirementList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除工单消耗需求
                        var deletePoConsumeRequirementList = await _poConsumeRequirement.FindList(m => deleteProdectionOrderList.Select(m => m.ID).ToArray().Contains(m.ProductionOrderId));
                        await _poConsumeRequirement.Delete(a => deletePoConsumeRequirementList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除工单生产需求
                        var deletePoProducedRequirementList = await _poProducedRequirement.FindList(m => deleteProdectionOrderList.Select(m => m.ID).ToArray().Contains(m.ProductionOrderId));
                        await _poProducedRequirement.Delete(a => deletePoProducedRequirementList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除批次
                        var deleteBatchList = await _batch.FindList(m => deleteProdectionOrderList.Select(n => n.ID).ToArray().Contains(m.ProductionOrderId));
                        await _batch.Delete(a => deleteBatchList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除批次消耗需求
                        var deleteBatchConsumeRequirementList = await _batchConsumeRequirement.FindList(m => deleteBatchList.Select(n => n.ID).ToArray().Contains(m.BatchId));
                        await _batchConsumeRequirement.Delete(a => deleteBatchConsumeRequirementList.Select(m => m.ID).ToArray().Contains(a.ID));

                        //删除批次生产需求
                        var deleteBatchProducedRequirementList = await _batchProducedRequirement.FindList(m => deleteBatchList.Select(n => n.ID).ToArray().Contains(m.BatchId));
                        await _batchProducedRequirement.Delete(a => deleteBatchProducedRequirementList.Select(m => m.ID).ToArray().Contains(a.ID));
                    }
                }
                #endregion

                #endregion

                _unitOfWork.CommitTran();
                result.Data = $"操作成功！新增{weekScheduleListAdd.Count}条，更新{weekScheduleListUpdate.Count}条，删除{weekScheduleListDelete.Count}";
            }
            catch (Exception e)
            {
                _unitOfWork.RollbackTran();
                result.AddError(e.StackTrace);
                return result;
            }
            finally
            {
                xssWorkbook?.Close();
            }
            return result;
        }
        #endregion

        #region 私有方法
        private static int _seq = 1;
        private string GetPlanOrderNo(string lineCode)
        {
            var date = DateTime.Now.ToString("yyyyMMdd");
            return $"{date}-{lineCode}-{_seq.ToString("0000")}";
        }

        /// <summary>
        /// 读取合并单元格的值
        /// </summary>
        /// <param name="cell">查询的单元格</param>
        /// <returns>返回有数值的单元格</returns>
        private static ICell GetCell(ISheet sheet, int rowIndex, int columnIndex)
        {
            var cell = sheet.GetRow(rowIndex).GetCell(columnIndex);
            //是否是合并单元格
            if (cell.IsMergedCell)
            {
                //遍历所有的合并单元格
                for (int i = 0; i < sheet.NumMergedRegions; i++)
                {
                    var cellRange = sheet.GetMergedRegion(i);
                    //判断查询的单元格是否在合并单元格内
                    if (cell.ColumnIndex >= cellRange.FirstColumn && cell.ColumnIndex <= cellRange.LastColumn
                        && cell.RowIndex >= cellRange.FirstRow && cell.RowIndex <= cellRange.LastRow)
                    {
                        return sheet.GetRow(cellRange.FirstRow).GetCell(cellRange.FirstColumn);
                    }
                }
            }
            return cell;
        }

        /// <summary>
        /// 读取合并单元格的值
        /// </summary>
        /// <param name="cell">查询的单元格</param>
        /// <returns>返回有数值的单元格</returns>
        private static ICell GetCell(IRow row, int columnIndex)
        {
            var cell = row.GetCell(columnIndex);
            //是否是合并单元格
            if (cell.IsMergedCell)
            {
                //遍历所有的合并单元格
                for (int i = 0; i < cell.Sheet.NumMergedRegions; i++)
                {
                    var cellRange = cell.Sheet.GetMergedRegion(i);
                    //判断查询的单元格是否在合并单元格内
                    if (cell.ColumnIndex >= cellRange.FirstColumn && cell.ColumnIndex <= cellRange.LastColumn
                        && cell.RowIndex >= cellRange.FirstRow && cell.RowIndex <= cellRange.LastRow)
                    {
                        return cell.Sheet.GetRow(cellRange.FirstRow).GetCell(cellRange.FirstColumn);
                    }
                }
            }
            return cell;
        }

        private static bool CompareColor(XSSFColor s, XSSFColor t)
        {
            if (s == null || t == null)
            {
                return false;
            }

            if (s.RGB.Length != t.RGB.Length)
            {
                return false;
            }

            for (int i = 0; i < s.RGB.Length; i++)
            {
                if (s.RGB[i] != t.RGB[i])
                {
                    return false;
                }
            }

            return true;
        }
        #endregion
    }
}