<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SEFA.PPM.Api</name>
    </assembly>
    <members>
        <member name="F:SEFA.PPM.Api.Controllers.AndonController._andonServices">
             <summary>
            
             </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.InterfaceController._esbHelper">
            <summary>
            ESB Interface Helper
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.InterfaceController.Invoke(SEFA.Base.ESB.DataRequestModel)">
            <summary>
            接收ESB推送数据
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.InterfaceController.SyncUnitConvertFromSap">
            <summary>
            接收ESB推送数据
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.LoginController.#ctor">
            <summary>
            构造函数注入
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PPM.PhaseSalescontainerViewController._phaseSalescontainerServices">
            <summary>
            PhaseSalescontainer
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.ActionPropertyController._actionPropertyServices">
            <summary>
            ActionProperty
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.BasePropertyController._basePropertyServices">
            <summary>
            BaseProperty
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.BWeightRecordsController._bWeightRecordsServices">
            <summary>
            BWeightRecords
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.CipinfoController._cipinfoServices">
            <summary>
            Cipinfo
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.CipinfoDetailController._cipinfoDetailServices">
            <summary>
            CipinfoDetail
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.ConsumeSelectController._consumeSelectServices">
            <summary>
            ConsumeSelect
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.CookConfirmationController._cookConfirmationServices">
            <summary>
            CookConfirmation
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.CookConfirmationListController._cookConfirmationListServices">
            <summary>
            CookConfirmationList
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.DowntimeController._downtimeServices">
            <summary>
            Downtime
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.DowntimeHistoryController._downtimeHistoryServices">
            <summary>
            DowntimeHistory
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.FunctionPropertyController._functionPropertyServices">
            <summary>
            FunctionProperty
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.GetWorkOrderInfoServiceController.GetWorkOrderInfo(SEFA.PPM.Model.Models.Interface.FWS_WorkOrderInfo_Res)">
            <summary>
            防伪系统获取工单信息接口
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.InfluxOpcServerController._influxOpcServerServices">
            <summary>
            InfluxOpcServer
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.InfluxOpcTagController._influxOpcTagServices">
            <summary>
            InfluxOpcTag
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.InfluxOpcTagController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            导入数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.InfluxOpcTagController.ExportData(SEFA.PPM.Model.ViewModels.PTM.InfluxOpcTagRequestModel)">
            <summary>
            导出数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.InfluxOpcTagController.ExportData2(SEFA.PPM.Model.ViewModels.PTM.InfluxOpcTagRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.InterfaceLogController._interfaceLogServices">
            <summary>
            InterfaceLog
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.LogsheetImageController._logsheetImageServices">
            <summary>
            LogsheetImage
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.ParaExecutionLogController._paraExecutionLogServices">
            <summary>
            ParaExecutionLog
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.PerformanceController._performanceServices">
            <summary>
            Performance
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.PerformanceController.GetProgressBar(SEFA.PPM.Model.ViewModels.PTM.PerformanceRequestModel)">
            <summary>
            获取进度条数据
            </summary>
            <param name="reqModel">{"EquipmentId":"02405072-0372-8882-163e-0370f6000000","StartTimeUtc":"2024-07-09 00:00:00","EndTimeUtc":"2024-07-09 23:59:59"}</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.PerformanceHistoryController._performanceHistoryServices">
            <summary>
            PerformanceHistory
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.PoEquipmentController._poEquipmentServices">
            <summary>
            PoEquipment
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.PoListController._poListServices">
            <summary>
            PoList
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController._processDataViewServices">
            <summary>
            ProcessData
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.GetLastProcessData(System.String,System.String)">
            <summary>
            根据materialVersionId和状态获取最新版本的工艺长文本
            </summary>
            <param name="materialVersionId"></param>
            <param name="status">状态 3 Pending_Release,2 Released, 1 Disable</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.GetLastNewProcessData(System.String,System.String)">
            <summary>
            根据materialVersionId和状态获取最新版本的工艺长文本
            </summary>
            <param name="materialVersionId"></param>
            <param name="status">状态 3 Pending_Release,2 Released, 1 Disable</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.SavePreprocessingData(SEFA.DFM.Model.Models.MaterialProcessDataEntity)">
            <summary>
            保存预处理长文本
            </summary>
            <param name="materialProcessDataEntity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.EditPreprocessingData(SEFA.DFM.Model.Models.MaterialProcessDataEntity)">
            <summary>
            更新预处理长文本
            </summary>
            <param name="materialProcessDataEntity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.GetLastProcessDataByContextVersion(System.String)">
            <summary>
            根据contextVersionId获取最新的工艺长文本
            </summary>
            <param name="contextVersionId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.GetLastProcessDataByOrder(System.String)">
            <summary>
            根据productionOrderId获取最新的工艺长文本
            </summary>
            <param name="productionOrderId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.GetLastProcessDataByPoId(System.String)">
            <summary>
            根据productionOrderId获取最新的工艺长文本
            </summary>
            <param name="productionOrderId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.SaveMaterialProcessData(SEFA.DFM.Model.Models.MaterialProcessDataEntity)">
            <summary>
            保存修改后的工艺长文本
            </summary>
            <param name="materialProcessDataEntity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.ProcessDataViewController.UpdateMaterialProcessDataStatus(SEFA.DFM.Model.Models.MaterialProcessDataEntity)">
            <summary>
            更新工艺长文本状态
            </summary>
            <param name="materialProcessDataEntity"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.SampleListController._sampleListServices">
            <summary>
            SampleList
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SampleListController.ScanSampleEquipment(System.String)">
            <summary>
            扫描设备铭牌，返回设备及设备当前运行工单的部分信息
            </summary>
            <param name="sampleEquipment"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SampleListController.ScanContainerCode(System.String)">
            <summary>
            扫描容器编码，返回容器
            </summary>
            <param name="containerCode"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SampleListController.CheckLogSheet(SEFA.PPM.Model.Models.PTM.EquipmentAndOrderEntity)">
            <summary>
            check当前设备是否存在待填写对应取样类型的表单，
            如果不存在则直接创建表单并关联容器；
            如存在待填写且取样类型为正常取样，则返回是否需要引用该表单
            如存在待填写且取样类型不为正常取样，则直接返回失败
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SampleListController.SaveSampling(SEFA.PPM.Model.Models.PTM.EquipmentAndOrderEntity)">
            <summary>
            check结果为存在时需要重新选择是否引用当前已存在表单在提交
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SampleListController.UpdateStatus(SEFA.PPM.Model.Models.PTM.SampleListEntity)">
            <summary>
            修改容器状态
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.SampleListVController._sampleListVServices">
            <summary>
            SampleListV
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.SegmentlistController._segmentlistServices">
            <summary>
            Segmentlist
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.SegmentMaterialController._segmentMaterialServices">
            <summary>
            SegmentMaterial
            </summary>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SegmentMaterialController.GetProcessDataList(SEFA.DFM.Model.ViewModels.MaterialProcessDataRequestModel)">
            <summary>
            根据产品版本id获取工艺长文本列表（带长文本解密）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Api.Controllers.PTM.SegmentMaterialController.SaveProcessData(SEFA.DFM.Model.Models.MaterialProcessDataEntity)">
            <summary>
            保存新版本工艺长文本entity.ID用旧版本ID，entity.ProcessData传输工艺长文本明文
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.TippingPrecheckController._tippingPrecheckServices">
            <summary>
            TippingPrecheck
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.TippingSclistController._tippingSclistServices">
            <summary>
            TippingSclist
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.WeightRecordController._weightRecordServices">
            <summary>
            WeightRecord
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.PTM.WeightRecordListController._weightRecordListServices">
            <summary>
            WeightRecordList
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.SIM.EnergyBydayController._energyBydayServices">
            <summary>
            EnergyByday
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.SIM.EnergyByorderController._energyByorderServices">
            <summary>
            EnergyByorder
            </summary>
        </member>
        <member name="F:SEFA.PPM.Api.Controllers.SIM.WorkingHourController._workingHourServices">
            <summary>
            WorkingHour
            </summary>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.#ctor(SqlSugar.ISqlSugarClient,Microsoft.AspNetCore.Hosting.IWebHostEnvironment)">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.GetFrameFiles">
            <summary>
            获取 整体框架 文件(主库)(一般可用第一次生成)
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.GetFrameFilesByTableNames(System.String[],System.String,System.String)">
            <summary>
            获取仓储层和服务层(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="serviceName"></param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.GetFrameFilesByTableNamesForEntity(System.String[],System.String)">
            <summary>
            获取实体(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.GetFrameFilesByTableNamesForController(System.String[],System.String)">
            <summary>
            获取控制器(需指定表名和数据库)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPM.Controllers.DbFirstController.GetAllFrameFilesByTableNames(System.String[],System.String)">
            <summary>
            DbFrist 根据数据库表名 生成整体框架,包含Model层(一般可用第一次生成)
            </summary>
            <param name="ConnID">数据库链接名称</param>
            <param name="serviceName">需要生成命名空间前缀（SEFA.PPM）</param>
            <param name="tableNames">需要生成的表名</param>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPM.SwaggerHelper.CustomRouteAttribute">
            <summary>
            自定义路由 /api/{version}/[controler]/[action]
            </summary>
        </member>
        <member name="P:SEFA.PPM.SwaggerHelper.CustomRouteAttribute.GroupName">
            <summary>
            分组名称,是来实现接口 IApiDescriptionGroupNameProvider
            </summary>
        </member>
        <member name="M:SEFA.PPM.SwaggerHelper.CustomRouteAttribute.#ctor(System.String)">
            <summary>
            自定义路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
        </member>
        <member name="M:SEFA.PPM.SwaggerHelper.CustomRouteAttribute.#ctor(SEFA.Base.Extensions.CustomApiVersion.ApiVersions,System.String)">
            <summary>
            自定义版本+路由构造函数，继承基类路由
            </summary>
            <param name="actionName"></param>
            <param name="version"></param>
        </member>
        <member name="T:SEFA.PPM.Filter.GlobalRouteAuthorizeConvention">
            <summary>
            Summary:全局路由权限公约
            Remarks:目的是针对不同的路由，采用不同的授权过滤器
            如果 controller 上不加 [Authorize] 特性，默认都是 Permission 策略
            否则，如果想特例其他授权机制的话，需要在 controller 上带上  [Authorize]，然后再action上自定义授权即可，比如 [Authorize(Roles = "Admin")]
            </summary>
        </member>
        <member name="T:SEFA.PPM.Filter.GlobalAuthorizeFilter">
            <summary>
            全局权限过滤器【无效】
            </summary>
        </member>
        <member name="T:SEFA.PPM.Filter.GlobalExceptionsFilter">
            <summary>
            全局异常错误日志
            </summary>
        </member>
        <member name="M:SEFA.PPM.Filter.GlobalExceptionsFilter.WriteLog(System.String,System.Exception)">
            <summary>
            自定义返回格式
            </summary>
            <param name="throwMsg"></param>
            <param name="ex"></param>
            <returns></returns>
        </member>
        <member name="P:SEFA.PPM.Filter.JsonErrorResponse.Message">
            <summary>
            生产环境的消息
            </summary>
        </member>
        <member name="P:SEFA.PPM.Filter.JsonErrorResponse.DevelopmentMessage">
            <summary>
            开发环境的消息
            </summary>
        </member>
        <member name="T:SEFA.PPM.Filter.GlobalRoutePrefixFilter">
            <summary>
            全局路由前缀公约
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.BatchPalletViewController._batchPalletViewServices">
            <summary>
            BatchPalletView
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetEntity(System.String)" -->
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetPageList(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            查新批次栈板信息（主界面查询按钮）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetPageListDetail(SEFA.MKM.Model.ViewModels.BpalletDetailViewRequestModel)">
            <summary>
            查询批次栈板明细 需要传入容器id和分页参数
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetBin">
            <summary>
            获取BIN下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetMachine(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.QueryBinS">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.GetListSelect(System.String,System.String,System.String)">
            <summary>
            批次栈板界面下拉框(再分配下拉框)
            </summary>
            <param name="batchID">批次ID</param>
            <param name="eqpID">设备ID</param>
            <param name="eqpName">设备名称</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.SaveDistribution(SEFA.MKM.Model.ViewModels.View.BatchPalletDistribution)">
            <summary>
            再分配（按钮）
            </summary>
            <param name="conID">容器ID</param>
            <param name="batchId">批次ID</param>
            <param name="productionId">订单ID</param>
            <param name="equipmentId">equipmentId</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BatchPalletViewController.DeletePag(System.String)">
            <summary>
            删袋
            </summary>
            <param name="inventID"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.BBatchDetailIiViewController._bBatchDetailIiViewServices">
            <summary>
            BBatchDetailIiView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BBatchDetailIiViewController.GetListByBatchID(System.String)">
            <summary>
            根据批次ID获取对应的物料信息
            </summary>
            <param name="batchID">批次ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BBatchDetailIiViewController.GetPageListByBatchIDS(System.String[],System.Int32,System.Int32)">
            <summary>
            查询选中的批次（函数用来选择多批次的物料信息并显示）
            </summary>
            <param name="batchIDS">批次ID组</param>
            <param name="pageIndex">当前界面</param>
            <param name="pageSize">每页数量</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.BBatchDetailViewController._bBatchDetailViewServices">
            <summary>
            BBatchDetailView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.BBatchDetailViewController.GetListByProOrderID(System.String)">
            <summary>
            备料跳转第一界面根据proOrderID查询批次
            </summary>
            <param name="proOrderID">订单号</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.CheckWeightViewController._checkWeightViewServices">
            <summary>
            CheckWeightView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.CheckWeightViewController.GetPageList(SEFA.MKM.Model.ViewModels.CheckWeightViewRequestModel)">
            <summary>
            复秤TOP界面
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.CheckWeightViewController.ResultListDown(SEFA.MKM.Model.ViewModels.CheckWeightViewRequestModel)">
            <summary>
            复秤Down界面
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.CheckWeightViewController.ConfirmCheck(SEFA.MKM.Model.ViewModels.CheckWeightViewRequestModel)">
            <summary>
            复秤确认
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.CheckWeightViewController.GetCheckWeight(SEFA.MKM.Model.ViewModels.CheckWeightModel)">
            <summary>
            复秤库存
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ContainerClassController._containerClassServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ContainerController._containerServices">
            <summary>
            
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetConBatchPallet_List(System.String)">
            <summary>
            获取托盘信息 传入参数(BatchPallet)
            </summary>
            <param name="classType"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetPageViewList(SEFA.MKM.Model.ViewModels.MContainerViewRequestModel)">
            <summary>
            容器管理视图分页查询（刷新）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetDestinationList">
            <summary>
            获取Destination
            </summary>
            <returns></returns>
            <summary>
            获取容器class BatchPallet
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetDestinationList_CYC">
            <summary>
            获取Destination
            </summary>
            <returns></returns>
            <summary>
            获取容器class BatchPallet
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetDestinationList_YL">
            <summary>
            获取Destination
            </summary>
            <returns></returns>
            <summary>
            获取容器class BatchPallet
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetConClassList">
            <summary>
            获取容器class BatchPallet
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetConStatusList">
            <summary>
            获取容器状态
            </summary>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:SEFA.MKMApi.Controllers.ContainerController.GetViewEntity(System.String)" -->
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ClearLineByEquipment(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            清线
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.TransferContainer(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            转移按钮（非容器转移，根据物料库存进行转移）
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.TransferContainer_WL(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            转移按钮（非容器转移，根据物料库存进行转移）
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.TransferContainerByContainer(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            转移按钮（根据容器转移进行转移）
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ScrapInventory(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            报废按钮
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ReturnInventory(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            退库按钮
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.MergePDFData(System.Collections.Generic.List{SEFA.MKM.Model.ViewModels.View.PrintPDFModel})">
            <summary>
            合并数据并返回
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ReturnInventoryALL(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            退库按钮
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ReturnInventoryALLVeriyDetail(SEFA.MKM.Model.ViewModels.View.TransferModel)">
            <summary>
            退库按钮
            </summary>
            <param name="tranModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ClearContainer(SEFA.MKM.Model.ViewModels.View.ClearContainerStatusModel)">
            <summary>
            清空容器
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ChangeContainerState(SEFA.MKM.Model.ViewModels.View.ChangeContainerStatusModel)">
            <summary>
            修改容器状态（容器必须是清空状态，库存表没有绑定任何数据）
            </summary>
            <param name="changeModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.ChangeContainerState2(SEFA.MKM.Model.ViewModels.View.ChangeContainerStatusModel)">
            <summary>
            修改容器状态（容器必须是清空状态，库存表没有绑定任何数据）
            </summary>
            <param name="changeModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetInventoryListByContainerID(SEFA.MKM.Model.ViewModels.InventorylistingViewRequestModel)">
            <summary>
            容器视图查询(这里需要传送ContainerID字段来定位当前库存)
            </summary>
            <param name="reqModel">容器视图实体</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.AddContainerByIds(System.String[],System.String,System.String)">
            <summary>
            添加物料到容器内部(add按钮)容器物料添加
            </summary>
            <param name="ids">ids集合(物料ID)</param>
            <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
            <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.RemoveContainerByIds(System.String[],System.String,System.String)">
            <summary>
            从容器内部移除物料
            </summary>
            <param name="ids">ids集合(物料ID)</param>
            <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
            <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.DeleteContainerByIds(System.String[],System.String,System.String)">
            <summary>
            删除容器中的物料信息
            </summary>
            <param name="ids">ids集合(物料ID)</param>
            <param name="containerId">容器ID（编辑界面进来的时候的主批次）</param>
            <param name="lotId">批次id(编辑界面进来的时候的主批次)</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetPageHistoryViewList(SEFA.MKM.Model.ViewModels.ContainerHistoryViewRequestModel)">
            <summary>
            查询历史数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.GetMaterial(System.String)">
            <summary>
            根据id获取其属性，SPEC （这里可能需要拆分字段）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.SaveSplit(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            拆分数据，前端完成按包数和数量的方式进行显示，传入后端需要用实际数量
            </summary>
            <param name="splitID">拆分ID</param>
            <param name="equipmentId">Destination</param>
            <param name="quantity">拆分数量</param>
            <param name="isPrint">是否打印</param>
            <param name="selectPrint">打印地址</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ContainerController.SaveSplitServer(System.String,System.String,SEFA.MKM.Model.Models.MaterialInventoryEntity)">
            <summary>
            进行数据拆分
            </summary>
            <param name="splitID"></param>
            <param name="oldTotalQuantity"></param>
            <param name="insertModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ContainerGroupController._containerGroupServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ContainerHistoryController._containerHistoryServices">
            <summary>
            ContainerHistory
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.DataitemdetailController._dataitemdetailServices">
            <summary>
            Dataitemdetail
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.DataitemdetailController.GetReasonCode">
            <summary>
            获取下拉Itemcode='ReverseType'反冲类型
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.EquipmentController._equipmentServices">
            <summary>
            Equipment
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.EquipmentController.GetMachine">
            <summary>
            获取LEVEL='machine'的设备
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.HistoryViewController._historyViewServices">
            <summary>
            HistoryView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetConsumMachineGBZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetConsumMachineZZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetConsumMachineSourceGBZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetConsumMachineSourceZZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetPageList(SEFA.MKM.Model.ViewModels.HistoryViewRequestModel)">
            <summary>
            查询产出历史（使用）
            </summary>
            <param name="reqModel">实体</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.ExportConsumptionData(SEFA.MKM.Model.ViewModels.HistoryViewRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.ExportConsumptionDataGBZ(SEFA.MKM.Model.ViewModels.HistoryViewRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.GetEntityByQTY(System.String)">
            <summary>
            查询编辑框数据并显示（consumed qty）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.Recoil(System.String,System.Decimal)">
            <summary>
            反冲
            </summary>
            <param name="id">主表ID</param>
            <param name="quantity">反冲数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.HistoryViewController.RepeatPlan(SEFA.MKM.Model.ViewModels.SendModel)">
            <summary>
            重发
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.InventorylistingViewController._inventorylistingViewServices">
            <summary>
            InventorylistingView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.InventorylistingViewController.GetEntityByID(System.String)">
            <summary>
            获取实体
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialDispositionController._materialDispositionServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialInventoryController._IPrintSelectViewServices">
            <summary>
            注册打印类
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetWeightEntityByID(SEFA.MKM.Model.ViewModels.MMaterialPropertyViewRequestModel)" -->
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetEquipmentSap(System.String)">
            <summary>
            根据下拉的设备code查询是否需要填写子批次(有关系让填写子批次)，判断false 和 true
            </summary>
            <param name="equipmentCode">选中下拉框</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetEntityByID(System.String)">
            <summary>
            获取实体
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetContainerSelectList">
            <summary>
            获取可选容器下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetMaterialSelectList">
            <summary>
            获取物料下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetMaterialSelectListClass(System.String)">
            <summary>
            获取物料下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetMSelectListByClass(System.String,System.String)">
            <summary>
            获取物料下拉框
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetMaterialSelectList2">
            <summary>
            获取物料下拉框(成品和半成品)FERT成品、HALB半成品ZSFG 新
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ExportInventData(SEFA.MKM.Model.ViewModels.InventorylistingViewRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.AddInventory(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            新增库存信息
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.NewAddInventory(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            New新增库存信息
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.NewAddInventoryByTotal(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            根据总量来创建数量（零头袋子需要拆分到最后一袋子）
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.NewAddInvenCYC(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            根据总量来创建数量（零头袋子需要拆分到最后一袋子）
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.DeleteInventory(System.String[])">
            <summary>
            删除库存数据string[]
            </summary>
            <param name="ids">string[]</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.UnbindInventory(System.String[])">
            <summary>
            删除库存数据string[]
            </summary>
            <param name="ids">string[]</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.BlockInventory(System.String,System.String)">
            <summary>
            上锁
            </summary>
            <param name="id">id</param>
            <param name="comment">备注</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.UBlockInventory(System.String,System.String,System.String)">
            <summary>
            解锁
            </summary>
            <param name="id"></param>
            <param name="comment">备注</param>
            <param name="subLotState">状态</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintInventLableALL(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            批量打印标签
            </summary>
            <param name="ids"></param>
            <param name="PrintId"></param>
            <param name="IS_YS"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintMinLableALL(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            批量打印标签
            </summary>
            <param name="ids"></param>
            <param name="PrintId"></param>
            <param name="IS_YS"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintInventLable(System.String,System.String,System.String)">
            <summary>
            更新生产和打印(打印库存标签)
            </summary>
            <param name="id"></param>
            <param name="times"></param>
            <param name="remark"></param>
            <param name="isPrint"></param>
            <param name="PrintId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintMinLabel(System.String,System.String)">
            <summary>
            更新生产和打印(打印小标签)
            </summary>
            <param name="id"></param>
            <param name="times"></param>
            <param name="remark"></param>
            <param name="isPrint"></param>
            <param name="PrintId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintPreparaLabel(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            更新生产和打印(打印小标签)
            </summary>
            <param name="id"></param>
            <param name="times"></param>
            <param name="remark"></param>
            <param name="isPrint"></param>
            <param name="PrintId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.PrintPreparaLabelKY(SEFA.MKM.Model.ViewModels.View.MaterialInventoryModel)">
            <summary>
            更新生产和打印(打印小标签)
            </summary>
            <param name="id"></param>
            <param name="times"></param>
            <param name="remark"></param>
            <param name="isPrint"></param>
            <param name="PrintId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ChangeTimesInventory(System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            更新生产和打印
            </summary>
            <param name="id"></param>
            <param name="times"></param>
            <param name="remark"></param>
            <param name="isPrint"></param>
            <param name="PrintId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ChangeQuantityInventory(System.String,System.String)">
            <summary>
            更新物料库存数量
            </summary>
            <param name="id"></param>
            <param name="quantity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.Validata(System.String)">
            <summary>
            校验子批次是否存在
            </summary>
            <param name="sscc"></param>
            <returns></returns>
            
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ChangeRemarkInventory(SEFA.MKM.Model.Models.InventoryModel)">
            <summary>
            更新生产日期操作
            </summary>
            <param name="id">ID</param>
            <param name="times">时间字符串（更新时间）</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetInventoryMergeList(System.String[])">
            <summary>
            根据选中的库存ID集合获取需要合并的列（这里需要考虑只能同批次才能合并，前端需要）
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetInventoryMergeOK(System.String[])">
            <summary>
            根据选中的库存ID集合获取需要合并的列（这里需要考虑只能同批次才能合并，前端需要）
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit(System.String)">
            <summary>
            根据设备获取打印机
            </summary>
            <param name="equmentID"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_Move">
             <summary>
            获取下拉打印机-备料包库存标签模板/复称(小标签)
             </summary>
             <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetPrinit_MoveByEqumentID(System.String)">
             <summary>
            获取下拉打印机-备料包库存标签模板/复称(小标签)通过设备
             </summary>
             <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_Bag">
            <summary>
             获取下拉打印机-库存
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_Pallet">
            <summary>
            获取下拉打印机-托盘和拼锅最后一个
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_CLBLPDA(System.String,System.String)">
            <summary>
            获取拼锅区域打印机
            </summary>
            <param name="equipmentId"></param>
            <param name="code"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_Process">
            <summary>
            获取下拉打印机-原料加工厂备料标签模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSelectPrinit_ProcessFour(SEFA.PPM.Model.ViewModels.PrintSelectViewRequestModel)">
            <summary>
            获取下拉打印机-原料加工厂备料标签模板
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.InventoryMerges(SEFA.MKM.Model.ViewModels.View.InventoryMergesModel)">
            <summary>
            库存合并并打印条码
            </summary>
            <param name="models">models</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetMaterial(System.String)">
            <summary>
            根据id获取其属性，SPEC （这里可能需要拆分字段）
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.InventorySplit(SEFA.MKM.Model.ViewModels.View.InventorySplitModel)">
            <summary>
            库存拆分（拆分数据，前端完成按包数和数量的方式进行显示，传入后端需要用实际数量）
            </summary>
            <param name="models"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSugarPre(SEFA.MKM.Model.ViewModels.InventorylistingViewRequestModel)">
            <summary>
            白糖预/查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetSugarPreAll(SEFA.MKM.Model.ViewModels.InventorylistingViewRequestModel)">
            <summary>
            白糖预/查询（网页专用）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SugarPreMerges(SEFA.MKM.Model.ViewModels.View.InventoryMergesModel)">
            <summary>
            白糖预/合并
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.TransferEquipment(SEFA.MKM.Model.ViewModels.View.TransferEquipment)">
            <summary>
            白糖预/转移
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SugarPutOrPrePutMtr(SEFA.MKM.Model.ViewModels.View.SugarPrePutMtr)">
            <summary>
            扫码收货和收料合并
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SugarPrePut(SEFA.MKM.Model.ViewModels.View.SugarPrePutModel)">
            <summary>
            白糖预扫码/收货（收货按钮）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.StorageOpen(System.String)">
            <summary>
            是否开启节点功能
            </summary>
            <param name="eqpID"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SugarPrePutMtr(SEFA.MKM.Model.ViewModels.View.SugarPrePutMtr)">
            <summary>
            白糖预/收料
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SugarPreSplit(SEFA.MKM.Model.ViewModels.View.SugarPreSplitModel)">
            <summary>
            白糖预处理/拆分
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SaveForm(SEFA.MKM.Model.Models.MaterialInventoryEntity)">
            <summary>
            修改和新增，修改需要传入ID和修改数量的数据
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SaveMergeServer(System.String,System.String[],System.String,System.String)">
            <summary>
            进行合并库存信息
            </summary>
            <param name="upID"></param>
            <param name="deleteID"></param>
            <param name="sscc"></param>
            <param name="totalQuantity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.SaveSplitServer(System.String,System.String,SEFA.MKM.Model.Models.MaterialInventoryEntity)">
            <summary>
            进行数据拆分
            </summary>
            <param name="splitID"></param>
            <param name="oldTotalQuantity"></param>
            <param name="insertModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.TransferServer(System.String,System.String,System.String)">
            <summary>
            转移
            </summary>
            <param name="id">转移数据id(库存表ID)</param>
            <param name="equipmentId"></param>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.TranferHisReverse(SEFA.MKM.Model.ViewModels.View.TranferReverse)">
            <summary>
            转移历史反冲用
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ReturnWMS(SEFA.MKM.Model.ViewModels.View.TranferReverse)">
            <summary>
            转移历史退货返还给SAP
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.InterFACesSC">
            <summary>
            C
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetScaleList(System.String)">
            <summary>
            查询电子称
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetInventLabel(System.String,System.String)">
            <summary>
            根据库存ID获取备料标签（库存）这里可能会有部分数据缺失
            </summary>
            <param name="inventID">库存ID</param>
            <param name="factoryName">表头使用功能组织(默认三厂)</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetThroatPDAList(SEFA.PPM.Model.ViewModels.ColdAWarehouseViewRequestModel)">
            <summary>
            查询喉头（状态1是已出仓，0是未出仓） PAD扫码后判断状态 已出仓就有撤回按钮
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.GetDataStatus(SEFA.PPM.Model.ViewModels.ColdAWarehouseViewRequestModel)">
            <summary>
            喉头出仓
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.OutWarehouse(SEFA.PPM.Model.ViewModels.ColdAWarehouseViewRequestModel)">
            <summary>
            喉头出仓
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialInventoryController.ReturnWarehouse(SEFA.PPM.Model.ViewModels.ColdAWarehouseViewRequestModel)">
            <summary>
            喉头撤回
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialLotController._materialLotServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialPreparationViewController._materialPreparationViewServices">
            <summary>
            MaterialPreparationView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetRoomSelectList">
            <summary>
            ROOM下拉框数据查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetRoomSelectListCLBL">
            <summary>
            ROOM下拉框数据查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetRoomCLBLByEquipmentID(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            ROOM下拉框数据查询
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.Get_SelectMaterial(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            原料标签物料标签
            </summary>
            <param name="reqModel">工单id和ROOM数据</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.Get_SelectPro(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            原料标签工单号下拉框
            </summary>
            <param name="reqModel">传入ROOMID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetRoomSelectListByPG(SEFA.PPM.Model.ViewModels.MKM.View.CallModel)">
            <summary>
            根据EquipmentName查询对应的数据()
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetRoomSelectListBySC(SEFA.PPM.Model.ViewModels.MKM.View.CallModel)">
            <summary>
            根据EquipmentName查询对应的数据()
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageList(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            工单工序批次备料查询 
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListMaterialPreTop(SEFA.MKM.Model.ViewModels.BBatchDetailMaterialViewRequestModel)">
            <summary>
            获取最后界面物料明细(根据物料id)(Top)
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetDown(SEFA.MKM.Model.ViewModels.BBatchDetailMaterialViewRequestModel)">
            <summary>
            获取最后界面物料明细(根据物料id)(Down)（拼锅）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetMateriaConsumlList(SEFA.MKM.Model.ViewModels.BBatchDetailMaterialViewRequestModel)">
            <summary>
            获取物料的耗用量(不传参数默认查询糖的)
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetMateriaConsumlListPG(SEFA.MKM.Model.ViewModels.BBatchDetailMaterialViewRequestModel)">
            <summary>
            拼锅首页打开传参  Pids(list)  MCode "7300030001"
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetConSelectList(System.String,System.String)">
            <summary>
            容器下拉框
            </summary>
            <param name="proOrderID"></param>
            <param name="batchID"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.ContainerSelectList(System.String,System.String)">
            <summary>
            查询最后一页容器下拉框
            </summary>
            <param name="proOrderID"></param>
            <param name="batchID"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetSegmentList(SEFA.PPM.Model.ViewModels.MBatchriiViewRequestModel)">
            <summary>
            工序等信息(标题)
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetByList(System.String)">
            <summary>
            备料跳转第一界面根据proOrderID订单号查询批次(这里需要根据batch表批次排序)
            </summary>
            <param name="proOrderID">订单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetByProOrderIDOLD(System.String,System.String)">
            <summary>
            备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)
            </summary>
            <param name="proOrderID">订单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetByProOrderID(System.String,System.String)">
            <summary>
            备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)（拼锅）
            </summary>
            <param name="proOrderID">订单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID(System.String,System.String)">
            <summary>
            根据批次ID获取对应的物料信息(混料机下面的一层的一层数据)
            </summary>
            <param name="batchID">批次ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.IsFinish_PG(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            拼锅是否完成
            </summary>
            <param name="batchID">批次ID</param>
            <param name="eqpmentID">roomID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByBatchIDS(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByBatchIDSII(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByMaterial(SEFA.MKM.Model.ViewModels.View.BBatchDModel)">
            <summary>
            查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByMaterialII(SEFA.MKM.Model.ViewModels.View.BBatchDModel)">
            <summary>
            查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.AddPallet(SEFA.MKM.Model.ViewModels.View.MPPallentAddModel)">
            <summary>
            创建托盘
            </summary>
            <param name="plateAddModel">TareWeight 目标重量来自于本页最上面的 Target   UomID(Target的单位id,来自于)  ProBatchID   EquipMentID  MaterialId  ProRequestID</param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetContainerBatchPalletList(System.String)" -->
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetMarialPreSelect">
            <summary>
            获取备料主页右上角下拉框里面的EquipmentId需要传入到后面的界面使用
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetContainerSelect(System.String)">
            <summary>
            备料界面最下面的容器下拉框
            </summary>
            <param name="batchID">根据批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_MATERIAL_VIEW）</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetBatchPalletsPageList(SEFA.MKM.Model.ViewModels.BBatchDetailMcontainerViewRequestModel)">
            <summary>
            查询最下面batch pallets
            </summary>
            <param name="reqModel">只需要传入批次表的LotId 和分页参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetContainerSelectQuantity(System.String)">
            <summary>
            查询实际耗用量（所有容器的）
            </summary>
            <param name="batchID">根据生产批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_II_VIEW）</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_FullBag(SEFA.PPM.Model.ViewModels.View.FullBagModel)">
            <summary>
            拼锅-转移-选可用库存
            </summary>
            <param name="reqModel"></param>
            <returns></returns>   
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_PartialBag(SEFA.PPM.Model.ViewModels.MKM.View.PartialBagModel)">
            <summary>
            备料转移PartialBag（只用于按照物料分）
            </summary> 
            <param name="subID">子批次</param>
            <param name="inputBagWeight">包数</param>
            <param name="bagsWeight">单包重量</param>
            <param name="targetWeight">目标重量在视图最上面</param>
            <param name="actualWeight">目标重量在视图最上面之前容器量</param>
            <param name="containerID">选中的容器ID</param>
            <param name="proOrderID">产线工单ID</param>
            <param name="batchID">批次ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_Merge(SEFA.PPM.Model.ViewModels.MKM.View.MergeModel)">
            <summary>
            备料转移PartialBag_Merge（前端校验数量）
            </summary>      
            <param name="subID">子批次</param>
            <param name="inputBagWeight">输入拆分数量</param>       
            <param name="ssccCode">子批次code</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.Preparation_PrintMinLable(SEFA.PPM.Model.ViewModels.MKM.View.MergeModel)">
            <summary>
            补打小标签
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_NewFullAmount(SEFA.PPM.Model.ViewModels.MKM.View.FullAmountModel)">
            <summary>
            新备料转移NewFullAmount（前端校验数量）
            </summary>      
            <param name="subID">子批次</param>
            <param name="containerID">选中的容器ID</param>
            <param name="proOrderID">产线工单ID</param>
            <param name="batchID">批次ID</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_FullAmount(SEFA.PPM.Model.ViewModels.MKM.View.FullAmountModel)">
            <summary>
            备料转移FullAmount（前端校验数量）
            </summary>      
            <param name="subID">子批次</param>
            <param name="containerID">选中的容器ID</param>
            <param name="proOrderID">产线工单ID</param>
            <param name="batchID">批次ID</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_Remove(SEFA.PPM.Model.ViewModels.MKM.View.RemoveBagModel)">
            <summary>
            备料 Transfer_Remove（前端校验数量）
            </summary>      
            <param name="subIDs">子批次（最下面一个批次选择的ID）</param>
            <param name="proOrderID">产线工单ID(来自于主界面)</param>
            <param name="batchID">批次ID来自于主界</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationNewTransfer_Remove(SEFA.PPM.Model.ViewModels.MKM.View.RemoveBagModel)">
            <summary>
            新备料 NewTransfer_Remove（前端校验数量）
            </summary>      
            <param name="subIDs">子批次（最下面一个批次选择的ID）</param>
            <param name="proOrderID">产线工单ID(来自于主界面)</param>
            <param name="batchID">批次ID来自于主界</param>      
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_CompletePallet(SEFA.PPM.Model.ViewModels.MKM.View.CompletePalletModel)">
            <summary>
            容器完成(CompletePalle)
            </summary>
            <param name="containerID">最下面选中参数</param>
            <param name="actualWeight">最上面的实际数量</param>
            <param name="uomId">单位ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_OpenPallet(SEFA.PPM.Model.ViewModels.MKM.View.OpenPalletModel)">
            <summary>
            容器打开(OpenPalle)
            </summary>
            <param name="containerID">最下面选中参数</param>
            <param name="actualWeight">最上面的实际数量</param>
            <param name="uomId">单位ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_DeletePallet(SEFA.MKM.Model.ViewModels.View.DeletePalletModel)">
            <summary>
            删除容器(DeletePallet)
            </summary>
            <param name="containerID">最下面选中参数</param>
            <param name="actualWeight">最上面的实际数量</param>
            <param name="uomId">单位ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageList_NewMaterial(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            工单工序批次备料查询(按照物料批次)备料查询 
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetByProOrderID_NewMaterial(System.String[])">
            <summary>
            备料NewMaterial跳转第一界面根据proOrderID查询批次(支持多订单)(按照物料批次)
            </summary>
            <param name="proOrderID">订单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetList_NewByMaterialID(System.String)">
            <summary>
            根据批次ID获取对应的物料信息(按照物料批次)
            </summary>
            <param name="batchID">物料ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetMPreparationII(SEFA.MKM.Model.ViewModels.View.PreparationIIModel)">
            <summary>
            获取工单界面跳转获取物料列表
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.Get_CLBLNew(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            称量备料界面查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.Get_YLLabelNew(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            原料标签查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageList_CLBL(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            称量备料界面查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetCLBL_ByList(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            称量备料界面查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetMPreparationII_CLBL(SEFA.MKM.Model.ViewModels.View.PreparationIIModel)">
            <summary>
            获取工单界面跳转获取物料列表
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByMaterial_CLBL(SEFA.MKM.Model.ViewModels.View.BBatchDModel)">
            <summary>
            查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetSegmentList_CLBL(SEFA.PPM.Model.ViewModels.MBatchriiViewRequestModel)">
            <summary>
            工序等信息(标题)
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetByProOrderID_CLBL(System.String,System.String)">
            <summary>
            备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)(称量界面独享)
            </summary>
            <param name="proOrderID">订单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchID_CLBL(System.String,System.String)">
            <summary>
            根据批次ID获取对应的物料信息(混料机下面的一层的一层数据)
            </summary>
            <param name="batchID">批次ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByBatchIDS_CLBL(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
            </summary>
            <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.ReprintSavePG(SEFA.MKM.Model.ViewModels.View.MPPallentAddModel)">
            <summary>
            拼锅最后一个界面打印
            </summary>
            <param name="plateAddModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.ReprintSave(SEFA.MKM.Model.ViewModels.View.MPPallentAddModel)">
            <summary>
            拼锅最后一个界面打印
            </summary>
            <param name="plateAddModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetContainerPDA(System.String)">
            <summary>
            根据条码查询对应的容器编码是否存在
            batchID;proID;equpmentID;batchData.Number;当前批次号;括号内当前第几批;容器id
            </summary>
            <param name="sscc"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPGTopPDA(SEFA.MKM.Model.ViewModels.MaterialPreparationViewRequestModel)">
            <summary>
            根据工单号查询表头数据(获取第一数据)（缸号，返回的数据取第1行数据 缸号：Sequence/Sequencetotal ）
            工单号:ProOrder+(GetContainerPDA最后个数据)+配方 FormulaNo +缸号：Sequence 批次产量：来自于接口GetListByBatchIDPDA 汇总
            生产线：EquipmentCode 生产日期：PlanStartTime
            </summary>
            <param name="ProID">工单号</param>
            <param name="RoomID">拼锅room的ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetListByBatchIDPDA(System.String,System.String)">
            <summary>
            根据批次ID和eqpmentID获取数据源（底部数据源）（物料 ：MCode +MName  数量：MQuantity  /MQuantityTotal MQuantityunit 批次产量： returnData[0].Total）
            单位ID returnData[0].UnitID
            </summary>
            <param name="batchID">批次id</param>
            <param name="eqpmentID">拼锅返回数据</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetInventPDA(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            用来查询可用库存的数据(实体)：sscc:  SSCC 追溯码 Bag 包数  Size 单包重量  MUnit 单位 TargetWeight 目标重量
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetCodeTypeByPDA(System.String,System.String,System.String,System.String,System.String)">
            <summary>
            判断当前数据所处位置（托盘/备料小标签+ invent.SubLotId;"/可用库存++ invent.SubLotId;）分号
            </summary>
            <param name="cName">容器名称首页第一界面缓存</param>
            <param name="sscc">扫码的二维码</param>
            <param name="batchID">批次ID</param>
            <param name="proID">工单ID</param>
            <param name="equpmentID">ROOMID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_FullAmountPDA(SEFA.PPM.Model.ViewModels.MKM.View.FullAmountModel)">
            <summary>
            工单库存转移（传参和网页一致）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.MPreparationTransfer_FullBagPDA(SEFA.PPM.Model.ViewModels.View.FullBagModel)">
            <summary>
            可用库存转移（传参和网页一致）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.IsFinish_PDA(SEFA.MKM.Model.ViewModels.View.BBatchDetailIIModel)">
            <summary>
            拼锅是否完成
            </summary>
            <param name="batchID">批次ID</param>
            <param name="eqpmentID">roomID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MaterialPreparationViewController.CompletePalletPDA(SEFA.PPM.Model.ViewModels.MKM.View.CompletePalletModel)">
            <summary>
            拼锅是否完成
            </summary>
            <param name="batchID">批次ID</param>
            <param name="eqpmentID">roomID</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialSubLotController._materialSubLotServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialTransferController._materialTransferServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MaterialTransferListController._materialTransferListServices">
            <summary>
            MaterialTransferList
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MPreparationViewController._mPreparationViewServices">
            <summary>
            MPreparationView
            </summary>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.MtippingPrecheckController._mtippingPrecheckServices">
            <summary>
            MtippingPrecheck
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MtippingPrecheckController.GetCount(SEFA.MKM.Model.ViewModels.MtippingPrecheckViewRequestModel)">
            <summary>
            获取检查完成数量/总数量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.MtippingPrecheckController.Precheck(SEFA.PTM.Model.ViewModels.ConsolPoRequestModel)">
            <summary>
            备料复检操作：通过key值判断执行不同逻辑 key=1开始备料复检,key=2扫描库存
            Body{\"BatchId\":\"\",\"TraceCode\":\"\"}key=1时只用传BatchId，key=2时两个都要传
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.PalletlistViewController._palletlistViewServices">
            <summary>
            PalletlistView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.GetDesinations(SEFA.MKM.Model.ViewModels.View.PalletDesinationModel)">
            <summary>
            Desinations下拉选 第二个Area下拉选用api/Equipment/GetListByLevel?key=Line
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.GetDesination(SEFA.MKM.Model.ViewModels.View.PalletModel)">
            <summary>
            获取Machine(这里和主表传入数据一致)
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.GetPageList(SEFA.MKM.Model.ViewModels.View.PalletModel)">
            <summary>
            获取栈板信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.GetEntity(System.String)">
            <summary>
            根据ID查询栈板明细
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.Verify(System.String)">
            <summary>
            Scan toVerify（校验）
            </summary>
            <param name="sscc">子批次号</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.Reverse(SEFA.MKM.Model.ViewModels.ReverseModel)">
            <summary>
            反冲
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.PalletlistViewController.Print(System.String)">
            <summary>
            打印
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ProductionHistoryViewController._productionHistoryViewServices">
            <summary>
            ProductionHistoryView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.GetProductionMachineGBZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.GetProductionMachineZZ(SEFA.MKM.Model.ViewModels.View.BatchPalletModel)">
            <summary>
            获取Machine
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.ExportProductionHistoryZZ(SEFA.MKM.Model.ViewModels.ProductionHistoryViewRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.ExportProductionHistoryGBZ(SEFA.MKM.Model.ViewModels.ProductionHistoryViewRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.ReverseAsync(SEFA.MKM.Model.ViewModels.ReverseModel)">
            <summary>
            反冲
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.GetEquipmentStorege(SEFA.MKM.Model.ViewModels.ReverseModel)">
            <summary>
             根据节点插叙是否管理库存（返回空表示未配置，否则返回（OK;1）是否是同节点;1代表管理库存,0代表不管理库存）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.RepeatPoProducedActual(SEFA.MKM.Model.ViewModels.SendModel)">
            <summary>
            重发
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.ScanPoProducedActualWMS(System.String)">
            <summary>
            消耗WMS标签数据
            </summary>
            <param name="sscc"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionHistoryViewController.ScanPoProducedActualWMSGBZ(System.String)">
            <summary>
            消耗WMS标签数据灌包装
            </summary>
            <param name="sscc"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ProductionSummaryDetailsViewController._productionSummaryDetailsViewServices">
            <summary>
            ProductionSummaryDetailsView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionSummaryDetailsViewController.ReverseAsync(SEFA.MKM.Model.ViewModels.ReverseModel)">
            <summary>
            反冲
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.ProductionSummaryViewController._productionSummaryViewServices">
            <summary>
            ProductionSummaryView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.ProductionSummaryViewController.GetPageList(SEFA.MKM.Model.ViewModels.ProductionSummaryViewRequestModel)">
            <summary>
            产出数据汇总查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKMApi.Controllers.TransferHistoryViewController._transferHistoryViewServices">
            <summary>
            TransferHistoryView
            </summary>
        </member>
        <member name="M:SEFA.MKMApi.Controllers.TransferHistoryViewController.GetPageList(SEFA.MKM.Model.ViewModels.TransferHistoryViewRequestModel)">
            <summary>
            查询物料转移历史（查询条件下拉框来自于webAIP soure(DFM_M_EQUIPMENT LEVEL='ProductLine')   soure bin(DFM_M_EQUIPMENT_REQUIREMENT LEVEL='3')）
            后面是查询条件
             StartTime，EndTime，SourceMaterial，DestinationMaterial，OldBatch，NewBatch，OLD_SUB_LOT_ID，NEW_SUB_LOT_ID，
             OldSoureID，OldSoureBin，NewDestinationID，NewDestinationBin    
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ColdInventoryViewController._coldInventoryViewServices">
            <summary>
            ColdInventoryView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ColdInventoryViewController.GetColdQuantity(SEFA.PPM.Model.ViewModels.ColdQuantityViewRequestModel)">
            <summary>
            获取喉头仓总库存
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ColdInventoryViewController.GetPageList(SEFA.PPM.Model.ViewModels.ColdInventoryViewRequestModel)">
            <summary>
            喉头仓库存
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ColdInventoryViewController.DayOutStock(SEFA.PPM.Model.ViewModels.ColdAWarehouseViewRequestModel)">
            <summary>
            当日出仓喉头提醒
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.CookRecipeViewController._cookRecipeViewServices">
            <summary>
            CookRecipeView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CookRecipeViewController.GetSchedule">
            <summary>
            煮料看板
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.GaveMaterialViewController._gaveMaterialViewServices">
            <summary>
            GaveMaterialView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.GaveMaterialViewController.GetPageList(SEFA.PPM.Model.ViewModels.GaveMaterialViewRequestModel)">
            <summary>
            原料库存看板送料提醒查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.GaveMaterialViewController.GetListTop">
            <summary>
             原料库存看板上方统计
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.GaveMaterialViewController.GetListDown(SEFA.PPM.Model.ViewModels.DeadlyMaterialViewRequestModel)">
            <summary>
            原料库存看板临期物料清单（MFG3）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.InventController._inventServices">
            <summary>
            Invent
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.MaterialLabelViewController._materialLabelViewServices">
            <summary>
            MaterialLabelView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MaterialLabelViewController.GetPageListByProId(SEFA.PPM.Model.ViewModels.MaterialLabelViewRequestModel)">
            <summary>
            查询当前工单的需求数量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MaterialLabelViewController.GetPageListByProIds(SEFA.PPM.Model.ViewModels.MaterialLabelViewRequestModel)">
            <summary>
            查询选择的多工单
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MaterialLabelViewController.Get_MaterialLabelData(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.MKM.View.MaterialLabelModel})">
            <summary>
            编辑界面的生成子批次按钮
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MaterialLabelViewController.PrintCodeByEquipmentId(System.String,System.String,System.String,System.Int32)">
            <summary>
            根据设备打印对应标签
            </summary>
            <param name="prinitID">打印机ID</param>
            <param name="id">选中的ID（库存ID）</param>
            <param name="equipmentId">选中数据的equipmentId</param>      
            <param name="size">打印张数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MaterialLabelViewController.AddInventByWMSLabel(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.MKM.View.MaterialLabelSaveModel})">
            <summary>
            按物料标签新增库存
            </summary>
            <param name="mModels"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.MpreDayViewController._mpreDayViewServices">
            <summary>
            MpreDayView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.MpreDayViewController.Preprogress(SEFA.PPM.Model.ViewModels.MpreDayViewRequestModel)">
            <summary>
            NEW备料看板
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PackIViewController._packIViewServices">
            <summary>
            PackIView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.AnnualOutPut(SEFA.PPM.Model.ViewModels.PackIViewRequestModel)">
            <summary>
            获取全年产量累加
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.CertainYearMonth(SEFA.PPM.Model.ViewModels.PackFebruaryViewRequestModel)">
            <summary>
            获取某年某月产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.GetEveryMonth(SEFA.PPM.Model.ViewModels.PackEveryMonthViewRequestModel)">
            <summary>
            获取某年各月产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.LineOutPutTon(SEFA.PPM.Model.ViewModels.PackFebruaryViewRequestModel)">
            <summary>
            包装车间各线情况（吨/箱）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.GetSpecOutPut(SEFA.PPM.Model.ViewModels.PackSpecIiViewRequestModel)">
            <summary>
            查规格类别查产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.PackSpecOutPut(SEFA.PPM.Model.ViewModels.PackSpecIiViewRequestModel)">
            <summary>
            查询每个规格分类下的规格产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.GetSpeType(SEFA.PPM.Model.ViewModels.PackSpecIiViewRequestModel)">
            <summary>
            规格大类下拉
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.FormulaType(SEFA.PPM.Model.ViewModels.PackFormulaViewRequestModel)">
            <summary>
            X月酱料类别产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackIViewController.DifferentWays(SEFA.PPM.Model.ViewModels.PackRecipeViewRequestModel)">
            <summary>
            按不同方式统计各月产量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PackTechnologyViewController._packTechnologyViewServices">
            <summary>
            PackTechnologyView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.GetJITMaterial(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-JIT物料状态指示灯
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.GetPackTechnology(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-获取PAC-ONPK工艺提醒
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.GetDowntime(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-停机时间
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.CurrentWorkOrderInfo(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-当前灌装工单信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.NextOrderInfo(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-下一张灌装工单信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackTechnologyViewController.GetProductivity(SEFA.PPM.Model.ViewModels.PackTechnologyViewRequestModel)">
            <summary>
            包装看板-关键设备指示灯
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.RequestIiViewController._requestIiViewServices">
            <summary>
            RequestIiView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.DeleteRecord(System.String[])">
            <summary>
            删除请料记录
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetPageList(SEFA.PPM.Model.ViewModels.RequestIiViewRequestModel)">
            <summary>
            请料记录查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetRequestInfo(SEFA.PPM.Model.ViewModels.RequestIiiViewRequestModel)">
            <summary>
            请料详细查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetRequestDetailList(SEFA.PPM.Model.ViewModels.RequestIiViewRequestModel)">
            <summary>
            统计
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetPageList_Pull(SEFA.PPM.Model.ViewModels.PullIiViewRequestModel)">
            <summary>
            请料记录查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetRequestInfo_Pull(SEFA.PPM.Model.ViewModels.PullIiiViewRequestModel)">
            <summary>
            请料详细查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestIiViewController.GetRequestDetailList_Pull(SEFA.PPM.Model.ViewModels.PullIiViewRequestModel)">
            <summary>
            统计
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.RequestInventoryViewController._requestInventoryViewServices">
            <summary>
            RequestInventoryView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.RequestMaterials(SEFA.MKM.Model.ViewModels.View.RequestMaterialsModel)">
            <summary>
            请料，时间点开始和结束时间，手动还是自动
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.JITRequestMaterials(SEFA.MKM.Model.ViewModels.View.JITRequestModel)">
             <summary>
             JIT请料，时间点开始和结束时间，手动还是自动
             </summary>
             <param name="reqModel">
             开始请料时间
              string planStarTime 
            未来X小时
             int ActureTime 
            手动 、自动
            string AutoType
             </param>
             <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.SugarRequestMaterials(SEFA.MKM.Model.ViewModels.View.RequestMaterialsModel)">
            <summary>
            白糖类请料，时间点开始和结束时间，手动还是自动
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.updateStste(SEFA.MKM.Model.ViewModels.View.RequestMaterialsModel)">
            <summary>
            每天0点调取更新全部未完成的状态为已完成
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.InterFaceTest">
            <summary>
            接口测试
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.SearchRequestInsertData(SEFA.PPM.Model.ViewModels.MKM.View.AddRequestInventory)">
            <summary>
            请料界面新增按钮
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.SearchPULLInsertData(SEFA.PPM.Model.ViewModels.MKM.View.AddRequestInventory)">
            <summary>
            拉界面新增按钮
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.AddRequestInventory(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.MKM.InterfaceView.MCodeReturnModel})">
            <summary>
            新增请料记录
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.AddFULLInventory(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.MKM.InterfaceView.MCodeReturnModel})">
            <summary>
            新增请料记录
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeMPull_13">
            <summary>
            每天13点定时调用该服务（原料料拉）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeMRequest_8">
            <summary>
            每天8点定时调用该服务（原料请料）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeMRequest_14">
            <summary>
            每天14点定时调用该服务（原料请料）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeRawMPull_02">
            <summary>
            每天02点定时调用该服务（物料拉料）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeMRequest_0">
            <summary>
            每天0点定时调用该服务（物料请料）
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.RequestInventoryViewController.FixedTimeMRequest_12">
            <summary>
            每天12点定时调用该服务（物料请料）
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.TimeAnalysisViewController._timeAnalysisViewServices">
            <summary>
            TimeAnalysisView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TimeAnalysisViewController.AnalyzeDowntime(SEFA.PPM.Model.ViewModels.TimeAnalysisViewRequestModel)">
            <summary>
            计划、非计划停机时间分析（停机）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.TippingEquipmentViewController._tippingEquipmentViewServices">
            <summary>
            TippingEquipmentView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingEquipmentViewController.GetFeedingMes(SEFA.PPM.Model.ViewModels.TippingEquipmentViewRequestModel)">
            <summary>
            投料看板获取投料信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingEquipmentViewController.GetLineTipping(SEFA.PPM.Model.ViewModels.LineTippingViewRequestModel)">
            <summary>
            投料看板-获取当日产线投料进度
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingEquipmentViewController.GetTippingVersion(SEFA.PPM.Model.ViewModels.TippingVersionPlayViewRequestModel)">
            <summary>
            投料看板-获取当前配方生产进度
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.VerifiyDetailController._verifiyDetailServices">
            <summary>
            VerifiyDetail
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.TK_ChangeState(SEFA.PPM.Model.ViewModels.VerifiyListRequestModel)">
            <summary>
            可退仓
            </summary>
            <param name="id"></param>
            <param name="stateType"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.ReInspection(System.String[])">
            <summary>
            复检查
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.IsReturn(System.String[])">
            <summary>
            退仓按钮检查
            </summary>
            <param name="id"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.ChangeRead(System.String,System.String)">
            <summary>
            改变勾选状态
            </summary>
            <param name="id"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.ChangeRemark(System.String,System.String)">
            <summary>
            改变备注同步数据库备注
            </summary>
            <param name="id"></param>
            <param name="remark"></param>
            <returns></returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:SEFA.PPMApi.Controllers.VerifiyDetailController.GetReturnWL_DetailByID(SEFA.PPM.Model.ViewModels.VerifiyListRequestModel)" -->
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.GetReturn_DetailTotal(SEFA.PPM.Model.ViewModels.VerifiyListRequestModel)">
            <summary>
            退库汇总按钮
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.VerifiyDetailController.ExportVerifiyData(SEFA.PPM.Model.ViewModels.VerifiyListRequestModel)">
            <summary>
            导出库存数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.VerifiyListController._verifiyListServices">
            <summary>
            VerifiyList
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchConsumeRequirementController._batchConsumeRequirementServices">
            <summary>
            BatchConsumeRequirement
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchController._batchServices">
            <summary>
            Batch
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchDcsController._batchDcsServices">
            <summary>
            BatchDcs
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchProducedRequirementController._batchProducedRequirementServices">
            <summary>
            BatchProducedRequirement
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchProductRequirementController._batchProductRequirementServices">
            <summary>
            BatchProductRequirement
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ChangemodelController">
            <summary>
            换型时间维护
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ChangemodelController._changemodelServices">
            <summary>
            Changemodel
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.CipSwitchtypeController">
            <summary>
            CIP切换方式维护
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.CipSwitchtypeController._cipSwitchtypeServices">
            <summary>
            CipSwitchtype
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipSwitchtypeController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipSwitchtypeController.ExportData(SEFA.PPM.Model.ViewModels.CipSwitchtypeRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipSwitchtypeController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.CipTimeController">
            <summary>
            CIP清洗时间
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.CipTimeController._cipTimeServices">
            <summary>
            CipTime
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipTimeController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipTimeController.ExportData(SEFA.PPM.Model.ViewModels.CipTimeRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CipTimeController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.CookinglossController">
            <summary>
            标准损耗维护
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.CookinglossController._cookinglossServices">
            <summary>
            Cookingloss
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CookinglossController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CookinglossController.ExportData(SEFA.PPM.Model.ViewModels.CookinglossRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.CookinglossController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.CooktimeController._cooktimeServices">
            <summary>
            Cooktime
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.EquipmentSalesContainerController._equipmentSalesContainerServices">
            <summary>
            EquipmentSalesContainer
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.EquipmentSalesContainerController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.EquipmentSalesContainerController.ExportData(SEFA.PPM.Model.ViewModels.EquipmentSalesContainerRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.EquipmentSalesContainerController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.FormulalossController">
            <summary>
            配方损耗计划表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.FormulalossController._formulalossServices">
            <summary>
            Formulaloss
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.FormulascheduleController">
            <summary>
            排产表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.FormulascheduleController._formulascheduleServices">
            <summary>
            Formulaschedule
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetBatchOrderByParentId(SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel)">
            <summary>
            根据主工单或者子工单列表
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetFormulaInfo(System.String)">
            <summary>
            获取调整信息
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackOrderInfo(System.String)">
            <summary>
            获取包装工单信息
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetProductionOrderInfo(System.String)">
            <summary>
            获取制造工单信息
            </summary>
            <param name="id">主键ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.FormulascheduleChange(SEFA.PPM.Model.Models.FormulascheduleEntity)">
            <summary>
            修改排产表  
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.FormulascheduleCompute(SEFA.PPM.Model.Models.FormulascheduleEntity)">
            <summary>
            计算排产表 
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.ProductionOrderChange(SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel)">
            <summary>
            修改生产工单
            </summary>
            <param name="request">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.ChangeProductionBatchOrder(SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel)">
            <summary>
            修改批次生产工单
            </summary>
            <param name="request">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreateProductionOrder(SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel)">
            <summary>
            创建生产工单
            </summary>
            <param name="request">请求信息</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.MergeFormulaOrder">
            <summary>
            合并同配方工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.AutoMergeFormulaOrder">
            <summary>
            自动合并同配方工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SplitFormulaOrder(SEFA.PPMApi.Controllers.SplitRequst)">
            <summary>
            同配方工单拆分
            </summary>
            <param name="request">拆分参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.ChangeOrderRecipeVersion(SEFA.PPMApi.Controllers.ChangeBomRequst)">
            <summary>
            修改配方版本
            </summary>
            <param name="requst">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.ComputeOrderRequire(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            计算工单需求量
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreatePlanOrder(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            生成计划工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreatePlanOrderByID(System.String[])">
            <summary>
            根据配方ID生成制造工单
            </summary>
            <param name="ids">配方排程ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreatePlanTable(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            生成计划大表
            </summary>
            <param name="req">开始时间</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreateScheduleByDate(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            生成排产表byDate
            </summary>
            <param name="req">开始时间</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreateScheduleByIds(System.String[])">
            <summary>
            生成排产表ById
            </summary>
            <param name="req">开始时间</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.FirstPlan(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            初步排产
            </summary>
            <param name="req">开始时间</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.LossChoose(System.String,System.String)">
            <summary>
            损耗选择
            </summary>
            <param name="orderId">工单ID</param>
            <param name="lossId">损耗ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetOrderToSortList(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            获取可排序工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SaveOrderSortList(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel})">
            <summary>
            保存配方排序
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackageOrderToSortList(SEFA.PPM.Model.ViewModels.PPM.PackOrderViewModel)">
            <summary>
            获取可排序包装工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackOrderByFillNo(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            获取可排序包装工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SavePackageOrderSortList(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel})">
            <summary>
            保存包装工单排序
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.PackOrderSort(SEFA.PPMApi.Controllers.TimeRequestModel)">
             <summary>
             包装工单排序
             </summary>
            <param name="req">请求参数</param>
             <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackOrderList(SEFA.PPM.Model.ViewModels.PPM.PackOrderViewModel)">
            <summary>
            获取包装工单列表
            </summary>
            <param name="reqModel">请求参数</param>
            <returns>返回结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackOrderByFormula(SEFA.PPM.Model.ViewModels.FormulascheduleRequestModel)">
            <summary>
            获取配方排产关联工单
            </summary>
            <param name="reqModel">请求参数</param>
            <returns>返回结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetBeatInfoList">
            <summary>
            获取节拍计算提醒
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SendOrderToSap">
            <summary>
            上传制造计划工单至SAP
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SendCkOrderToSap">
            <summary>
            上传制造工单至SAP
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SendCkOrderToSapByOrderId(System.String[])">
            <summary>
            上传制造工单至SAP
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CancleProductionOrder(System.String[])">
            <summary>
            取消工单
            </summary>
            <param name="orderId">工单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CancelBatchOrder(System.String[])">
            <summary>
            取消批次工单
            </summary>
            <param name="orderId">工单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.DeleteBatchOrder(System.String[])">
            <summary>
            删除批次工单
            </summary>
            <param name="orderId">工单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CreateBatchOrderByParentId(SEFA.PPMApi.Controllers.BatchAddModel)">
            <summary>
            添加批次工单
            </summary>
            <param name="orderId">工单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.UpdateFormulaProduceDate(SEFA.PPM.Model.ViewModels.FormulascheduleRequestModel)">
            <summary>
            修改配方排产日期
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.CancelFormula(SEFA.PPM.Model.ViewModels.FormulascheduleRequestModel)">
            <summary>
            取消配方
            </summary>
            <param name="reqModel">请求参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.DeletePlanOrder(System.String[])">
            <summary>
            删除计划工单
            </summary>
            <param name="orderId">请求参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.ChangePackOrderReworkInfo(SEFA.PPM.Model.ViewModels.PPM.PackOrderViewModel)">
            <summary>
            修改返工方法
            </summary>
            <param name="reqModel">请求参数</param>
            <returns>操作结果</returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.PublishOrder(System.String[])">
            <summary>
            发布工单
            </summary>
            <param name="orderId">工单ID</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetLineList(System.String)">
            <summary>
            获取产线清单
            </summary>
            <param name="areaCode">车间代码 CookingArea -- 煮制车间 ，PackingArea -- 灌包车间</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetPackOrderProperty(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            获取包装工单属性
            </summary>
            <param name="req">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.AddPackOrderProperty(SEFA.PPM.Model.ViewModels.PPM.OrderChangeProperyModel)">
            <summary>
            获取包装工单属性
            </summary>
            <param name="req">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.EditPackOrderRemark(SEFA.PPM.Model.ViewModels.PPM.OrderChangeProperyModel)">
            <summary>
            修改包装工单备注
            </summary>
            <param name="req">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetLineSegmentList(SEFA.PPMApi.Controllers.LineSegmentRequst)">
            <summary>
            获取产线清单
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetOrderBom(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            根据物料获取BOM
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetOrderBomByOrderId(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            根据工单ID获取BOM
            </summary>
            <param name="reqModel">工单ID请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetProOrderBomByOrderId(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            根据工单ID获取BOM
            </summary>
            <param name="reqModel">工单ID请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetFormulaScheduleDetail(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            根据工单ID获取缸重数据
            </summary>
            <param name="reqModel">工单ID请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetOrderUsableThroarInventory(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            根据工单ID获取可用喉头信息
            </summary>
            <param name="reqModel">工单ID请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.AddFormulascheduleDetail(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.FormulascheduleDetail})">
            <summary>
            修改保存配方分缸数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetSapprodversionByLine(SEFA.PPMApi.Controllers.OrderRequestModel)">
            <summary>
            获取pv
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.IgnorePackOrder(SEFA.PPM.Model.ViewModels.PPM.PackOrderIgnore)">
            <summary>
            忽略包装工单
            </summary>
            <param name="reqmodel">忽略实体</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.SaveSortFormulaTree(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PPM.SortFormulaTreeModel})">
            <summary>
            保存工单排序树
            </summary>
            <param name="reqmodel">排序数据</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetSortFormulaTree(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            获取工单排序树形结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetSortCookTree(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            获取煮制工单排序树形结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetHandPackTree(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            获取无灌装包装工单排序树形结构
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.GetSchedulePlanList(SEFA.PPMApi.Controllers.TimeRequestModel)">
            <summary>
            根据时间范围获取 排产计划 ，TPM专用
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.PoDataExport(SEFA.PPM.Model.ViewModels.PPM.ProductionOrderViewModel)">
            <summary>
            制造工单数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulascheduleController.FormulaDataExport(SEFA.PPM.Model.ViewModels.FormulascheduleRequestModel)">
            <summary>
            制造排产数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SchedulePlanModel.LineId">
            <summary>
            产线ID
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SchedulePlanModel.LineName">
            <summary>
            产线名称
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SchedulePlanModel.LineType">
            <summary>
            产线类型 煮制：COOK  灌包：FILLPACK
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SchedulePlanModel.PlanDate">
            <summary>
            计划日期
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SchedulePlanModel.PlanProductQty">
            <summary>
            计划生产重量
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.TimeRequestModel">
            <summary>
            时间请求
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.TimeRequestModel.lineId">
            <summary>
            产线id
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.TimeRequestModel.startTime">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.TimeRequestModel.endTime">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ChangeBomRequst">
            <summary>
            修改工单配方
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.ChangeBomRequst.orderId">
            <summary>
            工单id
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.ChangeBomRequst.newRecipeId">
            <summary>
            新配方
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.LineSegmentRequst">
            <summary>
            修改工单配方
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.LineSegmentRequst.areaCode">
            <summary>
            父节点代码
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.LineSegmentRequst.level">
            <summary>
            子节点类型
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.LineSegmentRequst.areaID">
            <summary>
            父节点ID
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.OrderRequestModel">
            <summary>
            请求类型
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderRequestModel.orderId">
            <summary>
            工单id
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderRequestModel.orderNo">
            <summary>
            工单号
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderRequestModel.mtrNo">
            <summary>
            物料编号
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderRequestModel.version">
            <summary>
            版本
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderRequestModel.lineId">
            <summary>
            产线
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.SplitRequst">
            <summary>
            拆分请求
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SplitRequst.orderId">
            <summary>
            工单ID
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SplitRequst.newRecipeId">
            <summary>
            新配方
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.SplitRequst.splitCount">
            <summary>
            拆分数量
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.BatchAddModel">
            <summary>
            批次工单添加模型
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.BatchAddModel.parentId">
            <summary>
            主工单ID
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.BatchAddModel.qty">
            <summary>
            数量
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.FormulaTankCapacityController">
            <summary>
            配方产线标准缸重配置
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.FormulaTankCapacityController._formulaTankCapacityServices">
            <summary>
            FormulaTankCapacity
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulaTankCapacityController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulaTankCapacityController.ExportData(SEFA.PPM.Model.ViewModels.FormulaTankCapacityRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.FormulaTankCapacityController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.LinerelationController">
            <summary>
            产线关系
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LinerelationController._linerelationServices">
            <summary>
            Linerelation
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LogsheetAdnDetailViewController._logsheetAdnDetailViewServices">
            <summary>
            LogsheetAdnDetailView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LogsheetController._logsheetServices">
            <summary>
            Logsheet
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetLogSheetList(SEFA.PPM.Model.ViewModels.LogsheetListByEqmentIdRequestModel)">
            <summary>
            根据设备id获取表单列表
            </summary>
            <param name="model">两个参数PoId和EqmentId</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.AutoCreateLogsheetAndDetail(SEFA.PPM.Model.ViewModels.AutoLogsheetCreatRequestModel)">
            <summary>
            根据输入参数创建表单及表单详情
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.LogsheetAndDetailQaSend(SEFA.PPM.Model.ViewModels.AutoLogsheetCreatRequestModel)">
            <summary>
            根据输入参数判断最新创建表单填的写结果是否通过
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.InserNewEntryByFrequency">
            <summary>
            根据输入参数判断最新创建表单填的写结果是否通过
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetLogsheetAndDetail(SEFA.PPM.Model.ViewModels.AutoLogsheetCreatRequestModel)">
            <summary>
            根据输入参数获取表单及表单详情
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetLogSheetAndDetailList(SEFA.PPM.Model.Models.LogsheetListEntity)">
            <summary>
            获取表单填写的历史记录
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetLogSheetAndDetailBySheetId(System.String)">
            <summary>
            获取指定的填写的历史记录
            </summary>
            <param name="logsheet"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.InserNewEntryWithParamter(SEFA.PPM.Model.Models.LogsheetByParameterRequestEntity)">
            <summary>
            通过输入参数创建表单
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.InsertNewEntry(SEFA.PPM.Model.Models.LogsheetListEntity)">
            <summary>
            New Entry按钮事件
            </summary>
            <param name="model"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.ApproveConfirm(System.Collections.Generic.List{System.String})">
            <summary>
            approve审核按钮事件
            </summary>
            <param name="sheetList"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.ApproveInPass(System.Collections.Generic.List{System.String})">
            <summary>
            approve审核不通过按钮事件
            </summary>
            <param name="sheetList"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetLogSheetListByPoId(System.String)">
            <summary>
            根据订单编号获取表单列表
            </summary>
            <param name="poId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetGroupName">
            <summary>
            获取表单组名
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetAllLogList(SEFA.PPM.Model.ViewModels.LogsheetHistoryListViewRequestModel)">
            <summary>
            获取所有表单列表的历史数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetController.GetPageAllLogList(SEFA.PPM.Model.ViewModels.LogsheetHistoryListViewRequestModel)">
            <summary>
            获取表单列表的历史数据
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LogsheetDetailController._logsheetDetailServices">
            <summary>
            LogsheetDetail
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetDetailController.GetLastSheetDetail(System.String)">
            <summary>
            获取最新填写的detail信息
            </summary>
            <param name="sheetId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetDetailController.UpDataList(System.Collections.Generic.List{SEFA.PPM.Model.Models.LogsheetDetailEntity})">
            <summary>
            detailList更新
            </summary>
            <param name="requestList"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetDetailController.UploadFile(Microsoft.AspNetCore.Http.IFormFile)">
            <summary>
            文件上传
            </summary>
            <param name="file"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetDetailController.Download(System.String)">
            <summary>
            直接下载文件
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LogsheetDetailController.GetFileUrl(System.String)">
            <summary>
            获取下载文件URL
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LogsheetHistoryListViewController._logsheetHistoryListViewServices">
            <summary>
            LogsheetHistoryListView
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.OilFormulaController">
            <summary>
            加热油物料配置
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.OilFormulaController._oilFormulaServices">
            <summary>
            OilFormula
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.OilFormulaController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.OilFormulaController.ExportData(SEFA.PPM.Model.ViewModels.OilFormulaRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.OilFormulaController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.OrderBomController">
            <summary>
            工单BOM
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.OrderBomController._orderBomServices">
            <summary>
            OrderBom
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.OrderBomController.GetSapOrderBom(SEFA.PPM.Model.Models.Interface.SapRequestModel)">
            <summary>
            获取SAP生产BOM信息
            </summary>
            <returns></returns>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderBomRequst.orderId">
            <summary>
            工单ID
            </summary>
        </member>
        <member name="P:SEFA.PPMApi.Controllers.OrderBomRequst.type">
            <summary>
            工单类型 CK -煮制， PK- 包装
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PackspeedController._packspeedServices">
            <summary>
            Packspeed
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackspeedController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackspeedController.ExportData(SEFA.PPM.Model.ViewModels.PackspeedRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackspeedController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.PackunitController">
            <summary>
            包装单位
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PackunitController._packunitServices">
            <summary>
            Packunit
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackunitController.GetSapPackingUnit">
            <summary>
            获取SAP 包装单位数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PackunitController.AsyncGetSapPackingUnit">
            <summary>
            异步获取SAP 包装单位数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.PlanlistController">
            <summary>
            计划工单大表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PlanlistController._planlistServices">
            <summary>
            Planlist
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PlanlistController.GetList(SEFA.PPM.Model.ViewModels.PlanlistRequestModel)">
            <summary>
            获取数据列表
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PlanlistController.GetPageList(SEFA.PPM.Model.ViewModels.PlanlistRequestModel)">
            <summary>
            获取分页数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.PlanOrderController">
            <summary>
            生产计划工单
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PlanOrderController._planOrderServices">
            <summary>
            PlanOrder
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PlanOrderController.ExportData(SEFA.PPM.Model.ViewModels.PlanOrderRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoConsumeActualController._poConsumeActualServices">
            <summary>
            PoConsumeActual
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoConsumeRequirementController._poConsumeRequirementServices">
            <summary>
            PoConsumeRequirement
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.PoMaterialRemarkController">
            <summary>
            工单BOM物料备注信息
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoMaterialRemarkController._poMaterialRemarkServices">
            <summary>
            PoMaterialRemark
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PoMaterialRemarkController.SaveRemarkList(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PoMaterialRemarkRequestModel})">
            <summary>
            保存工单备注信息
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PoMaterialRemarkController.GetOrderBomMatList(System.String[])">
            <summary>
            获取工单BOM物料备注信息
            </summary>
            <param name="orderId">查询ID列表</param>param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoProducedActualController._poProducedActualServices">
            <summary>
            PoProducedActual
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoProducedExecutionEquController._poProducedExecutionEquServices">
            <summary>
            PoProducedExecutionEqu
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoProducedRequirementController._poProducedRequirementServices">
            <summary>
            PoProducedRequirement
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoSegmentRequirementController._poSegmentRequirementServices">
            <summary>
            PoSegmentRequirement
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ProductionOrderCipController">
            <summary>
            工单CIP设置
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProductionOrderCipController._productionOrderCipServices">
            <summary>
            ProductionOrderCip
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProductionOrderController._productionOrderServices">
            <summary>
            ProductionOrder
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.CreateCookieOrder(SEFA.PPM.Model.Models.Interface.MM_Cookie_Order_Res)">
            <summary>
            SAP回调接口
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.BatchEditOrderRemark(SEFA.PPM.Model.ViewModels.PPM.OrderRemarkChangeModel)">
            <summary>
            批量修改工单备注
            </summary>
            <param name="req"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.GetCKPoBomAndRouting(System.String[])">
            <summary>
            批量获取煮制工单BOM和Routing信息
            </summary>
            <param name="ids">工单ID集合</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.GetPKPoBomAndRouting(System.String[])">
            <summary>
            批量获取包装工单BOM和Routing信息
            </summary>
            <param name="ids">工单ID集合</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.ComparePoLongTextById(System.Collections.Generic.List{System.String})">
            <summary>
            比对长文本信息
            </summary>
            <param name="ids">工单ID集合</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProductionOrderController.ComparePoLongTextJob">
            <summary>
            比对长文本信息Job
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ProductionOrderPropertyController">
            <summary>
            生产工单属性表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProductionOrderPropertyController._productionOrderPropertyServices">
            <summary>
            ProductionOrderProperty
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ProductspeedController">
            <summary>
            生产速度维护
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProductspeedController._productspeedServices">
            <summary>
            Productspeed
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProdWeightController._prodWeightServices">
            <summary>
            ProdWeight
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdWeightController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdWeightController.ExportData(SEFA.PPM.Model.ViewModels.ProdWeightRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdWeightController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.RecommandcapacityController">
            <summary>
            产线配方及缸容量推荐
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.RecommandcapacityController._recommandcapacityServices">
            <summary>
            Recommandcapacity
            </summary>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.SalescontainerController">
            <summary>
            销售容器
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SalescontainerController._salescontainerServices">
            <summary>
            Salescontainer
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SalescontainerController.GetSapSalesContainer">
            <summary>
            获取SAP SalesContainer
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SalescontainerController.AsyncGetSapSalesContainer">
            <summary>
            异步获取SAP SalesContainer
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.SappackorderController">
            <summary>
            SAP 包装工单
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SappackorderController._sappackorderServices">
            <summary>
            Sappackorder
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetSapPackOrder">
            <summary>
            获取SAP包装工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.AsyncGetSapPackOrder">
            <summary>
            异步获取SAP包装工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.AsynCKOrderResult(SEFA.PPM.Model.Models.Interface.PP_SAP_CKORD_AsynRequest)">
            <summary>
            接收SAP反馈的 煮制计划工单/煮制制造工单创建，反馈结果
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns> 
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetOrderRouting(SEFA.PPM.Model.Models.Interface.SapRequestModel)">
            <summary>
            获取SAP工单工艺路径
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.CreateCookPlanOrder(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PPM.OrderCreateModel})">
            <summary>
            创建煮制计划工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.CreateCookOrder(System.Collections.Generic.List{SEFA.PPM.Model.ViewModels.PPM.OrderCreateModel})">
            <summary>
            创建煮制工单
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.SendSapOrderStatus(SEFA.PPM.Model.ViewModels.PPM.OrderStatusRequestModel)">
            <summary>
            更新工单状态
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetSapPoRoutingInfo(SEFA.PPM.Model.ViewModels.PPM.OrderStatusRequestModel)">
            <summary>
            根据工单号获取工单工艺路线信息，包含标准工时
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetMaterialRequirementDto(SEFA.PPM.Model.ViewModels.PPM.SapPackOrderRequestModel)">
            <summary>
            汇总物料需求
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetMaterialRequirementList(SEFA.PPM.Model.ViewModels.PPM.SapPackOrderRequestModel)">
            <summary>
            汇总物料需求
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.GetMaterialRequirementTable(SEFA.PPM.Model.ViewModels.PPM.SapPackOrderRequestModel)">
            <summary>
            汇总物料需求Table
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.ExportData(SEFA.PPM.Model.ViewModels.PPM.SapPackOrderRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderController.UpdateSapPackOrderSpeed(SEFA.PPM.Model.ViewModels.PPM.OrderSpeedRequestModel)">
            <summary>
            更新工单速度
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SappackorderrecordController._sappackorderrecordServices">
            <summary>
            Sappackorderrecord
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SappackorderrecordController.DealSapPackOrder">
            <summary>
            处理SapPackOrder
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.SapprodversionController">
            <summary>
            SAP  ProdVersion
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SapprodversionController._sapprodversionServices">
            <summary>
            Sapprodversion
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SapprodversionController.GetSapSalesContainer(System.String)">
            <summary>
            获取SAP生产版本信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SapprodversionController.AsyncGetSapSalesContainer(System.String)">
            <summary>
            异步获取SAP生产版本信息
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.SpecialformulacapacityController">
            <summary>
            特殊配方对应缸重维护
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SpecialformulacapacityController._specialformulacapacityServices">
            <summary>
            Specialformulacapacity
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SpecialformulacapacityController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SpecialformulacapacityController.ExportData(SEFA.PPM.Model.ViewModels.SpecialformulacapacityRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SpecialformulacapacityController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.StandardPeriodLotController._standardPeriodLotServices">
            <summary>
            StandardPeriodLot
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.StandardPeriodLotController.GetLineList(System.String)">
            <summary>
            获取产线清单
            </summary>
            <param name="areaCode">车间代码 Formulation -- 配置车间 ，Packing -- 包装车间</param>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.ThroatadditionController">
            <summary>
            喉头添加基础表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ThroatadditionController._throatadditionServices">
            <summary>
            Throataddition
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ThroatadditionController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ThroatadditionController.ExportData(SEFA.PPM.Model.ViewModels.ThroatadditionRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ThroatadditionController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.UnproductiveSearchViewController._unproductiveSearchViewServices">
            <summary>
            UnproductiveSearchView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.UnproductiveTimeController._unproductiveTimeServices">
            <summary>
            UnproductiveTime
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.UnproductiveTimeController.SyncInsertData">
            <summary>
            定时执行往辅助工时表插入数据
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BatchComsumeMaterialListViewController._batchComsumeMaterialListViewServices">
            <summary>
            BatchComsumeMaterialListView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BBatchListViewController._bBatchListViewServices">
            <summary>
            BBatchListView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BBatchListViewController.UpdateBatchStatus(SEFA.PPM.Model.Models.UpdateStatusRequestModel)">
            <summary>
            修改批次状态
            </summary>
            <param name="reqModel">type=0修改批次状态 type=1修改批次备料状态</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.BProductionOrderListViewController._bProductionOrderListViewServices">
            <summary>
            BProductionOrderListView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.UpdatePoStatus(SEFA.PPM.Model.Models.UpdateStatusRequestModel)">
            <summary>
            修改工单状态
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.UpdateShift(SEFA.PPM.Model.Models.UpdateStatusRequestModel)">
            <summary>
            更新批次状态
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.GetShiftSelect">
            <summary>
            获取班次信息
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.UpdateQaStatus(SEFA.PPM.Model.Models.UpdateQaStatusRequestModel)">
            <summary>
            修改QA状态
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.GetLtexts(SEFA.PPM.Model.Models.UpdateQaStatusRequestModel)">
            <summary>
            获取工单长文本
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.GetCookieOrderLtexts(SEFA.PPM.Model.Models.UpdateQaStatusRequestModel)">
            <summary>
            获取工单长文本
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.UpdateLtexts(SEFA.PPM.Model.Models.UpdateQaStatusRequestModel)">
            <summary>
            修改工单长文本
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.RebuildBatch(System.Collections.Generic.List{System.String})">
            <summary>
            重新解析生成批次
            </summary>
            <param name="productionOrderIds"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.BindPoRecipe(System.Collections.Generic.List{System.String})">
            <summary>
            重新绑定配方
            </summary>
            <param name="productionOrderIds"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.PackReport(System.Collections.Generic.List{System.String})">
            <summary>
            
            </summary>
            <param name="productionOrderIds"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.SavePackReport(System.Collections.Generic.List{SEFA.PPM.IServices.OrderTreeModel})">
            <summary>
            
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.GetEquipmentsSelect">
            <summary>
            获取设备下拉选
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.BProductionOrderListViewController.ProduceOpen(SEFA.PPM.Model.ViewModels.PTM.StartPoRequestModel)">
            <summary>
            获取产出界面数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoConsumeMaterialListViewController._poConsumeMaterialListViewServices">
            <summary>
            PoConsumeMaterialListView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.PoConsumeMaterialListViewController.ExportData(SEFA.PPM.Model.ViewModels.PoConsumeMaterialListViewRequestModel)">
            <summary>
            导出数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
            <exception cref="T:System.Exception"></exception>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoProducedMaterialListViewController._poProducedMaterialListViewServices">
            <summary>
            PoProducedMaterialListView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.WeekScheduleBomController._weekScheduleBomServices">
            <summary>
            WeekScheduleBom
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.WeekScheduleController._weekScheduleServices">
            <summary>
            WeekSchedule
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.GetSplitBatchInfo(SEFA.PPM.Model.ViewModels.WeekScheduleRequestModel)">
            <summary>
            获取批次拆分信息
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.GetAddBatchInfo(SEFA.PPM.Model.ViewModels.ProductionOrderRequestModel)">
            <summary>
            获取批次拆分信息
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.SaveForm(SEFA.PPM.Model.Models.WeekScheduleEntity)">
            <summary>
            新增周计划
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.ExportData(SEFA.PPM.Model.ViewModels.WeekScheduleRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WeekScheduleController.DownLoadTemplate">
            <summary>
            下载模板
            </summary>
            <returns></returns>
        </member>
        <member name="T:SEFA.PPMApi.Controllers.WorkorderthroatController">
            <summary>
            工单添加喉头计划表
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.WorkorderthroatController._workorderthroatServices">
            <summary>
            Workorderthroat
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.WorkorderthroatController.ExportData(SEFA.PPM.Model.ViewModels.WorkorderthroatRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ConfirmationController._confirmationServices">
            <summary>
            Confirmation
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeAutoViewController._downtimeAutoViewServices">
            <summary>
            DowntimeAutoView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeCategroyController._downtimeCategroyServices">
            <summary>
            DowntimeCategroy
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeGroupController._downtimeGroupServices">
            <summary>
            DowntimeGroup
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimeGroupController.GetWithoutParentGroupIdList(SEFA.PPM.Model.ViewModels.DowntimeGroupRequestModel)">
            <summary>
            获取不包含ParentGroupId字段的group数据
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimeGroupController.NoStandardPro(SEFA.PPM.Model.ViewModels.SIM.View.NoStandardProRequestModel)">
            <summary>
            NEW 非标产品工时及产量（产量+工时）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeManualViewController._downtimeManualViewServices">
            <summary>
            DowntimeManualView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeReasonController._downtimeReasonServices">
            <summary>
            DowntimeReason
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeReasonEqAssocController._downtimeReasonEqAssocServices">
            <summary>
            DowntimeReasonEqAssoc
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimeReasonMappingController._downtimeReasonMappingServices">
            <summary>
            DowntimeReasonMapping
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimeReasonMappingController.GetListByEqMentId(System.String)">
            <summary>
            返回eqMentId的reasonList
            </summary>
            <param name="sort"></param>
            <param name="eqMentId"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.InfluxDbController._influxDbServices">
            <summary>
            InfluxDb
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LineAndEquipmentViewController._lineAndEquipmentViewServices">
            <summary>
            LineAndEquipmentView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.MReasonEqViewController._mReasonEqViewServices">
            <summary>
            MReasonEqView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.MReasonMappingViewController._mReasonMappingViewServices">
            <summary>
            MReasonMappingView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.PoExecutionHistroyController._poExecutionHistroyServices">
            <summary>
            PoExecutionHistroy
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProduceActualViewController._produceActualViewServices">
            <summary>
            ProduceActualView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProduceLocationViewController._produceLocationViewServices">
            <summary>
            ProduceLocationView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProduceViewController._produceViewServices">
            <summary>
            ProduceView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProductionOrderViewController._productionOrderViewServices">
            <summary>
            ProductionOrderView
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.TippingMlistViewController._tipingMlistViewServices">
            <summary>
            TipingMlistView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.StartTipping(SEFA.PPM.Model.Models.TippingMlistViewModel)">
            <summary>
            开始投料
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.Tipping(SEFA.PPM.Model.ViewModels.PTM.TippingSclistRequestModel)">
            <summary>
            扫描库存投料
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.CheckTippingType(System.String)">
            <summary>
            
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.GetTippingTransferSelectList(System.String)">
            <summary>
            获取下拉选
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.GetTippingTransferSelect(System.String)">
            <summary>
            获取已经选中的数据
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.SaveSelect(SEFA.MKM.Model.Models.MKM.DestinationSelect)">
            <summary>
            
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingMlistViewController.GetFunctionPropertyValue(System.String,System.String,System.String)">
            <summary>
            获取Function属性值
            </summary>
            <param name="equipmentId">02405072-0370-0836-163e-0370f6000000</param>
            <param name="functionCode">Tipping</param>
            <param name="propertyName">TippingType、TipOnDock</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.TippingPrecheckViewController._tippingPrecheckViewServices">
            <summary>
            TippingPrecheckView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingPrecheckViewController.GetCount(SEFA.PPM.Model.ViewModels.TippingPrecheckViewRequestModel)">
            <summary>
            获取检查完成数量/总数量
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.TippingPrecheckViewController.Precheck(SEFA.PTM.Model.ViewModels.ConsolPoRequestModel)">
            <summary>
            预检查操作：通过key值判断执行不同逻辑 key=1开始预检查,key=2扫描库存
            Body{\"BatchId\":\"\",\"TraceCode\":\"\"}key=1时只用传BatchId，key=2时两个都要传
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.DowntimetgtController._downtimetgtServices">
            <summary>
            Downtimetgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimetgtController.ExportData(SEFA.PPM.Model.ViewModels.DowntimetgtRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimetgtController.DowntimetgtDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.DowntimetgtController.SaveForm(SEFA.PPM.Model.Models.DowntimetgtEntity)">
            <summary>
            新增/编辑
            </summary>
            <param name="entity"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableCokadjustmentController._imtableCokadjustmentServices">
            <summary>
            ImtableCokadjustment
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableCokadjustmentController.AdjustmentRate(SEFA.PPM.Model.ViewModels.ImtableCokadjustmentRequestModel)">
            <summary>
            煮缸调节率（QA质检单）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableDowntimerateController._imtableDowntimerateServices">
            <summary>
            ImtableDowntimerate
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableOeeController._imtableOeeServices">
            <summary>
            ImtableOee
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtablePakmatLossController._imtablePakmatLossServices">
            <summary>
            ImtablePakmatLoss
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtablePakmatLossController.PackagingRate(SEFA.PPM.Model.ViewModels.ImtablePakmatLossRequestModel)">
            <summary>
            NEW 包材损耗率--按小类汇总
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtablePakmatLossController.PackagingLine(SEFA.PPM.Model.ViewModels.ImtablePakmatLossRequestModel)">
            <summary>
            NEW 包材损耗率--按产线汇总
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtablePakmatLossController.DownTimeList(SEFA.PPM.Model.ViewModels.ImtableDowntimerateRequestModel)">
            <summary>
            包装线设备（灌装机）停机率汇总（停机）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableRowmatLossController._imtableRowmatLossServices">
            <summary>
            ImtableRowmatLoss
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableRowmatLossController.GetMaterialLotSql(System.DateTime,System.DateTime,System.String,System.String,System.String,System.String,System.String,System.Int32,System.Int32,System.Int32)">
            <summary>
            NEW原料损耗率
            </summary>
            <param name="startTime"></param>
            <param name="endTime"></param>
            <param name="sapEquipmentId"></param>
            <param name="LineId"></param>
            <param name="formulaType"></param>
            <param name="materialCode"></param>
            <param name="MaterialGroupName"></param>
            <param name="GroupByFrequence"></param>
            <param name="GroupByFormulaGroup"></param>
            <param name="GroupByMaterial"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableRowmatLossController.MaterialLoss(SEFA.PPM.Model.ViewModels.ImtableRowmatLossRequestModel)">
            <summary>
            原料损耗率（损耗）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableSapreportController._imtableSapreportServices">
            <summary>
            ImtableSapreport
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.ProductionInfo(SEFA.PPM.Model.ViewModels.ImtableSapreportRequestModel)">
            <summary>
            产量明细表（产量+工时）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.ReworkWorkOrder(SEFA.PPM.Model.ViewModels.SIM.View.ReworkWorkOrderRequestModel)">
            <summary>
            返工工单（产量+工时）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.GetManHour(SEFA.PPM.Model.ViewModels.ImtableSapreportRequestModel)">
            <summary>
            人时部分
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.GetProduction(SEFA.PPM.Model.ViewModels.ImtableSapreportRequestModel)">
            <summary>
            产量部分
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.GetProductivity(SEFA.PPM.Model.ViewModels.ImtableSapreportRequestModel)">
            <summary>
            生产力部分
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.GetYield(SEFA.PPM.Model.ViewModels.SIM.View.YieldRequestModel)">
            <summary>
            NEW三厂每月产出率 （产量）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ImtableSapreportController.CompletionRate(SEFA.PPM.Model.ViewModels.SIM.View.CompletionRateReauestModel)">
            <summary>
            NEW 工单一次性完成率（产量）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ImtableUnproductivetimeController._imtableUnproductivetimeServices">
            <summary>
            ImtableUnproductivetime
            </summary>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.KpitgtController._kpitgtServices">
            <summary>
            Kpitgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.ExportData(SEFA.PPM.Model.ViewModels.KpiTgtViewRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.KpiDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetProOrderInfo(SEFA.PPM.Model.ViewModels.SIM.View.MPDRequeatModel)">
            <summary>
            SIM2 计划执行信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.MaterialProgress(SEFA.PPM.Model.ViewModels.SIM.View.MPDRequeatModel)">
            <summary>
            当前备料进度
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.ProcessWaiting(SEFA.PPM.Model.ViewModels.SIM.View.MPDRequeatModel)">
            <summary>
            流程等待时间
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetDataListBySearch(SEFA.PPM.Model.ViewModels.KpitgtRequestModel)">
            <summary>
            根据查询条件获取数据
            </summary>
            <param name="model">参数StartTime，EndTime选填</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetDataName">
            <summary>
            根据查询条件获取数据名称
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetDataType">
            <summary>
            根据查询条件获取数据类型
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            导入数据
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetPageList(SEFA.PPM.Model.ViewModels.KpiTgtViewRequestModel)">
            <summary>
            KPI目标值查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.LossRate_Day">
            <summary>
            原料损耗率
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.PackagingLossRate_Day">
            <summary>
            包材损耗率
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetKPIDataList(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestModel)">
            <summary>
            获取多个KPI值
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetKPISeries(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestSingleModel)">
            <summary>
            获取KPI折线图数据
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetKpiData(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestSingleModel)">
            <summary>
            获取单个KPI数据
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.GetRisingSunPictureSeries(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestSingleModel)">
            <summary>
            获取单个KPI数据
            </summary>
            <param name="reqModel">请求参数</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.MaterialGroupLoss(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestSingleModel)">
            <summary>
            物料损耗（TOP2超标提醒）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.EquipmentStatus(SEFA.PPM.Model.ViewModels.SIM.KpiValueRequestSingleModel)">
            <summary>
            设备管理信息
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.KpitgtController.EquipmentStatusHouur">
            <summary>
            设备管理信息+进度状态H
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LosstgtController._losstgtServices">
            <summary>
            Losstgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input">文件的物理路径</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtController.ExportData(SEFA.PPM.Model.ViewModels.LosstgtRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtController.LosstgtDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LosstgtControllerLine._losstgtServicesLine">
            <summary>
            Losstgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerLine.ExportData(SEFA.PPM.Model.ViewModels.LosstgtRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerLine.LossLineDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerLine.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input">文件的物理路径</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerLine.GetPageList(SEFA.PPM.Model.ViewModels.LosstgtViewRequestModel)">
            <summary>
            包材损耗-生产线 查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.LosstgtControllerPakMatGroup._losstgtServicesPakMatGroup">
            <summary>
            Losstgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerPakMatGroup.ExportData(SEFA.PPM.Model.ViewModels.LosstgtRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerPakMatGroup.MtrGroupDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.LosstgtControllerPakMatGroup.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input">文件的物理路径</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.OeeReportViewController._oeeReportViewServices">
            <summary>
            OeeReportView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.OeeReportViewController.GetOeeDate(SEFA.PPM.Model.ViewModels.OeeReportViewRequestModel)">
            <summary>
            12.3.7	OEE数据（产量+工时+停机）
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProdtgtContainerController._prodtgtContainerServices">
            <summary>
            ProdtgtContainer
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            产量预算-按产线导入吨
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.ImportBoxData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            产量预算-按产线导入箱
            </summary>
            <param name="input"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.ExportData(SEFA.PPM.Model.ViewModels.ProdtgtContainerViewRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.ProdtgtDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.GetPageList(SEFA.PPM.Model.ViewModels.ProdtgtContainerViewRequestModel)">
            <summary>
            产量预算-按产线规查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtContainerController.SaveForm(SEFA.PPM.Model.Models.ProdtgtContainerEntity)">
            <summary>
            产量规格-按产量预算新增/修改
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.ProdtgtSaucetypeController._prodtgtSaucetypeServices">
            <summary>
            ProdtgtSaucetype
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtSaucetypeController.ExportData(SEFA.PPM.Model.ViewModels.ProdtgtSaucetypeRequestModel)">
            <summary>
            数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtSaucetypeController.ProTagDownload">
            <summary>
            模板下载
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.ProdtgtSaucetypeController.ImportData(SEFA.DFM.Model.ViewModels.FileImportDto)">
            <summary>
            数据导入
            </summary>
            <param name="input">文件的物理路径</param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SafetytgtController._safetytgtServices">
            <summary>
            Safetytgt
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SafetytgtController.GetSafetMsg(SEFA.PPM.Model.ViewModels.SafetytgtRequestModel)">
            <summary>
            看板安全信息统计
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PPMApi.Controllers.SafetytgtViewController._safetytgtViewServices">
            <summary>
            SafetytgtView
            </summary>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SafetytgtViewController.GetPageList(SEFA.PPM.Model.ViewModels.SafetytgtViewRequestModel)">
            <summary>
            安全质量维护查询
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SafetytgtViewController.SafetyExportData(SEFA.PPM.Model.ViewModels.SafetytgtViewRequestModel)">
            <summary>
            安全质量维护数据导出
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SafetytgtViewController.SaveForm(SEFA.PPM.Model.Models.SafetytgtEntity)">
            <summary>
            安全质量维护新增/编辑
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PPMApi.Controllers.SafetytgtViewController.Delete(System.String[])">
            <summary>
            安全质量维护删除
            </summary>
            <param name="ids"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.MKM.Api.Controllers.WarehouseStorageController._warehouseStorageServices">
            <summary>
            
            </summary>
        </member>
        <member name="F:SEFA.PTMApi.Controllers.PoProducedExecutionController._poProducedExecutionServices">
            <summary>
            PoProducedExecution
            </summary>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.SendOrderInfoToSS(System.String,System.Int32,System.String,System.Decimal)">
            <summary>
            工单需求信息同步
            </summary>
            <param name="productionOrderId"></param>
            <param name="actionType"></param>
            <param name="lotCode"></param>
            <param name="quantity"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.SS_ScanInfo(SEFA.PPM.Model.Models.Interface.SS_Scan_Info)">
            <summary>
            输垛系统-上料扫码信息同步
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetWorkOrderInfo(SEFA.PPM.Model.Models.Interface.FWS_WorkOrderInfo_Res)">
            <summary>
            防伪系统获取工单信息接口
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.SendWorkOrderInfoToColos(System.String,System.String)">
            <summary>
            发送工单信息给COLOS接口
            </summary>
            <param name="equipmentId"></param>
            <param name="orderId"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetLabel(SEFA.PPM.Model.Models.Interface.COLOS_CreateLabel)">
            <summary>
            COLOS系统取得标签信息接口
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.UpdateLabelStatus(SEFA.PPM.Model.Models.Interface.COLOS_LabelStatus)">
            <summary>
            COLOS系统更新标签状态接口
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetTokenFromEMS">
            <summary>
            获取Token
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetEnergyInstrumentList(SEFA.PPM.Model.Models.Interface.ENERGY_InstrumentList_Req)">
            <summary>
            获取仪表清单
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetEnergyInstrumentPageList(SEFA.PPM.Model.Models.Interface.ENERGY_InstrumentList_ReqPage)">
            <summary>
            获取仪表清单
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetEnergyDataByDay(System.String)">
            <summary>
            获取能耗数据
            </summary>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.ConsumeFeedback(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_ConsumeFeedback})">
            <summary>
            物料消耗反馈_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.ProductionFeedback(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_ProductionFeedback})">
            <summary>
            生产执行信息反馈_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.RequestFeeding(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_RequestFeeding})">
            <summary>
            请求投料_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.InitiateQA(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_InitiateQA})">
            <summary>
            发起QA_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.CookingCompleted(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_CookingCompleted})">
            <summary>
            煮料完成出料_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.StorageToFilling(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_StorageToFilling})">
            <summary>
            储存缸到灌装机出料_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.ReceiveCIPRecords(System.Collections.Generic.List{SEFA.PPM.Model.Models.Interface.MMI_CIP})">
            <summary>
            接收CIP记录_Proleit
            </summary>
            <param name="reqModel"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetBatchCode(SEFA.PPM.Model.Models.GetBatchCodeModel)">
            <summary>
            获取批次号
            </summary>
            <param name="equipmentCode">设备编码</param>
            <param name="productionDate">生产日期</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetBatchCodeByProLine(SEFA.PPM.Model.Models.GetBatchCodeModel)">
            <summary>
            获取批次号
            </summary>
            <param name="equipmentCode">设备编码</param>
            <param name="productionDate">生产日期</param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.PoProducedExecutionController.GetRunOrder(System.String)">
            <summary>
            获取当前设备正在Running的工单
            </summary>
            <param name="equipmentId"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PTMApi.Controllers.ConsumeViewController._consumeViewServices">
            <summary>
            ConsumeView
            </summary>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.ConsumeViewController.ApiByType(System.String)">
            <summary>
            
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.ConsumeViewController.AutoReport(System.String,System.String,System.String)">
            <summary>
            自动报工接口
            </summary>
            <param name="reportType"></param>
            <param name="equipmentCode"></param>
            <param name="tag"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.ConsumeViewController.AutoReport2(System.String,System.String,System.String)">
            <summary>
            自动报工接口
            </summary>
            <param name="reportType"></param>
            <param name="equipmentCode"></param>
            <param name="tag"></param>
            <returns></returns>
        </member>
        <member name="M:SEFA.PTMApi.Controllers.ConsumeViewController.AutoReport_v2(System.String,System.String,System.String)">
            <summary>
            自动报工接口-增加油自动报工逻辑
            </summary>
            <param name="reportType"></param>
            <param name="equipmentCode"></param>
            <param name="tag"></param>
            <returns></returns>
        </member>
        <member name="F:SEFA.PTMApi.Controllers.EquipmentFunctionViewController._equipmentFunctionViewServices">
            <summary>
            EquipmentFunctionView
            </summary>
        </member>
        <member name="F:SEFA.PTMApi.Controllers.EquipmentProcessOrderViewController._equipmentProcessOrderViewServices">
            <summary>
            EquipmentProcessOrderView
            </summary>
        </member>
        <member name="F:SEFA.PTMApi.Controllers.ProcessOrderViewController._processOrderViewServices">
            <summary>
            ProcessOrderView
            </summary>
        </member>
    </members>
</doc>
