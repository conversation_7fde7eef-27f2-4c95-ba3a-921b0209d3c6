{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\index.vue", "mtime": 1749177894394}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+EA;AAEA;AACA,SACAA,oBADA,EACAC,wBADA,QAEA,wCAFA;AAGA,wD,CAEA;AACA;;AAEA;EACAC,iBADA;EAEAC;IACA;IACAC;EAFA,CAFA;;EAMAC;IACA;MACAC;QACAC,YADA;QAEAC;MAFA,CADA;MAKAC,QALA;MAMAC,eANA;MAOAC,2CAPA;MAQAC,aARA;MASAC,cATA;MAUAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,EAYA;QAAAF;QAAAC;QAAAC;MAAA,CAZA,EAaA;QAAAF;QAAAC;QAAAC;MAAA,CAbA,EAcA;QAAAF;QAAAC;QAAAC;MAAA,CAdA,EAeA;QAAAF;QAAAC;QAAAC;MAAA,CAfA,CAVA;MA2BAC,QA3BA;MA4BAC;QACAjB,cADA;QAEAkB,sBAFA,CAGA;QACA;QACA;;MALA;IA5BA;EAoCA,CA3CA;;EA4CAC;IACA;IACA;IACA;MACA;IACA,CAFA;;IAGAC;MACA;IACA,CAFA;EAGA,CArDA;;EAsDAC;IACAC;MACA;QACA;MACA;IACA,CALA;;IAMAC;MACA;IACA,CARA;;IASAC;MACA;MACA;IACA,CAZA;;IAaAC;MACA;MACA;IACA,CAhBA;;IAiBAC;MACA;MACA;IACA,CApBA;;IAsBAC;MACA;QACAC,4BADA;QAEAC,mCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAlC;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAmC,KAVA,CAUAC;QACAC;MACA,CAZA;IAaA,CApCA;;IAsCAC;MACArC;QACA;QACA;MACA,CAHA;IAIA;;EA3CA;AAtDA,E,CAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "names": ["delStandardPeriodLot", "getStandardPeriodLotList", "name", "components", "FormDialog", "data", "searchForm", "pageIndex", "pageSize", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "loading", "tableOption", "code", "width", "align", "mainH", "buttonOption", "serveIp", "mounted", "window", "methods", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "delRow", "title", "message", "confirmText", "cancelText", "then", "catch", "err", "console", "getTableData"], "sourceRoot": "src/views/planManagement/standardPeriodLot", "sources": ["index.vue"], "sourcesContent": ["<!--\n * @Descripttion: (标准时间批量/PPM_M_STANDARD_PERIOD_LOT)\n * @version: (1.0)\n * @Author: (admin)\n * @Date: (2025-05-14)\n * @LastEditors: (admin)\n * @LastEditTime: (2025-05-14)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n            \t\t\t\t    \n      <el-form-item label=\"产线代码\" prop=\"lineCode\">\n        <el-input v-model=\"searchForm.lineCode\" placeholder=\"请输入产线代码\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"物料代码\" prop=\"materialCode\">\n        <el-input v-model=\"searchForm.materialCode\" placeholder=\"请输入物料代码\" />\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n            {{ $t('GLOBAL._XZ') }}\n          </el-button>\n        </el-form-item>\n              </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n                        <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n          \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delStandardPeriodLot, getStandardPeriodLotList\n} from \"@/api/planManagement/standardPeriodLot\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\n//import { standardPeriodLotColumn } from '@/columns/pPM/standardPeriodLot.js';\n//import UploadButton from \"@/components/UploadButton.vue\";\n\nexport default {\n  name: 'index.vue',\n  components: {\n    //UploadButton,\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('StandardPeriodLot.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n      {code: 'LineCode', width: 130, align: 'center'},\n      {code: 'MaterialCode', width: 130, align: 'center'},\n      {code: 'MaterialName', width: 130, align: 'center'},\n      {code: 'Type', width: 130, align: 'center'},\n      {code: 'FirstLotPeriod', width: 130, align: 'center'},\n      {code: 'MiddleLotPeriod', width: 130, align: 'center'},\n      {code: 'LastLotPeriod', width: 130, align: 'center'},\n      {code: 'PlanQuantity', width: 130, align: 'center'},\n      {code: 'MinLotQuantity', width: 130, align: 'center'},\n      {code: 'MaxLotQuantity', width: 130, align: 'center'},\n      {code: 'Remark', width: 130, align: 'center'},\n      {code: 'CreateDate', width: 130, align: 'center'},\n      {code: 'CreateUserId', width: 130, align: 'center'},\n      {code: 'ModifyDate', width: 130, align: 'center'},\n      {code: 'ModifyUserId', width: 130, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'标准时间批量',\n        serveIp:'baseURL_PPM',\n        // uploadUrl:'/api/StandardPeriodLot/ImportData', //导入\n        // exportUrl:'/api/StandardPeriodLot/ExportData', //导出\n        // DownLoadUrl:'/api/StandardPeriodLot/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      for (let key in this.hansObj) {\n        this.tableName = getTableHead(this.hansObj, this.tableOption)\n      }\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delStandardPeriodLot([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getStandardPeriodLotList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"StandardPeriodLot\": {\n//    \"table\": {\n//        \"lineCode\": \"lineCode\",\n//        \"materialCode\": \"materialCode\",\n//        \"materialName\": \"materialName\",\n//        \"type\": \"type\",\n//        \"firstLotPeriod\": \"firstLotPeriod\",\n//        \"middleLotPeriod\": \"middleLotPeriod\",\n//        \"lastLotPeriod\": \"lastLotPeriod\",\n//        \"planQuantity\": \"planQuantity\",\n//        \"minLotQuantity\": \"minLotQuantity\",\n//        \"maxLotQuantity\": \"maxLotQuantity\",\n//        \"remark\": \"remark\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}