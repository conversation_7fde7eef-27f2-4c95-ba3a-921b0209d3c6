{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"SEFA.PPM.Api/1.0.0": {"dependencies": {"Abp": "7.3.0", "AspNetCoreRateLimit": "3.0.5", "AutoMapper.Extensions.Microsoft.DependencyInjection": "8.1.0", "Autofac.Extensions.DependencyInjection": "6.0.0", "Autofac.Extras.DynamicProxy": "5.0.0", "Castle.Core": "5.0.0", "Com.Ctrip.Framework.Apollo.Configuration": "2.7.0", "Confluent.Kafka": "1.7.0", "Consul": "*******", "InitQ": "*******", "Magicodes.IE.Core": "2.6.4", "Magicodes.IE.Excel.Abp": "2.6.4", "Magicodes.IE.Excel.AspNetCore": "2.6.4", "MicroKnights.Log4NetAdoNetAppender": "2.2.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "5.0.9", "Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.7", "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson": "6.0.7", "Microsoft.Extensions.Hosting.WindowsServices": "6.0.0", "Microsoft.Extensions.Http.Polly": "6.0.7", "Microsoft.Extensions.Logging.Log4Net.AspNetCore": "6.1.0", "Microsoft.IdentityModel.Tokens": "6.21.0", "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": "1.9.10", "MiniProfiler.AspNetCore.Mvc": "4.2.1", "MiniProfiler.Shared": "4.2.22", "Minio": "5.0.0", "Panda.DynamicWebApi": "1.2.1", "Polly": "7.2.2", "Polly.Extensions.Http": "3.0.0", "RestSharp": "108.0.1", "SEFA.PPM.Services": "1.0.0", "SapNwRfc": "1.4.0", "Serilog": "2.10.0", "Serilog.AspNetCore": "6.0.1", "Serilog.Sinks.Async": "1.5.0", "Serilog.Sinks.Elasticsearch": "8.4.1", "SkyAPM.Agent.AspNetCore": "1.3.0", "StackExchange.Redis": "2.6.48", "Swashbuckle.AspNetCore.Filters": "6.0.1", "Swashbuckle.AspNetCore.Newtonsoft": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0", "System.IdentityModel.Tokens.Jwt": "6.21.0", "System.Management": "7.0.2", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Federation": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Security": "4.10.3", "System.Text.Encoding.CodePages": "5.0.0", "WebApiClient.JIT": "1.1.4", "log4mongo-netcore": "3.2.0", "log4net": "2.0.14", "nacos-sdk-csharp": "1.2.1", "LKK.Lib.Core": "*******", "SEFA.Base.Common": "*******", "SEFA.Base.ESB": "*******", "SEFA.Base.EventBus": "*******", "SEFA.Base.Extensions": "*******", "SEFA.Base.InfluxDB": "*******", "SEFA.Base.IServices": "*******", "SEFA.Base.Model": "*******", "SEFA.Base.Repository": "*******", "SEFA.Base.Serilog.Es": "*******", "SEFA.DFM.IServices": "*******", "SEFA.DFM.Model": "*******", "SEFA.DFM.Repository": "*******", "SEFA.DFM.Services": "*******", "SEFA.LicenseTool.Util": "*******"}, "runtime": {"SEFA.PPM.Api.dll": {}}}, "Abp/7.3.0": {"dependencies": {"Castle.Core": "5.0.0", "Castle.Core.AsyncInterceptor": "2.1.0", "Castle.LoggingFacility": "5.1.1", "JetBrains.Annotations": "2022.1.0", "Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "Newtonsoft.Json": "13.0.2", "Nito.AsyncEx.Context": "5.1.2", "Nito.AsyncEx.Coordination": "5.1.2", "System.Collections.Immutable": "6.0.0", "System.ComponentModel.Annotations": "6.0.0-preview.4.21253.7", "System.Configuration.ConfigurationManager": "6.0.0", "System.Data.Common": "4.3.0", "System.Linq.Dynamic.Core": "1.2.18", "System.Linq.Queryable": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Security.Claims": "4.3.0", "System.Threading": "4.3.0", "System.Xml.XPath.XmlDocument": "4.3.0", "TimeZoneConverter": "5.0.0"}, "runtime": {"lib/netstandard2.0/Abp.dll": {"assemblyVersion": "7.3.0.0", "fileVersion": "7.3.0.0"}}}, "AspectCore.Extensions.Reflection/1.2.0": {"dependencies": {"System.Reflection.Emit.Lightweight": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/AspectCore.Extensions.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AspNetCoreRateLimit/3.0.5": {"dependencies": {"Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/AspNetCoreRateLimit.dll": {"assemblyVersion": "3.0.5.0", "fileVersion": "3.0.5.0"}}}, "Autofac/5.1.0": {"runtime": {"lib/netstandard2.1/Autofac.dll": {"assemblyVersion": "5.1.0.0", "fileVersion": "5.1.0.0"}}}, "Autofac.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Autofac": "5.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Autofac.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Autofac.Extras.DynamicProxy/5.0.0": {"dependencies": {"Autofac": "5.1.0", "Castle.Core": "5.0.0"}, "runtime": {"lib/netstandard2.1/Autofac.Extras.DynamicProxy.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "AutoMapper/10.1.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Reflection.Emit": "4.7.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "10.1.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.0": {"dependencies": {"AutoMapper": "10.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/netstandard2.0/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "*******"}}}, "BouncyCastle.Cryptography/2.3.0": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "2.3.0.53016"}}}, "Castle.Core/5.0.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/Castle.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Core.AsyncInterceptor/2.1.0": {"dependencies": {"Castle.Core": "5.0.0"}, "runtime": {"lib/net6.0/Castle.Core.AsyncInterceptor.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "2.1.0.0"}}}, "Castle.LoggingFacility/5.1.1": {"dependencies": {"Castle.Windsor": "5.1.1", "NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.6/Castle.Facilities.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Castle.Windsor/5.1.1": {"dependencies": {"Castle.Core": "5.0.0", "Microsoft.Extensions.DependencyModel": "5.0.0", "NETStandard.Library": "1.6.1", "System.Runtime.Loader": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard1.6/Castle.Windsor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Com.Ctrip.Framework.Apollo/2.7.0": {"dependencies": {"Microsoft.AspNet.WebApi.Client": "5.2.8"}, "runtime": {"lib/netstandard2.1/Com.Ctrip.Framework.Apollo.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Com.Ctrip.Framework.Apollo.Configuration/2.7.0": {"dependencies": {"Com.Ctrip.Framework.Apollo": "2.7.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Com.Ctrip.Framework.Apollo.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CommunityToolkit.HighPerformance/8.1.0": {"runtime": {"lib/net6.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Confluent.Kafka/1.7.0": {"dependencies": {"System.Memory": "4.5.3", "librdkafka.redist": "1.7.0"}, "runtime": {"lib/netcoreapp2.1/Confluent.Kafka.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Consul/*******": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Consul.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "CsvHelper/27.2.1": {"runtime": {"lib/net6.0/CsvHelper.dll": {"assemblyVersion": "2*******", "fileVersion": "********"}}}, "DnsClient/1.2.0": {"dependencies": {"System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/DnsClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "DynamicExpresso.Core/2.3.3": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/DynamicExpresso.Core.dll": {"assemblyVersion": "2.3.3.0", "fileVersion": "2.3.3.0"}}}, "EasyModbusTCP.NETCore/1.0.0": {"dependencies": {"System.IO.Ports": "6.0.0"}, "runtime": {"lib/net6.0/EasyModbusTCP.NETCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Elasticsearch.Net/7.8.1": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.Buffers": "4.5.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/netstandard2.1/Elasticsearch.Net.dll": {"assemblyVersion": "*******", "fileVersion": "7.8.1.0"}}}, "Enums.NET/4.0.1": {"runtime": {"lib/netcoreapp3.0/Enums.NET.dll": {"assemblyVersion": "*******", "fileVersion": "4.0.1.0"}}}, "ExtendedNumerics.BigDecimal/2023.1000.0.230": {"runtime": {"lib/net6.0/ExtendedNumerics.BigDecimal.dll": {"assemblyVersion": "2023.1000.0.230", "fileVersion": "2023.1000.0.230"}}}, "Google.Protobuf/3.15.0": {"dependencies": {"System.Memory": "4.5.3", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Protobuf.dll": {"assemblyVersion": "3.15.0.0", "fileVersion": "3.15.0.0"}}}, "Grpc/2.37.0": {"dependencies": {"Grpc.Core": "2.37.0"}}, "Grpc.Core/2.37.0": {"dependencies": {"Grpc.Core.Api": "2.37.0", "System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/Grpc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.37.0.0"}}, "runtimeTargets": {"runtimes/linux-arm64/native/libgrpc_csharp_ext.arm64.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libgrpc_csharp_ext.x64.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgrpc_csharp_ext.x64.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/grpc_csharp_ext.x64.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/grpc_csharp_ext.x86.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Grpc.Core.Api/2.37.0": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/Grpc.Core.Api.dll": {"assemblyVersion": "*******", "fileVersion": "2.37.0.0"}}}, "InfluxDB.Client/4.0.0": {"dependencies": {"InfluxDB.Client.Core": "4.0.0", "JsonSubTypes": "1.8.0", "Microsoft.Extensions.ObjectPool": "6.0.1", "Microsoft.Net.Http.Headers": "2.2.8", "System.Collections.Immutable": "6.0.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Reactive": "5.0.0"}, "runtime": {"lib/netstandard2.1/InfluxDB.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "InfluxDB.Client.Core/4.0.0": {"dependencies": {"CsvHelper": "27.2.1", "Newtonsoft.Json": "13.0.2", "NodaTime": "3.0.9", "NodaTime.Serialization.JsonNet": "3.0.0", "RestSharp": "108.0.1"}, "runtime": {"lib/netstandard2.1/InfluxDB.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "InfluxDB.Client.Linq/4.0.0": {"dependencies": {"InfluxDB.Client": "4.0.0", "Remotion.Linq": "2.2.0"}, "runtime": {"lib/netstandard2.1/InfluxDB.Client.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "InitQ/*******": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting": "6.0.0", "Newtonsoft.Json": "13.0.2", "StackExchange.Redis": "2.6.48"}, "runtime": {"lib/netcoreapp2.1/InitQ.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "JetBrains.Annotations/2022.1.0": {"runtime": {"lib/netstandard2.0/JetBrains.Annotations.dll": {"assemblyVersion": "4242.42.42.42", "fileVersion": "2022.1.0.0"}}}, "JsonSubTypes/1.8.0": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/JsonSubTypes.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "librdkafka.redist/1.7.0": {"runtimeTargets": {"runtimes/linux-arm64/native/librdkafka.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/alpine-librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/centos6-librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/centos7-librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/librdkafka.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/librdkafka.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/librdkafka.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/librdkafkacpp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libzstd.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/msvcp120.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "12.0.21005.1"}, "runtimes/win-x64/native/msvcr120.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "12.0.21005.1"}, "runtimes/win-x64/native/zlib.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.2.8.0"}, "runtimes/win-x86/native/librdkafka.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/librdkafkacpp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libzstd.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/msvcp120.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "12.0.21005.1"}, "runtimes/win-x86/native/msvcr120.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "12.0.21005.1"}, "runtimes/win-x86/native/zlib.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.2.8.0"}}}, "log4mongo-netcore/3.2.0": {"dependencies": {"MongoDB.Bson": "2.10.0", "MongoDB.Driver": "2.9.2", "MongoDB.Driver.Core": "2.10.0", "System.Configuration.ConfigurationManager": "6.0.0", "log4net": "2.0.14"}, "runtime": {"lib/netcoreapp2.2/Log4Mongo.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "log4net/2.0.14": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/log4net.dll": {"assemblyVersion": "2.0.14.0", "fileVersion": "2.0.14.0"}}}, "Magicodes.IE.Core/2.6.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyModel": "5.0.0", "System.ComponentModel.Annotations": "6.0.0-preview.4.21253.7", "System.Drawing.Common": "6.0.0", "System.Runtime.Loader": "4.3.0"}, "runtime": {"lib/netstandard2.1/Magicodes.IE.Core.dll": {"assemblyVersion": "2.6.4.0", "fileVersion": "2.6.4.0"}}, "resources": {"lib/netstandard2.1/zh-Hans/Magicodes.IE.Core.resources.dll": {"locale": "zh-Hans"}}}, "Magicodes.IE.EPPlus/2.6.4": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "System.ComponentModel.TypeConverter": "4.3.0", "System.Data.Common": "4.3.0", "System.Drawing.Common": "6.0.0", "System.Reflection": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Pkcs": "6.0.3", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding.CodePages": "5.0.0", "System.Xml.XPath.XmlDocument": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.1/Magicodes.IE.EPPlus.dll": {"assemblyVersion": "2.6.4.0", "fileVersion": "2.6.4.0"}}}, "Magicodes.IE.Excel/2.6.4": {"dependencies": {"DynamicExpresso.Core": "2.3.3", "Magicodes.IE.Core": "2.6.4", "Magicodes.IE.EPPlus": "2.6.4", "Microsoft.CSharp": "4.7.0", "System.Linq.Dynamic.Core": "1.2.18", "runtime.osx.10.10-x64.CoreCompat.System.Drawing": "5.8.64"}, "runtime": {"lib/netstandard2.1/Magicodes.IE.Excel.dll": {"assemblyVersion": "2.6.4.0", "fileVersion": "2.6.4.0"}}}, "Magicodes.IE.Excel.Abp/2.6.4": {"dependencies": {"Magicodes.IE.Excel": "2.6.4", "Volo.Abp.Core": "4.4.0"}, "runtime": {"lib/netstandard2.0/Magicodes.IE.Excel.Abp.dll": {"assemblyVersion": "2.6.4.0", "fileVersion": "2.6.4.0"}}}, "Magicodes.IE.Excel.AspNetCore/2.6.4": {"dependencies": {"Magicodes.IE.Excel": "2.6.4", "Microsoft.AspNetCore.Mvc.Core": "2.2.5"}, "runtime": {"lib/net5.0/Magicodes.IE.Excel.AspNetCore.dll": {"assemblyVersion": "2.6.4.0", "fileVersion": "2.6.4.0"}}}, "MathNet.Numerics.Signed/5.0.0": {"runtime": {"lib/net6.0/MathNet.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MicroKnights.Log4NetAdoNetAppender/2.2.0": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.Extensions.Configuration.Json": "6.0.0", "log4net": "2.0.14"}, "runtime": {"lib/netstandard2.1/MicroKnights.Log4NetAdoNetAppender.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNet.WebApi.Client/5.2.8": {"dependencies": {"Newtonsoft.Json": "13.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/netstandard2.0/System.Net.Http.Formatting.dll": {"assemblyVersion": "5.2.8.0", "fileVersion": "5.2.34876.0"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.9": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0"}, "runtime": {"lib/net5.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.921.36503"}}}, "Microsoft.AspNetCore.Authorization/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Authorization": "2.2.0"}}, "Microsoft.AspNetCore.Connections.Abstractions/6.0.7": {"dependencies": {"Microsoft.Extensions.Features": "6.0.7", "System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.AspNetCore.Http/2.2.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "6.0.1", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.8"}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Net.Http.Headers": "2.2.8", "System.Buffers": "4.5.0"}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.JsonPatch/6.0.7": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.JsonPatch.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.8"}}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Authorization.Policy": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.2", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Mvc.Abstractions": "2.2.0", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyModel": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.7": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "6.0.7", "Newtonsoft.Json": "13.0.2", "Newtonsoft.Json.Bson": "1.0.2"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.ObjectPool": "6.0.1", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}}, "Microsoft.AspNetCore.SignalR.Common/6.0.7": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "6.0.7", "Microsoft.Extensions.Options": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/6.0.7": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "6.0.7", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.8", "System.Text.Encodings.Web": "6.0.0"}}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.Data.SqlClient/2.1.7": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "6.21.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.8.0", "Microsoft.Win32.Registry": "4.7.0", "System.Configuration.ConfigurationManager": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Runtime.Caching": "4.7.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.Data.Sqlite/8.0.1": {"dependencies": {"Microsoft.Data.Sqlite.Core": "8.0.1", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.6"}}, "Microsoft.Data.Sqlite.Core/8.0.1": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.123.58002"}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.222.6406"}}}, "Microsoft.Extensions.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "System.Text.Json": "6.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.DependencyModel/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Features/6.0.7": {"runtime": {"lib/net6.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileSystemGlobbing": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {}, "Microsoft.Extensions.Hosting/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Physical": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Logging.Console": "6.0.0", "Microsoft.Extensions.Logging.Debug": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "Microsoft.Extensions.Logging.EventSource": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.0": {"dependencies": {"Microsoft.Extensions.Hosting": "6.0.0", "Microsoft.Extensions.Logging.EventLog": "6.0.0", "System.ServiceProcess.ServiceController": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Hosting.WindowsServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Http.Polly/6.0.7": {"dependencies": {"Microsoft.Extensions.Http": "6.0.0", "Polly": "7.2.2", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.722.32205"}}}, "Microsoft.Extensions.Localization/5.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Localization.Abstractions": "5.0.8", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Localization.Abstractions/5.0.8": {}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0"}}, "Microsoft.Extensions.Logging.Console/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Configuration": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Text.Json": "6.0.0"}}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.EventLog": "6.0.0"}}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Json": "6.0.0"}}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore/6.1.0": {"dependencies": {"Microsoft.Extensions.Configuration": "6.0.0", "Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "log4net": "2.0.14"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Log4Net.AspNetCore.dll": {"assemblyVersion": "6.1.0.0", "fileVersion": "6.1.0.0"}}}, "Microsoft.Extensions.ObjectPool/6.0.1": {"runtime": {"lib/net6.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.121.56714"}}}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.Abstractions/6.21.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "6.21.0.0", "fileVersion": "6.21.0.30701"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.21.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.21.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "6.21.0.0", "fileVersion": "6.21.0.30701"}}}, "Microsoft.IdentityModel.Logging/6.21.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.21.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "6.21.0.0", "fileVersion": "6.21.0.30701"}}}, "Microsoft.IdentityModel.Protocols/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.21.0", "Microsoft.IdentityModel.Tokens": "6.21.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.8.0", "System.IdentityModel.Tokens.Jwt": "6.21.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens.Saml": "6.8.0", "Microsoft.IdentityModel.Xml": "6.8.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.WsTrust.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11018"}}}, "Microsoft.IdentityModel.Tokens/6.21.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.IdentityModel.Logging": "6.21.0", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "6.21.0.0", "fileVersion": "6.21.0.30701"}}}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.21.0", "Microsoft.IdentityModel.Xml": "6.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.Saml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IdentityModel.Xml/6.8.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.21.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Xml.dll": {"assemblyVersion": "6.8.0.0", "fileVersion": "6.8.0.11012"}}}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"runtime": {"lib/net6.0/Microsoft.IO.RecyclableMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.2.8": {"dependencies": {"Microsoft.Extensions.Primitives": "6.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.9.10": {}, "Microsoft.Win32.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Microsoft.Win32.SystemEvents/6.0.0": {"runtime": {"lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "MiniExcel/1.33.0": {"runtime": {"lib/net6.0/MiniExcel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Minio/5.0.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.1.0", "System.IO.Hashing": "7.0.0", "System.Reactive.Linq": "5.0.0"}, "runtime": {"lib/net6.0/Minio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MiniProfiler.AspNetCore/4.2.1": {"dependencies": {"MiniProfiler.Shared": "4.2.22", "System.Text.Json": "6.0.0"}, "runtime": {"lib/netcoreapp3.0/MiniProfiler.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.1.31666"}}}, "MiniProfiler.AspNetCore.Mvc/4.2.1": {"dependencies": {"MiniProfiler.AspNetCore": "4.2.1"}, "runtime": {"lib/netcoreapp3.0/MiniProfiler.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.1.31666"}}}, "MiniProfiler.Shared/4.2.22": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Newtonsoft.Json": "13.0.2", "System.ComponentModel.Primitives": "4.3.0", "System.Data.Common": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.StackTrace": "4.3.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0", "System.Threading.Tasks.Parallel": "4.3.0"}, "runtime": {"lib/netstandard2.0/MiniProfiler.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.22.25413"}}}, "MongoDB.Bson/2.10.0": {"dependencies": {"NETStandard.Library": "1.6.1", "System.Collections.NonGeneric": "4.3.0", "System.Diagnostics.Process": "4.1.0", "System.Dynamic.Runtime": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Runtime.Serialization.Formatters": "4.3.0"}, "runtime": {"lib/netstandard1.5/MongoDB.Bson.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Driver/2.9.2": {"dependencies": {"MongoDB.Bson": "2.10.0", "MongoDB.Driver.Core": "2.10.0", "NETStandard.Library": "1.6.1", "System.ComponentModel.TypeConverter": "4.3.0", "System.Linq.Queryable": "4.3.0"}, "runtime": {"lib/netstandard1.5/MongoDB.Driver.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MongoDB.Driver.Core/2.10.0": {"dependencies": {"DnsClient": "1.2.0", "MongoDB.Bson": "2.10.0", "MongoDB.Libmongocrypt": "1.0.0", "NETStandard.Library": "1.6.1", "SharpCompress": "0.23.0", "System.Collections.Specialized": "4.3.0", "System.Diagnostics.TextWriterTraceListener": "4.0.0", "System.Diagnostics.TraceSource": "4.0.0", "System.Net.NameResolution": "4.3.0", "System.Net.Security": "4.3.2", "System.Security.SecureString": "4.0.0", "System.Threading.Thread": "4.3.0"}, "runtime": {"lib/netstandard1.5/MongoDB.Driver.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "MongoDB.Libmongocrypt/1.0.0": {"dependencies": {"NETStandard.Library": "1.6.1"}, "runtime": {"lib/netstandard1.5/MongoDB.Libmongocrypt.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MySqlConnector/2.2.5": {"runtime": {"lib/net6.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.5.0"}}}, "nacos-sdk-csharp/1.2.1": {"dependencies": {"Google.Protobuf": "3.15.0", "Grpc.Core": "2.37.0", "Microsoft.Extensions.DependencyModel": "5.0.0", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/net5.0/Nacos.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/1.6.1": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "Newtonsoft.Json/13.0.2": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.2.27524"}}}, "Newtonsoft.Json.Bson/1.0.2": {"dependencies": {"Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netstandard2.0/Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}, "Nito.AsyncEx.Context/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Context.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Coordination/5.1.2": {"dependencies": {"Nito.AsyncEx.Tasks": "5.1.2", "Nito.Collections.Deque": "1.1.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Coordination.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.AsyncEx.Tasks/5.1.2": {"dependencies": {"Nito.Disposables": "2.2.1"}, "runtime": {"lib/netstandard2.0/Nito.AsyncEx.Tasks.dll": {"assemblyVersion": "5.1.2.0", "fileVersion": "5.1.2.0"}}}, "Nito.Collections.Deque/1.1.1": {"runtime": {"lib/netstandard2.0/Nito.Collections.Deque.dll": {"assemblyVersion": "1.1.1.0", "fileVersion": "1.1.1.0"}}}, "Nito.Disposables/2.2.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}, "runtime": {"lib/netstandard2.1/Nito.Disposables.dll": {"assemblyVersion": "2.2.1.0", "fileVersion": "2.2.1.0"}}}, "NodaTime/3.0.9": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/NodaTime.dll": {"assemblyVersion": "3.0.9.0", "fileVersion": "3.0.9.0"}}}, "NodaTime.Serialization.JsonNet/3.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.2", "NodaTime": "3.0.9"}, "runtime": {"lib/netstandard2.0/NodaTime.Serialization.JsonNet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql/5.0.7": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net5.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NPOI/2.7.0": {"dependencies": {"BouncyCastle.Cryptography": "2.3.0", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2023.1000.0.230", "MathNet.Numerics.Signed": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "SharpZipLib": "1.3.3", "SixLabors.Fonts": "1.0.1", "SixLabors.ImageSharp": "2.1.7", "System.Configuration.ConfigurationManager": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.3", "System.Security.Cryptography.Xml": "6.0.1"}, "runtime": {"lib/net6.0/NPOI.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OOXML.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXml4Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}, "lib/net6.0/NPOI.OpenXmlFormats.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Oracle.ManagedDataAccess.Core/3.21.120": {"dependencies": {"System.Diagnostics.PerformanceCounter": "6.0.1", "System.DirectoryServices": "6.0.1", "System.DirectoryServices.Protocols": "6.0.2"}, "runtime": {"lib/netstandard2.1/Oracle.ManagedDataAccess.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Oscar.Data.SqlClient/4.0.4": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/Oscar.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Panda.DynamicWebApi/1.2.1": {"runtime": {"lib/net6.0/Panda.DynamicWebApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Pipelines.Sockets.Unofficial/2.2.2": {"dependencies": {"System.IO.Pipelines": "6.0.3"}, "runtime": {"lib/net5.0/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.2.34088"}}}, "Polly/7.2.2": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.2.2"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Remotion.Linq/2.2.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}, "runtime": {"lib/netstandard1.0/Remotion.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.30000"}}}, "RestSharp/108.0.1": {"runtime": {"lib/net6.0/RestSharp.dll": {"assemblyVersion": "10*******", "fileVersion": "10*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.linux-arm.runtime.native.System.IO.Ports/6.0.0": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/6.0.0": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/6.0.0": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Ports/6.0.0": {"dependencies": {"runtime.linux-arm.runtime.native.System.IO.Ports": "6.0.0", "runtime.linux-arm64.runtime.native.System.IO.Ports": "6.0.0", "runtime.linux-x64.runtime.native.System.IO.Ports": "6.0.0", "runtime.osx-arm64.runtime.native.System.IO.Ports": "6.0.0", "runtime.osx-x64.runtime.native.System.IO.Ports": "6.0.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Net.Security/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.osx-arm64.runtime.native.System.IO.Ports/6.0.0": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/6.0.0": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.CoreCompat.System.Drawing/5.8.64": {"runtime": {"lib/netstandard2.0/runtime.osx.10.10-x64.CoreCompat.System.Drawing.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "runtimeTargets": {"runtimes/osx-x64/native/libcairo.2.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libfontconfig.1.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libfreetype.6.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgdiplus-lighthouse.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgdiplus.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libgif.7.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libglib-2.0.0.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libintl.8.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libjpeg.9.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libpcre.1.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libpixman-1.0.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libpng16.16.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libtiff.5.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {}, "SapNwRfc/1.4.0": {"runtime": {"lib/netstandard2.1/SapNwRfcDotNet.dll": {"assemblyVersion": "1.4.0.0", "fileVersion": "1.4.0.0"}}}, "Scrutor/3.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.DependencyModel": "5.0.0"}, "runtime": {"lib/netstandard2.0/Scrutor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/2.10.0": {"runtime": {"lib/netstandard2.1/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Serilog.AspNetCore/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Hosting": "5.0.1", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Settings.Configuration": "3.3.0", "Serilog.Sinks.Console": "4.0.1", "Serilog.Sinks.Debug": "2.0.0", "Serilog.Sinks.File": "5.0.0"}, "runtime": {"lib/net5.0/Serilog.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/5.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Serilog": "2.10.0", "Serilog.Extensions.Logging": "3.1.0"}, "runtime": {"lib/netstandard2.1/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/3.1.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Compact/1.1.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Compact.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Formatting.Elasticsearch/8.4.1": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Formatting.Elasticsearch.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "Serilog.Settings.Configuration/3.3.0": {"dependencies": {"Microsoft.Extensions.DependencyModel": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Settings.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Async/1.5.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Async.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "Serilog.Sinks.Console/4.0.1": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.0.1.0"}}}, "Serilog.Sinks.Debug/2.0.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/netstandard2.1/Serilog.Sinks.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Elasticsearch/8.4.1": {"dependencies": {"Elasticsearch.Net": "7.8.1", "Microsoft.CSharp": "4.7.0", "Serilog": "2.10.0", "Serilog.Formatting.Compact": "1.1.0", "Serilog.Formatting.Elasticsearch": "8.4.1", "Serilog.Sinks.File": "5.0.0", "Serilog.Sinks.PeriodicBatching": "2.1.1", "System.Diagnostics.DiagnosticSource": "6.0.0"}, "runtime": {"lib/netstandard2.0/Serilog.Sinks.Elasticsearch.dll": {"assemblyVersion": "8.4.1.0", "fileVersion": "8.4.1.0"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "2.10.0"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.PeriodicBatching/2.1.1": {"dependencies": {"Serilog": "2.10.0", "System.Collections.Concurrent": "4.3.0", "System.Threading.Timer": "4.3.0"}, "runtime": {"lib/netstandard1.2/Serilog.Sinks.PeriodicBatching.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.RollingFile/3.3.0": {"dependencies": {"Serilog.Sinks.File": "5.0.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0"}, "runtime": {"lib/netstandard1.3/Serilog.Sinks.RollingFile.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SharpCompress/0.23.0": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/SharpCompress.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SharpZipLib/1.3.3": {"runtime": {"lib/netstandard2.1/ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SixLabors.Fonts/1.0.1": {"runtime": {"lib/netcoreapp3.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/2.1.7": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Abstractions/1.3.0": {"runtime": {"lib/netstandard2.0/SkyAPM.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.AspNetCore/1.3.0": {"dependencies": {"SkyAPM.Agent.Hosting": "1.3.0", "SkyAPM.Diagnostics.AspNetCore": "1.3.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Agent.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Agent.Hosting/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite": "1.3.0", "SkyAPM.Diagnostics.HttpClient": "1.3.0", "SkyAPM.Diagnostics.SqlClient": "1.3.0", "SkyAPM.Transport.Grpc": "1.3.0", "SkyAPM.Utilities.Configuration": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0", "SkyAPM.Utilities.Logging": "1.3.0", "SkyApm.Diagnostics.Grpc": "1.0.0", "SkyApm.Diagnostics.Grpc.Net.Client": "1.0.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Agent.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Core/1.3.0": {"dependencies": {"AspectCore.Extensions.Reflection": "1.2.0", "SkyAPM.Abstractions": "1.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Memory": "4.5.3", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.AspNetCore/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netcoreapp3.1/SkyAPM.Diagnostics.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Diagnostics.EntityFrameworkCore": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc/1.0.0": {"dependencies": {"Grpc": "2.37.0", "SkyAPM.Abstractions": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyApm.Diagnostics.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyApm.Diagnostics.Grpc.Net.Client/1.0.0": {"dependencies": {"Grpc.Core.Api": "2.37.0", "SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyApm.Diagnostics.Grpc.Net.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.HttpClient/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.HttpClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Diagnostics.SqlClient/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Core": "1.3.0", "SkyAPM.Utilities.DependencyInjection": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Diagnostics.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc/1.3.0": {"dependencies": {"SkyAPM.Abstractions": "1.3.0", "SkyAPM.Transport.Grpc.Protocol": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Transport.Grpc.Protocol/1.3.0": {"dependencies": {"Google.Protobuf": "3.15.0", "Grpc": "2.37.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Transport.Grpc.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Configuration/1.3.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "6.0.0", "Microsoft.Extensions.Configuration.Binder": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.0", "Microsoft.Extensions.Configuration.Json": "6.0.0", "SkyAPM.Abstractions": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.DependencyInjection/1.3.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "SkyAPM.Core": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkyAPM.Utilities.Logging/1.3.0": {"dependencies": {"Microsoft.Extensions.Logging": "6.0.0", "Serilog.Extensions.Logging": "3.1.0", "Serilog.Sinks.RollingFile": "3.3.0", "SkyAPM.Abstractions": "1.3.0"}, "runtime": {"lib/netstandard2.0/SkyAPM.Utilities.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.6", "SQLitePCLRaw.provider.e_sqlite3": "2.1.6"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.core/2.1.6": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"runtimeTargets": {"runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-ppc64le/native/libe_sqlite3.so": {"rid": "linux-ppc64le", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"dependencies": {"SQLitePCLRaw.core": "2.1.6"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.6.2060", "fileVersion": "2.1.6.2060"}}}, "SqlSugarCore/*********": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.Data.Sqlite": "8.0.1", "MySqlConnector": "2.2.5", "Newtonsoft.Json": "13.0.2", "Npgsql": "5.0.7", "Oracle.ManagedDataAccess.Core": "3.21.120", "Oscar.Data.SqlClient": "4.0.4", "SqlSugarCore.Dm": "1.3.0", "SqlSugarCore.Kdbndp": "8.3.715", "System.Data.Common": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0"}, "runtime": {"lib/netstandard2.1/SqlSugar.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "SqlSugarCore.Dm/1.3.0": {"dependencies": {"System.Text.Encoding.CodePages": "5.0.0"}, "runtime": {"lib/netstandard2.0/DmProvider.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.18485"}}}, "SqlSugarCore.Kdbndp/8.3.715": {"runtime": {"lib/netstandard2.1/Kdbndp.dll": {"assemblyVersion": "8.3.712.0", "fileVersion": "8.3.712.0"}}}, "StackExchange.Redis/2.6.48": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.2", "System.Diagnostics.PerformanceCounter": "6.0.1"}, "runtime": {"lib/net5.0/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.6.48.48654"}}}, "Swashbuckle.AspNetCore/5.0.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}}, "Swashbuckle.AspNetCore.Annotations/5.0.0": {"dependencies": {"Swashbuckle.AspNetCore.SwaggerGen": "6.4.0"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Filters/6.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.OpenApi": "1.2.3", "Newtonsoft.Json": "13.0.2", "Scrutor": "3.0.1", "Swashbuckle.AspNetCore": "5.0.0", "Swashbuckle.AspNetCore.Annotations": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Filters.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.Newtonsoft/6.4.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.7", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Newtonsoft.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "6.4.0.0", "fileVersion": "6.4.0.0"}}}, "System.AppContext/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Buffers/4.5.0": {}, "System.CodeDom/7.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations/6.0.0-preview.4.21253.7": {}, "System.ComponentModel.Primitives/4.3.0": {"dependencies": {"System.ComponentModel": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.TypeConverter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.NonGeneric": "4.3.0", "System.Collections.Specialized": "4.3.0", "System.ComponentModel": "4.3.0", "System.ComponentModel.Primitives": "4.3.0", "System.Globalization": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Configuration.ConfigurationManager/6.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Console/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Data.Common/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/6.0.0": {}, "System.Diagnostics.PerformanceCounter/6.0.1": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.422.16404"}}}, "System.Diagnostics.Process/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "Microsoft.Win32.Registry": "4.7.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.StackTrace/4.3.0": {"dependencies": {"System.IO.FileSystem": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Metadata": "1.4.1", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"dependencies": {"System.Diagnostics.TraceSource": "4.0.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.TraceSource/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.DirectoryServices/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Permissions": "6.0.0"}, "runtime": {"lib/net6.0/System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.DirectoryServices.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1423.7309"}}}, "System.DirectoryServices.Protocols/6.0.2": {"runtime": {"lib/net6.0/System.DirectoryServices.Protocols.dll": {"assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/linux/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "linux", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}, "runtimes/osx/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "osx", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}, "runtimes/win/lib/net6.0/System.DirectoryServices.Protocols.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.2", "fileVersion": "6.0.1823.26907"}}}, "System.Drawing.Common/6.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}, "runtime": {"lib/net6.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Formats.Asn1/6.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/6.21.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.21.0", "Microsoft.IdentityModel.Tokens": "6.21.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "6.21.0.0", "fileVersion": "6.21.0.30701"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Buffers": "4.5.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.Compression.ZipFile/4.3.0": {"dependencies": {"System.Buffers": "4.5.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.Hashing/7.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO.Pipelines/6.0.3": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.IO.Ports/6.0.0": {"dependencies": {"runtime.native.System.IO.Ports": "6.0.0"}, "runtime": {"lib/net6.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}, "runtimes/win/lib/net6.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Dynamic.Core/1.2.18": {"runtime": {"lib/net6.0/System.Linq.Dynamic.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}}, "System.Memory/4.5.3": {}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "6.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.NameResolution/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Net.Security/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.2", "System.Security.Principal": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Security": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Net.Sockets/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Private.ServiceModel/4.10.3": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "5.0.0", "Microsoft.Extensions.ObjectPool": "6.0.1", "System.Numerics.Vectors": "4.5.0", "System.Reflection.DispatchProxy": "4.7.1", "System.Security.Cryptography.Xml": "6.0.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Private.ServiceModel.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.Private.ServiceModel.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.Private.ServiceModel.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.Private.ServiceModel.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.Private.ServiceModel.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.Private.ServiceModel.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.Private.ServiceModel.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.Private.ServiceModel.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.Private.ServiceModel.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.Private.ServiceModel.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.Private.ServiceModel.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.Private.ServiceModel.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.Private.ServiceModel.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.Private.ServiceModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Reactive/5.0.0": {"runtime": {"lib/net5.0/System.Reactive.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.0.1"}}}, "System.Reactive.Linq/5.0.0": {"dependencies": {"System.Reactive": "5.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/System.Reactive.Linq.dll": {"assemblyVersion": "3.0.6000.0", "fileVersion": "3.0.6000.0"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.DispatchProxy/4.7.1": {}, "System.Reflection.Emit/4.7.0": {}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.4.1": {"dependencies": {"System.Collections": "4.3.0", "System.Collections.Immutable": "6.0.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.Linq": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Loader/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Runtime.Serialization.Formatters/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Serialization.Primitives": "4.3.0"}}, "System.Runtime.Serialization.Primitives/4.3.0": {"dependencies": {"System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Security.AccessControl/6.0.0": {}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Pkcs/6.0.3": {"dependencies": {"System.Formats.Asn1": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.1823.26907"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.ProtectedData/6.0.0": {"runtime": {"lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Cryptography.X509Certificates/4.3.2": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.5.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.2"}}, "System.Security.Cryptography.Xml/6.0.1": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Security.Cryptography.Pkcs": "6.0.3"}, "runtime": {"lib/net6.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.822.36306"}}}, "System.Security.Permissions/6.0.0": {"dependencies": {"System.Security.AccessControl": "6.0.0", "System.Windows.Extensions": "6.0.0"}, "runtime": {"lib/net6.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Security.SecureString/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.ServiceModel.Duplex/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Duplex.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Federation/4.10.3": {"dependencies": {"Microsoft.IdentityModel.Protocols.WsTrust": "6.8.0", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.Primitives": "4.10.3", "System.ServiceModel.Security": "4.10.3"}, "runtime": {"lib/netstandard2.0/System.ServiceModel.Federation.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}, "resources": {"lib/netstandard2.0/cs/System.ServiceModel.Federation.resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/System.ServiceModel.Federation.resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/System.ServiceModel.Federation.resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/System.ServiceModel.Federation.resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/System.ServiceModel.Federation.resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/System.ServiceModel.Federation.resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/System.ServiceModel.Federation.resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/System.ServiceModel.Federation.resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/System.ServiceModel.Federation.resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/System.ServiceModel.Federation.resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/System.ServiceModel.Federation.resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/System.ServiceModel.Federation.resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/System.ServiceModel.Federation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.ServiceModel.Http/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Http.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.NetTcp/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.NetTcp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Primitives/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}, "lib/net6.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceModel.Security/4.10.3": {"dependencies": {"System.Private.ServiceModel": "4.10.3", "System.ServiceModel.Primitives": "4.10.3"}, "runtime": {"lib/net6.0/System.ServiceModel.Security.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.323.51101"}}}, "System.ServiceProcess.ServiceController/6.0.0": {"dependencies": {"System.Diagnostics.EventLog": "6.0.0"}, "runtime": {"lib/net6.0/System.ServiceProcess.ServiceController.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.ServiceProcess.ServiceController.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "6.0.0"}}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Thread/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading.ThreadPool/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Threading.Timer/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "System.Windows.Extensions/6.0.0": {"dependencies": {"System.Drawing.Common": "6.0.0"}, "runtime": {"lib/net6.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.5.4"}}, "System.Xml.XDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}}, "System.Xml.XPath.XmlDocument/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XPath": "4.3.0", "System.Xml.XmlDocument": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XmlDocument.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "TimeZoneConverter/5.0.0": {"runtime": {"lib/netstandard2.0/TimeZoneConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Volo.Abp.Core/4.4.0": {"dependencies": {"JetBrains.Annotations": "2022.1.0", "Microsoft.Extensions.Configuration.CommandLine": "6.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "6.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "6.0.0", "Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.Hosting.Abstractions": "6.0.0", "Microsoft.Extensions.Localization": "5.0.8", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Nito.AsyncEx.Context": "5.1.2", "Nito.AsyncEx.Coordination": "5.1.2", "System.Collections.Immutable": "6.0.0", "System.ComponentModel.Annotations": "6.0.0-preview.4.21253.7", "System.Linq.Dynamic.Core": "1.2.18", "System.Linq.Queryable": "4.3.0", "System.Runtime.Loader": "4.3.0", "System.Text.Encodings.Web": "6.0.0"}, "runtime": {"lib/netstandard2.0/Volo.Abp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "WebApiClient.JIT/1.1.4": {"dependencies": {"Microsoft.Extensions.Caching.Memory": "6.0.1", "Microsoft.Extensions.Http": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "6.0.0", "Newtonsoft.Json": "13.0.2"}, "runtime": {"lib/netcoreapp2.1/WebApiClient.JIT.dll": {"assemblyVersion": "1.1.4.0", "fileVersion": "1.1.4.0"}}}, "SEFA.PPM.IServices/1.0.0": {"dependencies": {"SEFA.PPM.Model": "1.0.0"}, "runtime": {"SEFA.PPM.IServices.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "SEFA.PPM.Model/1.0.0": {"dependencies": {"Abp": "7.3.0", "AutoMapper": "10.1.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "8.1.0", "InfluxDB.Client": "4.0.0", "Magicodes.IE.Excel": "2.6.4", "Magicodes.IE.Excel.AspNetCore": "2.6.4", "MiniExcel": "1.33.0", "Oracle.ManagedDataAccess.Core": "3.21.120", "SqlSugarCore": "*********"}, "runtime": {"SEFA.PPM.Model.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "SEFA.PPM.Repository/1.0.0": {"dependencies": {"MiniProfiler.AspNetCore.Mvc": "4.2.1", "MongoDB.Bson": "2.10.0", "MongoDB.Driver.Core": "2.10.0", "Newtonsoft.Json": "13.0.2", "SEFA.PPM.Model": "1.0.0"}, "runtime": {"SEFA.PPM.Repository.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "SEFA.PPM.Services/1.0.0": {"dependencies": {"EasyModbusTCP.NETCore": "1.0.0", "InfluxDB.Client": "4.0.0", "InfluxDB.Client.Linq": "4.0.0", "MiniExcel": "1.33.0", "NPOI": "2.7.0", "SEFA.PPM.IServices": "1.0.0", "SEFA.PPM.Repository": "1.0.0", "System.ServiceModel.Duplex": "4.10.3", "System.ServiceModel.Federation": "4.10.3", "System.ServiceModel.Http": "4.10.3", "System.ServiceModel.NetTcp": "4.10.3", "System.ServiceModel.Security": "4.10.3"}, "runtime": {"SEFA.PPM.Services.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "LKK.Lib.Core/*******": {"runtime": {"LKK.Lib.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Common/*******": {"runtime": {"SEFA.Base.Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.ESB/*******": {"runtime": {"SEFA.Base.ESB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.EventBus/*******": {"runtime": {"SEFA.Base.EventBus.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Extensions/*******": {"runtime": {"SEFA.Base.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.InfluxDB/*******": {"runtime": {"SEFA.Base.InfluxDB.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.IServices/*******": {"runtime": {"SEFA.Base.IServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Model/*******": {"runtime": {"SEFA.Base.Model.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Repository/*******": {"runtime": {"SEFA.Base.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Serilog.Es/*******": {"runtime": {"SEFA.Base.Serilog.Es.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.DFM.IServices/*******": {"runtime": {"SEFA.DFM.IServices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.DFM.Model/*******": {"runtime": {"SEFA.DFM.Model.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.DFM.Repository/*******": {"runtime": {"SEFA.DFM.Repository.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.DFM.Services/*******": {"runtime": {"SEFA.DFM.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.LicenseTool.Util/*******": {"runtime": {"SEFA.LicenseTool.Util.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Base.Services/*******": {"runtime": {"SEFA.Base.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SEFA.Resource/0.0.0.0": {"runtime": {"SEFA.Resource.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}}}, "libraries": {"SEFA.PPM.Api/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Abp/7.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-wXq5buzZkJs33Hyi7uP4B/h42vkmtx2WXs70aia9Br3dsjvsNKvV2r3l9spxfBibKpT632Rkuzefte69AeH9yA==", "path": "abp/7.3.0", "hashPath": "abp.7.3.0.nupkg.sha512"}, "AspectCore.Extensions.Reflection/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+9Ebk4YNMIueiJGhFbrfF8Zr9XY462lRaw6IgYgtfxSopoZSMRlksPnCIxWGoXBijuMeUjRbnULWaTwuZXZLgw==", "path": "aspectcore.extensions.reflection/1.2.0", "hashPath": "aspectcore.extensions.reflection.1.2.0.nupkg.sha512"}, "AspNetCoreRateLimit/3.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0luEnplR6jn8GK0iKjWDct+V4c2V9ySdGFsZDFV3ZPnXIhg1J8XjybeNx+hYJCrsgPTDe69zWr6iYKcZb5R8Qg==", "path": "aspnetcoreratelimit/3.0.5", "hashPath": "aspnetcoreratelimit.3.0.5.nupkg.sha512"}, "Autofac/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-cBW4cIWAIvVTGv1h0EGw2d0FlxX/8NLjPWXYLLrFcjnYwvloshmGcB34KYNpCtyOJZTg3xjgq4UkGRs4nC9KyQ==", "path": "autofac/5.1.0", "hashPath": "autofac.5.1.0.nupkg.sha512"}, "Autofac.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+1WNYENm4s/bSBrmTy2sD0zs6MCiCAlUaEbHJ+y7fKpXKqnFl4oTkIkUoDKlp05dl1VoAASBUJocaKKJa1KPCw==", "path": "autofac.extensions.dependencyinjection/6.0.0", "hashPath": "autofac.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Autofac.Extras.DynamicProxy/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gBiW5LOprDt+d3K5g++HYekVyF+2KcgBohae3w1NFZwEHMC9yb7fYNBdpEZZpxmSV+judbTz9oQtVoDASRxiOQ==", "path": "autofac.extras.dynamicproxy/5.0.0", "hashPath": "autofac.extras.dynamicproxy.5.0.0.nupkg.sha512"}, "AutoMapper/10.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-uMgbqOdu9ZG5cIOty0C85hzzayBH2i9BthnS5FlMqKtMSHDv4ts81a2jS1VFaDBVhlBeIqJ/kQKjQY95BZde9w==", "path": "automapper/10.1.1", "hashPath": "automapper.10.1.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-dQyGCAYcHbGuimVvCMu4Ea2S1oYOlgO9XfVdClmY5wgygJMZoS57emPzH0qNfknmtzMm4QbDO9i237W5IDjU1A==", "path": "automapper.extensions.microsoft.dependencyinjection/8.1.0", "hashPath": "automapper.extensions.microsoft.dependencyinjection.8.1.0.nupkg.sha512"}, "BouncyCastle.Cryptography/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-IaVIiYxZLaBulveGDRUx/pBoW/Rc8QeXGF5u2E8xL8RWhVKCgfmtX9NUyGRbnSqnbFQU2zyP3MkXIdH+jUuQBw==", "path": "bouncycastle.cryptography/2.3.0", "hashPath": "bouncycastle.cryptography.2.3.0.nupkg.sha512"}, "Castle.Core/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-edc8jjyXqzzy8jFdhs36FZdwmlDDTgqPb2Zy1Q5F/f2uAc88bu/VS/0Tpvgupmpl9zJOvOo5ZizVANb0ltN1NQ==", "path": "castle.core/5.0.0", "hashPath": "castle.core.5.0.0.nupkg.sha512"}, "Castle.Core.AsyncInterceptor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1vOovJnbjjoCFbPPNLvLTeiqJwoA+aRXkhXcgCQY0pi1eejGqCegJwl58pIIPH/uKDfUXnPIo7aqSrcXEyEH1Q==", "path": "castle.core.asyncinterceptor/2.1.0", "hashPath": "castle.core.asyncinterceptor.2.1.0.nupkg.sha512"}, "Castle.LoggingFacility/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-bNVitNOxpQTPkjAs1AtxFhIdXBUndcqjC/nhKRW8ofOuwbPgVtKrGFaSLLLleYNW5F+Whj+s9TIWgmj2WxqwWw==", "path": "castle.loggingfacility/5.1.1", "hashPath": "castle.loggingfacility.5.1.1.nupkg.sha512"}, "Castle.Windsor/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-m1YCo+V+MHUqJV7+KtXlNYig93tEAnPdPevIHi8hVqV8/ceF82Nqe8vS1ROQ25nweTcxYgkrLHwhvJsH2SFwZA==", "path": "castle.windsor/5.1.1", "hashPath": "castle.windsor.5.1.1.nupkg.sha512"}, "Com.Ctrip.Framework.Apollo/2.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0hK9u9DHQYr0elOyzH5t7Xo+RL1w22S33gHC+SKwZXZndpY9RYXr74KdFnwh+4eglX38L9iiTozSSPB2Efo7TA==", "path": "com.ctrip.framework.apollo/2.7.0", "hashPath": "com.ctrip.framework.apollo.2.7.0.nupkg.sha512"}, "Com.Ctrip.Framework.Apollo.Configuration/2.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-MBob6SptkwaCWGDhzSC3ZLPuZ9M20Py8VtszQ/l6U82MTcB6IXWUz1uxIJPrnicQqN7xaAjt0YTn96Op8UUbnQ==", "path": "com.ctrip.framework.apollo.configuration/2.7.0", "hashPath": "com.ctrip.framework.apollo.configuration.2.7.0.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kgDi65k02hrgnHy3N0xENecsr0spW13RdIA1tipovi9t16gKziI7uZIu3qkxz0GctCHNM4hfeqXYg//6wHJ6Kw==", "path": "communitytoolkit.highperformance/8.1.0", "hashPath": "communitytoolkit.highperformance.8.1.0.nupkg.sha512"}, "Confluent.Kafka/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-g7dbCUxJPF3SM5qBrIZso0dw3jgRD6tQ0oDb9Cf1oL5l6U5OxSKC90kN79F1QHx8P2wEPBmiWiIUf9Y5ZPPpQw==", "path": "confluent.kafka/1.7.0", "hashPath": "confluent.kafka.1.7.0.nupkg.sha512"}, "Consul/*******": {"type": "package", "serviceable": true, "sha512": "sha512-AJkBiix1bT3cSZRO6OD0Ee31Co1qgVkmVBFwpxym9WdxH5K6LfmAZ8Vb8nGvo9EKC+ir2sv5fKIrY75aENcMDA==", "path": "consul/*******", "hashPath": "consul.*******.nupkg.sha512"}, "CsvHelper/27.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-uS5ix5hL9gL5taiAG//CScyJa8Fn1ZOh3FDhDvf4laboESFs84mCNropfp7PIt8xCkyQofljFpqu1B5UnSXjyA==", "path": "csvhelper/27.2.1", "hashPath": "csvhelper.27.2.1.nupkg.sha512"}, "DnsClient/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-P34wUkeqU4FoQiOMV8OjpdeDZKs4d3r+VlHuKJ6eO5feiZgna3+9MF5orHRUn3DAv1g/HPE5hlkGucmxmsFfBw==", "path": "dnsclient/1.2.0", "hashPath": "dnsclient.1.2.0.nupkg.sha512"}, "DynamicExpresso.Core/2.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-p6GEP3BphaT9xa59VjpQeozkloXjcDmoL6aPXOInl5S5chWtB82H+GiirV3H1bP39ZeXX2e1UN0w7/pD1wCUlg==", "path": "dynamicexpresso.core/2.3.3", "hashPath": "dynamicexpresso.core.2.3.3.nupkg.sha512"}, "EasyModbusTCP.NETCore/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON><PERSON><PERSON>8UdrutwRENFmXy6d2DMcIskyK2fZZvz83QyEYmx72WhqKoRzZQnSUnlFbfO6o6uOLxSL3t8pXZZNkwxA==", "path": "easymodbustcp.netcore/1.0.0", "hashPath": "easymodbustcp.netcore.1.0.0.nupkg.sha512"}, "Elasticsearch.Net/7.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-vGHlxY72LH8/DcKb/QDpvrIelQIUFxNnXa+HmS/ifX7M7dgwmTpA2i4SagQ65gg7oi088cteUuDl4fKIystg7Q==", "path": "elasticsearch.net/7.8.1", "hashPath": "elasticsearch.net.7.8.1.nupkg.sha512"}, "Enums.NET/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OUGCd5L8zHZ61GAf436G0gf/H6yrSUkEpV5vm2CbCUuz9Rx7iLFLP5iHSSfmOtqNpuyo4vYte0CvYEmPZXRmRQ==", "path": "enums.net/4.0.1", "hashPath": "enums.net.4.0.1.nupkg.sha512"}, "ExtendedNumerics.BigDecimal/2023.1000.0.230": {"type": "package", "serviceable": true, "sha512": "sha512-kbEPff0V3AOgVpOidwEvB/k4d/74h6NgUbnHp3ZvPxz6QqsKXP5Zry2zUroyQUyztp3yDLtFT89XY5rfaShZAw==", "path": "extendednumerics.bigdecimal/2023.1000.0.230", "hashPath": "extendednumerics.bigdecimal.2023.1000.0.230.nupkg.sha512"}, "Google.Protobuf/3.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-2na7LsYrtxkEQaVCNjaafBzVu4IJ4U4+dwwBazK8oUkpDiIFjO0GkyTc+IbStx4xNVAV4EqHl+qp2uegFgFiiA==", "path": "google.protobuf/3.15.0", "hashPath": "google.protobuf.3.15.0.nupkg.sha512"}, "Grpc/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hl4+qpEfTvKP95diLRZMi3nAaPp91myTuM8Z/lmC9XsbxUroMR4OM0HMKNC9rty2Gz+CHq4mq/E7CBqbcVdnPA==", "path": "grpc/2.37.0", "hashPath": "grpc.2.37.0.nupkg.sha512"}, "Grpc.Core/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOmeCLyygA3ZWB7XOjPH5qWcfsw20XNWUA1AMQbFvBig3pXQR0Tz7FMSqLFW0oa34E0iiIlgROFoncOVBfVnRw==", "path": "grpc.core/2.37.0", "hashPath": "grpc.core.2.37.0.nupkg.sha512"}, "Grpc.Core.Api/2.37.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubqW2nTpiHyDudYGVXM+Vjh6WbgsI1fVQxsDK14/GnyPgiNMuNl8+GQcAYp5QPhAk5H4fjHJPI+KvbpVk8z6iQ==", "path": "grpc.core.api/2.37.0", "hashPath": "grpc.core.api.2.37.0.nupkg.sha512"}, "InfluxDB.Client/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kC7BFuz/m+iTYmClJcLoSdQdNPGuKMYZAWwe5VSp38eYo9oSfmgj5AwG1jugnvrN97FwMWEVj7+sQImA50Db6w==", "path": "influxdb.client/4.0.0", "hashPath": "influxdb.client.4.0.0.nupkg.sha512"}, "InfluxDB.Client.Core/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNntWDubwsbADvDsDi4TOPuaGfmu6d+ABbjQEwaS9CTlFmpRf8T173xXhZuUTio2XXlwYBulh4Tdq/n29G2tBA==", "path": "influxdb.client.core/4.0.0", "hashPath": "influxdb.client.core.4.0.0.nupkg.sha512"}, "InfluxDB.Client.Linq/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8UMwCOjR+Hc383GwfH//F7or4TwD08glWtSDw6GRIQwCKDI4KFc0e1DfpUKSc9w0FVaDtbZ/44ipfAsyZCZvNA==", "path": "influxdb.client.linq/4.0.0", "hashPath": "influxdb.client.linq.4.0.0.nupkg.sha512"}, "InitQ/*******": {"type": "package", "serviceable": true, "sha512": "sha512-cvj1sQY9XtFsBOQqFDZVv8Xuni6xApibaJ8kXUaR5XHQJxN1T0I7DHRWqb+XSBylQiGOGXRN+1ygX9tPYBTRqA==", "path": "initq/*******", "hashPath": "initq.*******.nupkg.sha512"}, "JetBrains.Annotations/2022.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ASfpoFJxiRsC9Xc4TWuPM41Zb/gl64xwfMOhnOZ3RnVWGYIZchjpWQV5zshJgoc/ZxVtgjaF7b577lURj7E6ig==", "path": "jetbrains.annotations/2022.1.0", "hashPath": "jetbrains.annotations.2022.1.0.nupkg.sha512"}, "JsonSubTypes/1.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-pBChxyl9mt0F1s1giHlN6yQ9WWb5FljluCNhMnSb7sspPfPRPTrc0a0psdu2/FVb5nZu5bQN50OMMaSp8jm8xQ==", "path": "jsonsubtypes/1.8.0", "hashPath": "jsonsubtypes.1.8.0.nupkg.sha512"}, "librdkafka.redist/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JoR57Zi4lpRj4GdYynXZoantQMoqt8bZ6g3izCgrhe80aUSUeGbAm+cq9xlCINIfX5Wy0oomU4+NGNeHo/4iAA==", "path": "librdkafka.redist/1.7.0", "hashPath": "librdkafka.redist.1.7.0.nupkg.sha512"}, "log4mongo-netcore/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qX6HVEQChL8EiTHFYT3R4/+HfVYsczPAThDt9xTedQpNMDSBzx+3ovfZsYLDYWkYGnt0VWgQ5Yhs4FGOna9MDA==", "path": "log4mongo-netcore/3.2.0", "hashPath": "log4mongo-netcore.3.2.0.nupkg.sha512"}, "log4net/2.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-KevyXUuhOyhx7l1jWwq6ZGVlRC2Aetg0qDp6rJpfSZGcDPKQDwfOE6yEuVkVf0kEP08NQqBDn/TQ/TJv4wgyhw==", "path": "log4net/2.0.14", "hashPath": "log4net.2.0.14.nupkg.sha512"}, "Magicodes.IE.Core/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-FWLa2mU+egDkoi+xatEOv+zxeUrDrAjyuqPaDhpDvWEt75Tn1W4A4lI5fZlAk/9nGjYP8u/zdvWVYH3Gj1CJBg==", "path": "magicodes.ie.core/2.6.4", "hashPath": "magicodes.ie.core.2.6.4.nupkg.sha512"}, "Magicodes.IE.EPPlus/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-wZ/oJdnInzVv5J+pGsSvMc0TKdO/ADyT/BrpkE0gCcPrhJfTx9Z2gyYR/11RDbSzOGHezgL12DO6OofZILXYWg==", "path": "magicodes.ie.epplus/2.6.4", "hashPath": "magicodes.ie.epplus.2.6.4.nupkg.sha512"}, "Magicodes.IE.Excel/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-JLhZ8sMaFb2MmVe+te2VN4YpfOIYYxSLgd1YTC2nT2yKQC211Z7VtEtiM04WcvD1Sq5MtaakPhzKOSyZavkzfw==", "path": "magicodes.ie.excel/2.6.4", "hashPath": "magicodes.ie.excel.2.6.4.nupkg.sha512"}, "Magicodes.IE.Excel.Abp/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-pO7yE0Elju5l8lQDqbQqkRsuRfIytovuTg3xkWkqrtL38uC5K/7Hxcnzz/Wjx+sO8P2L1/8ugj+lIZL2Q13wRQ==", "path": "magicodes.ie.excel.abp/2.6.4", "hashPath": "magicodes.ie.excel.abp.2.6.4.nupkg.sha512"}, "Magicodes.IE.Excel.AspNetCore/2.6.4": {"type": "package", "serviceable": true, "sha512": "sha512-MTOrOKC1fGTctx9dUl/Z6dqeIdKbNC66TD4NFLu1qU5RZjLaZQDP0xLcFPBBN3oZBMqUp8kucfRPVn1Ta2vwTA==", "path": "magicodes.ie.excel.aspnetcore/2.6.4", "hashPath": "magicodes.ie.excel.aspnetcore.2.6.4.nupkg.sha512"}, "MathNet.Numerics.Signed/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PSrHBVMf41SjbhlnpOMnoir8YgkyEJ6/nwxvjYpH+vJCexNcx2ms6zRww5yLVqLet1xLJgZ39swtKRTLhWdnAw==", "path": "mathnet.numerics.signed/5.0.0", "hashPath": "mathnet.numerics.signed.5.0.0.nupkg.sha512"}, "MicroKnights.Log4NetAdoNetAppender/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-FryL/5pLWynP8znM5LTXd4M+DmD+Ftls5D8IlyMwCbH7MFWviLyNvGneJZFjmdXschal5lTUOB3Cs1FkEPs7tQ==", "path": "microknights.log4netadonetappender/2.2.0", "hashPath": "microknights.log4netadonetappender.2.2.0.nupkg.sha512"}, "Microsoft.AspNet.WebApi.Client/5.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-dkGLm30CxLieMlUO+oQpJw77rDs0IIx/w3lIHsp+8X94HXCsUfLYuFmlZPsQDItC0O2l1ZlWeKLkZX7ZiNRekw==", "path": "microsoft.aspnet.webapi.client/5.2.8", "hashPath": "microsoft.aspnet.webapi.client.5.2.8.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/5.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-zG4G0waQzkdG0TazXbyuvIxpmWgQllnIjuKNcXNIh5mwcMVG1UPqT09L5EkAGmW2TtktJNWvojr77OpiOpK9JA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/5.0.9", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.5.0.9.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-/L0W8H3jMYWyaeA9gBJqS/tSWBegP9aaTM0mjRhxTttBY9z4RVDRYJ2CwPAmAXIuPr3r1sOw+CS8jFVRGHRezQ==", "path": "microsoft.aspnetcore.authorization/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization.Policy/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aJCo6niDRKuNg2uS2WMEmhJTooQUGARhV2ENQ2tO5443zVHUo19MSgrgGo9FIrfD+4yKPF8Q+FF33WkWfPbyKw==", "path": "microsoft.aspnetcore.authorization.policy/2.2.0", "hashPath": "microsoft.aspnetcore.authorization.policy.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-OYZetO1Sn/33dmRsWfzJGsAtDoHWRndfi3Idyse0Qc6gKGh6rW8zhxM+mWm7JeFThAwG5X1RS0cqiFcIoKUT2g==", "path": "microsoft.aspnetcore.connections.abstractions/6.0.7", "hashPath": "microsoft.aspnetcore.connections.abstractions.6.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-BAibpoItxI5puk7YJbIGj95arZueM8B8M5xT1fXBn3hb3L2G3ucrZcYXv1gXdaroLbntUs8qeV8iuBrpjQsrKw==", "path": "microsoft.aspnetcore.http/2.2.2", "hashPath": "microsoft.aspnetcore.http.2.2.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.JsonPatch/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ybv3udO2SaGW74NKIxj06ozTtcEJf2yOCNj4OGY/ZvB+LG/mZGBVKrwZYaZBXaWR9dakrf4+M3LiMAhasTEamg==", "path": "microsoft.aspnetcore.jsonpatch/6.0.7", "hashPath": "microsoft.aspnetcore.jsonpatch.6.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ET6uZpfVbGR1NjCuLaLy197cQ3qZUjzl7EG5SL4GfJH/c9KRE89MMBrQegqWsh0w1iRUB/zQaK0anAjxa/pz4g==", "path": "microsoft.aspnetcore.mvc.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.mvc.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.Core/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-/8sr8ixIUD57UFwUntha9bOwex7/AkZfdk1f9oNJG1Ek7p/uuKVa7fuHmYZpQOf35Oxrt+2Ku4WPwMSbNxOuWg==", "path": "microsoft.aspnetcore.mvc.core/2.2.5", "hashPath": "microsoft.aspnetcore.mvc.core.2.2.5.nupkg.sha512"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-zkt7EFRR5fZQUc6taa8sO8aG+EF7XQ6Z7+fPW3gXCh6l7+vyDpZ9PbTODYMsXaS1tOjP1b+jJgf7hY/kaciO4Q==", "path": "microsoft.aspnetcore.mvc.newtonsoftjson/6.0.7", "hashPath": "microsoft.aspnetcore.mvc.newtonsoftjson.6.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-CIHWEKrHzZfFp7t57UXsueiSA/raku56TgRYauV/W1+KAQq6vevz60zjEKaazt3BI76zwMz3B4jGWnCwd8kwQw==", "path": "microsoft.aspnetcore.responsecaching.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.responsecaching.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-JzZdrFAh+weJ4G1MR1ME7p2axOjvhslt1LcfL/RKjejmgp36UW56syMaLHfvgTfZTJ3bEUSpWJYyF5TrA7yRxA==", "path": "microsoft.aspnetcore.signalr.common/6.0.7", "hashPath": "microsoft.aspnetcore.signalr.common.6.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-bUowH3Aa9PxJcl2etIeQhv/nO0jW0/Napr+U1S37dXUGEHOMfC6FDbUPqN9TA4fEkOuZRDdDaaJsdJbGA+kuIg==", "path": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson/6.0.7", "hashPath": "microsoft.aspnetcore.signalr.protocols.newtonsoftjson.6.0.7.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W8<PERSON>QjkMScOMTtJbPwmPyj9c3zYSFGawDW3jwlBOOsnY+EzZFLgNQ/UMkK35JmkNOVPdCyPr2Tw7Vv9N+KA3ZQ==", "path": "microsoft.bcl.asyncinterfaces/5.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.5.0.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-dSdlcXPszeOjjDX9a0buMFgYqKrI5bTxdSgX3JyCa+OL80NUstJSxOJr0j9oOn8mpP5PgWeRC2bVf/Zf2Cjv+g==", "path": "microsoft.data.sqlclient/2.1.7", "hashPath": "microsoft.data.sqlclient.2.1.7.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+7uDWNYZmLrVq9eABAKwy1phGbpoFVohKCUoh/nGg9WiBwi856EkAJYFiQhTJWoXxzpInkLFj/6KACoSB7ODYg==", "path": "microsoft.data.sqlite/8.0.1", "hashPath": "microsoft.data.sqlite.8.0.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s8C8xbwMb79EqzTaIhwiBrYtbv6ATnUW19pJed4fKVgN5K4VPQ7JUGqBLztknvD6EJIMKrfRnINGTjnZghrDGw==", "path": "microsoft.data.sqlite.core/8.0.1", "hashPath": "microsoft.data.sqlite.core.8.0.1.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bcz5sSFJbganH0+YrfvIjJDIcKNW7TL07C4d1eTmXy/wOt52iz4LVogJb6pazs7W0+74j0YpXFErvp++Aq5Bsw==", "path": "microsoft.extensions.caching.abstractions/6.0.0", "hashPath": "microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B4y+Cev05eMcjf1na0v9gza6GUtahXbtY1JCypIgx3B4Ea/KAgsWyXEmW4q6zMbmTMtKzmPVk09rvFJirvMwTg==", "path": "microsoft.extensions.caching.memory/6.0.1", "hashPath": "microsoft.extensions.caching.memory.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tq2wXyh3fL17EMF2bXgRhU7JrbO3on93MRKYxzz4JzzvuGSA1l0W3GI9/tl8EO89TH+KWEymP7bcFway6z9fXg==", "path": "microsoft.extensions.configuration/6.0.0", "hashPath": "microsoft.extensions.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qWzV9o+ZRWq+pGm+1dF+R7qTgTYoXvbyowRoBxQJGfqTpqDun2eteerjRQhq5PQ/14S+lqto3Ft4gYaRyl4rdQ==", "path": "microsoft.extensions.configuration.abstractions/6.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3<PERSON>rKzND8LIC7o08QAVlKfaEIYEvLJbtmVbFZVBRXeu9YkKfSSzLZfR1SUfQPBIy9mKLhEtJgGYImkcMNaKE0A==", "path": "microsoft.extensions.configuration.binder/6.0.0", "hashPath": "microsoft.extensions.configuration.binder.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3nL1qCkZ1Oxx14ZTzgo4MmlO7tso7F+TtMZAY2jUAtTLyAcDp+EDjk3RqafoKiNaePyPvvlleEcBxh3b2Hzl1g==", "path": "microsoft.extensions.configuration.commandline/6.0.0", "hashPath": "microsoft.extensions.configuration.commandline.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DjYkzqvhiHCq38LW71PcIxXk6nhtV6VySP9yDcSO0goPl7YCU1VG1f2Wbgy58lkA10pWkjHCblZPUyboCB93ZA==", "path": "microsoft.extensions.configuration.environmentvariables/6.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V4Dth2cYMZpw3HhGw9XUDIijpI6gN+22LDt0AhufIgOppCUfpWX4483OmN+dFXRJkJLc8Tv0Q8QK+1ingT2+KQ==", "path": "microsoft.extensions.configuration.fileextensions/6.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GJGery6QytCzS/BxJ96klgG9in3uH26KcUBbiVG/coNDXCRq6LGVVlUT4vXq34KPuM+R2av+LeYdX9h4IZOCUg==", "path": "microsoft.extensions.configuration.json/6.0.0", "hashPath": "microsoft.extensions.configuration.json.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lB0Hb2V4+RUHy+LjEcqEr4EcV4RWc9EnjAV2GdtWQEdljQX+R4hGREftI7sInU9okP93pDrJiaj6QUJ6ZsslOA==", "path": "microsoft.extensions.configuration.usersecrets/6.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-umBECCoMC+sOUgm083yFr8SxTobUOcPFH4AXigdO2xJiszCHAnmeDl4qPphJt+oaJ/XIfV1wOjIts2nRnki61Q==", "path": "microsoft.extensions.dependencymodel/5.0.0", "hashPath": "microsoft.extensions.dependencymodel.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Features/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-PkRyg0dugUaolfS7jdLeEVeV8ZdfiejpMOuSxM0F0HmeCcPYmtmewVPESAkt3BPN8NN5yOU8FaQr5XpkR3VNGg==", "path": "microsoft.extensions.features/6.0.7", "hashPath": "microsoft.extensions.features.6.0.7.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0pd4/fho0gC12rQswaGQxbU34jOS1TPS8lZPpkFCH68ppQjHNHYle9iRuHeev1LhrJ94YPvzcRd8UmIuFk23Qw==", "path": "microsoft.extensions.fileproviders.abstractions/6.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QvkL7l0nM8udt3gfyu0Vw8bbCXblxaKOl7c2oBfgGy4LCURRaL9XWZX1FWJrQc43oMokVneVxH38iz+bY1sbhg==", "path": "microsoft.extensions.fileproviders.physical/6.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ip8jnL1aPiaPeKINCqaTEbvBFDmVx9dXQEBZ2HOBRXPD1eabGNqP/bKlsIcp7U2lGxiXd5xIhoFcmY8nM4Hdiw==", "path": "microsoft.extensions.filesystemglobbing/6.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M8VzD0ni5VarIRT8njnwK4K2WSAo0kZH4Zc3mKcSGkP4CjDZ91T9ZEFmmwhmo4z7x8AFq+tW0WFi9wX+K2cxkQ==", "path": "microsoft.extensions.hosting/6.0.0", "hashPath": "microsoft.extensions.hosting.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GcT5l2CYXL6Sa27KCSh0TixsRfADUgth+ojQSD5EkzisZxmGFh7CwzkcYuGwvmXLjr27uWRNrJ2vuuEjMhU05Q==", "path": "microsoft.extensions.hosting.abstractions/6.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.WindowsServices/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qrYcNoYOI7AouQf0edY1NotePLNfz6wwLfjcEL5IQMXQm61k9glChzr7bt4I8uFelAHK8dYJPvCHEd6idxoXOw==", "path": "microsoft.extensions.hosting.windowsservices/6.0.0", "hashPath": "microsoft.extensions.hosting.windowsservices.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/6.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-JfnvJFyyREfeOVLuBSndcxMtK0kPiIE42FlN1qLjlwnk4irdNOrExYf9GoDLve9MIkEncDuqJPbmGLIfFaPuKA==", "path": "microsoft.extensions.http.polly/6.0.7", "hashPath": "microsoft.extensions.http.polly.6.0.7.nupkg.sha512"}, "Microsoft.Extensions.Localization/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BVhrZbV/SV1THU8QpE7RIz1DVLr8hLXUmQ/3TUERkLnfcGcZX/hYKgY9O7JxKQRawXihqBma5C6IHf6+0Dmcew==", "path": "microsoft.extensions.localization/5.0.8", "hashPath": "microsoft.extensions.localization.5.0.8.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/5.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-1aLVqVMq+t1hJCKMm24iokUYpIvL//rrxHpyXNVJDB0nnBDsY6RBl9BtFesbMTOT5W+3cjoSpq18juCyQDaIzA==", "path": "microsoft.extensions.localization.abstractions/5.0.8", "hashPath": "microsoft.extensions.localization.abstractions.5.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZDskjagmBAbv+K8rYW9VhjPplhbOE63xUD0DiuydZJwt15dRyoqicYklLd86zzeintUc7AptDkHn+YhhYkYo8A==", "path": "microsoft.extensions.logging.configuration/6.0.0", "hashPath": "microsoft.extensions.logging.configuration.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gsqKzOEdsvq28QiXFxagmn1oRB9GeI5GgYCkoybZtQA0IUb7QPwf1WmN3AwJeNIsadTvIFQCiVK0OVIgKfOBGg==", "path": "microsoft.extensions.logging.console/6.0.0", "hashPath": "microsoft.extensions.logging.console.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-M9g/JixseSZATJE9tcMn9uzoD4+DbSglivFqVx8YkRJ7VVPmnvCEbOZ0AAaxsL1EKyI4cz07DXOOJExxNsUOHw==", "path": "microsoft.extensions.logging.debug/6.0.0", "hashPath": "microsoft.extensions.logging.debug.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rlo0RxlMd0WtLG3CHI0qOTp6fFn7MvQjlrCjucA31RqmiMFCZkF8CHNbe8O7tbBIyyoLGWB1he9CbaA5iyHthg==", "path": "microsoft.extensions.logging.eventlog/6.0.0", "hashPath": "microsoft.extensions.logging.eventlog.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BeDyyqt7nkm/nr+Gdk+L8n1tUT/u33VkbXAOesgYSNsxDM9hJ1NOBGoZfj9rCbeD2+9myElI6JOVVFmnzgeWQA==", "path": "microsoft.extensions.logging.eventsource/6.0.0", "hashPath": "microsoft.extensions.logging.eventsource.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-qHb3GudscgrgEVByh86kAq3FMmjOdPIVoqvff3eokEP+QkZHzr42l4PlgxBsqhhlIX02SazC7ZdbfiFGDPRv9A==", "path": "microsoft.extensions.logging.log4net.aspnetcore/6.1.0", "hashPath": "microsoft.extensions.logging.log4net.aspnetcore.6.1.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ZyWqqp7d54J7ej5dVNm8YZ4DydyIlVZqTgmCZFkO7tmZRSWxuFYKZa6z+K2Zla9YCUSuWH5IH6rMeHel9sNiKA==", "path": "microsoft.extensions.objectpool/6.0.1", "hashPath": "microsoft.extensions.objectpool.6.0.1.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXWINbTn0vC0FYc9GaQTISbxhQLAMrvtbuvD9N6JelEaIS/Pr62wUCinrq5bf1WRBGczt1v4wDhxFtVFNcMdUQ==", "path": "microsoft.extensions.options.configurationextensions/6.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-XeE6LQtD719Qs2IG7HDi1TSw9LIkDbJ33xFiOBoHbApVw/8GpIBCbW+t7RwOjErUDyXZvjhZliwRkkLb8Z1uzg==", "path": "microsoft.identitymodel.abstractions/6.21.0", "hashPath": "microsoft.identitymodel.abstractions.6.21.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-d3h1/BaMeylKTkdP6XwRCxuOoDJZ44V9xaXr6gl5QxmpnZGdoK3bySo3OQN8ehRLJHShb94ElLUvoXyglQtgAw==", "path": "microsoft.identitymodel.jsonwebtokens/6.21.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.21.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-tuEhHIQwvBEhMf8I50hy8FHmRSUkffDFP5EdLsSDV4qRcl2wvOPkQxYqEzWkh+ytW6sbdJGEXElGhmhDfAxAKg==", "path": "microsoft.identitymodel.logging/6.21.0", "hashPath": "microsoft.identitymodel.logging.6.21.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJZx5nPdiH+MEkwCkbJrTAUiO/YzLe0VSswNlDxJsJD9bhOIdXHufh650pfm59YH1DNevp3/bXzukKrG57gA1w==", "path": "microsoft.identitymodel.protocols/6.8.0", "hashPath": "microsoft.identitymodel.protocols.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/PiV5l3nYYsodtrNMrNQIVlDmHpjQQ5w48E+o/D5H4es2+4niEyQf3l03chvZGWNzBRhfSstaXr25/Ye4AeYw==", "path": "microsoft.identitymodel.protocols.openidconnect/6.8.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.WsTrust/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-KiRRp/pAtbJtyK7wPIiUW1FMna1T0IEizDWFL9R0C60jk6t66U95fo0SPl3ztOkmnS4v7uF1zWjfAEgZ/i+Zhg==", "path": "microsoft.identitymodel.protocols.wstrust/6.8.0", "hashPath": "microsoft.identitymodel.protocols.wstrust.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-AAEHZvZyb597a+QJSmtxH3n2P1nIJGpZ4Q89GTenknRx6T6zyfzf592yW/jA5e8EHN4tNMjjXHQaYWEq5+L05w==", "path": "microsoft.identitymodel.tokens/6.21.0", "hashPath": "microsoft.identitymodel.tokens.6.21.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens.Saml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRbtJ7Kvr2RMcXi4g4ta3og/wX0GZpLGfb/h7aohwNAaUtCooRpx7Gl8Cv7tn4FDAp6MZwBQL/w0jMeyVTkjPQ==", "path": "microsoft.identitymodel.tokens.saml/6.8.0", "hashPath": "microsoft.identitymodel.tokens.saml.6.8.0.nupkg.sha512"}, "Microsoft.IdentityModel.Xml/6.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-p2DOCNVVOQpxfx9c3FW0kJve2jAAZB/zecWsi9S5fpznbJ/VcH0zxKdz6wIXjDQgwf2xg/u/k58uHiS/o+0qiA==", "path": "microsoft.identitymodel.xml/6.8.0", "hashPath": "microsoft.identitymodel.xml.6.8.0.nupkg.sha512"}, "Microsoft.IO.RecyclableMemoryStream/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irv0HuqoH8Ig5i2fO+8dmDNdFdsrO+DoQcedwIlb810qpZHBNQHZLW7C/AHBQDgLLpw2T96vmMAy/aE4Yj55Sg==", "path": "microsoft.io.recyclablememorystream/3.0.0", "hashPath": "microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-wHdwMv0QDDG2NWDSwax9cjkeQceGC1Qq53a31+31XpvTXVljKXRjWISlMoS/wZYKiqdqzuEvKFKwGHl+mt2jCA==", "path": "microsoft.net.http.headers/2.2.8", "hashPath": "microsoft.net.http.headers.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets/1.9.10": {"type": "package", "serviceable": true, "sha512": "sha512-d5/5CDc4TAjrgO9oyyj+FHUDVumxgrlCMJqDtUX4GyTnr1Q/BP88PhnVunRZ9AQcL2UQkM4EkRNyFelPXunHtw==", "path": "microsoft.visualstudio.azure.containers.tools.targets/1.9.10", "hashPath": "microsoft.visualstudio.azure.containers.tools.targets.1.9.10.nupkg.sha512"}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "path": "microsoft.win32.primitives/4.3.0", "hashPath": "microsoft.win32.primitives.4.3.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A==", "path": "microsoft.win32.systemevents/6.0.0", "hashPath": "microsoft.win32.systemevents.6.0.0.nupkg.sha512"}, "MiniExcel/1.33.0": {"type": "package", "serviceable": true, "sha512": "sha512-mDOofcSIXRjC9XR0ZlqzE8EUpkYkCVArhlUw5Trrn1XJsjdfJT9JTnSILnj6ke1wVKbEcuhOYW8WFItFDRZYoQ==", "path": "miniexcel/1.33.0", "hashPath": "miniexcel.1.33.0.nupkg.sha512"}, "Minio/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7tZj90WEuuH60RAP4wBYexjMuJOhCnK7I46hCiX3CtZPackHisLZ8aAJmn3KlwbUX22dBDphwemD+h37vet8Qw==", "path": "minio/5.0.0", "hashPath": "minio.5.0.0.nupkg.sha512"}, "MiniProfiler.AspNetCore/4.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-AfVJg/dSgXE94O5X+hMueTijVLrg2g3vbKqusUQRMX5UQoi7ql/kWkDmsUOgnnO8SqlhfOq6SohgeaJT1HRLsw==", "path": "miniprofiler.aspnetcore/4.2.1", "hashPath": "miniprofiler.aspnetcore.4.2.1.nupkg.sha512"}, "MiniProfiler.AspNetCore.Mvc/4.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-bWJT+t6BlrCDmR3TKopZ9sgEXX5kOIQqTznseKylkfnKmBKyYdFus1OmPZ0+2wp1XHrhS2uFOZtX6kbTLuveQg==", "path": "miniprofiler.aspnetcore.mvc/4.2.1", "hashPath": "miniprofiler.aspnetcore.mvc.4.2.1.nupkg.sha512"}, "MiniProfiler.Shared/4.2.22": {"type": "package", "serviceable": true, "sha512": "sha512-OOA99Iu7FjFrdYaADcWL78KK9Kq6M+hfnZac5577aSrx0UYOM2apKlhBPKzoPtGPTRtQNKe4RK00u/FmahcU3g==", "path": "miniprofiler.shared/4.2.22", "hashPath": "miniprofiler.shared.4.2.22.nupkg.sha512"}, "MongoDB.Bson/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMUjSQ4jSDkAtMRod/tTRkMmiPnu6e7GqhLOPWkLvtY/uHKmn4BXp82wF2Cwd+kktW1GtOQA7wgP9YpGhQDVow==", "path": "mongodb.bson/2.10.0", "hashPath": "mongodb.bson.2.10.0.nupkg.sha512"}, "MongoDB.Driver/2.9.2": {"type": "package", "serviceable": true, "sha512": "sha512-On2uj3x0EXUIJCogdkJHrTF17rsDolyrdeXGJw+alse2R11WRa657noz44fyYX/00wPpbjHBx15Qqig8mAYvHQ==", "path": "mongodb.driver/2.9.2", "hashPath": "mongodb.driver.2.9.2.nupkg.sha512"}, "MongoDB.Driver.Core/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-xz40z4BxfVY/Q99JiaPNwT/2/00auITn0hoWfSZy3WtkYkMQQIcZNlLr/ywRihJbDQXhVpAbyKCO9chYstQXEA==", "path": "mongodb.driver.core/2.10.0", "hashPath": "mongodb.driver.core.2.10.0.nupkg.sha512"}, "MongoDB.Libmongocrypt/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zkcsra5riDiKXqOiCKskycakpbO5RYkuUWG+z2AZML3A4NShvAs/D3InwkxH0OcbjZQOWo763Hjdmhr0AaylcA==", "path": "mongodb.libmongocrypt/1.0.0", "hashPath": "mongodb.libmongocrypt.1.0.0.nupkg.sha512"}, "MySqlConnector/2.2.5": {"type": "package", "serviceable": true, "sha512": "sha512-6sinY78RvryhHwpup3awdjYO7d5hhWahb5p/1VDODJhSxJggV/sBbYuKK5IQF9TuzXABiddqUbmRfM884tqA3Q==", "path": "mysqlconnector/2.2.5", "hashPath": "mysqlconnector.2.2.5.nupkg.sha512"}, "nacos-sdk-csharp/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vi37zkhNyE0U1yUQcc8jh8FAPhkpFXF3x5m/OpzdWlzj5OLL4IqRMlezTu4Ff5sLJoKw2XV3DQF1P4hPivl5xA==", "path": "nacos-sdk-csharp/1.2.1", "hashPath": "nacos-sdk-csharp.1.2.1.nupkg.sha512"}, "NETStandard.Library/1.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "path": "netstandard.library/1.6.1", "hashPath": "netstandard.library.1.6.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-R2pZ3B0UjeyHShm9vG+Tu0EBb2lC8b0dFzV9gVn50ofHXh9Smjk6kTn7A/FdAsC8B5cKib1OnGYOXxRBz5XQDg==", "path": "newtonsoft.json/13.0.2", "hashPath": "newtonsoft.json.13.0.2.nupkg.sha512"}, "Newtonsoft.Json.Bson/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-QYFyxhaABwmq3p/21VrZNYvCg3DaEoN/wUuw5nmfAf0X3HLjgupwhkEWdgfb9nvGAUIv3osmZoD3kKl4jxEmYQ==", "path": "newtonsoft.json.bson/1.0.2", "hashPath": "newtonsoft.json.bson.1.0.2.nupkg.sha512"}, "Nito.AsyncEx.Context/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-rMwL7Nj3oNyvFu/jxUzQ/YBobEkM2RQHe+5mpCDRyq6mfD7vCj7Z3rjB6XgpM6Mqcx1CA2xGv0ascU/2Xk8IIg==", "path": "nito.asyncex.context/5.1.2", "hashPath": "nito.asyncex.context.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Coordination/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-QMyUfsaxov//0ZMbOHWr9hJaBFteZd66DV1ay4J5wRODDb8+K/uHC7+3VsOflo6SVw/29mu8OWZp8vMDSuzc0w==", "path": "nito.asyncex.coordination/5.1.2", "hashPath": "nito.asyncex.coordination.5.1.2.nupkg.sha512"}, "Nito.AsyncEx.Tasks/5.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-jEkCfR2/M26OK/U4G7SEN063EU/F4LiVA06TtpZILMdX/quIHCg+wn31Zerl2LC+u1cyFancjTY3cNAr2/89PA==", "path": "nito.asyncex.tasks/5.1.2", "hashPath": "nito.asyncex.tasks.5.1.2.nupkg.sha512"}, "Nito.Collections.Deque/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-CU0/Iuv5VDynK8I8pDLwkgF0rZhbQoZahtodfL0M3x2gFkpBRApKs8RyMyNlAi1mwExE4gsmqQXk4aFVvW9a4Q==", "path": "nito.collections.deque/1.1.1", "hashPath": "nito.collections.deque.1.1.1.nupkg.sha512"}, "Nito.Disposables/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6sZ5uynQeAE9dPWBQGKebNmxbY4xsvcc5VplB5WkYEESUS7oy4AwnFp0FhqxTSKm/PaFrFqLrYr696CYN8cugg==", "path": "nito.disposables/2.2.1", "hashPath": "nito.disposables.2.2.1.nupkg.sha512"}, "NodaTime/3.0.9": {"type": "package", "serviceable": true, "sha512": "sha512-7KzJBkgSzLLyBXNYafDlnzlji3aB/8myvt5RJu5aaTFq6puVCq2WDJY9v9yDMg22+5or5IaKNItQ1jNA8nzQNA==", "path": "nodatime/3.0.9", "hashPath": "nodatime.3.0.9.nupkg.sha512"}, "NodaTime.Serialization.JsonNet/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-buSE64oL5eHDiImMgFRF74X/URygSpklFsblyqXafjSW6lMsB7iWfGO5lu7D7Zikj9bXggnMa90a5EqgpPJEYg==", "path": "nodatime.serialization.jsonnet/3.0.0", "hashPath": "nodatime.serialization.jsonnet.3.0.0.nupkg.sha512"}, "Npgsql/5.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-EQWwxb2lN9w78YG4f6Fxhw5lFEx4LuaNGasXzw86kTOJxiPsUORSh/BTencNZJO4uVqGZx3EO9Z8JXTAvRjgeg==", "path": "npgsql/5.0.7", "hashPath": "npgsql.5.0.7.nupkg.sha512"}, "NPOI/2.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-yexXTxBKJT845yOSRDkgrgc3ciEWRnp8vMIRoZLOmUs7QJoCp5ZSi4wTPXtwYdhRg2v5Up9nC1TVKxrtkDcsYg==", "path": "npoi/2.7.0", "hashPath": "npoi.2.7.0.nupkg.sha512"}, "Oracle.ManagedDataAccess.Core/3.21.120": {"type": "package", "serviceable": true, "sha512": "sha512-RrSu5ZlhIqpV7mFauPfc2AkCsuWXA2cWCNXOBW+Wt7R8g6wqonWIWZN3iyhwr4mZMRrClJB/SRMtQzQMHuZPmg==", "path": "oracle.manageddataaccess.core/3.21.120", "hashPath": "oracle.manageddataaccess.core.3.21.120.nupkg.sha512"}, "Oscar.Data.SqlClient/4.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-VJ3xVvRjxrPi/mMPT5EqYiMZor0MjFu83mw1qvUveBFWJSudGh9BOKZq7RkhqeNCcL1ud0uK0/TVkw+xTa4q4g==", "path": "oscar.data.sqlclient/4.0.4", "hashPath": "oscar.data.sqlclient.4.0.4.nupkg.sha512"}, "Panda.DynamicWebApi/1.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-NBmhr3LmcH28mq7RzvcVMUeQgBxhcy5qEw07+4Xms1UKqIU2fTTW1GypRZM5g2AlPt58xq7ZUc5fSCQcVv4CmQ==", "path": "panda.dynamicwebapi/1.2.1", "hashPath": "panda.dynamicwebapi.1.2.1.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-Bhk0FWxH1paI+18zr1g5cTL+ebeuDcBCR+rRFO+fKEhretgjs7MF2Mc1P64FGLecWp4zKCUOPzngBNrqVyY7Zg==", "path": "pipelines.sockets.unofficial/2.2.2", "hashPath": "pipelines.sockets.unofficial.2.2.2.nupkg.sha512"}, "Polly/7.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-E6CeKyS513j7taKAq4q2MESDBvzuzWnR1rQ2Y2zqJvpiVtKMm699Aubb20MUPBDmb0Ov8PmcLHTCVFdCjoy2kA==", "path": "polly/7.2.2", "hashPath": "polly.7.2.2.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Remotion.Linq/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-fK/76UmpC0FXBlGDFVPLJHQlDLYnGC+XY3eoDgCgbtrhi0vzbXDQ3n/IYHhqSKqXQfGw/u04A1drWs7rFVkRjw==", "path": "remotion.linq/2.2.0", "hashPath": "remotion.linq.2.2.0.nupkg.sha512"}, "RestSharp/108.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XKwuwWq7A4dxZ8l9QkS2+ePLJ9ImmLaJSeFO6H2kpII4Us4n1NRs4w3c//8H2BYpL1XqBE8nmsd9aNCVBwXmOA==", "path": "restsharp/108.0.1", "hashPath": "restsharp.108.0.1.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7VSGO0URRKoMEAq0Sc9cRz8mb6zbyx/BZDEWhgPdzzpmFhkam3fJ1DAGWFXBI4nGlma+uPKpfuMQP5LXRnOH5g==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-0oAaTAm6e2oVH+/Zttt0cuhGaePQYKII1dY8iaqP7CvOpVKgLybKRFvQjXR2LtxXOXTVPNv14j0ot8uV+HrUmw==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-G24ibsCNi5Kbz0oXWynBoRgtGvsw5ZSVEWjv13/KiCAM8C6wz9zzcCniMeQFIkJ2tasjo2kXlvlBZhplL51kGg==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-75q52H7CSpgIoIDwXb9o833EvBZIXJ0mdPhz1E6jSisEXUBlSCPalC29cj3EXsjpuDwr0dj1LRXZepIQH/oL4Q==", "path": "runtime.linux-arm.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xn2bMThmXr3CsvOYmS8ex2Yz1xo+kcnhVg2iVhS9PlmqjZPAkrEo/I40wjrBZH/tU4kvH0s1AE8opAvQ3KIS8g==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-16nbNXwv0sC+gLGIuecri0skjuh6R1maIJggsaNP7MQBcbVcEfWFUOkEnsnvoLEjy0XerfibuRptfQ8AmdIcWA==", "path": "runtime.linux-x64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KaaXlpOcuZjMdmyF5wzzx3b+PRKIzt6A5Ax9dKenPDQbVJAFpev+casD0BIig1pBcbs3zx7CqWemzUJKAeHdSQ==", "path": "runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Security/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-M2nN92ePS8BgQ2oi6Jj3PlTUzadYSIWLdZrHY1n1ZcW9o4wAQQ6W+aQ2lfq1ysZQfVCgDwY58alUdowrzezztg==", "path": "runtime.native.system.net.security/4.3.0", "hashPath": "runtime.native.system.net.security.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-QR1OwtwehHxSeQvZKXe+iSd+d3XZNkEcuWMFYa2i0aG1l+lR739HPicKMlTbJst3spmeekDVBUS7SeS26s4U/g==", "path": "runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-I+GNKGg2xCHueRd1m9PzeEW7WLbNNLznmTuEi8/vZX71HudUbx1UTwlGkiwMri7JLl8hGaIAWnA/GONhu+LOyQ==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-1Z3TAq1ytS1IBRtPXJvEUZdVsfWfeNEhBkbiOCGEl9wwAfsjP2lz3ZFDx5tq8p60/EqbS0HItG5piHuB71RjoA==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fXG12NodG1QrCdoaeSQ1gVnk/koi4WYY4jZtarMkZeQMyReBm1nZlSRoPnUjLr2ZR36TiMjpcGnQfxymieUe7w==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/As+zPY49+dSUXkh+fTUbyPhqrdGN//evLxo4Vue88pfh1BHZgF7q4kMblTkxYvwR6Vi03zSYxysSFktO8/SDQ==", "path": "runtime.osx-x64.runtime.native.system.io.ports/6.0.0", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.6.0.0.nupkg.sha512"}, "runtime.osx.10.10-x64.CoreCompat.System.Drawing/5.8.64": {"type": "package", "serviceable": true, "sha512": "sha512-Ey7xQgWwixxdrmhzEUvaR4kxZDSQMWQScp8ViLvmL5xCBKG6U3TaMv/jzHilpfQXpHmJ4IylKGzzMvnYX2FwHQ==", "path": "runtime.osx.10.10-x64.corecompat.system.drawing/5.8.64", "hashPath": "runtime.osx.10.10-x64.corecompat.system.drawing.5.8.64.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-6mU/cVmmHtQiDXhnzUImxIcDL48GbTk+TsptXyJA+MIOG9LRjPoAQC/qBFB7X+UNyK86bmvGwC8t+M66wsYC8w==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-vjwG0GGcTW/PPg6KVud8F9GLWYuAV1rrw1BKAqY0oh4jcUqg15oYF1+qkGR2x2ZHM4DQnWKQ7cJgYbfncz/lYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-7KMFpTkHC/zoExs+PwP8jDCWcrK9H6L7soowT80CUx3e+nxP/AFnq0AQAW5W76z2WYbLAYCRyPfwYFG6zkvQRw==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xrlmRCnKZJLHxyyLIqkZjNXqgxnKdZxfItrPkjI+6pkRo5lHX8YvSZlWrSI5AVwLMi4HbNWP7064hcAWeZKp5w==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-leXiwfiIkW7Gmn7cgnNcdtNAU70SjmKW3jxGj1iKHOvdn0zRWsgv/l2OJUO5zdGdiv2VRFnAsxxhDgMzofPdWg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.2", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.2.nupkg.sha512"}, "SapNwRfc/1.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2J7qzVLWHqY5DOgASmeBD8YQBs6ouNfuDJZ08oxYfeFlVyxE6PxsAN748lN3XIdbTicJdFPiBD55sK5vA6towA==", "path": "sapnwrfc/1.4.0", "hashPath": "sapnwrfc.1.4.0.nupkg.sha512"}, "Scrutor/3.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-biheXROWXbciLzPOg/PttVH4w4Q8ADx89bQP8eKiGf1IJj0EOLYRjoctsMGQzi4mB+e4ICMqFeA8Spr0NKN4ZA==", "path": "scrutor/3.0.1", "hashPath": "scrutor.3.0.1.nupkg.sha512"}, "Serilog/2.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA==", "path": "serilog/2.10.0", "hashPath": "serilog.2.10.0.nupkg.sha512"}, "Serilog.AspNetCore/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5XW90k62V7G9I0D/j9Iz+NyRBB6/SnoFpHUPeLnV40gONV2vs2A/ewWi91QVjQmyHBfzFeqIrkvE/DJMZ0alTg==", "path": "serilog.aspnetcore/6.0.1", "hashPath": "serilog.aspnetcore.6.0.1.nupkg.sha512"}, "Serilog.Extensions.Hosting/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-o0VUyt3npAqOJaZ6CiWLFeLYs3CYJwfcAqaUqprzsmj7qYIvorcn8cZLVR8AQX6vzX7gee2bD0sQeA17iO2/Aw==", "path": "serilog.extensions.hosting/5.0.1", "hashPath": "serilog.extensions.hosting.5.0.1.nupkg.sha512"}, "Serilog.Extensions.Logging/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IWfem7wfrFbB3iw1OikqPFNPEzfayvDuN4WP7Ue1AVFskalMByeWk3QbtUXQR34SBkv1EbZ3AySHda/ErDgpcg==", "path": "serilog.extensions.logging/3.1.0", "hashPath": "serilog.extensions.logging.3.1.0.nupkg.sha512"}, "Serilog.Formatting.Compact/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pNroKVjo+rDqlxNG5PXkRLpfSCuDOBY0ri6jp9PLe505ljqwhwZz8ospy2vWhQlFu5GkIesh3FcDs4n7sWZODA==", "path": "serilog.formatting.compact/1.1.0", "hashPath": "serilog.formatting.compact.1.1.0.nupkg.sha512"}, "Serilog.Formatting.Elasticsearch/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-768KS00+XwQSxVIYKJ4KWdqyLd5/w3DKndf+94U8NCk7qpXCeZl4HlczsDeyVsNPTyRF6MVss6Wr9uj4rhprfA==", "path": "serilog.formatting.elasticsearch/8.4.1", "hashPath": "serilog.formatting.elasticsearch.8.4.1.nupkg.sha512"}, "Serilog.Settings.Configuration/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7GNudISZwqaT902hqEL2OFGTZeUFWfnrNLupJkOqeF41AR3GjcxX+Hwb30xb8gG2/CDXsCMVfF8o0+8KY0fJNg==", "path": "serilog.settings.configuration/3.3.0", "hashPath": "serilog.settings.configuration.3.3.0.nupkg.sha512"}, "Serilog.Sinks.Async/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-csHYIqAwI4Gy9oAhXYRwxGrQEAtBg3Ep7WaCzsnA1cZuBZjVAU0n7hWaJhItjO7hbLHh/9gRVxALCUB4Dv+gZw==", "path": "serilog.sinks.async/1.5.0", "hashPath": "serilog.sinks.async.1.5.0.nupkg.sha512"}, "Serilog.Sinks.Console/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-apLOvSJQLlIbKlbx+Y2UDHSP05kJsV7mou+fvJoRGs/iR+jC22r8cuFVMjjfVxz/AD4B2UCltFhE1naRLXwKNw==", "path": "serilog.sinks.console/4.0.1", "hashPath": "serilog.sinks.console.4.0.1.nupkg.sha512"}, "Serilog.Sinks.Debug/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y6g3OBJ4JzTyyw16fDqtFcQ41qQAydnEvEqmXjhwhgjsnG/FaJ8GUqF5ldsC/bVkK8KYmqrPhDO+tm4dF6xx4A==", "path": "serilog.sinks.debug/2.0.0", "hashPath": "serilog.sinks.debug.2.0.0.nupkg.sha512"}, "Serilog.Sinks.Elasticsearch/8.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-SM17WdHUshJSm44uC45jEUW4Wzp9wCltbWry5iY5fNgxJ3PkIkW6I8p+WviU5lx/bayCvAoB5uO07UK2qjBSAQ==", "path": "serilog.sinks.elasticsearch/8.4.1", "hashPath": "serilog.sinks.elasticsearch.8.4.1.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "Serilog.Sinks.PeriodicBatching/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1iZtcEzQdEIYCPvhYJYB2RofPg+i1NhHJfS+DpXLyLSMS6OXebqaI1fxWhmJRIjD9D9BuXi23FkZTQDiP7cHw==", "path": "serilog.sinks.periodicbatching/2.1.1", "hashPath": "serilog.sinks.periodicbatching.2.1.1.nupkg.sha512"}, "Serilog.Sinks.RollingFile/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2lT5X1r3GH4P0bRWJfhA7etGl8Q2Ipw9AACvtAHWRUSpYZ42NGVyHoVs2ALBZ/cAkkS+tA4jl80Zie144eLQPg==", "path": "serilog.sinks.rollingfile/3.3.0", "hashPath": "serilog.sinks.rollingfile.3.3.0.nupkg.sha512"}, "SharpCompress/0.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-HBbT47JHvNrsZX2dTBzUBOSzBt+EmIRGLIBkbxcP6Jef7DB4eFWQX5iHWV3Nj7hABFPCjISrZ8s0z72nF2zFHQ==", "path": "sharpcompress/0.23.0", "hashPath": "sharpcompress.0.23.0.nupkg.sha512"}, "SharpZipLib/1.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-N8+hwhsKZm25tDJfWpBSW7EGhH/R7EMuiX+KJ4C4u+fCWVc1lJ5zg1u3S1RPPVYgTqhx/C3hxrqUpi6RwK5+Tg==", "path": "sharpziplib/1.3.3", "hashPath": "sharpziplib.1.3.3.nupkg.sha512"}, "SixLabors.Fonts/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ljezRHWc7N0azdQViib7Aa5v+DagRVkKI2/93kEbtjVczLs+yTkSq6gtGmvOcx4IqyNbO3GjLt7SAQTpLkySNw==", "path": "sixlabors.fonts/1.0.1", "hashPath": "sixlabors.fonts.1.0.1.nupkg.sha512"}, "SixLabors.ImageSharp/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-CiOV86pE4v1U850lO8zTRXQMysFCo40DJnHwxX8mIJbJ/unr7I9avhHqCh3BvU0sOSDdCp/2LcIz+7zVCWjMZw==", "path": "sixlabors.imagesharp/2.1.7", "hashPath": "sixlabors.imagesharp.2.1.7.nupkg.sha512"}, "SkyAPM.Abstractions/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YYipEOeNvCQgG28mV4xqcT7QG08so5RLkRRw7P/VZMC8K3kyqVKSaeDrGWU/noWhIdG0qmFGMWdFCD9gzSALeA==", "path": "skyapm.abstractions/1.3.0", "hashPath": "skyapm.abstractions.1.3.0.nupkg.sha512"}, "SkyAPM.Agent.AspNetCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-xVkv7VnGKGaX82AlT7D1jkDb+pGqNIlCPEpLOeFGQMF559bRN2OpBzSgOFt9FOrniuPjuQSGxkgoA83tK9YfsA==", "path": "skyapm.agent.aspnetcore/1.3.0", "hashPath": "skyapm.agent.aspnetcore.1.3.0.nupkg.sha512"}, "SkyAPM.Agent.Hosting/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rzb7CRl13g5XOHLln6VTdaim42GBelb6ua/lprjxlJRGshTJKLSKDzMdyAlZCQ/1MMAvihD3ad9ZrgjgBBBSJQ==", "path": "skyapm.agent.hosting/1.3.0", "hashPath": "skyapm.agent.hosting.1.3.0.nupkg.sha512"}, "SkyAPM.Core/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JA30FQVAmZyOG5NL07s7kfY/SEqIQ3aWg/VRsNhpblcaCCp6GuaCyEeowqDCekpxuwxmpfYSfdiWy6bzrdSFfg==", "path": "skyapm.core/1.3.0", "hashPath": "skyapm.core.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.AspNetCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RkZdEJxaKtouWDpAM6oBxWystLJDUF4A+58JHQZnf6+ExrpsnbemAc8jV7lJanUVmQxss8x3DyDQCMd8jefN9A==", "path": "skyapm.diagnostics.aspnetcore/1.3.0", "hashPath": "skyapm.diagnostics.aspnetcore.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jJcpGY/Zn67kPXVzq8xp7bf+/aDkAJaKazwCmR42H2AGlKrcUETP7wq4ahOMi2S1X0kOLlcc/tObhOnCYdlnTw==", "path": "skyapm.diagnostics.entityframeworkcore/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Npgsql/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-dxfvfdKod2FutgogwbNqeIF8rvswZ3r3d042+LP6BwADSb3JWttjzGOskSW+Fu+m4/Ikq2WWWi8oqnSvg3kPSQ==", "path": "skyapm.diagnostics.entityframeworkcore.npgsql/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.npgsql.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Pomelo.MySql/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vqt2rF+SPuSjquO08OB/450ma2TOHp5/g4cSkQR31CRFg5GviB2fRGg8IsCun02IhMDHLdGtre5YgjyyBMHUEQ==", "path": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.pomelo.mysql.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.EntityFrameworkCore.Sqlite/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-zDf325OiCEtijxL7/0hZNsIa7BWEU4n4hKpx8/M+nSBG9z/ECkv2ROTpOqexSP0JxfsMv6gtO0uFwwnwS42lSQ==", "path": "skyapm.diagnostics.entityframeworkcore.sqlite/1.3.0", "hashPath": "skyapm.diagnostics.entityframeworkcore.sqlite.1.3.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6ooWfCCIJHN+nEjXXzI7xgH+P6p3v6gJqbWBo39g7f6e7nxmeYOjecKmv5kMas11sJZ6enVstaM03vemOPb37A==", "path": "skyapm.diagnostics.grpc/1.0.0", "hashPath": "skyapm.diagnostics.grpc.1.0.0.nupkg.sha512"}, "SkyApm.Diagnostics.Grpc.Net.Client/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wos7+HP2FScWsfXymXu2YSve2rWykhH2UT+6ZW4tFoRo3OEu3g2BNxsR5qoXwP+VLMchJfo7eaAY+FvGOMUK1w==", "path": "skyapm.diagnostics.grpc.net.client/1.0.0", "hashPath": "skyapm.diagnostics.grpc.net.client.1.0.0.nupkg.sha512"}, "SkyAPM.Diagnostics.HttpClient/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j3ZwrCse3gx9Vyx2xa/8aqub+jk+HM7Xwe5IjYH9l6t7h3qiY4jsMBpr4/JFaMxEKPvASxbA3e6cPPMJb7JQPg==", "path": "skyapm.diagnostics.httpclient/1.3.0", "hashPath": "skyapm.diagnostics.httpclient.1.3.0.nupkg.sha512"}, "SkyAPM.Diagnostics.SqlClient/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GoYgBWpwdixBZcL+4LAxmNhRNTpdqhmVGkv5tl8cIm5fvKlX0YpIpW/n8+uabEoBJ2uw391HM1uSgcYvIlQJGQ==", "path": "skyapm.diagnostics.sqlclient/1.3.0", "hashPath": "skyapm.diagnostics.sqlclient.1.3.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-2//10IXoRhQYaShyv8WhE9OxpzcbbOgQKAO3GOD6v4FyUwEPkiyipu10q2HI4jUWmYEMUy2sMFrjwWt0+CguWw==", "path": "skyapm.transport.grpc/1.3.0", "hashPath": "skyapm.transport.grpc.1.3.0.nupkg.sha512"}, "SkyAPM.Transport.Grpc.Protocol/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-9Rz3PLq3Z8kvpMTBE4CzaE7JqSGBtfL01pUaDzlbsGsLHz7ox3TO+DQXei+uczw6S1VCHUWjiLX0zsFZ5b5kHA==", "path": "skyapm.transport.grpc.protocol/1.3.0", "hashPath": "skyapm.transport.grpc.protocol.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.Configuration/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oH4eE748fLULwDRlAGQsGjyhQM9UWsqZMKmxiEM13wzvjQlKbezyNv056yNqyUbStRNZtIwzFld3Afo6wXe1EA==", "path": "skyapm.utilities.configuration/1.3.0", "hashPath": "skyapm.utilities.configuration.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.DependencyInjection/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-K0jbdw5wapzwKRLk3b6wvcS6u48RRKMvJ1X6Jxwg7EkVSk0ayms4M2UlXkoPv4yv7Zlut1sphhVE32r0l68qeg==", "path": "skyapm.utilities.dependencyinjection/1.3.0", "hashPath": "skyapm.utilities.dependencyinjection.1.3.0.nupkg.sha512"}, "SkyAPM.Utilities.Logging/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UpEY/8uXSs99vxXHGSNZ2tzbvV+IW4GGfVBZx+Q+2qMFWHgtYpTjxI9eeltcJsaRarLEkez3eIJ7OsPvZmMMtQ==", "path": "skyapm.utilities.logging/1.3.0", "hashPath": "skyapm.utilities.logging.1.3.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-BmAf6XWt4TqtowmiWe4/5rRot6GerAeklmOPfviOvwLoF5WwgxcJHAxZtySuyW9r9w+HLILnm8VfJFLCUJYW8A==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-wO6v9GeMx9CUngAet8hbO7xdm+M42p1XeJq47ogyRoYSvNSp0NGLI+MgC0bhrMk9C17MTVFlLiN6ylyExLCc5w==", "path": "sqlitepclraw.core/2.1.6", "hashPath": "sqlitepclraw.core.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-2<PERSON>bJJLkIUIxRpOUlZNGuD4rICpBnrBR5anjyfUFQep4hMOIeqW+XGQYzrNmHSVz5xSWZ3klSbh7sFR6UyDj68Q==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.6.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.6": {"type": "package", "serviceable": true, "sha512": "sha512-PQ2Oq3yepLY4P7ll145P3xtx2bX8xF4PzaKPRpw9jZlKvfe4LE/saAV82inND9usn1XRpmxXk7Lal3MTI+6CNg==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.6", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.6.nupkg.sha512"}, "SqlSugarCore/*********": {"type": "package", "serviceable": true, "sha512": "sha512-T81cn8hSEDdGwM/WCZXbmIvSo9FGTsR9ITakC3vTGXRjvi2R7n4VE9Ocj5Oo6NOCcq4SoXrk/pAVXRA/uRrsIA==", "path": "sqlsugarcore/*********", "hashPath": "sqlsugarcore.*********.nupkg.sha512"}, "SqlSugarCore.Dm/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JIKtvU8LySwSKBsqFijO4kWH24j8Ks3t03fFY1w6YMbFiJ6yuq7AI8iR4oSPiMhDtoYuj6yBz6u/gttrHBX1Sg==", "path": "sqlsugarcore.dm/1.3.0", "hashPath": "sqlsugarcore.dm.1.3.0.nupkg.sha512"}, "SqlSugarCore.Kdbndp/8.3.715": {"type": "package", "serviceable": true, "sha512": "sha512-fhQlXO/vxAnu0vAUMz7dYM4e/hwIQi7N5TEGUUsrt7C6nEUnxZ2+Ghcvm060XSAlz/thIohC50L6kn5oLKq7BA==", "path": "sqlsugarcore.kdbndp/8.3.715", "hashPath": "sqlsugarcore.kdbndp.8.3.715.nupkg.sha512"}, "StackExchange.Redis/2.6.48": {"type": "package", "serviceable": true, "sha512": "sha512-T0rLGogyT6Zny+IMrDx1Z8r4nA3B0C7EVo5SHNjzT4ndOn9aGKe5K7KTVx0y41WaWmfSWpaX7HrPl0tfZ4zuUw==", "path": "stackexchange.redis/2.6.48", "hashPath": "stackexchange.redis.2.6.48.nupkg.sha512"}, "Swashbuckle.AspNetCore/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HoJbhDNyeDqr2R1H3YhtPqGacxgZKBFBS6g5U3tlJpv80G/IHW8hHbcnHSTXZpcatnD+xh8UiUrKp4Ua857LSQ==", "path": "swashbuckle.aspnetcore/5.0.0", "hashPath": "swashbuckle.aspnetcore.5.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HrCFKfQNFFEdCdbsMiuZK2NDYlIR1Osik46XK4X3rJlDKngdMrGenUb2ax7JWO5wEr77OGPEMidQ39fgCyTaDQ==", "path": "swashbuckle.aspnetcore.annotations/5.0.0", "hashPath": "swashbuckle.aspnetcore.annotations.5.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Filters/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-e9n8g5FerM9LEzErUQFIP2YoRK+3LMAQdpOddJgDsyHQE60886l1GSu2UnVzdzTh0TEDHJ0yIjN6ciitjs9Wdw==", "path": "swashbuckle.aspnetcore.filters/6.0.1", "hashPath": "swashbuckle.aspnetcore.filters.6.0.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Newtonsoft/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-b+qQJ3TV+JDAVGvn5oYtnhiocSxE9E3yja9VvPol6XJ7x6E1NRoYp+wvo+KGSZM+igha2Nxgu1a95n92FEe7ng==", "path": "swashbuckle.aspnetcore.newtonsoft/6.4.0", "hashPath": "swashbuckle.aspnetcore.newtonsoft.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512"}, "System.AppContext/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "path": "system.appcontext/4.3.0", "hashPath": "system.appcontext.4.3.0.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyGn1jGRZVfxnh8EdvDCi71v3bMXrsu8aYJOwoV7SNDLVhiEqwP86pPMyRGsDsxhXAm2b3o9OIqeETfN5qfezw==", "path": "system.componentmodel/4.3.0", "hashPath": "system.componentmodel.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/6.0.0-preview.4.21253.7": {"type": "package", "serviceable": true, "sha512": "sha512-h0eWBYUk1rLI31AAmLMgmVzy6UBMUBvRfAqB9VH+O8pMT5GBFpBJoTEIdhBfzcn7VS+aPPDzpLlASHXdjt2qXQ==", "path": "system.componentmodel.annotations/6.0.0-preview.4.21253.7", "hashPath": "system.componentmodel.annotations.6.0.0-preview.4.21253.7.nupkg.sha512"}, "System.ComponentModel.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-j8GUkCpM8V4d4vhLIIoBLGey2Z5bCkMVNjEZseyAlm4n5arcsJOeI3zkUP+zvZgzsbLTYh4lYeP/ZD/gdIAPrw==", "path": "system.componentmodel.primitives/4.3.0", "hashPath": "system.componentmodel.primitives.4.3.0.nupkg.sha512"}, "System.ComponentModel.TypeConverter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-16pQ6P+EdhcXzPiEK4kbA953Fu0MNG2ovxTZU81/qsCd1zPRsKc3uif5NgvllCY598k6bI0KUyKW8fanlfaDQg==", "path": "system.componentmodel.typeconverter/4.3.0", "hashPath": "system.componentmodel.typeconverter.4.3.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7T+m0kDSlIPTHIkPMIu6m6tV6qsMqJpvQWW2jIc2qi7sn40qxFo0q+7mEQAhMPXZHMKnWrnv47ntGlM/ejvw3g==", "path": "system.configuration.configurationmanager/6.0.0", "hashPath": "system.configuration.configurationmanager.6.0.0.nupkg.sha512"}, "System.Console/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "path": "system.console/4.3.0", "hashPath": "system.console.4.3.0.nupkg.sha512"}, "System.Data.Common/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lm6E3T5u7BOuEH0u18JpbJHxBfOJPuCyl4Kg1RH10ktYLp5uEEE1xKrHW56/We4SnZpGAuCc9N0MJpSDhTHZGQ==", "path": "system.data.common/4.3.0", "hashPath": "system.data.common.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lcyUiXTsETK2ALsZrX+nWuHSIQeazhqPphLfaRxzdGaG93+0kELqpgEHtwWOlQe7+jSFnKwaCAgL4kjeZCQJnw==", "path": "system.diagnostics.eventlog/6.0.0", "hashPath": "system.diagnostics.eventlog.6.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-dDl7Gx3bmSrM2k2ZIm+ucEJnLloZRyvfQF1DvfvATcGF3jtaUBiPvChma+6ZcZzxWMirN3kCywkW7PILphXyMQ==", "path": "system.diagnostics.performancecounter/6.0.1", "hashPath": "system.diagnostics.performancecounter.6.0.1.nupkg.sha512"}, "System.Diagnostics.Process/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA==", "path": "system.diagnostics.process/4.1.0", "hashPath": "system.diagnostics.process.4.1.0.nupkg.sha512"}, "System.Diagnostics.StackTrace/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiHg0vgtd35/DM9jvtaC1eKRpWZxr0gcQd643ABG7GnvSlf5pOkY2uyd42mMOJoOmKvnpNj0F4tuoS1pacTwYw==", "path": "system.diagnostics.stacktrace/4.3.0", "hashPath": "system.diagnostics.stacktrace.4.3.0.nupkg.sha512"}, "System.Diagnostics.TextWriterTraceListener/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w36Dr8yKy8xP150qPANe7Td+/zOI3G62ImRcHDIEW+oUXUuTKZHd4DHmqRx5+x8RXd85v3tXd1uhNTfsr+yxjA==", "path": "system.diagnostics.textwritertracelistener/4.0.0", "hashPath": "system.diagnostics.textwritertracelistener.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.TraceSource/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6WVCczFZKXwpWpzd/iJkYnsmWTSFFiU24Xx/YdHXBcu+nFI/ehTgeqdJQFbtRPzbrO3KtRNjvkhtj4t5/WwWsA==", "path": "system.diagnostics.tracesource/4.0.0", "hashPath": "system.diagnostics.tracesource.4.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.DirectoryServices/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-935IbO7h5FDGYxeO3cbx/CuvBBuk/VI8sENlfmXlh1pupNOB3LAGzdYdPY8CawGJFP7KNrHK5eUlsFoz3F6cuA==", "path": "system.directoryservices/6.0.1", "hashPath": "system.directoryservices.6.0.1.nupkg.sha512"}, "System.DirectoryServices.Protocols/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-vDDPWwHn3/DNZ+kPkdXHoada+tKPEC9bVqDOr4hK6HBSP7hGCUTA0Zw6WU5qpGaqa5M1/V+axHMIv+DNEbIf6g==", "path": "system.directoryservices.protocols/6.0.2", "hashPath": "system.directoryservices.protocols.6.0.2.nupkg.sha512"}, "System.Drawing.Common/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "path": "system.drawing.common/6.0.0", "hashPath": "system.drawing.common.6.0.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Formats.Asn1/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T6fD00dQ3NTbPDy31m4eQUwKW84s03z0N2C8HpOklyeaDgaJPa/TexP4/SkORMSOwc7WhKifnA6Ya33AkzmafA==", "path": "system.formats.asn1/6.0.0", "hashPath": "system.formats.asn1.6.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.21.0": {"type": "package", "serviceable": true, "sha512": "sha512-JRD8AuypBE+2zYxT3dMJomQVsPYsCqlyZhWel3J1d5nzQokSRyTueF+Q4ID3Jcu6zSZKuzOdJ1MLTkbQsDqcvQ==", "path": "system.identitymodel.tokens.jwt/6.21.0", "hashPath": "system.identitymodel.tokens.jwt.6.21.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "path": "system.io.compression.zipfile/4.3.0", "hashPath": "system.io.compression.zipfile.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.Hashing/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDnWM0N3AMCa86LrKTWeF3BZLD2sgWyYUc7HL6z4+xyDZNQRwzmxbo4qP2rX2MqC+Sy1/gOSRDah5ltxY5jPxw==", "path": "system.io.hashing/7.0.0", "hashPath": "system.io.hashing.7.0.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.IO.Ports/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dRyGI7fUESar5ZLIpiBOaaNLW7YyOBGftjj5Of+xcduC/Rjl7RjhEnWDvvNBmHuF3d0tdXoqdVI/yrVA8f00XA==", "path": "system.io.ports/6.0.0", "hashPath": "system.io.ports.6.0.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Dynamic.Core/1.2.18": {"type": "package", "serviceable": true, "sha512": "sha512-+RH90sKD6SK2c9MD2Xo2jz1hkAJYfgPVyW1VgAwiPURR+JzOJCdvsDBg2Iq97FmTymxlQBY76G1cMxsF6j+6tA==", "path": "system.linq.dynamic.core/1.2.18", "hashPath": "system.linq.dynamic.core.1.2.18.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Linq.Queryable/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "path": "system.linq.queryable/4.3.0", "hashPath": "system.linq.queryable.4.3.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.NameResolution/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "path": "system.net.nameresolution/4.3.0", "hashPath": "system.net.nameresolution.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Net.Security/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-xT2jbYpbBo3ha87rViHoTA6WdvqOAW37drmqyx/6LD8p7HEPT2qgdxoimRzWtPg8Jh4X5G9BV2seeTv4x6FYlA==", "path": "system.net.security/4.3.2", "hashPath": "system.net.security.4.3.2.nupkg.sha512"}, "System.Net.Sockets/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "path": "system.net.sockets/4.3.0", "hashPath": "system.net.sockets.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Private.ServiceModel/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-BcUV7OERlLqGxDXZuIyIMMmk1PbqBblLRbAoigmzIUx/M8A+8epvyPyXRpbgoucKH7QmfYdQIev04Phx2Co08A==", "path": "system.private.servicemodel/4.10.3", "hashPath": "system.private.servicemodel.4.10.3.nupkg.sha512"}, "System.Reactive/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-erBZjkQHWL9jpasCE/0qKAryzVBJFxGHVBAvgRN1bzM0q2s1S4oYREEEL0Vb+1kA/6BKb5FjUZMp5VXmy+gzkQ==", "path": "system.reactive/5.0.0", "hashPath": "system.reactive.5.0.0.nupkg.sha512"}, "System.Reactive.Linq/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IB4/qlV4T1WhZvM11RVoFUSZXPow9VWVeQ1uDkSKgz6bAO+gCf65H/vjrYlwyXmojSSxvfHndF9qdH43P/IuAw==", "path": "system.reactive.linq/5.0.0", "hashPath": "system.reactive.linq.5.0.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.DispatchProxy/4.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-C1sMLwIG6ILQ2bmOT4gh62V6oJlyF4BlHcVMrOoor49p0Ji2tA8QAoqyMcIhAdH6OHKJ8m7BU+r4LK2CUEOKqw==", "path": "system.reflection.dispatchproxy/4.7.1", "hashPath": "system.reflection.dispatchproxy.4.7.1.nupkg.sha512"}, "System.Reflection.Emit/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ==", "path": "system.reflection.emit/4.7.0", "hashPath": "system.reflection.emit.4.7.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-tc2ZyJgweHCLci5oQGuhQn9TD0Ii9DReXkHtZm3aAGp8xe40rpRjiTbMXOtZU+fr0BOQ46goE9+qIqRGjR9wGg==", "path": "system.reflection.metadata/1.4.1", "hashPath": "system.reflection.metadata.1.4.1.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Loader/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DHMaRn8D8YCK2GG2pw+UzNxn/OHVfaWx7OTLBD/hPegHZZgcZh3H6seWegrC4BYwsfuGrywIuT+MQs+rPqRLTQ==", "path": "system.runtime.loader/4.3.0", "hashPath": "system.runtime.loader.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Formatters/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KT591AkTNFOTbhZlaeMVvfax3RqhH1EJlcwF50Wm7sfnBLuHiOeZRRKrr1ns3NESkM20KPZ5Ol/ueMq5vg4QoQ==", "path": "system.runtime.serialization.formatters/4.3.0", "hashPath": "system.runtime.serialization.formatters.4.3.0.nupkg.sha512"}, "System.Runtime.Serialization.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wz+0KOukJGAlXjtKr+5Xpuxf8+c8739RI1C+A2BoQZT+wMCCoMDDdO8/4IRHfaVINqL78GO8dW8G2lW/e45Mcw==", "path": "system.runtime.serialization.primitives/4.3.0", "hashPath": "system.runtime.serialization.primitives.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AUADIc0LIEQe7MzC+I0cl0rAT8RrTAKFHl53yHjEUzNVIaUlhFY11vc2ebiVJzVBuOzun6F7FBA+8KAbGTTedQ==", "path": "system.security.accesscontrol/6.0.0", "hashPath": "system.security.accesscontrol.6.0.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-18UT1BdZ4TYFBHk/wuq7JzCdE3X75z81X+C2rXqIlmEnC21Pm60spGV/dKQSzbyomstqYE33EuN5hfnC86VFxA==", "path": "system.security.cryptography.pkcs/6.0.3", "hashPath": "system.security.cryptography.pkcs.6.0.3.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rp1gMNEZpvx9vP0JW0oHLxlf8oSiQgtno77Y4PLUBjSiDYoD77Y8uXHr1Ea5XG4/pIKhqAdxZ8v8OTUtqo9PeQ==", "path": "system.security.cryptography.protecteddata/6.0.0", "hashPath": "system.security.cryptography.protecteddata.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.2": {"type": "package", "serviceable": true, "sha512": "sha512-uwlfOnvJd7rXRvP3aV126Q9XebIIEGEaZ245Rd5/ZwOg7U7AU+AmpE0vRh2F0DFjfOTuk7MAexv4nYiNP/RYnQ==", "path": "system.security.cryptography.x509certificates/4.3.2", "hashPath": "system.security.cryptography.x509certificates.4.3.2.nupkg.sha512"}, "System.Security.Cryptography.Xml/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5e5bI28T0x73AwTsbuFP4qSRzthmU2C0Gqgg3AZ3KTxmSyA+Uhk31puA3srdaeWaacVnHhLdJywCzqOiEpbO/w==", "path": "system.security.cryptography.xml/6.0.1", "hashPath": "system.security.cryptography.xml.6.0.1.nupkg.sha512"}, "System.Security.Permissions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-T/uuc7AklkDoxmcJ7LGkyX1CcSviZuLCa4jg3PekfJ7SU0niF0IVTXwUiNVP9DSpzou2PpxJ+eNY2IfDM90ZCg==", "path": "system.security.permissions/6.0.0", "hashPath": "system.security.permissions.6.0.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Security.SecureString/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sqzq9GD6/b0yqPuMpgIKBuoLf4VKAj8oAfh4kXSzPaN6eoKY3hRi9C5L27uip25qlU+BGPfb0xh2Rmbwc4jFVA==", "path": "system.security.securestring/4.0.0", "hashPath": "system.security.securestring.4.0.0.nupkg.sha512"}, "System.ServiceModel.Duplex/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-IZ8ZahvTenWML7/jGUXSCm6jHlxpMbcb+Hy+h5p1WP9YVtb+Er7FHRRGizqQMINEdK6HhWpD6rzr5PdxNyusdg==", "path": "system.servicemodel.duplex/4.10.3", "hashPath": "system.servicemodel.duplex.4.10.3.nupkg.sha512"}, "System.ServiceModel.Federation/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-VFQgE+XFdUXvxShtdQwNJ0AxfYr0ZVS0TlR3yRwCZxtcRPD0rnkSNfTbIiLxjH0j+O+IrtNZh9EDAQCLEiSNMA==", "path": "system.servicemodel.federation/4.10.3", "hashPath": "system.servicemodel.federation.4.10.3.nupkg.sha512"}, "System.ServiceModel.Http/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-hodkn0rPTYmoZ9EIPwcleUrOi1gZBPvU0uFvzmJbyxl1lIpVM5GxTrs/pCETStjOXCiXhBDoZQYajquOEfeW/w==", "path": "system.servicemodel.http/4.10.3", "hashPath": "system.servicemodel.http.4.10.3.nupkg.sha512"}, "System.ServiceModel.NetTcp/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-tP7GN7ehqSIQEz7yOJEtY8ziTpfavf2IQMPKa7r9KGQ75+uEW6/wSlWez7oKQwGYuAHbcGhpJvdG6WoVMKYgkw==", "path": "system.servicemodel.nettcp/4.10.3", "hashPath": "system.servicemodel.nettcp.4.10.3.nupkg.sha512"}, "System.ServiceModel.Primitives/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-aNcdry95wIP1J+/HcLQM/f/AA73LnBQDNc2uCoZ+c1//KpVRp8nMZv5ApMwK+eDNVdCK8G0NLInF+xG3mfQL+g==", "path": "system.servicemodel.primitives/4.10.3", "hashPath": "system.servicemodel.primitives.4.10.3.nupkg.sha512"}, "System.ServiceModel.Security/4.10.3": {"type": "package", "serviceable": true, "sha512": "sha512-vqelKb7DvP2inb6LDJ5Igi8wpOYdtLXn5luDW5qEaqkV2sYO1pKlVYBpr6g6m5SevzbdZlVNu67dQiD/H6EdGQ==", "path": "system.servicemodel.security/4.10.3", "hashPath": "system.servicemodel.security.4.10.3.nupkg.sha512"}, "System.ServiceProcess.ServiceController/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qMBvG8ZFbkXoe0Z5/D7FAAadfPkH2v7vSuh2xsLf3U6jNoejpIdeV18A0htiASsLK1CCAc/p59kaLXlt2yB1gw==", "path": "system.serviceprocess.servicecontroller/6.0.0", "hashPath": "system.serviceprocess.servicecontroller.6.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NyscU59xX6Uo91qvhOs2Ccho3AR2TnZPomo1Z0K6YpyztBPM/A5VbkzOO19sy3A3i1TtEnTxA7bCe3Us+r5MWg==", "path": "system.text.encoding.codepages/5.0.0", "hashPath": "system.text.encoding.codepages.5.0.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaJsHfESQvJ11vbXnNlkrR46IaMULk/gHxYsJphzSF+07kTjPHv+Oc14w6QEOfo3Q4hqLJgStUaYB9DBl0TmWg==", "path": "system.text.json/6.0.0", "hashPath": "system.text.json.6.0.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbjBNZHf/vQCfcdhzx7knsiygoCKgxL8mZOeocXZn5gWhCdzHIq6bYNKWX0LAJCWYP7bds4yBK8p06YkP0oa0g==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.Threading.Thread/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "path": "system.threading.thread/4.3.0", "hashPath": "system.threading.thread.4.3.0.nupkg.sha512"}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "path": "system.threading.threadpool/4.3.0", "hashPath": "system.threading.threadpool.4.3.0.nupkg.sha512"}, "System.Threading.Timer/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "path": "system.threading.timer/4.3.0", "hashPath": "system.threading.timer.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "System.Windows.Extensions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IXoJOXIqc39AIe+CIR7koBtRGMiCt/LPM3lI+PELtDIy9XdyeSrwXFdWV9dzJ2Awl0paLWUaknLxFQ5HpHZUog==", "path": "system.windows.extensions/6.0.0", "hashPath": "system.windows.extensions.6.0.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}, "System.Xml.XDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "path": "system.xml.xdocument/4.3.0", "hashPath": "system.xml.xdocument.4.3.0.nupkg.sha512"}, "System.Xml.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-lJ8AxvkX7GQxpC6GFCeBj8ThYVyQczx2+f/cWHJU8tjS7YfI6Cv6bon70jVEgs2CiFbmmM8b9j1oZVx0dSI2Ww==", "path": "system.xml.xmldocument/4.3.0", "hashPath": "system.xml.xmldocument.4.3.0.nupkg.sha512"}, "System.Xml.XPath/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-v1JQ5SETnQusqmS3RwStF7vwQ3L02imIzl++sewmt23VGygix04pEH+FCj1yWb+z4GDzKiljr1W7Wfvrx0YwgA==", "path": "system.xml.xpath/4.3.0", "hashPath": "system.xml.xpath.4.3.0.nupkg.sha512"}, "System.Xml.XPath.XmlDocument/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-A/uxsWi/Ifzkmd4ArTLISMbfFs6XpRPsXZonrIqyTY70xi8t+mDtvSM5Os0RqyRDobjMBwIDHDL4NOIbkDwf7A==", "path": "system.xml.xpath.xmldocument/4.3.0", "hashPath": "system.xml.xpath.xmldocument.4.3.0.nupkg.sha512"}, "TimeZoneConverter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-U7Oilf3Ya6Rmu6gOaBfWyT3q0kwy2av6a5PfTn05CF54C+7DvuLsE3ljASvYmCpsSQeJvpnqU5Uzag6+ysWUeA==", "path": "timezoneconverter/5.0.0", "hashPath": "timezoneconverter.5.0.0.nupkg.sha512"}, "Volo.Abp.Core/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6h9Fnb7hpgsFelv2sTyRsz+Y5zGducKa2fFE+Ma58a0CtTx2wo2xzUurJ9c7A1hPGEaxO6hfRxi0PRnmK0Mgg==", "path": "volo.abp.core/4.4.0", "hashPath": "volo.abp.core.4.4.0.nupkg.sha512"}, "WebApiClient.JIT/1.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-uTAXUdt3iUhURhB6Wlk6ccEEYHXmysYSz/VN/KO++VsJ9czTaeMEGOxjfvp10OnH+IMWbDIs9hvDdfdEXCnSkQ==", "path": "webapiclient.jit/1.1.4", "hashPath": "webapiclient.jit.1.1.4.nupkg.sha512"}, "SEFA.PPM.IServices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SEFA.PPM.Model/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SEFA.PPM.Repository/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "SEFA.PPM.Services/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "LKK.Lib.Core/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Common/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.ESB/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.EventBus/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Extensions/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.InfluxDB/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.IServices/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Model/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Repository/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Serilog.Es/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.DFM.IServices/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.DFM.Model/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.DFM.Repository/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.DFM.Services/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.LicenseTool.Util/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Base.Services/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "SEFA.Resource/0.0.0.0": {"type": "reference", "serviceable": false, "sha512": ""}}}