{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\form-dialog.vue", "mtime": 1749177894363}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZ2V0QmF0Y2hEY3NEZXRhaWwsIHNhdmVCYXRjaERjc0Zvcm0gfSBmcm9tICJAL2FwaS9wbGFuTWFuYWdlbWVudC9iYXRjaERjcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7fSwKCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRpYWxvZ0Zvcm06IHt9LAogICAgICBkaWFsb2dWaXNpYmxlOiBmYWxzZSwKICAgICAgZm9ybUxvYWRpbmc6IGZhbHNlLAogICAgICBsaW5lT3B0aW9uczogW10sCiAgICAgIHRhcmdldExpbmVPcHRpb25zOiBbXSwKICAgICAgY3VycmVudFJvdzoge30sCiAgICAgIG1hdEluZm86IHt9CiAgICB9OwogIH0sCgogIG1vdW50ZWQoKSB7fSwKCiAgbWV0aG9kczogewogICAgc3VibWl0KCkgewogICAgICBzYXZlQmF0Y2hEY3NGb3JtKHRoaXMuZGlhbG9nRm9ybSkudGhlbihyZXMgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXMubXNnKTsKICAgICAgICB0aGlzLiRlbWl0KCdzYXZlRm9ybScpOwogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCgogICAgc2hvdyhkYXRhKSB7CiAgICAgIHRoaXMuZGlhbG9nRm9ybSA9IHt9OwogICAgICB0aGlzLmN1cnJlbnRSb3cgPSBkYXRhOwogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgICB0aGlzLiRuZXh0VGljayhfID0+IHsKICAgICAgICBpZiAoZGF0YS5JRCkgewogICAgICAgICAgdGhpcy5nZXREaWFsb2dEZXRhaWwoZGF0YS5JRCk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCgogICAgZ2V0RGlhbG9nRGV0YWlsKGlkKSB7CiAgICAgIGdldEJhdGNoRGNzRGV0YWlsKGlkKS50aGVuKHJlcyA9PiB7CiAgICAgICAgdGhpcy5kaWFsb2dGb3JtID0gcmVzLnJlc3BvbnNlOwogICAgICB9KTsKICAgIH0KCiAgfQp9Ow=="}, {"version": 3, "mappings": "AAiIA,SACAA,iBADA,EAEAC,gBAFA,QAGA,+BAHA;AAKA;EACAC,cADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,eAJA;MAKAC,qBALA;MAMAC,cANA;MAOAC;IAPA;EASA,CAdA;;EAeAC,WACA,CAhBA;;EAiBAC;IACAC;MACAZ;QACA;QACA;QACA;MACA,CAJA;IAKA,CAPA;;IAQAa;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;IAKA,CAjBA;;IAkBAC;MACAf;QACA;MACA,CAFA;IAGA;;EAtBA;AAjBA", "names": ["getBatchDcsDetail", "saveBatchDcsForm", "components", "data", "dialogForm", "dialogVisible", "formLoading", "lineOptions", "targetLineOptions", "currentRow", "matInfo", "mounted", "methods", "submit", "show", "getDialogDetail"], "sourceRoot": "src/views/planManagement/batchDcs", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"700px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n       \n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"Id\">{{dialogForm.id}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"工单ID\">{{dialogForm.productionOrderId}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"工单号\" prop=\"poNo\">\n              <el-input v-model=\"dialogForm.poNo\" placeholder=\"请输入工单号\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"批次ID\">{{dialogForm.batchtId}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"批次号\" prop=\"batchtNo\">\n              <el-input v-model=\"dialogForm.batchtNo\" placeholder=\"请输入批次号\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"物料ID\">{{dialogForm.materialId}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\" prop=\"materialCode\">\n              <el-input v-model=\"dialogForm.materialCode\" placeholder=\"请输入物料代码\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"物料名称\">{{dialogForm.materialName}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"物料版本ID\">{{dialogForm.materialVer}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"单位\" prop=\"unit\">\n              <el-input v-model=\"dialogForm.unit\" placeholder=\"请输入单位\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"单位ID\">{{dialogForm.unitId}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"标准需求数量\" prop=\"standardQuantity\">\n              <el-input v-model=\"dialogForm.standardQuantity\" placeholder=\"请输入标准需求数量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划数量\" prop=\"planQuantity\">\n              <el-input v-model=\"dialogForm.planQuantity\" placeholder=\"请输入计划数量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"状态\" prop=\"status\">\n              <el-radio-group v-model=\"dialogForm.status\">\n                <el-radio v-for=\"item in statusOptions\" :key=\"item.dictValue\" :label=\"item.dictValue\">{{item.dictLabel}}</el-radio>\n              </el-radio-group>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"24\">\n            <el-form-item label=\"下发数据\" prop=\"sendData\">\n              <el-input type=\"textarea\" v-model=\"dialogForm.sendData\" placeholder=\"请输入下发数据\"/>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"24\">\n            <el-form-item label=\"返回数据\" prop=\"responseData\">\n              <el-input type=\"textarea\" v-model=\"dialogForm.responseData\" placeholder=\"请输入返回数据\"/>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"dialogForm.remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"创建时间\">{{dialogForm.createdate}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"创建者\">{{dialogForm.createuserid}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"更新时间\">{{dialogForm.modifydate}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"更新者\">{{dialogForm.modifyuserid}}</el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\" v-if=\"opertype == 2\">\n            <el-form-item label=\"更新戳\">{{dialogForm.updatetimestamp}}</el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\n          @click=\"submit()\">确定\n        </el-button>\n      </div>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getBatchDcsDetail,\n    saveBatchDcsForm\n  } from \"@/api/planManagement/batchDcs\";\n\n  export default {\n    components:{\n      \n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        lineOptions: [],\n        targetLineOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    mounted() {\n    },\n    methods: {\n      submit() {\n        saveBatchDcsForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getBatchDcsDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n    }\n  }\n  </script>"]}]}