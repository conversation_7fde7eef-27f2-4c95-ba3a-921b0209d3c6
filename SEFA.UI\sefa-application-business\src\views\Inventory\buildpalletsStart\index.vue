<template>
    <div class="usemystyle buildpalletsStart">
        <div class="InventorySearchBox" style="margin-bottom: 0">
            <div class="searchbox">
                <el-button style="margin-left: 5px; width: 160px" size="small" icon="el-icon-back" @click="back()">{{ this.$t('MaterialPreparationBuild.IngredientSelection') }}</el-button>
                <el-button style="margin-left: 5px" size="small" icon="el-icon-refresh" @click="refresh()">{{ this.$t('Inventory.refresh') }}</el-button>
                <div class="searchtipbox">
                    {{ this.$t('MaterialPreparationBuild.tiptitle') }}
                </div>
                <el-button class="tablebtn" @click="GetAddPallet" v-if="this.SelectList.length == 0 && way == 'Batch'" size="small" style="margin-left: 5px; width: 120px" icon="el-icon-plus">
                    {{ this.$t('MaterialPreparationBuild.AddPallet') }}
                </el-button>
                <el-button class="tablebtn" @click="openKeyDown()" size="small" style="margin-left: 5px">
                    {{ keepKeyDown == false ? $t('MaterialPreparationBuild.OpenKeyDown') : $t('MaterialPreparationBuild.CloseKeyDown') }}
                </el-button>
                <div class="searchtipbox" style="background: #fff; color: red">计划备注：{{ detailobj.Remark }}</div>
                <div class="rightsearchbox" style="position: absolute; right: 10px; display: flex">
                    <el-button style="margin-left: 5px; width: 100px" size="small" icon="el-icon-back" :disabled="!MaterialList[MaterialNow - 1]" @click="ChangeMaterial(-1)">
                        {{ this.$t('MaterialPreparationBuild.Previous') }}
                    </el-button>
                    <el-button style="margin-left: 0px; width: 130px" size="small" icon="el-icon-right" :disabled="!MaterialList[MaterialNow + 1]" @click="ChangeMaterial(+1)">
                        {{ this.$t('MaterialPreparationBuild.NextMaterial') }}
                    </el-button>
                </div>
            </div>
            <div class="searchbox">
                <!-- <div class="searchboxTitle" v-if="way == 'Material'">{{ detailobj.MCode }} - {{ detailobj.MName }}</div> -->
                <div class="searchboxTitle">{{ detailobj.MCode }} - {{ detailobj.MName }}</div>
                <div class="searchboxTitle">
                    {{ $t('MaterialPreparationBuild.PO') }}：{{ detailobj.ProductionOrderNo }} /{{ $t('MaterialPreparationBuild.FormulaNo') }}:{{ detailobj.FormulaNo }}/
                    {{ $t('MaterialPreparationBuild.Batch') }}：{{ detailobj.MBatchNumber }}/{{ detailobj.Sequencetotal }}
                    <!-- /{{ detailobj.PrepStatuscount }}  -->
                    <!-- -{{ detailobj.EquipmentName }} -->
                </div>
            </div>
            <div class="searchbox">
                <div class="searchboxColorTitle" v-if="way == 'Batch'" :style="{ background: detailobj.CompleteStates == 'OK' ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.FullBags') }}：{{ detailobj.FullPage }}
                </div>

                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Partial') }}：{{ detailobj.TagpS }}/{{ detailobj.ParitialPage }}{{ detailobj.isGUnit ? 'g' : detailobj.QuantityTotalUnit }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Min') }}：{{ Number(detailobj.MinPvalue).toFixed(3) }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Actual') }}：{{
                        way == 'Material'
                            ? Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0
                                ? detailobj.MQuantity
                                : Math.floor(detailobj.MQuantity / Number(detailobj.BagSize))
                            : detailobj.MQuantity
                    }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Target') }}：{{
                        way == 'Material'
                            ? Math.floor(detailobj.MQuantityTotal / Number(detailobj.BagSize)) == 0
                                ? detailobj.MQuantityTotal
                                : Math.floor(detailobj.MQuantityTotal % Number(detailobj.BagSize))
                            : detailobj.MQuantityTotal
                    }}
                </div>
                <div class="searchboxColorTitle" :style="{ background: (way == 'Material' ? clblFlag : detailobj.CompleteStates == 'OK') ? '#3DCD58' : '#FFA500' }">
                    {{ this.$t('MaterialPreparationBuild.Max') }}：{{ Number(detailobj.MaxPvalue).toFixed(3) }}
                </div>
            </div>
        </div>
        <div class="tableboxheightall">
            <div class="tablebox available-inventory-container" :style="{
                '--table-height': tableHeight + 'px',
                '--dynamic-table-height': tableHeight + 'px'
            }">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">
                            {{ $t('MaterialPreparationBuild.AvallableInventory') }}
                            <!-- 测试状态显示 - 测试完成后删除 -->
                            <span style="font-size: 12px; color: #666; margin-left: 10px;">
                                [{{ useViewportHeight ? '视口模式' : '行数模式' }} |
                                数据: {{ tableList.length }}行 |
                                高度: {{ tableHeight }}px]
                            </span>
                        </div>
                        <div style="position: absolute; right: 10px; display: flex; flex-wrap: wrap; gap: 5px; align-items: center; z-index: 10;">
                            <!-- 打印按钮 -->
                            <el-button v-if="way == 'Material'" class="tablebtn" size="small" style="width: 140px;" @click="PrintAvallable()">
                                {{ $t('Inventory.Print') }}{{ $t('MaterialPreparationBuild.AvallableInventory') }}
                            </el-button>

                            <!-- 模式切换按钮 - 始终显示 -->
                            <el-button
                                class="tablebtn"
                                size="small"
                                @click="toggleTableHeightMode()"
                                :title="useViewportHeight ? '切换到数据行数模式' : '切换到视口高度模式'"
                                :type="useViewportHeight ? 'primary' : 'default'"
                                style="min-width: 80px; background: #fff; border: 1px solid #ddd;">
                                {{ useViewportHeight ? '视口模式' : '行数模式' }}
                            </el-button>

                            <!-- 测试按钮组 - 测试完成后删除 -->
                            <el-button size="mini" type="success" @click="addTestData()" title="添加测试数据" style="background: #67c23a; color: white; border: none;">+数据</el-button>
                            <el-button size="mini" type="warning" @click="removeTestData()" title="删除测试数据" style="background: #e6a23c; color: white; border: none;">-数据</el-button>
                            <el-button size="mini" type="info" @click="showTestInfo()" title="显示测试信息" style="background: #909399; color: white; border: none;">信息</el-button>
                            <el-button size="mini" type="danger" @click="forceSetTableHeight(300)" title="强制设置300px" style="background: #f56c6c; color: white; border: none;">300px</el-button>
                            <el-button size="mini" type="danger" @click="forceSetTableHeight(500)" title="强制设置500px" style="background: #f56c6c; color: white; border: none;">500px</el-button>
                        </div>
                    </div>
                </div>

                <!-- 测试控制面板 - 测试完成后删除 -->
                <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px; padding: 10px; margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                        <span style="font-weight: bold; color: #0369a1;">🧪 表格高度测试:</span>

                        <el-button
                            size="small"
                            @click="toggleTableHeightMode()"
                            :type="useViewportHeight ? 'primary' : 'default'">
                            {{ useViewportHeight ? '🖥️ 视口模式' : '📏 行数模式' }}
                        </el-button>

                        <el-button size="small" type="success" @click="addTestData()">➕ 添加数据</el-button>
                        <el-button size="small" type="warning" @click="removeTestData()">➖ 删除数据</el-button>
                        <el-button size="small" type="info" @click="showTestInfo()">ℹ️ 详细信息</el-button>
                        <el-button size="small" type="danger" @click="ultimateForceHeight(200)">� 终极200px</el-button>
                        <el-button size="small" type="danger" @click="ultimateForceHeight(400)">� 终极400px</el-button>

                        <span style="color: #64748b; font-size: 12px;">
                            当前: {{ useViewportHeight ? '视口模式' : '行数模式' }} |
                            数据: {{ tableList.length }}行 |
                            高度: {{ tableHeight }}px
                        </span>
                    </div>
                </div>

                <el-table
                    :data="tableList"
                    ref="TopTabel"
                    @row-click="GetCurrentRow"
                    highlight-current-row
                    :class="['dynamic-table', `height-${Math.floor(tableHeight)}`]"
                    :style="{
                        width: '100%',
                        height: tableHeight + 'px !important',
                        maxHeight: tableHeight + 'px !important',
                        minHeight: tableHeight + 'px !important'
                    }"
                    :height="tableHeight"
                    :max-height="tableHeight"
                    :key="tableHeightKey">
                    <el-table-column
                        v-for="(item, index) in header"
                        :fixed="item.fixed ? item.fixed : false"
                        :key="index"
                        :align="item.align"
                        :prop="item.prop ? item.prop : item.value"
                        :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                        :width="item.width"
                    >
                        <template slot-scope="scope">
                            <span v-if="scope.column.property == 'BatchStatus'">
                                <div :class="'statusbox batchstatus' + scope.row.LStatus">
                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'SSCCStatus'">
                                <div :class="'statusbox status' + scope.row.SbStatus">
                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>

                            <span v-else-if="scope.column.property == 'ExpirationDate'">
                                <div class="statusbox" :style="{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red' }">
                                    {{ scope.row.ExpirationDate }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <!-- <div class="tablebox" style="height: 32%" v-if="way == 'Batch'">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">{{ $t('MaterialPreparationBuild.POInventory') }}</div>
                    </div>
                </div>
                <el-table :data="tableListBatchPO" ref="TopBatchTabel" @row-click="GetCurrentRow2" highlight-current-row style="width: 100%" height="200">
                    <el-table-column
                        v-for="(item, index) in headerBatchPO"
                        :fixed="item.fixed ? item.fixed : false"
                        :key="index"
                        :align="item.align"
                        :prop="item.prop ? item.prop : item.value"
                        :label="$t(`$vuetify.dataTable.${tableId}.${item.value}`)"
                        :width="item.width"
                    >
                        <template slot-scope="scope">
                            <span v-if="scope.column.property == 'SSCC/Batch'">{{ scope.row.LBatch }}/{{ scope.row.SbSscc }}</span>
                            <span v-else-if="scope.column.property == 'Material'">
                                <div>{{ scope.row.MaterialCode }}</div>
                                <div style="color: #808080">{{ scope.row.MaterialName }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'SbStatus'">
                                <div :class="'statusbox status' + scope.row.SbStatus">
                                    {{ scope.row.SbStatus == 1 ? 'B' : scope.row.SbStatus == 2 ? 'Q' : scope.row.SbStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'LStatus'">
                                <div :class="'statusbox batchstatus' + scope.row.LStatus">
                                    {{ scope.row.LStatus == 1 ? 'B' : scope.row.LStatus == 2 ? 'U' : scope.row.LStatus == 3 ? 'U' : '' }}
                                </div>
                            </span>
                            <span v-else-if="scope.column.property == 'ExpirationDate'">
                                <div class="statusbox" :style="{ background: !isDateInThePast(scope.row.ExpirationDate) ? '#3dcd58' : 'red', width: '200px' }">{{ scope.row.ExpirationDate }}</div>
                            </span>
                            <span v-else-if="scope.column.property == 'Quantity'">{{ scope.row.InQuantity }}{{ scope.row.MaterialUnit1 }}</span>
                            <span v-else>{{ scope.row[item.prop] }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div> -->
            <div class="tablebox" style="height: 21%">
                <div class="InventorySearchBox">
                    <div class="searchbox">
                        <div class="searchboxTitle" style="font-size: 16px">{{ $t('MaterialPreparationBuild.MaterialTransfer') }}</div>
                    </div>
                </div>
                <el-tabs v-model="activeName" type="border-card">
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.FullBag')" name="FullBag">
                        <FullBag ref="FullBag" @getRefresh="refresh()" @getRowSSCC="GetSSCC" @getRowBySscc="getRowBySscc"></FullBag>
                    </el-tab-pane>
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.PartialBag')" name="PartialBag">
                        <PartialBag ref="PartialBag" @getRefresh="refresh()" @getRowBySscc="getRowBySscc"></PartialBag>
                    </el-tab-pane>
                    <el-tab-pane :label="this.$t('MaterialPreparationBuild.FullAmount')" name="FullAmount">
                        <FullAmount ref="FullAmount" @getRefresh="refresh()" @getRowSSCC="GetSSCC" @getRowBySscc="getRowBySscc"></FullAmount>
                    </el-tab-pane>
                </el-tabs>
            </div>
            <div class="tablebox" style="height: 600px" v-if="this.SelectList.length != 0 && way == 'Batch'">
                <BatchPallets ref="BatchPallets"></BatchPallets>
            </div>
            <div class="tablebox" style="height: 600px" v-if="way == 'Material'">
                <POInventory ref="POInventory"></POInventory>
            </div>
        </div>
        <el-dialog :title="$t('Inventory.Print')" id="Printdialog" :visible.sync="PrintModel" width="500px">
            <div class="dialogdetailbox" style="margin: 10px 0">
                <div class="dialogdetailsinglelabel">{{ $t('Inventory.selectprinter') }}</div>
                <div class="dialogdetailsinglevalue" style="width: auto">
                    <el-select disabled clearable v-model="PrintId" filterable>
                        <el-option v-for="item in printeroption" :key="item.ItemValue" :label="item.ItemName" :value="item.ItemValue"></el-option>
                    </el-select>
                </div>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button class="tablebtn" icon="el-icon-orange" @click="getPrint()">
                    {{ $t('Inventory.Print') }}
                </el-button>
                <el-button @click="PrintModel = false" icon="el-icon-circle-close">{{ $t('GLOBAL._QX') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import '@/views/Inventory/mystyle.scss';
import { Message, MessageBox } from 'element-ui';
import { AvallableInventoryColumn, POInventoryPalletsColumn } from '@/columns/factoryPlant/tableHeaders';
import {
    GetPageListNewMaterialPreDown,
    GetPageListByMaterial,
    GetPageListByBatchIDSByID,
    GetPageListByMaterialII,
    GetPageListByBatchIDSII,
    GetPageListMaterialPreTop,
    GetPageListNewMaterialPreTop,
    GetConSelectList,
    FirstAddPallet,
    GetPageListByBatchIDS,
    MygetSSCC
} from '@/api/Inventory/MaterialPreparation.js';
import { GetPrinit2, GetPrinit3, GetPrinit8, PrintPreparaLabelKY } from '@/api/Inventory/common.js';
import { Empty } from 'ant-design-vue';

export default {
    components: {
        PartialBag: () => import('./components/PartialBag'),
        FullAmount: () => import('./components/FullAmount'),
        FullBag: () => import('./components/FullBag'),
        POInventory: () => import('./components/POInventory'),
        BatchPallets: () => import('./components/BatchPallets')
    },
    data() {
        return {
            PrintId: '',
            printeroption: [],
            PrintModel: false,
            isExpirationDate: false,
            tableId: 'INV_CLZB',
            activeName: 'PartialBag',
            OnlyFullAmount: false,
            Hidecompleted: false,
            detailobj: {},
            room: '',
            tableList: [],
            tableListBatchPO: [],
            SelectList: [],
            headerBatchPO: POInventoryPalletsColumn,
            header: AvallableInventoryColumn,
            way: '',
            listId: '',
            nowChooseRow: {},
            MaterialList: [],
            MaterialNow: null,
            UseType: '',
            clblFlag: false,
            keepKeyDown: false,
            minTableHeight: 150, // 最小表格高度
            maxTableHeight: 400, // 最大表格高度
            rowHeight: 40, // 每行的高度
            windowHeight: window.innerHeight, // 窗口高度
            useViewportHeight: false, // 是否使用视口高度模式
            tableHeight: 200, // 当前表格高度
            tableHeightKey: 0, // 强制重新渲染的key
            isResizing: false, // 防止resize死循环
            // 测试相关数据 - 测试完成后删除
            testDataCounter: 0,
            originalTableList: [] // 保存原始数据
        };
    },
    mounted() {
        console.log(this.$route)
        console.log(this.$route.path)
        let mykey = window.sessionStorage.getItem('MaterialPreparation');
        if (mykey == 'clbl') {
            this.way = 'Material';
        } else {
            this.way = 'Batch';
        }
        this.detailobj = JSON.parse(this.$route.query.query);
        console.log(this.detailobj, 123);
        this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);
        console.log(this.clblFlag);
        this.UseType = this.$route.query.UseType;
        this.listId = JSON.parse(this.$route.query.List);
        this.room = window.sessionStorage.getItem('room');
        this.getprintList();
        // this.way = this.$route.query.way;
        this.GetMaterialList();
        this.getSelectList();
        this.getTopData();
        if (this.way == 'Batch') {
            this.getPOTabelData();
            this.activeName = 'FullBag';
        } else {
            this.activeName = 'PartialBag';
        }

        // 添加窗口大小变化监听器
        this.isResizing = false; // 防止死循环标志
        this.handleResize = () => {
            if (this.isResizing) {
                console.log('跳过resize事件，防止死循环');
                return;
            }
            this.windowHeight = window.innerHeight;
            console.log('窗口大小变化:', this.windowHeight);
            if (this.useViewportHeight) {
                this.updateTableHeight();
            }
        };
        window.addEventListener('resize', this.handleResize);

        // 初始化日志
        console.log('=== 表格自适应高度初始化 ===');
        console.log('初始模式:', this.useViewportHeight ? '视口模式' : '行数模式');
        console.log('初始窗口高度:', this.windowHeight);
        console.log('way变量值:', this.way);
        console.log('tableList长度:', this.tableList ? this.tableList.length : 'undefined');

        // 初始化表格高度
        this.$nextTick(() => {
            this.updateTableHeight();
            console.log('=== 页面初始化完成 ===');
        });
    },
    beforeMount() {
        window.removeEventListener('keyup', this.getKeyDown);
    },
    beforeDestroy() {
        // 清理事件监听器
        window.removeEventListener('keyup', this.getKeyDown);
        if (this.handleResize) {
            window.removeEventListener('resize', this.handleResize);
        }
    },
    methods: {
        openKeyDown() {
            console.log(2);
            if (this.keepKeyDown == false) {
                window.addEventListener('keyup', this.getKeyDown);
                this.keepKeyDown = true;
            } else {
                window.removeEventListener('keyup', this.getKeyDown);
                this.keepKeyDown = false;
            }
        },
        getKeyDown(event) {
            if (this.keepKeyDown) {
                let code = event.keyCode;
                console.log(code);
                switch (code) {
                    case 37:
                        if (this.MaterialList[this.MaterialNow - 1]) {
                            this.ChangeMaterial(-1);
                        } else {
                            Message({
                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,
                                type: 'warning'
                            });
                        }
                        break;
                    case 39:
                        if (this.MaterialList[this.MaterialNow + 1]) {
                            this.ChangeMaterial(+1);
                        } else {
                            Message({
                                message: `${this.$t('MaterialPreparationBuild.NoMaterial')}`,
                                type: 'warning'
                            });
                        }
                        break;
                    case 32:
                        if (this.activeName == 'FullBag') {
                            console.log('FullBag');
                            if (this.$refs.FullBag.getbtnStatus()) {
                                this.$refs.FullBag.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        if (this.activeName == 'PartialBag') {
                            console.log('PartialBag');
                            if (this.$refs.PartialBag.getbtnStatus()) {
                                this.$refs.PartialBag.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        if (this.activeName == 'FullAmount') {
                            console.log('FullAmount');
                            if (this.$refs.FullAmount.getbtnStatus()) {
                                this.$refs.FullAmount.Transfer();
                            } else {
                                Message({
                                    message: `${this.$t('MaterialPreparationBuild.NoTransfer')}`,
                                    type: 'warning'
                                });
                            }
                        }
                        break;
                }
                return false;
            }
        },
        async getPrint() {
            let ids = this.tableList.map(item => {
                return item.ID;
            });

            // let params = {
            //     Ids: ids,
            //     equmentid: this.room,
            //     PrintId: this.PrintId
            // };

            let params = {
                printId: this.PrintId,
                EquipmentId: this.room,
                BagSiZe: this.detailobj.BagSize,
                MCode: this.detailobj.MCode,
                ids: ids
            };

            let res = await PrintPreparaLabelKY(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.PrintModel = false;
        },
        async getprintList() {
            let params = {
                equipmentId: this.room
            };
            let res2 = await GetPrinit8(params);
            res2.response.forEach(item => {
                item.value = item.ID;
                item.label = item.Code;
                item.ItemName = item.Code;
                item.ItemValue = item.ID;
            });
            this.printeroption = res2.response;
            if (this.$refs.POInventory) {
                this.$refs.POInventory.printeroption = this.printeroption;
            }
        },
        PrintAvallable() {
            this.PrintId = window.sessionStorage.getItem('PrintId');
            this.PrintModel = true;
        },
        async getPOTabelData() {
            let params = {
                BatchId: this.detailobj.BatchId,
                ProOrderid: this.detailobj.ProductionOrderId,
                MaterialId: this.detailobj.MaterialId,
                EquipmentId: this.room,
                pageIndex: 1,
                pageSize: 1000
            };
            let res = await GetPageListNewMaterialPreDown(params);
            this.tableListBatchPO = res.response.data;
        },
        GetSSCC(key) {
            let flag = this.tableList.some(item => {
                return item.SbSscc == key;
            });
            if (flag == true) {
                if (this.$refs.FullBag) {
                    this.$refs.FullBag.ssccFlag = true;
                }
                if (this.$refs.PartialBag) {
                    this.$refs.PartialBag.ssccFlag = true;
                }
                if (this.$refs.FullAmount) {
                    this.$refs.FullAmount.ssccFlag = true;
                }
            } else {
                if (this.$refs.FullBag) {
                    this.$refs.FullBag.ssccFlag = false;
                }
                if (this.$refs.PartialBag) {
                    this.$refs.PartialBag.ssccFlag = false;
                }
                if (this.$refs.FullAmount) {
                    this.$refs.FullAmount.ssccFlag = false;
                }
            }
        },
        EmptySscc() {
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.sscc = '';
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.sscc = '';
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.sscc = '';
            }
        },
        ChangeMaterial(num) {
            if (this.way == 'Batch' && !this.OnlyFullAmount) {
                this.activeName = 'FullBag';
            }
            if (this.way == 'Material') {
                this.activeName = 'PartialBag';
            }
            if (this.OnlyFullAmount) {
                this.activeName = 'FullAmount';
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.sscc = '';
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.sscc = '';
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.sscc = '';
            }
            let now = this.MaterialNow + num;
            this.detailobj = this.MaterialList[now];
            console.log(this.detailobj);
            this.MaterialNow = now;
            this.refresh();
        },
        async GetMaterialList() {
            let res;
            if (window.sessionStorage.getItem('MaterialPreparation') == 'clbl') {
                if (this.UseType == 'Batch') {
                    let data = {
                        ID: this.listId,
                        pageIndex: 1,
                        EquipmentId: this.room,
                        pageSize: 1000
                    };
                    // alert('aa');
                    res = await GetPageListByBatchIDS(data); // GetPageListByBatchIDS(data); //id每个都是一样的 但是MaterialId不一样
                } else {
                    let ProIds = window.sessionStorage.getItem('ProIds');
                    let data = {
                        EqumentId: this.room,
                        ProId: JSON.parse(ProIds),
                        MaterialId: this.listId,
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByMaterial(data); //MaterialId每个都是一样的 但是Id不一样
                }
            } else {
                if (this.UseType == 'Batch') {
                    let data = {
                        ID: this.listId,
                        EquipmentId: this.room,
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByBatchIDSII(data); //id每个都是一样的 但是MaterialId不一样
                } else {
                    let ProIds = window.sessionStorage.getItem('ProIds');
                    let data = {
                        MaterialId: this.listId,
                        EqumentId: this.room,
                        ProId: JSON.parse(ProIds),
                        pageIndex: 1,
                        pageSize: 1000
                    };
                    res = await GetPageListByMaterialII(data); //MaterialId每个都是一样的 但是Id不一样
                }
            }

            let response = res.response;
            this.MaterialList = response.data;
            this.MaterialList.forEach((item, index) => {
                if (item.OnlyId == this.detailobj.OnlyId) {
                    this.MaterialNow = index;
                }
            });
            this.detailobj = this.MaterialList[this.MaterialNow];
            this.detailobj.isGUnit = false;
            if (this.detailobj.ChangeUnit) {
                if (this.detailobj.ChangeUnit == 'g') {
                    // this.detailobj.TagpS = Number(this.detailobj.TagpS) * 1000;
                    // this.detailobj.ParitialPage = Number(this.detailobj.ParitialPage) * 1000;
                    // this.detailobj.MinPvalue = Number(this.detailobj.MinPvalue) * 1000;
                    // this.detailobj.MaxPvalue = Number(this.detailobj.MaxPvalue) * 1000;
                    // this.detailobj.MQuantity = Number(this.detailobj.MQuantity) * 1000;
                    // this.detailobj.MQuantityTotal = Number(this.detailobj.MQuantityTotal) * 1000;
                    this.detailobj.isGUnit = true;
                }
            }
            this.clblFlag = Number(this.detailobj.TagpS) >= Number(this.detailobj.MinPvalue) && Number(this.detailobj.TagpS) <= Number(this.detailobj.MaxPvalue);
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.getDetailobj();
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.getInQuantity();
            }
            if (this.$refs.FullBag) {
                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;
                this.$refs.FullBag.Bags = 0;
            }
            if (this.$refs.BatchPallets) {
                this.$refs.BatchPallets.getTabelData();
                this.$refs.BatchPallets.getBatchPalletsStatus();
            }
        },
        async getRowBySscc(val) {
            let params = {
                MCode: this.detailobj.MCode,
                SSCC: val
            };
            let res = await MygetSSCC(params);
            if (res.response.data == null) {
                Message({
                    message: `该追溯码不存在`,
                    type: 'error'
                });
            } else {
                let data = res.response.data[0];
                if (data.Remark == 'ky') {
                    this.$refs.TopTabel.tableData.forEach(item => {
                        if (item.ID == data.ID) {
                            this.$refs.TopTabel.setCurrentRow(item);
                            this.GetCurrentRow(item);
                        }
                    });
                } else {
                    this.$refs.TopBatchTabel.tableData.forEach(item => {
                        if (item.ID == data.ID) {
                            this.$refs.TopBatchTabel.setCurrentRow(item);
                            this.GetCurrentRow2(item);
                        }
                    });
                }
            }
        },
        GetCurrentRow(val) {
            console.log(val, 2);
            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);
            // this.$refs.TopTabel.setCurrentRow();
            // this.$refs.TopBatchTabel.setCurrentRow();
            if (this.$refs.TopBatchTabel) {
                this.$refs.TopBatchTabel.setCurrentRow();
            }
            this.OnlyFullAmount = false;
            this.nowChooseRow = val;
            if (this.$refs.BatchPallets) {
                this.$refs.BatchPallets.InQuantity = this.nowChooseRow.InQuantity;
            }
            if (this.$refs.PartialBag) {
                this.$refs.PartialBag.ssccFlag = true;
                this.$refs.PartialBag.InQuantity = this.nowChooseRow.InQuantity;
                this.$refs.PartialBag.SubId = this.nowChooseRow.SubId;
                this.$refs.PartialBag.sscc = this.nowChooseRow.SbSscc;
            }
            //判断整袋转移
            if (this.$refs.FullBag) {
                let InQuantity = this.nowChooseRow.InQuantity;
                if (this.detailobj.ChangeUnit) {
                    if (this.detailobj.ChangeUnit == 'g') {
                        InQuantity = InQuantity * 1000;
                    }
                }
                this.$refs.FullBag.BagWeight = this.detailobj.BagSize;
                let num = this.detailobj.FullPage.split('/')[1] - this.detailobj.FullPage.split('/')[0];
                let key = num * this.detailobj.BagSize;
                if (num == 0) {
                    //左右相等就禁止转移，并且包数为0
                    this.$refs.FullBag.ssccFlag = false;
                    this.$refs.FullBag.Bags = 0;
                } else {
                    //不相等就判断选中数量跟差值乘以单包数量
                    this.$refs.FullBag.ssccFlag = true;
                    if (InQuantity >= key) {
                        this.$refs.FullBag.Bags = Math.floor(num);
                    } else {
                        this.$refs.FullBag.Bags = Math.floor(InQuantity / this.detailobj.BagSize);
                    }
                }

                this.$refs.FullBag.InQuantity = InQuantity;
                this.$refs.FullBag.SubId = this.nowChooseRow.SubId;
                this.$refs.FullBag.sscc = this.nowChooseRow.SbSscc;
            }
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.ssccFlag = true;
                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;
                this.$refs.FullAmount.InQuantity = this.nowChooseRow.InQuantity;
                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;
                this.$refs.FullAmount.getInQuantity();
            }
        },
        GetCurrentRow2(val) {
            this.isExpirationDate = this.isDateInThePast(val.ExpirationDate);
            this.$refs.TopTabel.setCurrentRow();
            this.activeName = 'FullAmount';
            this.OnlyFullAmount = true;
            // this.$refs.TopBatchTabel.setCurrentRow();
            this.nowChooseRow = val;
            if (this.$refs.FullAmount) {
                this.$refs.FullAmount.ssccFlag = true;
                this.$refs.FullAmount.SubId = this.nowChooseRow.SubId;
                this.$refs.FullAmount.sscc = this.nowChooseRow.SbSscc;
                this.$refs.FullAmount.getInQuantity(true, this.nowChooseRow);
            }
        },
        async GetAddPallet() {
            let params = {
                TareWeight: this.detailobj.MQuantityTotal,
                UomID: this.detailobj.TUintid,
                ProBatchID: this.detailobj.BatchId,
                EquipMentID: this.room,
                MaterialId: this.detailobj.MaterialId,
                ProRequestID: this.detailobj.ProductionOrderId
            };
            let res = await FirstAddPallet(params);
            Message({
                message: res.msg,
                type: 'success'
            });
            this.refresh();
        },
        refresh() {
            this.getTopData();
            this.GetMaterialList();
            if (this.way == 'Batch') {
                this.getPOTabelData();
                this.getSelectList();
            } else {
                this.$refs.POInventory.getTabelData();
            }
        },
        async getSelectList() {
            let params = {
                proOrderID: this.detailobj.ProductionOrderId,
                batchID: this.detailobj.BatchId
            };
            let res = await GetConSelectList(params);
            console.log(res, 123123);
            this.SelectList = res.response;
            if (this.way == 'Batch') {
                if (this.SelectList.length != 0) {
                    window.sessionStorage.setItem('BatchPallets', res.response[0].ID);
                    if (this.$refs.BatchPallets) {
                        this.$refs.BatchPallets.BatchPalletsOption = res.response;
                        this.$refs.BatchPallets.BatchPallets = res.response[0].ID;
                    }
                }
            } else {
                this.$refs.POInventory.getTabelData();
            }
        },
        async getTopData() {
            let res;
            let params = {
                MaterialId: this.detailobj.MaterialId,
                EquipmentId: this.room,
                pageIndex: 1,
                pageSize: 1000
            };
            if (this.way == 'Batch') {
                res = await GetPageListMaterialPreTop(params);
            } else {
                res = await GetPageListNewMaterialPreTop(params);
            }
            this.tableList = res.response.data;
            // 数据加载完成后更新表格高度
            this.$nextTick(() => {
                this.updateTableHeight();
            });
        },
        back(val) {
            this.$router.go(-1);
        },
        isDateInThePast(dateString) {
            const givenDate = new Date(dateString);
            const now = new Date();
            return givenDate < now;
        },
        // 切换表格高度模式
        toggleTableHeightMode() {
            console.log('=== 切换表格高度模式 ===');
            console.log('切换前:', this.useViewportHeight ? '视口模式' : '行数模式');
            console.log('当前表格高度:', this.tableHeight);

            this.useViewportHeight = !this.useViewportHeight;

            console.log('切换后:', this.useViewportHeight ? '视口模式' : '行数模式');

            // 立即重新计算表格高度
            this.updateTableHeight();

            // 显示切换成功消息
            Message({
                message: `已切换到${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}`,
                type: 'success',
                duration: 2000
            });
        },
        // 更新表格高度
        updateTableHeight() {
            if (this.isResizing) {
                console.log('跳过updateTableHeight，正在resize中');
                return;
            }

            console.log('=== updateTableHeight 被调用 ===');
            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');
            console.log('tableList.length:', this.tableList ? this.tableList.length : 'undefined');

            let newHeight;

            if (!this.tableList || this.tableList.length === 0) {
                newHeight = this.minTableHeight;
                console.log('使用最小高度:', newHeight);
            } else if (this.useViewportHeight) {
                // 基于视口高度的响应式计算
                const availableHeight = this.windowHeight * 0.25;
                newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);
                console.log('视口模式计算:', {
                    windowHeight: this.windowHeight,
                    availableHeight,
                    newHeight
                });
            } else {
                // 基于数据行数计算高度
                const headerHeight = 40;
                const dataHeight = headerHeight + (this.tableList.length * this.rowHeight);
                newHeight = Math.min(Math.max(dataHeight, this.minTableHeight), this.maxTableHeight);
                console.log('行数模式计算:', {
                    headerHeight,
                    dataRows: this.tableList.length,
                    rowHeight: this.rowHeight,
                    dataHeight,
                    newHeight
                });
            }

            console.log('旧高度:', this.tableHeight, '新高度:', newHeight);

            // 只有高度真的改变时才更新
            if (Math.abs(this.tableHeight - newHeight) > 1) {
                this.tableHeight = newHeight;
                this.tableHeightKey++; // 强制重新渲染
                console.log('高度已更新到:', this.tableHeight, 'Key:', this.tableHeightKey);

                this.$nextTick(() => {
                    this.forceTableHeightDOM(newHeight);
                });
            } else {
                console.log('高度无变化，跳过更新');
            }
        },

        // 强制设置DOM高度
        forceTableHeightDOM(height) {
            console.log('=== 强制设置DOM高度 ===', height);

            if (this.$refs.TopTabel) {
                const tableComponent = this.$refs.TopTabel;
                const tableEl = tableComponent.$el;

                console.log('表格组件:', tableComponent);
                console.log('表格DOM元素:', tableEl);

                // 方法1: 直接设置表格元素样式
                tableEl.style.height = height + 'px';
                tableEl.style.maxHeight = height + 'px';
                tableEl.style.minHeight = height + 'px';

                // 方法2: 设置表格内部容器
                const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');
                if (bodyWrapper) {
                    bodyWrapper.style.maxHeight = (height - 40) + 'px';
                    console.log('设置body wrapper高度:', (height - 40) + 'px');
                }

                // 方法3: 调用Element UI的内部方法
                if (tableComponent.doLayout) {
                    tableComponent.doLayout();
                }

                // 验证设置结果
                setTimeout(() => {
                    console.log('DOM验证 - 表格高度:', tableEl.offsetHeight);
                    console.log('DOM验证 - 样式高度:', tableEl.style.height);
                    console.log('DOM验证 - 计算样式:', window.getComputedStyle(tableEl).height);

                    if (bodyWrapper) {
                        console.log('DOM验证 - body wrapper高度:', bodyWrapper.offsetHeight);
                        console.log('DOM验证 - body wrapper样式:', bodyWrapper.style.maxHeight);
                    }
                }, 100);

            } else {
                console.log('表格引用不存在');
            }
        },
        // 手动设置表格高度参数
        setTableHeightParams(minHeight, maxHeight, rowHeight) {
            this.minTableHeight = minHeight || 150;
            this.maxTableHeight = maxHeight || 400;
            this.rowHeight = rowHeight || 40;
        },
        // 测试方法 - 可以在浏览器控制台调用
        testTableHeight() {
            console.log('=== 表格高度测试 ===');
            console.log('当前模式:', this.useViewportHeight ? '视口模式' : '行数模式');
            console.log('表格数据行数:', this.tableList ? this.tableList.length : 0);
            console.log('窗口高度:', this.windowHeight);
            console.log('当前表格高度:', this.tableHeight);
            console.log('最小高度:', this.minTableHeight);
            console.log('最大高度:', this.maxTableHeight);
            console.log('行高:', this.rowHeight);
            console.log('表格Key:', this.tableHeightKey);

            // 检查表格DOM元素
            if (this.$refs.TopTabel) {
                const tableEl = this.$refs.TopTabel.$el;
                console.log('表格DOM高度:', tableEl.style.height);
                console.log('表格实际高度:', tableEl.offsetHeight);
                console.log('表格计算样式:', window.getComputedStyle(tableEl).height);
            }

            return this.tableHeight;
        },

        // 强制设置表格高度 - 测试用
        forceSetTableHeight(height) {
            console.log('=== 强制设置表格高度 ===', height);
            this.tableHeight = height;
            this.tableHeightKey++;

            this.$nextTick(() => {
                this.forceTableHeightDOM(height);
            });
        },

        // 终极DOM操作方法
        ultimateForceHeight(height) {
            console.log('=== 终极强制高度设置 ===', height);

            // 1. 更新Vue数据
            this.tableHeight = height;
            this.tableHeightKey++;

            // 2. 强制更新组件
            this.$forceUpdate();

            // 3. 等待DOM更新后操作
            this.$nextTick(() => {
                if (this.$refs.TopTabel) {
                    const tableComponent = this.$refs.TopTabel;
                    const tableEl = tableComponent.$el;

                    // 4. 设置所有可能的高度属性
                    tableEl.style.setProperty('height', height + 'px', 'important');
                    tableEl.style.setProperty('max-height', height + 'px', 'important');
                    tableEl.style.setProperty('min-height', height + 'px', 'important');

                    // 5. 设置CSS变量
                    tableEl.style.setProperty('--table-height', height + 'px');
                    tableEl.style.setProperty('--dynamic-table-height', height + 'px');

                    // 6. 设置父容器CSS变量
                    const container = tableEl.closest('.available-inventory-container');
                    if (container) {
                        container.style.setProperty('--table-height', height + 'px');
                        container.style.setProperty('--dynamic-table-height', height + 'px');
                    }

                    // 7. 设置内部元素
                    const bodyWrapper = tableEl.querySelector('.el-table__body-wrapper');
                    if (bodyWrapper) {
                        bodyWrapper.style.setProperty('max-height', (height - 40) + 'px', 'important');
                    }

                    // 8. 调用Element UI方法
                    if (tableComponent.doLayout) {
                        tableComponent.doLayout();
                    }

                    // 9. 触发resize事件（防止死循环）
                    setTimeout(() => {
                        this.isResizing = true;
                        window.dispatchEvent(new Event('resize'));
                        setTimeout(() => {
                            this.isResizing = false;
                        }, 100);

                        // 10. 最终验证
                        console.log('=== 最终验证结果 ===');
                        console.log('表格offsetHeight:', tableEl.offsetHeight);
                        console.log('表格style.height:', tableEl.style.height);
                        console.log('表格计算样式:', window.getComputedStyle(tableEl).height);

                        if (bodyWrapper) {
                            console.log('body wrapper offsetHeight:', bodyWrapper.offsetHeight);
                            console.log('body wrapper style:', bodyWrapper.style.maxHeight);
                        }
                    }, 200);
                }
            });
        },

        // === 以下为测试方法，测试完成后删除 ===

        // 生成测试数据
        generateTestData() {
            const testData = {
                ID: `TEST_${++this.testDataCounter}`,
                SbSscc: `SSCC${String(this.testDataCounter).padStart(4, '0')}`,
                LBatch: `BATCH${String(this.testDataCounter).padStart(3, '0')}`,
                MaterialCode: `MAT${String(this.testDataCounter % 5 + 1).padStart(3, '0')}`,
                MaterialName: `测试物料${this.testDataCounter}`,
                InQuantity: Math.floor(Math.random() * 1000) + 100,
                MaterialUnit1: 'kg',
                LStatus: Math.floor(Math.random() * 3) + 1,
                SbStatus: Math.floor(Math.random() * 3) + 1,
                ExpirationDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                Location: `A${String(Math.floor(Math.random() * 10) + 1).padStart(2, '0')}-${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`
            };
            return testData;
        },

        // 添加测试数据
        addTestData() {
            // 第一次添加时保存原始数据
            if (this.originalTableList.length === 0 && this.tableList.length > 0) {
                this.originalTableList = [...this.tableList];
            }

            const newData = this.generateTestData();
            this.tableList.push(newData);

            this.$nextTick(() => {
                this.updateTableHeight();
            });

            Message({
                message: `已添加测试数据，当前共 ${this.tableList.length} 行`,
                type: 'success',
                duration: 2000
            });
        },

        // 删除测试数据
        removeTestData() {
            if (this.tableList.length > 0) {
                // 优先删除测试数据
                const testIndex = this.tableList.findIndex(item => item.ID && item.ID.startsWith('TEST_'));
                if (testIndex !== -1) {
                    this.tableList.splice(testIndex, 1);
                } else {
                    this.tableList.pop();
                }

                this.$nextTick(() => {
                    this.updateTableHeight();
                });

                Message({
                    message: `已删除数据，当前共 ${this.tableList.length} 行`,
                    type: 'warning',
                    duration: 2000
                });
            } else {
                Message({
                    message: '没有数据可删除',
                    type: 'info',
                    duration: 2000
                });
            }
        },

        // 显示测试信息
        showTestInfo() {
            const info = `
=== 表格自适应高度测试信息 ===
当前模式: ${this.useViewportHeight ? '视口高度模式' : '数据行数模式'}
数据行数: ${this.tableList.length}
窗口高度: ${this.windowHeight}px
表格高度: ${this.tableHeight}px
最小高度: ${this.minTableHeight}px
最大高度: ${this.maxTableHeight}px
行高设置: ${this.rowHeight}px

测试说明:
1. 点击"视口模式/行数模式"按钮切换计算方式
2. 点击"+数据"按钮添加测试数据观察高度变化
3. 点击"-数据"按钮删除数据观察高度变化
4. 调整浏览器窗口大小测试响应式效果
5. 在视口模式下，表格高度为窗口高度的25%
6. 在行数模式下，表格高度根据数据行数计算
            `;

            console.log(info);

            this.$alert(info, '测试信息', {
                confirmButtonText: '确定',
                type: 'info'
            });
        },

        // 恢复原始数据 (可在控制台调用)
        restoreOriginalData() {
            if (this.originalTableList.length > 0) {
                this.tableList = [...this.originalTableList];
                this.originalTableList = [];
                this.testDataCounter = 0;

                this.$nextTick(() => {
                    this.updateTableHeight();
                });

                Message({
                    message: '已恢复原始数据',
                    type: 'success'
                });
            }
        }

        // === 测试方法结束 ===
    }
};
</script>
<style lang="scss" scoped>
.buildpalletsStart {
    .InventorySearchBox {
        margin-bottom: 0px;
    }
    .tablebox {
        margin-top: 10px;
    }
    .tableboxheightall {
        overflow-y: auto;
        max-height: 87%;
    }
    .searchtipbox {
        margin: 0 5px;
        background: #90ffa2;
        height: 30px;
        padding: 0 2vh;
        display: flex;
        margin-bottom: 0.5vh;
        align-items: center;
        justify-content: center;
        color: black;
        font-size: 16px;
    }

    .expandbox {
        background: #f5f5f5;
        padding: 10px;
    }
    .el-tabs--border-card {
        border: 0;
        box-shadow: none;
    }

    // 可用库存表格容器自适应样式
    .available-inventory-container {
        min-height: 200px;
        max-height: 450px;
        height: auto !important;
        transition: height 0.3s ease;

        // 动态表格样式 - 强制覆盖Element UI默认样式
        .dynamic-table {
            transition: height 0.3s ease !important;
            width: 100% !important;

            // 强制设置表格高度
            &.el-table {
                height: var(--table-height, 200px) !important;
                max-height: var(--table-height, 200px) !important;
                min-height: var(--table-height, 200px) !important;
            }

            // 确保表格内容区域也使用动态高度
            .el-table__body-wrapper {
                max-height: calc(var(--table-height, 200px) - 40px) !important;
                overflow-y: auto !important;

                // 自定义滚动条样式
                &::-webkit-scrollbar {
                    width: 6px;
                }

                &::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }

                &::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;

                    &:hover {
                        background: #a8a8a8;
                    }
                }
            }

            // 表头固定高度
            .el-table__header-wrapper {
                height: 40px !important;
                min-height: 40px !important;
                max-height: 40px !important;
            }

            // 固定列样式
            .el-table__fixed,
            .el-table__fixed-right {
                height: var(--table-height, 200px) !important;
            }
        }

        // 响应式设计
        @media (max-height: 768px) {
            max-height: 300px;
        }

        @media (max-height: 600px) {
            max-height: 250px;
        }
    }

    // 全局强制样式 - 确保表格高度生效
    .el-table {
        &.dynamic-table {
            height: var(--dynamic-table-height, 200px) !important;
            max-height: var(--dynamic-table-height, 200px) !important;

            .el-table__body-wrapper {
                max-height: calc(var(--dynamic-table-height, 200px) - 40px) !important;
            }
        }
    }

    /* 测试按钮样式 - 测试完成后删除 */
    .test-buttons {
        .el-button {
            margin-left: 3px !important;
            font-size: 11px;
            padding: 5px 8px;
        }
    }

    /* 确保按钮容器可见 */
    .searchbox {
        position: relative;

        > div:last-child {
            z-index: 100 !important;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 4px;
            padding: 5px;
        }
    }
}
</style>
