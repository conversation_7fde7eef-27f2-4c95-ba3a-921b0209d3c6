{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue", "mtime": 1749180158972}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAyIA,SACA;AACAA,iBAFA,EAGAC,UAHA,QAIA,mCAJA;AAKA;AACA;AACA;EACAC;IACAC;EADA,CADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIA;MACA;MACA;MACA;MACA;MACA;MACAC,6BAVA;MAWAC,aAXA;MAYAC,mDAZA;MAaAC,cACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,CAbA;MA0BAC,aA1BA;MA2BAC,cA3BA;MA4BAC,iBA5BA;MA6BAC;IA7BA;EA+BA,CApCA;;EAqCAC;IACA,oBADA,CAEA;EACA,CAxCA;;EAyCAC,WACA,CA1CA;;EA2CAC;IACA;MACA;IACA,CAHA;;IAIA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACArB;QACA;QACA;QACA;MACA,CAJA;IAKA,CAlBA;;IAmBAsB;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;MACA,CALA;IAMA,CA9BA;;IA+BAC;MACA;IACA,CAjCA;;IAkCAC;MACA;MACAzB;QACA;QACA;MACA,CAHA;IAIA,CAxCA;;IAyCA0B;MACA;MACA;IACA,CA5CA;;IA6CA;MACAC;MACAA;MACA;MACA;MACA;MACA,oBANA,CAOA;MACA;MACA;MACA;MACA;IACA;;EAzDA;AA3CA", "names": ["getSplitBatchInfo", "splitBatch", "components", "BomDetailForm", "data", "dialogForm", "dialogVisible", "formLoading", "standardPeriodTypeOptions", "tableName", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tableOption", "code", "width", "align", "tableData", "currentRow", "currentBomRow", "matInfo", "created", "mounted", "methods", "submit", "show", "initTableHead", "getDialogInfo", "showDialog", "console"], "sourceRoot": "src/views/planManagement/weekFormulation", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"$t('GLOBAL._PCGDCF')\" :visible.sync=\"dialogVisible\" width=\"1200px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"产线\" prop=\"LineCode\">\n              <div>\n                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"物料\" prop=\"MaterialCode\">\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输计划数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-input v-model=\"dialogForm.StartWorkday\" placeholder=\"请输入开始工作日\" disabled />\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"8\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-input v-model=\"dialogForm.FinishWorkday\" placeholder=\"请输入结束工作日\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"首批时长\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入首批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"中间批时长\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"末批时长\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入末批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"配方版本\" prop=\"BomVersion\">\n              <el-input v-model=\"dialogForm.BomVersion\" placeholder=\"请输入配方版本\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"每批数量\" prop=\"LotQuantity\">\n              <el-input v-model=\"dialogForm.LotQuantity\" placeholder=\"请输入每批数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"批次总数\" prop=\"LotCount\">\n              <el-input v-model=\"dialogForm.LotCount\" placeholder=\"请输入批次总数\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"对应关系\" prop=\"StandardLotType\">\n              <el-select v-model=\"dialogForm.StandardLotType\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in standardPeriodTypeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"从第几批返工\" prop=\"ReworkLot\">\n              <el-input v-model=\"dialogForm.ReworkLot\" placeholder=\"请输入从第几批返工\" />\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"24\">\n            <el-form-item label=\"\" > -->\n              <el-table class=\"mt-3\"\n                :height=\"200\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n                <el-table-column prop=\"operation\" width=\"100\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"combination\">\n                      <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._TD') }}</el-button>\n                    </div>                        \n                  </template>\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                :default-sort=\"{prop: 'date', order: 'descending'}\"\n                                :key=\"item.ID\"\n                                :prop=\"item.field\"\n                                :label=\"item.label\"\n                                :width=\"item.width\"\n                                :align=\"item.alignType\"\n                                sortable\n                                show-overflow-tooltip\n                >\n                  <template slot-scope=\"scope\">\n                    {{ scope.row[item.field] }}\n                  </template>\n                </el-table-column>\n              </el-table>\n            <!-- </el-form-item>\n          </el-col> -->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">拆分</el-button>\n      </div>      \n      <BomDetailForm @saveForm=\"setBomRowData\" ref=\"formDialog\" />\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    //getLineList,\n    getSplitBatchInfo,\n    splitBatch\n  } from \"@/api/planManagement/weekSchedule\";\n  import BomDetailForm from './bomDetailForm'\n  import {getTableHead} from \"@/util/dataDictionary.js\";\n  export default {\n    components:{\n      BomDetailForm\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        // factoryOptions: [],\n        // workshopOptions: [],\n        // lineOptions: [],        \n        // categoryOptions: [],\n        // shiftOptions: [],\n        // typeOptions: [],\n        standardPeriodTypeOptions: [],\n        tableName : [],\n        hansObjDrawer: this.$t('WeekFormulation.bomDetail'),\n        tableOption: [\n          {code: 'SegmentName', width: 100, align: 'center'},  \n          {code: 'Sort', width: 80, align: 'center'},\n          {code: 'MaterialCode', width: 150, align: 'left'},\n          {code: 'MaterialName', width: 180, align: 'left'},\n          {code: 'MaterialType', width: 100, align: 'left'},\n          {code: 'InsteadMaterialCode', width: 150, align: 'left'},\n          {code: 'InsteadMaterialName', width: 180, align: 'left'},\n          {code: 'Unit', width: 100, align: 'center'},\n          {code: 'StandardQuantity', width: 100, align: 'left'},\n          {code: 'PlanQuantity', width: 100, align: 'left'},\n          {code: 'Remark', width: 180, align: 'left'},\n        ],\n        tableData : [],\n        currentRow: {},\n        currentBomRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      //this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType');\n      },\n      // async getLineList() {\n      //   const { response } = await getLineList({\n      //    //areaCode: 'PackingArea'\n      //    areaCode: 'Formulation'\n      //   })\n      //   console.log(response)\n      //   this.lineOptions = response\n      // },\n      submit() {\n        splitBatch(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.initTableHead()\n        this.$nextTick(_ => {\n          if(data.ID){\n            //console.log(\"show\")\n            this.getDialogInfo(data)\n          }\n        })\n      },\n      initTableHead() {\n        this.tableName = getTableHead(this.hansObjDrawer, this.tableOption)\n      },\n      getDialogInfo(data){\n        //console.log(\"getDialogInfo\")\n        getSplitBatchInfo(data).then(res => {\n          this.dialogForm = res.response\n          this.tableData = res.response.wsBomModels\n        })\n      },    \n      showDialog(row) {\n        this.currentBomRow = row\n        this.$refs.formDialog.show(row)\n      },\n      async setBomRowData(val) {\n        console.log(\"setBomRowData\")\n        console.log(val)\n        this.currentBomRow.MaterialId = val.MaterialId\n        this.currentBomRow.MaterialCode = val.MaterialCode\n        this.currentBomRow.MaterialName = val.MaterialCode\n        this.$forceUpdate()\n        // const { response } = await getWeekScheduleBomList({\n        //   WeekScheduleId: this.currentRow.ID,\n        //   ...this.searchForm\n        // })\n        // this.tableData = response\n      },\n    }\n  }\n  </script>"]}]}