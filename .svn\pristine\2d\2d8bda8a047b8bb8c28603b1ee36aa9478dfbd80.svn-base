﻿
using SEFA.Base.IRepository.Base;
using SEFA.Base.Model;
using SEFA.Base.Services.BASE;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SqlSugar;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.Services
{
    public class BatchDcsServices : BaseServices<BatchDcsEntity>, IBatchDcsServices
    {
        private readonly IBaseRepository<BatchDcsEntity> _dal;
        public BatchDcsServices(IBaseRepository<BatchDcsEntity> dal)
        {
            this._dal = dal;
            base.BaseDal = dal;
        }

        public async Task<List<BatchDcsEntity>> GetList(BatchDcsRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BatchDcsEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.LineCode), it => it.LineCode == reqModel.LineCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.LineName), it => it.LineName == reqModel.LineName)
                .AndIF(!string.IsNullOrEmpty(reqModel.PoNo), it => it.PoNo == reqModel.PoNo)
                .AndIF(!string.IsNullOrEmpty(reqModel.BatchtNo), it => it.BatchtNo == reqModel.BatchtNo)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), it => it.MaterialCode == reqModel.MaterialCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialName), it => it.MaterialName == reqModel.MaterialName)
                .ToExpression();
            var data = await _dal.FindList(whereExpression);
            return data;
        }

        public async Task<PageModel<BatchDcsEntity>> GetPageList(BatchDcsRequestModel reqModel)
        {
            var whereExpression = Expressionable.Create<BatchDcsEntity>()
                .AndIF(!string.IsNullOrEmpty(reqModel.LineCode), it => it.LineCode == reqModel.LineCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.LineName), it => it.LineName == reqModel.LineName)
                .AndIF(!string.IsNullOrEmpty(reqModel.PoNo), it => it.PoNo == reqModel.PoNo)
                .AndIF(!string.IsNullOrEmpty(reqModel.BatchtNo), it => it.BatchtNo == reqModel.BatchtNo)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialCode), it => it.MaterialCode == reqModel.MaterialCode)
                .AndIF(!string.IsNullOrEmpty(reqModel.MaterialName), it => it.MaterialName == reqModel.MaterialName)
                             .ToExpression();
            var data = await _dal.QueryPage(whereExpression, reqModel.pageIndex, reqModel.pageSize);

            return data;
        }

        public async Task<bool> SaveForm(BatchDcsEntity entity)
        {
            if (string.IsNullOrEmpty(entity.ID))
            {
                return await this.Add(entity) > 0;
            }
            else
            {
                return await this.Update(entity);
            }
        }
    }
}