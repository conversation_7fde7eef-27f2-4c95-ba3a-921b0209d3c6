﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base.Model;
using SEFA.PPM.Controllers;
using SEFA.PPM.IServices;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;

namespace SEFA.PPMApi.Controllers
{
    [Route("ppm/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class BatchDcsController : BaseApiController
    {
        /// <summary>
        /// BatchDcs
        /// </summary>
        private readonly IBatchDcsServices _batchDcsServices;

        public BatchDcsController(IBatchDcsServices BatchDcsServices)
        {
            _batchDcsServices = BatchDcsServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BatchDcsEntity>>> GetList([FromBody] BatchDcsRequestModel reqModel)
        {
            var data = await _batchDcsServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BatchDcsEntity>>> GetPageList([FromBody] BatchDcsRequestModel reqModel)
        {
            var data = await _batchDcsServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BatchDcsEntity>> GetEntity(string id)
        {
            var data = await _batchDcsServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BatchDcsEntity request)
        {
            var data = new MessageModel<string>();
            if (string.IsNullOrEmpty(request.ID))
            {
                data.success = await _batchDcsServices.Add(request) > 0;
                if (data.success)
                {
                    return Success("", "添加成功");
                }
                else
                {
                    return Failed("添加失败");
                }
            }
            else
            {
                data.success = await _batchDcsServices.Update(request);
                if (data.success)
                {
                    return Success("", "更新成功");
                }
                else
                {
                    return Failed("更新失败");
                }
            }
        }

        [HttpPost]
        public async Task<MessageModel<string>> Insert([FromBody] BatchDcsEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _batchDcsServices.Add(request) > 0;
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }
        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _batchDcsServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BatchDcsRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}