﻿using SEFA.Base.Model.BASE;
using SqlSugar;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///批次DCS下发
    ///</summary>

    [SugarTable("PPM_B_BATCH_DCS")]
    public class BatchDcsEntity : EntityBase
    {
        public BatchDcsEntity()
        {
        }
        /// <summary>
        /// Desc:LineID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_ID")]
        public string LineId { get; set; }
        /// <summary>
        /// Desc:LineCode
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_CODE")]
        public string LineCode { get; set; }
        /// <summary>
        /// Desc:LineName
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "LINE_NAME")]
        public string LineName { get; set; }
        /// <summary>
        /// Desc:工单ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }
        /// <summary>
        /// Desc:工单号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PO_NO")]
        public string PoNo { get; set; }
        /// <summary>
        /// Desc:批次ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BATCHT_ID")]
        public string BatchtId { get; set; }
        /// <summary>
        /// Desc:批次号
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "BATCHT_NO")]
        public string BatchtNo { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VER")]
        public string MaterialVer { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_ID")]
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:标准需求数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STANDARD_QUANTITY")]
        public decimal StandardQuantity { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_QUANTITY")]
        public decimal PlanQuantity { get; set; }
        /// <summary>
        /// Desc:状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "STATUS")]
        public string Status { get; set; }
        /// <summary>
        /// Desc:下发数据
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SEND_DATA")]
        public string SendData { get; set; }
        /// <summary>
        /// Desc:返回数据
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "RESPONSE_DATA")]
        public string ResponseData { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

    }
}