{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetail.vue?vue&type=style&index=0&id=62b64346&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetail.vue", "mtime": 1749177894577}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZHJhd2VyIHsNCiAgOmRlZXAoLmVsLWRyYXdlcl9fYm9keSkgew0KICAgIHBhZGRpbmctdG9wOiAxMHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICNGRkZGRkY7DQogICAgb3ZlcmZsb3cteTogaGlkZGVuDQogIH0NCg0KICA6ZGVlcCguZWwtZm9ybS0taW5saW5lKSB7DQogICAgaGVpZ2h0OiAzMnB4Ow0KICB9DQoNCiAgLnRpdGxlLWJveCB7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIGNvbG9yOiAjOTA5Mzk5Ow0KICB9DQoNCiAgLnBkNSB7DQogICAgcGFkZGluZzogNXB4Ow0KICB9DQoNCiAgLnRhYmxlLWJveCB7DQogICAgcGFkZGluZzogMCAxMHB4Ow0KDQogICAgOmRlZXAoLmVsLWJ1dHRvbi5pcy1kaXNhYmxlZCkgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsNCiAgICAgIGJvcmRlcjogMCAhaW1wb3J0YW50Ow0KICAgIH0NCg0KICAgIGkgew0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgICBmb250LXNpemU6IDE1cHggIWltcG9ydGFudDsNCiAgICAgIGNvbG9yOiAjNjdjMjNhOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["bomDetail.vue"], "names": [], "mappings": ";AAsHA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bomDetail.vue", "sourceRoot": "src/views/planManagement/weekPacking", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\"\r\n      :append-to-body=\"false\" size=\"80%\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.LineCode}-${currentRow.Factory}：${currentRow.MaterialCode}-${currentRow.MaterialName}：${currentRow.OrderNo}` }}</span>\r\n      </div>\r\n      <div class=\"InventorySearchBox\">\r\n        <div class=\"searchbox pd5\">\r\n          <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n            <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n              <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button icon=\"el-icon-search\" @click=\"getTableData\">{{ $t('GLOBAL._CX') }}</el-button>\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                $t('GLOBAL._XZ') }}\r\n              </el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"table-box\">\r\n        <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n          element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n          <el-table-column prop=\"operation\" width=\"100\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\" :disabled=\"scope.row.Status == 'NotSplit' ? true : false\">{{ $t('GLOBAL._TD') }}</el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column v-for=\"(item, index) in tableHead\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)\">\r\n                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.field === 'Status'\"> {{ status[scope.row[item.field]] }} </span>\r\n              <span v-else> {{ scope.row[item.field] }} </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n        </el-table>\r\n      </div>\r\n    </el-drawer>\r\n    <BomDetailForm @saveForm=\"getTableData\" ref=\"text\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWeekScheduleBomList } from '@/api/planManagement/weekSchedule'\r\nimport BomDetailForm from './bomDetailForm'\r\nexport default {\r\n  name: 'BomDetail',\r\n  components: {\r\n    BomDetailForm\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      drawer: false,\r\n      tableData: [],\r\n      tableHead: [\r\n        {code: 'SegmentName', width: 100, align: 'center'},  \r\n        {code: 'Sort', width: 80, align: 'center'},\r\n        {code: 'MaterialCode', width: 150, align: 'left'},\r\n        {code: 'MaterialName', width: 180, align: 'left'},\r\n        {code: 'MaterialType', width: 100, align: 'left'},\r\n        {code: 'InsteadMaterialCode', width: 150, align: 'left'},\r\n        {code: 'InsteadMaterialName', width: 180, align: 'left'},\r\n        {code: 'Unit', width: 100, align: 'center'},\r\n        {code: 'StandardQuantity', width: 100, align: 'left'},\r\n        {code: 'PlanQuantity', width: 100, align: 'left'},\r\n        {code: 'Remark', width: 180, align: 'left'},\r\n      ],\r\n      loading: false,\r\n      hansObjDrawer: this.$t('WeekSchedule.bomDetail'),\r\n      sapSegmentMaterialId: 0,\r\n      currentRow: {},\r\n      status: {\r\n        '1': 'Disable',\r\n        '2': 'Released',\r\n        '3': 'Pending_Release'\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    show(val) {\r\n      this.currentRow = val\r\n      this.sapSegmentMaterialId = val.ID\r\n      this.drawer = true\r\n      this.initTableHead()\r\n      this.getTableData()\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    async getTableData() {\r\n      const { response } = await getWeekScheduleBomList({\r\n        WeekScheduleId: this.currentRow.ID,\r\n        ...this.searchForm\r\n      })\r\n      this.tableData = response\r\n    },\r\n    initTableHead() {\r\n      this.tableHead = []\r\n      for (let key in this.hansObjDrawer) {\r\n        this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })\r\n      }\r\n    },\r\n    showDialog(row) {\r\n      this.$refs.text.show(row,this.currentRow.MaterialVersionId)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    padding-top: 10px;\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    :deep(.el-button.is-disabled) {\r\n      background-color: transparent !important;\r\n      border: 0 !important;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}