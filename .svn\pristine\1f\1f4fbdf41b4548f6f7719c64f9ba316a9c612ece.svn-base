﻿using SEFA.Base.Model.BASE;
using SqlSugar;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///周计划BOM
    ///</summary>

    [SugarTable("PPM_B_WEEK_SCHEDULE_BOM")]
    public class WeekScheduleBomEntity : EntityBase
    {
        public WeekScheduleBomEntity()
        {
        }
        /// <summary>
        /// Desc:周计划ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "WEEK_SCHEDULE_ID")]
        public string WeekScheduleId { get; set; }
        /// <summary>
        /// Desc:SegmentID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_ID")]
        public string SegmentId { get; set; }
        /// <summary>
        /// Desc:Segment名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_NAME")]
        public string SegmentName { get; set; }
        /// <summary>
        /// Desc:Segment物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_MATERIAL_ID")]
        public string SegmentMaterialId { get; set; }
        /// <summary>
        /// Desc:Segment物料步骤ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "SEGMENT_MATERIAL_STEP_ID")]
        public string SegmentMaterialStepId { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料代码
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_CODE")]
        public string MaterialCode { get; set; }
        /// <summary>
        /// Desc:物料名称
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_NAME")]
        public string MaterialName { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_VER")]
        public string MaterialVer { get; set; }
        /// <summary>
        /// Desc:物料类型
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_TYPE")]
        public string MaterialType { get; set; }
        /// <summary>
        /// Desc:使用顺序
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SORT")]
        public int? Sort { get; set; }
        /// <summary>
        /// Desc:上级ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "PARENT_ID")]
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:替代物料ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_MATERIAL_ID")]
        public string InsteadMaterialId { get; set; }
        /// <summary>
        /// Desc:替代物料代码
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_MATERIAL_CODE")]
        public string InsteadMaterialCode { get; set; }
        /// <summary>
        /// Desc:替代物料名称
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_MATERIAL_NAME")]
        public string InsteadMaterialName { get; set; }
        /// <summary>
        /// Desc:替代物料版本
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_MATERIAL_VER")]
        public string InsteadMaterialVer { get; set; }
        /// <summary>
        /// Desc:替代物料分组
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_MATERIAL_GROUP")]
        public string InsteadMaterialGroup { get; set; }
        /// <summary>
        /// Desc:替代比例
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "INSTEAD_RATE")]
        public int? InsteadRate { get; set; }
        /// <summary>
        /// Desc:单位
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT")]
        public string Unit { get; set; }
        /// <summary>
        /// Desc:单位ID
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "UNIT_ID")]
        public string UnitId { get; set; }
        /// <summary>
        /// Desc:标准需求数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "STANDARD_QUANTITY")]
        public decimal StandardQuantity { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        [SugarColumn(ColumnName = "PLAN_QUANTITY")]
        public decimal PlanQuantity { get; set; }
        /// <summary>
        /// Desc:实际数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "ACTUAL_QUANTITY")]
        public decimal? ActualQuantity { get; set; }
        /// <summary>
        /// Desc:SAP工单号
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_ORDER_NO")]
        public string SapOrderNo { get; set; }
        /// <summary>
        /// Desc:SAP报工数量
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_FEEDBACK_QUANTITY")]
        public decimal? SapFeedbackQuantity { get; set; }
        /// <summary>
        /// Desc:SAP状态
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "SAP_STATUS")]
        public string SapStatus { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        [SugarColumn(ColumnName = "REMARK")]
        public string Remark { get; set; }

    }
}