<template>
  <div class="">
    <v-img class="img" src="/static/defaultbg_2.png" alt=""></v-img>

    <v-row class="page-login float" fill-height>
      <v-col :cols="12">
        <div class="logo1">
          <!-- <v-img class="float-left mx-15" src="/static/LKKlogin.png" width="260px" style="position: relative;margin-top: -10vh;"></v-img> -->
          <h2 class="float-right mx-15" style="color: white; line-height: 82px;">
            <div style="margin-left: 2vw">数字化工厂管理平台</div>
          </h2>
        </div>
        <v-card class="d-flex flex-row mt-4">
          <v-img class="loginimg" lazy-src="/static/Slogo_left_2.png" src="/static/Slogo_left_2.png"></v-img>
          <v-card-text>
            <v-form ref="form" v-model="formValid" class="ma-10" width="500" lazy-validation>
              <v-text-field v-model="formModel.username" append-icon="mdi-account" autocomplete="off"
                            name="login" autofocus :label="$t('username')" :placeholder="$t('username')" type="text"
                            required outlined :rules="formRule.username" />
              <v-text-field v-model="formModel.password" append-icon="mdi-lock" autocomplete="off"
                            name="password" :label="$t('password')" :placeholder="$t('password')" type="password"
                            :rules="formRule.password" required outlined @keyup.enter="handleLogin" />
              <v-btn large color="primary" style="width: 100%" :loading="loading" @click="handleLogin">
                {{ $t('login') }}
              </v-btn>
            </v-form>
            <div class="lang">
              <v-btn text color="primary" @click="goDomainEntry()">{{ $t('GLOBAL._YDL') }}</v-btn>
              <LocaleSwitch />
            </div>
          </v-card-text>
        </v-card>
        <div class="copy" align-center>CopyRight&copy;2022施耐德电气（中国）有限公司</div>
      </v-col>
    </v-row>
    <v-row class="img my-0" fill-height></v-row>
    <!-- <div class="loading-box">
        <a-spin tip="系统正在初始化中..." :spinning="loading">
        </a-spin>
    </div> -->
  </div>
</template>

<script>
const name = 'page-login';
import { configUrl } from '@/config';

const SSO_URL = configUrl[process.env.VUE_APP_SERVE].SSO_URL;
export default {
  name: name,
  components: {
    LocaleSwitch: () => import('@/components/locale/LocaleSwitch')
  },
  data() {
    return {
      loading: false,
      formValid: false,
      formModel: {
        username: '',
        password: '',
        token: ''
      },
      formRule: {
        username: [v => !!v || this.$t('rule.requiredUsername')],
        password: [v => !!v || this.$t('rule.requiredPassword')]
      },
      redirectPath: '/',
      queryPar: {}
    };
  },
  watch: {
    $route() {
      location.reload();
    }
  },
  mounted() {
    this.isOuterChain();
  },
  methods: {
    // 跳转使用域登录
    goDomainEntry() {
      console.log("host", window.document.location.host)
      const callback = window.location.origin;
      const url = `https://sso.aac.com/login.aspx?url=${decodeURIComponent(callback)}`;
      window.location.href = url
    },
    isOuterChain() {
      const o = this.$route.query;
      const { redirect, par, code, loginType } = o;

      if (loginType && loginType.toLowerCase() === 'sso') {
        window.location.href = SSO_URL
        return false
      }

      this.redirectPath = redirect;
      // queryPar：外链跳转过来带的参数
      this.queryPar = par ? JSON.parse(par) : {};
      //_DLZH： 自动登录账号；_DLMM： 自动登录密码
      const { _DLZH, _DLMM } = this.queryPar;
      // this.$nextTick(()=>{

      // })
      if (_DLZH && _DLMM) {
        console.log(_DLZH, _DLMM);
        this.formModel.username = _DLZH;
        this.formModel.password = _DLMM;
        this.loginPar();
      } else if (code) {
        this.formModel.token = code
        this.loginPar()
      } else {
        // this.goDomainEntry()
      }
    },
    handleLogin() {
      if (this.$refs.form.validate()) {
        this.loginPar();
      }
    },
    loginPar() {
      this.loading = true;
      this.$store
          .dispatch('login', this.formModel)
          .then(() => {
            this.handlePermission();
          })
          .catch(() => {
            window._VMA.$emit('SHOW_SNACKBAR', {
              show: true,
              text: '登录失败',
              color: 'error'
            });
            this.loading = false;
          });
    },
    handlePermission() {
      this.$store
          .dispatch('getPermission')
          .then(() => {
            this.loading = false;
            // 登录成功，去掉地址栏账号密码
            delete this.queryPar._DLZH;
            delete this.queryPar._DLMM;
            const route = this.redirectPath ? { path: this.redirectPath, query: this.queryPar } : { path: '/' };
            this.$store.commit('SETFLAG', true)
            this.$router.push(route);
          })
          .catch(() => {
            this.$router.push('/404');
          });
    },
    handleRegister() {
      console.log(this);
    },
    handleSocialLogin() { }
  }
};
</script>

<style lang="scss" scoped>
.page-login {
  position: relative;
  min-width: 920px;
  max-width: 920px;
  margin: -221px auto;

  // .loginimg {
  //     .v-responsive__content {
  //         background: rgba(255, 255, 255, 0.4) !important;
  //     }
  // }
  .lang {
    float: right;
  }

  .copy {
    text-align: center;
    margin-top: 48px;
    color: #aaa;
  }

  .logo1 {
    position: absolute;
    top: -74px;
  }
}

.img {
  height: calc(50vh - 16px);
}

.loading-box {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  // background: #fff;

  // #3dcd58
  ::v-deep .ant-spin-text {
    color: #3dcd58;
  }

  ::v-deep .ant-spin-dot.ant-spin-dot-spin {
    i {
      background-color: #3dcd58;
    }
  }
}
</style>
