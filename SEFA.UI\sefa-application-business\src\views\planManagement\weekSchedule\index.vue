<!--
 * @Descripttion: (周计划/PPM_B_WEEK_SCHEDULE)
 * @version: (1.0)
 * @Author: (SECI)
 * @Date: (2025-04-10)
 * @LastEditors: (SECI)
 * @LastEditTime: (2025-04-10)
-->

<template>
  <div class="root">
    <div class="root-head">
      <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
            				    
        <el-form-item label="工厂" class="mb-2">
          <!--el-select v-model="searchForm.factory" placeholder="请选择工厂">
            <el-option v-for="item in  sys_classify_type " :key="item.dictValue" :label="item.dictLabel" :value="item.dictValue"></el-option>
          </!--el-select-->
          <el-input clearable v-model="searchForm.factory" placeholder="请输入工厂" />
        </el-form-item>
        <el-form-item label="车间" class="mb-2">
          <el-input clearable v-model="searchForm.workshop" placeholder="请输入车间" />
        </el-form-item>
        <el-form-item label="产线" class="mb-2">
          <el-input clearable v-model="searchForm.lineCode" placeholder="请输入产线" />
        </el-form-item>
        <el-form-item label="物料代码" class="mb-2">
          <el-input clearable v-model="searchForm.materialCode" placeholder="请输入物料代码" />
        </el-form-item>
        <el-form-item label="计划编号" class="mb-2">
          <el-input clearable v-model="searchForm.orderNo" placeholder="请输入计划编号" />
        </el-form-item>
        <el-form-item class="mb-2">
          <el-button icon="el-icon-search" @click="getSearchBtn()">{{ $t('GLOBAL._CX') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="success" icon="el-icon-circle-plus-outline" @click="showDialog({})">
            {{ $t('GLOBAL._XZ') }}
          </el-button>
        </el-form-item>
        <upload-button :option="buttonOption" :searchForm="searchForm" ref="uploadButton"></upload-button>

      </el-form>
    </div>
    <div class="root-main">
      <el-table class="mt-3"
                :height="mainH"
                border
                :data="tableData"
                style="width: 100%">
        <el-table-column prop="operation" width="160" :label="$t('GLOBAL._ACTIONS')" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="bomDialog(scope.row)">{{ $t('GLOBAL._BOM') }}</el-button>
            <el-button size="mini" type="text" @click="showDialog(scope.row)">{{ $t('GLOBAL._BJ') }}</el-button>
            <el-button size="mini" type="text" @click="delRow(scope.row)">{{ $t('GLOBAL._SC') }}</el-button>          
          </template>
        </el-table-column>
        <el-table-column v-for="(item) in tableName"
                         :default-sort="{prop: 'date', order: 'descending'}"
                         :key="item.ID"
                         :prop="item.field"
                         :label="item.label"
                         :width="item.width"
                         :align="item.alignType"
                         sortable
                         show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row[item.field] }}
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="root-footer">
      <el-pagination
          class="mt-3"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageIndex"
          :page-sizes="[10,20,50,100,500]"
          :page-size="searchForm.pageSize"
          layout="->,total, sizes, prev, pager, next, jumper"
          :total="total"
          background
      ></el-pagination>
    </div>
    <form-dialog @saveForm="getSearchBtn" ref="formDialog"></form-dialog>
    <BomDetail ref="bomDetail" />
  </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';
import FormDialog from './form-dialog'
import BomDetail from './bomDetail.vue'
import {delWeekSchedule, getWeekScheduleList} from "@/api/planManagement/weekSchedule";
import {getTableHead} from "@/util/dataDictionary.js";
import UploadButton from "@/components/UploadButton.vue";

export default {
  name: 'index',
  components: {
    UploadButton,
    FormDialog,
    BomDetail
  },
  data() {
    return {
      searchForm: {
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [{}],
      hansObj: this.$t('WeekSchedule.table'),
      tableName: [],
      loading: false,
      tableOption: [
        {code: 'Factory', width: 100, align: 'center'},
        {code: 'Workshop', width: 150, align: 'center'},
        {code: 'Category', width: 150, align: 'center'},
        {code: 'DesignCode', width: 150, align: 'center'},
        {code: 'FinishWorkday', width: 180, align: 'center'},
        {code: 'LineCode', width: 150, align: 'center'},
        {code: 'FinishShift', width: 100, align: 'center'},
        {code: 'MaterialCode', width: 100, align: 'center'},
        {code: 'MaterialName', width: 180, align: 'center'},
        {code: 'OrderNo', width: 150, align: 'center'},
        {code: 'Output', width: 100, align: 'center'},
        {code: 'PackSize', width: 150, align: 'center'},
        {code: 'PlanQuantity', width: 100, align: 'center'},
        {code: 'Remark', width: 150, align: 'center'},
        {code: 'SapOrderNo', width: 150, align: 'center'},
        {code: 'StartWorkday', width: 150, align: 'center'},
        {code: 'StartShift', width: 100, align: 'center'},
        {code: 'Status', width: 100, align: 'center'},
        {code: 'Type', width: 100, align: 'center'},
        {code: 'Unit', width: 100, align: 'center'},
        {code: 'WorkCenter', width: 100, align: 'center'},
      ],
      mainH: 0,
      buttonOption:{
        name:'周计划',
        serveIp:'baseURL_PPM',
        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入
        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出
        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板
      }
    }
  },
  mounted() {
    this.getZHHans()
    this.getTableData()
    this.$nextTick(() => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    })
    window.onresize = () => {
      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)
    }
  },
  methods: {
    getZHHans() {
      this.tableName = getTableHead(this.hansObj, this.tableOption)
    },
    showDialog(row) {
      this.$refs.formDialog.show(row)
    },
    handleCurrentChange(page) {
      this.searchForm.pageIndex = page
      this.getTableData()
    },
    handleSizeChange(size) {
      this.searchForm.pageSize = size
      this.getTableData()
    },
    getSearchBtn() {
      this.searchForm.pageIndex = 1
      this.getTableData()
    },
    bomDialog(row) {
      this.$refs.bomDetail.show(row)
    },
    delRow(row) {
      this.$confirms({
        title: this.$t('GLOBAL._TS'),
        message: this.$t('GLOBAL._COMFIRM'),
        confirmText: this.$t('GLOBAL._QD'),
        cancelText: this.$t('GLOBAL._QX')
      }).then(async () => {
        delWeekSchedule([row.ID]).then(res => {
          this.$message.success(res.msg)
          this.getTableData()
        })
      }).catch(err => {
        console.log(err);
      });
    },

    getTableData(data) {
      getWeekScheduleList(this.searchForm).then(res => {
        console.log(res);
        this.tableData = res.response.data
        this.total = res.response.dataCount
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

.mt-8p {
  margin-top: 8px;
}

.pd-left {
  padding-left: 5px
}
</style>