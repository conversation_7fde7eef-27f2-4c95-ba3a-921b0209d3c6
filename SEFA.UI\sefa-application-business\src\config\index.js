export const configUrl = {
    token: '',
    development: {
        evn: 'development',
        localhost: '',
        baseURL_JOB: 'http://*************:30035',
        // baseURL_DFM: 'http://*************:30032',
        baseURL_DFM: 'http://127.0.0.1:30019',
        //baseURL_DFM: '',
        baseURL_KPI: '',
        //baseURL_EQUIPMENT: "http://*************:30006",
        baseURL_EQUIPMENT: "http://*************:30034",
        baseURL_TRACE: '',
        baseURL_ORDER: 'http://localhost:30018',
        baseURL_SHIFT: '',
        baseURL_MATERIAL: 'http://*************:30032',
        baseURL_Resource: 'http://*************:30032',
        baseURL_Inventory: 'http://127.0.0.1:30018', //'http://***********:30018',
        baseURL_Inventory2: "http://127.0.0.1:30018", //"http://************:30018",
        baseURL_Inventory3: "http://127.0.0.1:30018",
        // baseURL_Inventory: 'http://*************:30033', //'http://***********:30018',
        // baseURL_Inventory2: "http://*************:30033", //"http://************:30018",
        // baseURL_Inventory3: "http://*************:30033",
        baseURL_30015: "http://*************:30033",
        baseURL_ANDON: 'http://*************:30012',
        baseURL_QUALITY: '',
        baseURL_TPM: '',
        baseURL_QMS: '',
        baseURL_SIM: '',
        baseURL_Formula: 'http://*************:30033',
        baseURL_API2: 'http://*************:30012',
        IMG_SERVE_URL: "http://*************:8080",
        baseURL_TEST: "http://************:30018",
        SSO_URL: ''
    },
    production: {
        evn: 'production',
        localhost: '',
        baseURL_JOB: 'http://*************:30035',
        baseURL_SHIFT: 'http://*************:30020',
        baseURL_Resource: 'http://*************:30032',
        baseURL_EQUIPMENT: "http://*************:30034",
        baseURL_DFM: 'http://*************:30032',
        baseURL_Formula: 'http://*************:30033',
        baseURL_Inventory: 'http://*************:30033',
        baseURL_Inventory2: 'http://*************:30033',
        baseURL_Inventory3: 'http://*************:30033',
        baseURL_30015: "http://*************:30033",
        baseURL_KPI: 'http://*************:30034',
        baseURL_TRACE: 'http://*************:30032',
        baseURL_ORDER: 'http://*************:30014',
        baseURL_MATERIAL: 'http://*************:30032',
        baseURL_ANDON: 'http://*************:30012',
        baseURL_QUALITY: 'http://*************:30008',
        baseURL_TPM: 'http://*************:30007',
        baseURL_SIM: 'http://*************:30006',
        baseURL_API2: 'http://*************:30012',
        IMG_SERVE_URL: "http://*************:8080",
        baseURL_TEST: "http://*************:30033",
        
        // baseURL_JOB: '',
        // baseURL_DFM: 'http://localhost:30019',
        // //baseURL_DFM: '',
        // baseURL_KPI: '',
        // //baseURL_EQUIPMENT: "http://*************:30006",
        // baseURL_EQUIPMENT: "http://127.0.0.1:30006",
        // baseURL_TRACE: '',
        // baseURL_ORDER: '',
        // baseURL_SHIFT: '',
        // baseURL_MATERIAL: 'http://localhost:30019',
        // baseURL_Resource: 'http://localhost:30019',
        // baseURL_Inventory: 'http://localhost:30018', //'http://***********:30018',
        // baseURL_Inventory2: "http://localhost:30018", //"http://************:30018",
        // baseURL_Inventory3: "http://localhost:30018",
        // baseURL_30015: "http://localhost:30018",
        // baseURL_ANDON: '',
        // baseURL_QUALITY: '',
        // baseURL_TPM: '',
        // baseURL_QMS: '',
        // baseURL_SIM: '',
        // baseURL_Formula: 'http://localhost:30018',
        // baseURL_API2: 'http://*************:30012',
        // IMG_SERVE_URL: "http://*************:8080",
        // baseURL_TEST: "http://************:30018",
        SSO_URL: ''
    },
    test: {
        evn: 'test',
        localhost: '',
        baseURL_Formula: 'http://*************:30017',
        baseURL_JOB: 'http://*************:30022',
        baseURL_SHIFT: 'http://*************:30020',
        baseURL_DFM: 'http://*************:30017',
        baseURL_Inventory: 'http://*************:30033',
        baseURL_Inventory2: 'http://*************:30033',
        baseURL_Inventory3: 'http://*************:30033',
        baseURL_KPI: 'http://*************:30024',
        baseURL_TRACE: 'http://*************:30015',
        baseURL_ORDER: 'http://*************:30014',
        baseURL_MATERIAL: 'http://*************:30013',
        baseURL_ANDON: 'http://*************:30012',
        baseURL_QUALITY: 'http://*************:30021',
        baseURL_TPM: 'http://*************:30023',
        baseURL_QMS: 'https://qms.aac.tech',
        baseURL_SIM: 'http://*************:30024',
        baseURL_API2: 'http://*************:30012',
        IMG_SERVE_URL: "http://*************:8080",
        SSO_URL: ''
    },
    cztest: {
        evn: 'cztest',
        localhost: '',
        baseURL_JOB: 'http://*************:30022',
        baseURL_SHIFT: 'http://*************:30020',
        baseURL_DFM: 'http://*************:9093',
        baseURL_KPI: 'http://*************:30024',
        baseURL_TRACE: 'http://*************:30015',
        baseURL_ORDER: 'http://*************:30014',
        baseURL_MATERIAL: 'http://*************:30013',
        baseURL_ANDON: 'http://*************:30012',
        baseURL_QUALITY: 'http://*************:30021',
        baseURL_TPM: 'http://*************:30023',
        baseURL_QMS: 'https://qms.aac.tech',
        baseURL_SIM: 'http://*************:9094',
        baseURL_API2: 'http://*************:30012',
        IMG_SERVE_URL: "http://*************:8080",
        SSO_URL: ''
    },
    devtest: {
        evn: 'devtest',
        localhost: '',
        baseURL_JOB: 'http://*************:30002',
        baseURL_SHIFT: 'http://*************:30020',
        baseURL_DFM: 'http://*************:30019',
        baseURL_KPI: 'http://*************:30018',
        baseURL_TRACE: 'http://*************:30033',
        baseURL_ORDER: 'http://*************:30014',
        baseURL_MATERIAL: 'http://*************:30013',
        baseURL_ANDON: 'http://*************:30012',
        baseURL_QUALITY: 'http://*************:30008',
        baseURL_TPM: 'http://*************:30007',
        baseURL_QMS: 'http://*************:30008',
        baseURL_SIM: 'http://*************:30018',
        baseURL_API2: 'http://*************:30012',
        IMG_SERVE_URL: "http://*************:8080",
        SSO_URL: ''
    },
    lkktest: {
         evn: 'lkktest',
         localhost: '',
         baseURL_JOB: 'https://mesxhtest.lkk.com.cn:8087',
         baseURL_SHIFT: 'https://mesxhtest.lkk.com.cn:30020',
         baseURL_Resource: 'https://mesxhtest.lkk.com.cn:8081',
         baseURL_EQUIPMENT: "https://mesxhtest.lkk.com.cn:8086",
         baseURL_DFM: 'https://mesxhtest.lkk.com.cn:8081',
         baseURL_Formula: 'https://mesxhtest.lkk.com.cn:8085',
         baseURL_Inventory: 'https://mesxhtest.lkk.com.cn:8085',
         baseURL_Inventory2: 'https://mesxhtest.lkk.com.cn:8085',
         baseURL_Inventory3: 'https://mesxhtest.lkk.com.cn:8085',
         baseURL_30015: "https://mesxhtest.lkk.com.cn:8085",
         baseURL_KPI: 'https://mesxhtest.lkk.com.cn:8085',
         baseURL_TRACE: 'https://mesxhtest.lkk.com.cn:8081',
         baseURL_ORDER: 'https://mesxhtest.lkk.com.cn:30014',
         baseURL_MATERIAL: 'https://mesxhtest.lkk.com.cn:8081',
         baseURL_ANDON: 'https://mesxhtest.lkk.com.cn:8090',
         baseURL_QUALITY: 'https://mesxhtest.lkk.com.cn:30008',
         baseURL_TPM: 'https://mesxhtest.lkk.com.cn:8086',
         baseURL_QMS: 'https://qms.aac.tech',
         baseURL_SIM: 'https://mesxhtest.lkk.com.cn:30006',
         baseURL_API2: 'https://mesxhtest.lkk.com.cn:8090',
         IMG_SERVE_URL: "https://mesxhtest.lkk.com.cn:8080",
         baseURL_TEST: "https://mesxhtest.lkk.com.cn:8085",
         SSO_URL: ''
        },
        lkkprod: {
             evn: 'lkkprod',
             localhost: '',
             baseURL_JOB: 'https://mesxh.lkk.com.cn:8087',
             baseURL_SHIFT: 'https://mesxh.lkk.com.cn:30020',
             baseURL_Resource: 'https://mesxh.lkk.com.cn:8081',
             baseURL_EQUIPMENT: "https://mesxh.lkk.com.cn:8086",
             baseURL_DFM: 'https://mesxh.lkk.com.cn:8081',
             baseURL_Formula: 'https://mesxh.lkk.com.cn:8085',
             baseURL_Inventory: 'https://mesxh.lkk.com.cn:8085',
             baseURL_Inventory2: 'https://mesxh.lkk.com.cn:8085',
             baseURL_Inventory3: 'https://mesxh.lkk.com.cn:8085',
             baseURL_30015: "https://mesxh.lkk.com.cn:8085",
             baseURL_KPI: 'https://mesxh.lkk.com.cn:8085',
             baseURL_TRACE: 'https://mesxh.lkk.com.cn:8081',
             baseURL_ORDER: 'https://mesxh.lkk.com.cn:30014',
             baseURL_MATERIAL: 'https://mesxh.lkk.com.cn:8081',
             baseURL_ANDON: 'https://mesxh.lkk.com.cn:8090',
             baseURL_QUALITY: 'https://mesxh.lkk.com.cn:30008',
             baseURL_TPM: 'https://mesxh.lkk.com.cn:8086',
             baseURL_QMS: 'https://qms.aac.tech',
             baseURL_SIM: 'https://mesxh.lkk.com.cn:30006',
             baseURL_API2: 'https://mesxh.lkk.com.cn:8090',
             IMG_SERVE_URL: "https://mesxh.lkk.com.cn:8080",
             baseURL_TEST: "https://mesxh.lkk.com.cn:8085",
             SSO_URL: ''
     },
    build: {
        evn: 'build',
        localhost: '',
        baseURL_JOB: 'https://amesjobgw.aac.tech',
        baseURL_SHIFT: 'https://messhiftgw.aac.tech',
        baseURL_DFM: 'https://mesdfmgw.aac.tech',
        baseURL_KPI: 'https://messimgw.aac.tech',
        baseURL_TRACE: 'https://mestracegw.aac.tech',
        baseURL_ORDER: 'https://mesordergw.aac.tech',
        baseURL_MATERIAL: 'https://mesmkmgw.aac.tech',
        baseURL_ANDON: 'https://mesandongw.aac.tech',
        baseURL_QUALITY: 'https://mesqmsgw.aac.tech',
        baseURL_TPM: 'https://mestpmgw.aac.tech',
        baseURL_QMS: 'https://qms.aac.tech',
        baseURL_SIM: 'https://messimgw.aac.tech',
        baseURL_API2: 'http://************:30012',
        IMG_SERVE_URL: "http://************:8080",
        SSO_URL: ''
    }
};
