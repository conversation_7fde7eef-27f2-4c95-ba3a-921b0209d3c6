﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>

		<OutputType>Exe</OutputType>

		<TargetFramework>net6.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<EnableUnsafeBinaryFormatterSerialization>true</EnableUnsafeBinaryFormatterSerialization>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>..\SEFA.PPM.Api\SEFA.PPM.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>..\SEFA.PPM\SEFA.PPM.xml</DocumentationFile>
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup>
		<ServerGarbageCollection>false</ServerGarbageCollection>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="Connected Services\SSOLogin\Reference.cs" />
	</ItemGroup>
	<ItemGroup>
	  <Content Remove="Connected Services\SSOLogin\ConnectedService.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Abp" Version="7.3.0" />
		<PackageReference Include="AspNetCoreRateLimit" Version="3.0.5" />
		<PackageReference Include="Autofac.Extensions.DependencyInjection" Version="6.0.0" />
		<PackageReference Include="Autofac.Extras.DynamicProxy" Version="5.0.0" />
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="8.1.0" />
		<PackageReference Include="Castle.Core" Version="5.0.0" />
		<PackageReference Include="Com.Ctrip.Framework.Apollo.Configuration" Version="2.7.0" />
		<PackageReference Include="Confluent.Kafka" Version="1.7.0" />
		<PackageReference Include="Consul" Version="*******" />
		<PackageReference Include="InitQ" Version="*******" />
		<PackageReference Include="log4mongo-netcore" Version="3.2.0" />
		<PackageReference Include="log4net" Version="2.0.14" />
		<PackageReference Include="Magicodes.IE.Core" Version="2.6.4" />
		<PackageReference Include="Magicodes.IE.Excel.Abp" Version="2.6.4" />
		<PackageReference Include="Magicodes.IE.Excel.AspNetCore" Version="2.6.4" />
		<PackageReference Include="MicroKnights.Log4NetAdoNetAppender" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="5.0.9" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.7" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.NewtonsoftJson" Version="6.0.7" />
		<PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Http.Polly" Version="6.0.7" />
		<PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="6.1.0" />
		<PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.21.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.9.10" />
		<PackageReference Include="Minio" Version="5.0.0" />
		<PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.2.1" />
		<PackageReference Include="MiniProfiler.Shared" Version="4.2.22" />
		<PackageReference Include="nacos-sdk-csharp" Version="1.2.1" />
		<PackageReference Include="Panda.DynamicWebApi" Version="1.2.1" />
		<PackageReference Include="Polly" Version="7.2.2" />
		<PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="RestSharp" Version="108.0.1" />
    <!--<PackageReference Include="RestSharp" Version="110.1.0" />-->
    <PackageReference Include="SapNwRfc" Version="1.4.0" />
		<PackageReference Include="Serilog" Version="2.10.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
		<PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
		<PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
		<PackageReference Include="SkyAPM.Agent.AspNetCore" Version="1.3.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.6.48" />
		<PackageReference Include="Swashbuckle.AspNetCore.Filters" Version="6.0.1" />
		<PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="6.4.0" />
		<PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.4.0" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.21.0" />
		<PackageReference Include="System.Management" Version="7.0.2" />
		<PackageReference Include="System.ServiceModel.Duplex" Version="4.10.*" />
		<PackageReference Include="System.ServiceModel.Federation" Version="4.10.*" />
		<PackageReference Include="System.ServiceModel.Http" Version="4.10.*" />
		<PackageReference Include="System.ServiceModel.NetTcp" Version="4.10.*" />
		<PackageReference Include="System.ServiceModel.Security" Version="4.10.*" />
		<PackageReference Include="System.Text.Encoding.CodePages" Version="5.0.0" />
		<PackageReference Include="WebApiClient.JIT" Version="1.1.4" />
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="index.html" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\SEFA.PPM.Services\SEFA.PPM.Services.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Reference Include="LKK.Lib.Core">
	    <HintPath>..\..\common\LKK.Lib.Core.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Common">
	    <HintPath>..\..\common\SEFA.Base.Common.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.ESB">
	    <HintPath>..\..\common\SEFA.Base.ESB.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.EventBus">
	    <HintPath>..\..\common\SEFA.Base.EventBus.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Extensions">
	    <HintPath>..\..\common\SEFA.Base.Extensions.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.InfluxDB">
	    <HintPath>..\..\common\SEFA.Base.InfluxDB.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.IServices">
	    <HintPath>..\..\common\SEFA.Base.IServices.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Model">
	    <HintPath>..\..\common\SEFA.Base.Model.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Repository">
	    <HintPath>..\..\common\SEFA.Base.Repository.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.Base.Serilog.Es">
	    <HintPath>..\..\common\SEFA.Base.Serilog.Es.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.IServices">
	    <HintPath>..\..\common\SEFA.DFM.IServices.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Model">
	    <HintPath>..\..\common\SEFA.DFM.Model.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Repository">
	    <HintPath>..\..\common\SEFA.DFM.Repository.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.DFM.Services">
	    <HintPath>..\..\common\SEFA.DFM.Services.dll</HintPath>
	  </Reference>
	  <Reference Include="SEFA.LicenseTool.Util">
	    <HintPath>..\..\common\SEFA.LicenseTool.Util.dll</HintPath>
	    <CopyLocal>True</CopyLocal>
	  </Reference>
	</ItemGroup>

	<ItemGroup>
		<Content Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Log4net.config">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="Dockerfile">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="StopContainerImg.sh">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Connected Services\SSOLogin\" />
	  <Folder Include="Controllers\PPM\LogSheet\" />
	  <Folder Include="Controllers\Report\" />
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties appsettings_1json__JsonSchema="" />
		</VisualStudio>
	</ProjectExtensions>

</Project>
