﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;
using SEFA.Base.Model.BASE;

namespace SEFA.MKM.Model.Models
{
    ///<summary>
    ///第二层批次下物料信息表(包括跳转界面)
    ///</summary>
    [SugarTable("V_PPM_B_BATCH_DETAIL_II_VIEW")]
    public class BBatchDetailIiViewEntity : EntityBase
    {
        public BBatchDetailIiViewEntity()
        {
        }

        /// <summary>
        /// 是否允许整袋合并标识（0: 不合并，1: 合并） 物料类属性---IsFullBagMerge
        /// </summary>
        [SugarColumn(ColumnName = "IS_PG")]
        public string IsPg { get; set; }

        /// <summary>
        /// 设备 ID（通常为产线或机器 ID）
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_ID")]
        public string EquipmentId { get; set; }

        /// <summary>
        /// 复合主键（格式：MATERIAL_ID/PRODUCTION_ORDER_ID/MATERIAL_CODE/BATCH_ID）
        /// </summary>
        [SugarColumn(ColumnName = "Only_Id")]
        public string OnlyId { get; set; }

        /// <summary>
        /// 生产订单编号
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_NO")]
        public string ProductionOrderNo { get; set; }

        /// <summary>
        /// 设备名称（显示用）
        /// </summary>
        [SugarColumn(ColumnName = "EQUIPMENT_NAME")]
        public string EquipmentName { get; set; }

        /// <summary>
        /// 批次编码 例如：BATCH20240301）
        /// </summary>
        [SugarColumn(ColumnName = "M_BATCH_NUMBER")]
        public string MBatchNumber { get; set; }

        /// <summary>
        /// 物料名称
        /// </summary>
        [SugarColumn(ColumnName = "M_NAME")]
        public string MName { get; set; }

        /// <summary>
        /// 物料编码（唯一标识符）
        /// </summary>
        [SugarColumn(ColumnName = "M_CODE")]
        public string MCode { get; set; }

        /// <summary>
        /// 实际使用数量（单位由 M_QUANTITYUNIT 定义）
        /// 计算方式：Full_QTY + P_QTY
        /// </summary>
        [SugarColumn(ColumnName = "M_QUANTITY")]
        public decimal? MQuantity { get; set; }

        /// <summary>
        /// 实际数量的单位名称（如 kg, g, L 等）
        /// </summary>
        [SugarColumn(ColumnName = "M_QUANTITYUNIT")]
        public string MQuantityunit { get; set; }

        /// <summary>
        /// 目标使用数量（预设值，用于比对实际用量）
        /// </summary>
        [SugarColumn(ColumnName = "M_QUANTITY_TOTAL")]
        public decimal MQuantityTotal { get; set; }

        /// <summary>
        /// 目标数量单位名称（与 M_QUANTITYUNIT 一致）
        /// </summary>
        [SugarColumn(ColumnName = "QUANTITY_TOTAL_UNIT")]
        public string QuantityTotalUnit { get; set; }

        /// <summary>
        /// 单位 ID（对应 DFM_M_UNITMANAGE 表 ID）
        /// </summary>
        [SugarColumn(ColumnName = "T_UINTID")]
        public string TUintid { get; set; }

        /// <summary>
        /// 当前库存可用数量（可能未启用）
        /// </summary>
        [SugarColumn(ColumnName = "IN_QUANTITY")]
        public decimal? InQuantity { get; set; }

        /// <summary>
        /// 材料默认单位（Material Unit）
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_UNIT1")]
        public string MaterialUnit1 { get; set; }

        /// <summary>
        /// 每袋标准重量（来自物料属性 FullBagWeight）
        /// 注意：当前存储为字符串类型，建议改为 decimal 类型
        /// </summary>
        [SugarColumn(ColumnName = "BAG_SIZE")]
        public string BagSize { get; set; }

        /// <summary>
        /// 整袋计数显示格式（示例：5/10 或 Infinity）
        /// 表示：已装袋数/总袋数
        /// </summary>
        [SugarColumn(ColumnName = "FULL_PAGE")]
        public string FullPage { get; set; }

        /// <summary>
        /// 零头袋实际重量（小数）
        /// </summary>
        [SugarColumn(ColumnName = "Tagp_s")]
        public decimal? TagpS { get; set; }

        /// <summary>
        /// 需求零头重量（剩余不足一袋的数量）
        /// </summary>
        [SugarColumn(ColumnName = "PARITIAL_PAGE")]
        public string ParitialPage { get; set; }

        /// <summary>
        /// 零头袋重量单位名称
        /// </summary>
        [SugarColumn(ColumnName = "Tagp_s_UNIT")]
        public string TagpSUnit { get; set; }

        /// <summary>
        /// 整袋已完成备料重量（INT 类型）
        /// </summary>
        [SugarColumn(ColumnName = "Full_Finish")]
        public int FullFinish { get; set; }

        /// <summary>
        /// 总需求袋数（计算所得，INT）
        /// </summary>
        [SugarColumn(ColumnName = "BAG_S")]
        public int? BagS { get; set; }

        /// <summary>
        /// 预称重完成状态（"OK"/空），判断是否在设定公差范围内
        /// </summary>
        [SugarColumn(ColumnName = "COMPLETE_STATES")]
        public string CompleteStates { get; set; }

        /// <summary>
        /// 物料是否已消耗标识（"OK"/空）
        /// </summary>
        [SugarColumn(ColumnName = "CONSUMED_STATES")]
        public string ConsumedStates { get; set; }

        /// <summary>
        /// 物料唯一标识 ID
        /// </summary>
        [SugarColumn(ColumnName = "MATERIAL_ID")]
        public string MaterialId { get; set; }

        /// <summary>
        /// 最小允许值（目标量 × (1 - 公差下限%)）
        /// </summary>
        [SugarColumn(ColumnName = "MIN_PVALUE")]
        public decimal? MinPvalue { get; set; }

        /// <summary>
        /// 设置的最小公差百分比（例如：1%）--物料属性：PreweighToleranceMinPercent
        /// </summary>
        [SugarColumn(ColumnName = "SET_MIN")]
        public int? SetMin { get; set; }

        /// <summary>
        /// 最大允许值（目标量 × (1 + 公差上限%)）--物料属性：PreweighToleranceMaxPercent
        /// </summary>
        [SugarColumn(ColumnName = "MAX_PVALUE")]
        public decimal? MaxPvalue { get; set; }

        /// <summary>
        /// 设置的最大公差百分比（例如：1%）
        /// </summary>
        [SugarColumn(ColumnName = "SET_MAX")]
        public int? SetMax { get; set; }

        /// <summary>
        /// 已消耗标识（"Y"=已消耗，"N"=未消耗）
        /// </summary>
        [SugarColumn(ColumnName = "CON_SUMED")]
        public string ConSumed { get; set; }

        /// <summary>
        /// 批次唯一标识 ID
        /// </summary>
        [SugarColumn(ColumnName = "BATCH_ID")]
        public string BatchId { get; set; }

        /// <summary>
        /// 生产订单唯一标识 ID
        /// </summary>
        [SugarColumn(ColumnName = "PRODUCTION_ORDER_ID")]
        public string ProductionOrderId { get; set; }

        /// <summary>
        /// 批次物料需求ID
        /// </summary>
        [SugarColumn(IsIgnore = true)]
        public string BatchConsumeRequirementId { get; set; }
    }
}