{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\poListDetail.vue", "mtime": 1749177894467}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAmDA;AACA;AACA;AACA;EACAA,oBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,cADA;MAEAC,aAFA;MAGAC,aAHA;MAIAC,mBAJA;MAKAC,sDALA;MAMAC,oBACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,EAYA;QAAAF;QAAAC;QAAAC;MAAA,CAZA,EAaA;QAAAF;QAAAC;QAAAC;MAAA,CAbA,EAcA;QAAAF;QAAAC;QAAAC;MAAA,CAdA,EAeA;QAAAF;QAAAC;QAAAC;MAAA,CAfA,EAgBA;QAAAF;QAAAC;QAAAC;MAAA,CAhBA,EAiBA;QAAAF;QAAAC;QAAAC;MAAA,CAjBA,EAkBA;QAAAF;QAAAC;QAAAC;MAAA,CAlBA,EAmBA;QAAAF;QAAAC;QAAAC;MAAA,CAnBA,EAoBA;QAAAF;QAAAC;QAAAC;MAAA,CApBA,CANA;MA4BAC,cA5BA;MA6BAC,uBA7BA;MA8BAC,cA9BA;MA+BAC;QACA,sBADA;QAEA,eAFA;QAGA,eAHA;QAIA,aAJA;QAKA,WALA;QAMA;MANA;IA/BA;EAwCA,CA9CA;;EA+CAC;IACA;EACA,CAjDA;;EAkDAC;IACAC;MACA;MACA;MACA;MACA;IACA,CANA;;IAOAC;MACA;IACA,CATA;;IAUA;MACA;QAAAC;MAAA;QACAC,4BADA;QAEA;MAFA;MAIA;IACA,CAhBA;;IAiBAC;MACA,0BADA,CAEA;MACA;MACA;;MACA;MACA;MACAC;IACA,CAzBA;;IA0BAC;MACA;IACA,CA5BA;;IA6BAC;MACA;QACAC,4BADA;QAEAC,yCAFA;QAGAC,kCAHA;QAIAC;MAJA,GAKAC,IALA,CAKA;QACAL;UACA;UACA;QACA,CAHA;MAIA,CAVA,EAUAM,KAVA,CAUAC;QACAT;MACA,CAZA;IAaA;;EA3CA;AAlDA", "names": ["name", "components", "DetailForm", "data", "searchForm", "drawer", "tableData", "tableHeadDrawer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tableOptionDrawer", "code", "width", "align", "loading", "sapSegmentMaterialId", "currentRow", "status", "mounted", "methods", "show", "handleClose", "response", "ParentId", "initTableHead", "console", "showDialog", "downloadDCS", "title", "message", "confirmText", "cancelText", "then", "catch", "err"], "sourceRoot": "src/views/planManagement/weekFormulation", "sources": ["poListDetail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\"\r\n      :append-to-body=\"false\" size=\"80%\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.OrderNo} | ${currentRow.LineCode} | ${currentRow.Factory} | ${currentRow.MaterialCode}-${currentRow.MaterialName}` }}</span>\r\n      </div>\r\n      <div class=\"InventorySearchBox\">\r\n        <div class=\"searchbox pd5\">\r\n          <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n            <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n              <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button icon=\"el-icon-search\" @click=\"getTableData\">{{ $t('GLOBAL._CX') }}</el-button>\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                $t('GLOBAL._XZ') }}\r\n              </el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"table-box\">\r\n        <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n          element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n          <el-table-column prop=\"operation\" width=\"180\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog((scope.row))\">{{$t('GLOBAL._XZ') }}</el-button>\r\n              <el-button size=\"mini\" type=\"text\" @click=\"downloadDCS(scope.row)\" :disabled=\"scope.row.Status == 'NotSplit' ? true : false\">{{ $t('GLOBAL._XFDCS') }}</el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column v-for=\"(item, index) in tableHeadDrawer\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\" :width=\"item.width\" :align=\"item.alignType\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)\">\r\n                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.field === 'PoStatus'\"> {{ status[scope.row[item.field]] }} </span>\r\n              <span v-else> {{ scope.row[item.field] }} </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n        </el-table>\r\n      </div>\r\n    </el-drawer>\r\n    <DetailForm @saveForm=\"getTableData\" ref=\"detailDialog\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getProductionOrderList, downloadDCS } from '@/api/planManagement/weekSchedule'\r\nimport DetailForm from './poListDetailForm'\r\nimport {getTableHead} from \"@/util/dataDictionary.js\";\r\nexport default {\r\n  name: 'POListDetail',\r\n  components: {\r\n    DetailForm\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      drawer: false,\r\n      tableData: [],\r\n      tableHeadDrawer: [],      \r\n      hansObjDrawer: this.$t('WeekFormulation.poListDetail'),\r\n      tableOptionDrawer:[        \r\n        {code: 'Sequence', width: 100, align: 'left'},\r\n        {code: 'ProductionOrderNo', width: 200, align: 'left'},\r\n        {code: 'SegmentCode', width: 150, align: 'left'},\r\n        {code: 'LineCode', width: 100, align: 'left'},\r\n        {code: 'MaterialCode', width: 180, align: 'left'},\r\n        {code: 'MaterialName', width: 180, align: 'left'},\r\n        {code: 'PlanQty', width: 100, align: 'left'},\r\n        {code: 'PlanDate', width: 180, align: 'left'},        \r\n        {code: 'PrepareShiftid', width: 100, align: 'left'},\r\n        {code: 'PlanStartTime', width: 150, align: 'left'},\r\n        {code: 'PlanEndTime', width: 150, align: 'left'},\r\n        {code: 'StartTime', width: 150, align: 'left'},\r\n        {code: 'EndTime', width: 150, align: 'left'},\r\n        {code: 'PoStatus', width: 150, align: 'left'},\r\n        {code: 'ReleaseStatus', width: 100, align: 'left'},\r\n        {code: 'ProduceStatus', width: 100, align: 'left'},\r\n        {code: 'BomVersion', width: 100, align: 'left'},\r\n        {code: 'Type', width: 100, align: 'left'},\r\n        {code: 'Sapordertype', width: 100, align: 'left'},\r\n        {code: 'Remark', width: 200, align: 'left'},\r\n      ],\r\n      loading: false,\r\n      sapSegmentMaterialId: 0,\r\n      currentRow: {},\r\n      status: {\r\n        '1': 'Pending Release',\r\n        '2': 'Released',\r\n        '3': 'Complete',\r\n        '4': 'Cancel',\r\n        '5' : 'Stop',\r\n        '6' : 'Running'\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTableHead()\r\n  },\r\n  methods: {\r\n    show(val) {\r\n      this.currentRow = val\r\n      this.sapSegmentMaterialId = val.ID\r\n      this.drawer = true\r\n      this.getTableData()\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    async getTableData() {\r\n      const { response } = await getProductionOrderList({\r\n        ParentId: this.currentRow.ID,\r\n        ...this.searchForm\r\n      })\r\n      this.tableData = response\r\n    },\r\n    initTableHead() {\r\n      this.tableHeadDrawer = []\r\n      // for (let key in this.hansObjDrawer) {\r\n      //   this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })\r\n      // }\r\n      this.tableHeadDrawer = getTableHead(this.hansObjDrawer, this.tableOptionDrawer)\r\n      this.$forceUpdate()\r\n      console.log(this.tableHeadDrawer);\r\n    },\r\n    showDialog(row) {\r\n      this.$refs.detailDialog.show(row)\r\n    },\r\n    downloadDCS(row) {\r\n      this.$confirms({\r\n        title: this.$t('GLOBAL._TS'),\r\n        message: this.$t('GLOBAL._COMFIRM_XFDCS'),\r\n        confirmText: this.$t('GLOBAL._QD'),\r\n        cancelText: this.$t('GLOBAL._QX')\r\n      }).then(async () => {\r\n        downloadDCS([row.ID]).then(res => {\r\n          this.$message.success(res.msg)\r\n          this.getTableData()\r\n        })\r\n      }).catch(err => {\r\n        console.log(err);\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    padding-top: 10px;\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    :deep(.el-button.is-disabled) {\r\n      background-color: transparent !important;\r\n      border: 0 !important;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}