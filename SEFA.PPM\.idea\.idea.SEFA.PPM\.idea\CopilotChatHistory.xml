<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotChatHistory">
    <option name="conversations">
      <list>
        <Conversation>
          <option name="createTime" value="1749515891964" />
          <option name="id" value="01975745f4fc7374becabba10d8da114" />
          <option name="title" value="新对话 2025年6月10日 08:38:11" />
          <option name="updateTime" value="1749515891964" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1749432303373" />
          <option name="id" value="0197524a7f0d764e86fc4720e884ce4b" />
          <option name="title" value="新对话 2025年6月09日 09:25:03" />
          <option name="updateTime" value="1749432303373" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1749173641774" />
          <option name="id" value="019742dfaa507aafbdee853c4887710c" />
          <option name="title" value="根据SELECT&#10;DISTINCT&#10;&#9;ISNULL( IsFullBagMerge, 0 ) AS IS_PG,&#10;-- DISTINCT&#10;--g2.Full_QTY,g3.P_QTY,&#10;&#9; EQUIPMENT_ID,&#10;&#9; Only_Id,&#10;&#9;PRODUCTION_ORDER_NO,&#10;&#9;EQUIPMENT_NAME,&#10;&#9; M_BATCH_NUMBER,&#10;--material&#10;&#9; M_NAME,&#10;&#9; M_CODE,&#10;--实际值(实际生产了多少)&#10;&#9; M_QUANTITY,&#10;-- SUM(CASE &#10;-- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- &#9;ELSE 0&#10;-- END&#10;-- )  AS M_QUANTITY,&#10;&#9;M_QUANTITYUNIT,&#10;--TARGET_QUANTITY(目标值)&#10;&#9; M_QUANTITY_TOTAL,&#10;&#9;QUANTITY_TOTAL_UNIT,&#10;&#9;T_UINTID,&#10;--inventory quantity available库存数量&#10;&#9;IN_QUANTITY,&#10;&#9;MATERIAL_UNIT1,&#10;--BAG_SIZE（单包数量）&#10;ISNULL( FullBagWeight , 0 ) AS BAG_SIZE,&#10;--fullBag（整袋数量，已经合并成斜杠数据）&#10;CASE&#10;&#9;&#9;&#10;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;'0/Infinity' ELSE CONVERT ( VARCHAR ( 200 ), CONVERT ( INT, ISNULL( Full_QTY, 0 ) ) / ISNULL( FullBagWeight, 0 ) ) + '/' + CONVERT (&#10;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;&#9;&#9;&#9;&#9;( CONVERT ( INT, WEIGHING_QTY ) / CONVERT ( INT, ISNULL( FullBagWeight, 0 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;END AS FULL_PAGE,&#10;--实际值PARITIAL_PAGE （partial Bags）&#10;&#9;&#9;Tagp_s,--&#10;&#9;&#9;CASE--目标值 （partial）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;'0' ELSE CAST ( ( WEIGHING_QTY % CAST ( ISNULL( FullBagWeight, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) ) &#10;&#9;&#9;&#9;&#9;END AS PARITIAL_PAGE,&#10;--单位&#10;&#9;&#9;&#9;Tagp_s_UNIT,&#10;&#9;&#9;&#9;Full_Finish,&#10;--BAG_S&#10;&#9;&#9;CASE&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;WHEN FullBagWeight IS NULL &#10;&#9;&#9;&#9;&#9;OR FullBagWeight = '0' THEN&#10;&#9;&#9;&#9;&#9;&#9;'0' ELSE CAST ( (WEIGHING_QTY/ ISNULL( FullBagWeight, 0 ) ) AS INT ) &#10;&#9;&#9;&#9;&#9;&#9;END AS BAG_S,&#10;--COMPLETE&#10;&#9;&#9;&#9;&#9;CASE-- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;WHEN ( ( COALESCE (Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &gt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * ( WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;AND ( ( COALESCE ( Full_QTY, 0 ) + COALESCE (P_QTY, 0 ) ) ) &lt;= CAST (&#10;&#9;&#9;&#9;&#9;&#9;&#9;ISNULL(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 1.00+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 19, 6 ) ) / 100.00 ) ) * (WEIGHING_QTY ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;0 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) AS DECIMAL ( 19, 6 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;MATERIAL_ID,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 1- ( CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MIN_PVALUE,&#10;--最小比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMinPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MIN,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 1+ ( CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) / 100.00 ) ) * WEIGHING_QTY AS MAX_PVALUE,&#10;--最大比例 1 1%&#10;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( PreweighToleranceMaxPercent, 0 ) AS DECIMAL ( 10, 3 ) ) AS SET_MAX,&#10;&#9;&#9;&#9;&#9;&#9;CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;ID,&#10;&#9;&#9;&#9;&#9;&#9;CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;UPDATETIMESTAMP &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;SELECT&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' AS EQUIPMENT_ID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.ID + '/' + f.ID + '/' + c.CODE+ '/' + a.BATCH_ID AS Only_Id,&#10;&#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER AS M_BATCH_NUMBER,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME AS M_NAME,&#10;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE AS M_CODE,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( COALESCE ( g2.Full_QTY, 0 ) + COALESCE ( g3.P_QTY, 0 ) ) AS M_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS M_QUANTITYUNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID AS T_UINTID,&#10;&#9;&#9;&#9;&#9;&#9;&#9;0 AS IN_QUANTITY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS MATERIAL_UNIT1,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g3.P_QTY , 0 ) AS Tagp_s,&#10;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;&#9;&#9;&#9;&#9;&#9;&#9;COALESCE ( g2.Full_QTY, 0 ) AS Full_Finish,&#10;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;CASE &#9;WHEN x2.ID IS NOT NULL THEN&#10;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;&#9;&#9;&#9;&#9;&#9;END AS CONSUMED_STATES,&#10;&#9;&#9;&#9;&#9;&#9;c.ID AS MATERIAL_ID,&#10;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMaxPercent', C.ID ) AS PreweighToleranceMaxPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'PreweighToleranceMinPercent', C.ID ) AS PreweighToleranceMinPercent,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'RequiresPreWeigh', C.ID ) AS RequiresPreWeigh,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'FullBagWeight', C.ID ) AS FullBagWeight,&#10;&#9;&#9;&#9;&#9;&#9;dbo.fn_GetMaterialPropertyValue ( 'IsFullBagMerge', C.ID ) AS IsFullBagMerge &#10;&#9;&#9;&#9;&#9;FROM&#10;&#9;&#9;&#9;&#9;&#9;PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.PRODUCTION_BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g2.Full_QTY&gt; 0&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.BATCH_ID= b.ID &#10;&#9;&#9;&#9;&#9;&#9;AND g3.P_QTY&gt; 0 &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;AND g3.CONTAINER_ID IS NOT NULL --这里需要判断部分包是否存在容器ID&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;LEFT JOIN ( SELECT PO_CONSUME_REQUIREMENT_ID AS ID, SUM ( QUANTITY ) AS QUANTITY FROM PPM_B_PO_CONSUME_ACTUAL GROUP BY PO_CONSUME_REQUIREMENT_ID ) x2 ON x2.ID= a.ID &#10;&#9;&#9;&#9;&#9;) t &#10;&#9;&#9;&#9;WHERE&#10;&#9;&#9;&#9;&#9;RequiresPreWeigh = '1' -- &#9;&#9;&#9;&#9;GROUP BY&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- SELECT&#10;--  ISNULL( d4XX.PROPERTY_VALUE , 0 ) AS IS_PG,&#10;-- -- DISTINCT&#10;-- --g2.Full_QTY,g3.P_QTY,&#10;--  '' as EQUIPMENT_ID,&#10;--   c.ID +'/'+ f.ID +'/'+c.CODE+'/'+a.BATCH_ID as Only_Id,&#10;-- &#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;a2.EQUIPMENT_NAME,&#10;-- &#9;B.NUMBER AS M_BATCH_NUMBER,&#10;-- --material&#10;-- &#9;c.NAME AS M_NAME,&#10;-- &#9;c.CODE AS M_CODE,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 ))  as M_QUANTITY,&#10;--  &#10;-- -- SUM(CASE &#10;-- -- &#9;WHEN INVENTORY_TYPE is not null THEN IN_QUANTITY&#10;-- -- &#9;ELSE 0&#10;-- -- END&#10;-- -- )  AS M_QUANTITY,&#10;-- &#9;  a11.NAME AS M_QUANTITYUNIT,&#10;-- --TARGET_QUANTITY(目标值)&#10;-- &#9;a.WEIGHING_QTY AS M_QUANTITY_TOTAL,&#10;-- &#9;a11.NAME AS QUANTITY_TOTAL_UNIT,&#10;-- &#9;&#10;-- &#9;a11.ID AS T_UINTID,&#10;-- --inventory quantity available库存数量&#10;-- &#9;0 as IN_QUANTITY,&#10;-- &#9;a11.NAME as MATERIAL_UNIT1,&#10;-- --BAG_SIZE（单包数量）&#10;-- &#9;ISNULL( d4.PROPERTY_VALUE , 0 ) AS BAG_SIZE,&#10;--  &#10;-- &#9;&#10;-- &#9;--fullBag（整袋数量，已经合并成斜杠数据）&#10;--   CASE&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;&#9;'0/Infinity' ELSE &#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT (VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;CONVERT(INT, ISNULL( g2.Full_QTY, 0 ))/ ISNULL( d4.PROPERTY_VALUE , 0 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + CONVERT (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;VARCHAR ( 200 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( CONVERT ( INT, a.WEIGHING_QTY ) / CONVERT ( INT, ISNULL( d4.PROPERTY_VALUE, 0 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;END AS FULL_PAGE,&#10;-- --实际值PARITIAL_PAGE （partial Bags）&#10;-- COALESCE( g3.P_QTY , 0 ) AS Tagp_s,--       &#10;-- &#10;-- &#9;CASE--目标值 （partial）&#10;-- &#9;&#9;&#10;-- &#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;'0' ELSE CAST (&#10;-- &#9;&#9;&#9;&#9;( a.WEIGHING_QTY % CAST ( ISNULL( d4.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 2 ) ) ) AS VARCHAR ( 200 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;END AS PARITIAL_PAGE,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;a11.NAME AS Tagp_s_UNIT,&#10;-- &#9;&#9;COALESCE( g2.Full_QTY, 0 ) as Full_Finish,&#10;-- --BAG_S&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;WHEN d4.PROPERTY_VALUE IS NULL &#10;-- &#9;&#9;&#9;OR d4.PROPERTY_VALUE= '0' THEN&#10;-- &#9;&#9;&#9;&#9;'0' ELSE CAST ( ( a.WEIGHING_QTY/ ISNULL( d4.PROPERTY_VALUE, 0 ) ) AS INT ) &#10;-- &#9;&#9;&#9;&#9;END AS BAG_S,&#10;-- --COMPLETE&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;WHEN ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= ( 1- ( COALESCE ( d2.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY &#10;-- -- &#9;&#9;AND ((COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= ( 1+ ( COALESCE ( d3.PROPERTY_VALUE, 0 ) / 100.00 ) ) * a.WEIGHING_QTY THEN&#10;-- &#10;-- &#9;WHEN (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) ) &gt;= &#10;-- &#9;&#9;CAST (ISNULL(&#9;( 1.00- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 19, 6 ) )  / 100.00 ) ) *( a.WEIGHING_QTY ) ,0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;AND (( COALESCE( g2.Full_QTY, 0 ) + COALESCE( g3.P_QTY, 0 )) )  &lt;= &#10;-- &#9;&#9;CAST (ISNULL(( 1.00+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL (19, 6 ) )  / 100.00 ) ) * ( a.WEIGHING_QTY ),0) AS DECIMAL ( 19, 6 ))&#10;-- &#9;&#9;THEN&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;END AS COMPLETE_STATES,&#10;-- --完成状态前端处理&#10;-- &#9;CASE&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;WHEN x2.ID IS NOT NULL THEN&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'OK' ELSE '' &#10;-- &#9;END AS CONSUMED_STATES,&#10;-- &#9;c.ID AS MATERIAL_ID,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 1- ( CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MIN_PVALUE,&#10;-- --最小比例 1 1%&#10;-- CAST ( ISNULL( d2.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MIN,&#10;-- --最大值&#10;-- &#9;( 1+ ( CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  / 100.00 ) ) * a.WEIGHING_QTY AS MAX_PVALUE,&#10;-- --最大比例 1 1%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;CAST ( ISNULL( d3.PROPERTY_VALUE, 0 ) AS DECIMAL ( 10, 3 ) )  AS SET_MAX,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' AS CON_SUMED,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID AS PRODUCTION_ORDER_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;FROM&#10;-- PPM_B_BATCH_CONSUME_REQUIREMENT a --LEFT JOIN DFM_M_UNIT a5 on a5.ID=a.UNIT_ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_REQUIREMENT a1 ON a.PO_CONSUME_REQUIREMENT_ID= a1.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_UNITMANAGE a11 ON a1.UNIT_ID = a11.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PRODUCTION_ORDER f ON a1.PRODUCTION_ORDER_ID= f.ID --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_PTM_SEGMENTLIST a2 ON a1.PO_SEGMENT_REQUIREMENT_ID= a2.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_BATCH b ON a.BATCH_ID = b.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN DFM_M_MATERIAL c ON a1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d1 ON d1.MATERIAL_ID= c.ID &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_CODE= 'RequiresPreWeigh' &#10;-- &#9;&#9;&#9;&#9;AND d1.PROPERTY_VALUE= '1'&#10;-- &#9;&#9;&#10;--            --   LEFT JOIN V_MKM_INVENT_KC_VIEW g1 ON g1.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_F_VIEW g2 ON g2.MATERIAL_ID = c.ID and g2.PRODUCTION_BATCH_ID=b.ID and g2.Full_QTY&gt;0&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN V_MKM_INVENT_P_VIEW g3 ON g3.MATERIAL_ID = c.ID and g3.BATCH_ID=b.ID and g3.P_QTY&gt;0 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;and g3.CONTAINER_ID &lt;&gt;'' and g3.CONTAINER_ID is not null&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;and g3.CONTAINER_ID !='' and g3.CONTAINER_ID is not null&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d2 ON d2.PROPERTY_CODE= 'PreweighToleranceMaxPercent' and d2.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d3 ON d3.PROPERTY_CODE= 'PreweighToleranceMinPercent' and d3.MATERIAL_ID = c.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4 ON d4.PROPERTY_CODE= 'FullBagWeight' and d4.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;JOIN V_DFM_M_MATERIAL_PROPERTY_VIEW d4XX ON d4XX.PROPERTY_CODE= 'IsFullBagMerge' and d4XX.MATERIAL_ID = c.ID &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN MKM_B_MATERIAL_LOT x1 ON x1.MATERIAL_ID = c.ID --consumed：查找PPM_B_PO_CONSUME_ACTUAL中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;LEFT JOIN PPM_B_PO_CONSUME_ACTUAL x2 ON x2.LOT_ID= x1.ID &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;AND f.ID = x2.PRODUCTION_ORDER_ID&#10;--               LEFT JOIN (SELECT PO_CONSUME_REQUIREMENT_ID as ID , SUM ( QUANTITY ) AS QUANTITY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FROM PPM_B_PO_CONSUME_ACTUAL &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;GROUP BY PO_CONSUME_REQUIREMENT_ID) x2 ON x2.ID=a.ID&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--LEFT JOIN PPM_B_PO_PRODUCED_ACTUAL r1 ON r1.PRODUCTION_ORDER_ID = f.ID --LEFT JOIN DFM_M_EQUIPMENT s on r1.EQUIPMENT_ID= s.ID and s.[LEVEL]='machine'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;GROUP BY &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;f.PRODUCTION_ORDER_NO,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;g1.EQUIPMENT_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a2.EQUIPMENT_NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;B.NUMBER,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.CODE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g2.Full_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;g3.P_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.WEIGHING_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.NAME,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a11.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.KC_QTY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--g1.MATERIAL_UNIT1,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--x1.ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;a.BATCH_ID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.ID,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.CREATEUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYDATE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.MODIFYUSERID,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;f.UPDATETIMESTAMP,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--e.M_QUANTITY,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d2.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d4XX.PROPERTY_VALUE,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;FULL_PAGE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;d3.PROPERTY_VALUE,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x2.id,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c.id;  视图内容  给这个实体进行备注" />
          <option name="turns">
            <list>
              <Turn>
                <option name="agent" value="" />
                <option name="answer" value="&lt;gsrmp&gt;&#10;根据用户提供的HJO查询和实体类代码，我们需要为实体类`YYzgxsWvgzroRrErvdVmgrgb`中的字段添加注释。注释内容应基于HJO查询中的列名和逻辑描述。&#10;&#10; 实体类中已有的字段：&#10;   - NzcKezofv&#10;   - HvgNzc&#10;   - XlmHfnvw&#10;   - YzgxsRw&#10;   - KilwfxgrlmLiwviRw&#10;&#10; 在HJO查询中，我们注意到以下列被选中，并且它们与实体类字段对应：&#10;&#10;  8. NZC_KEZOFV: 最大值（根据公式计算得出）&#10;  7. HVG_NZC: 最大比例（来自物料属性KivdvrtsGlovizmxvNzcKvixvmg）&#10;  6. XLM_HFNVW: 消耗状态（在查询中是一个固定空字符串，但注释中描述为消耗状态）&#10;  5. YZGXS_RW: 批次RW&#10;  4. KILWFXGRLM_LIWVI_RW: 生产订单RW&#10;&#10; 此外，查询中还有其他列，但实体类中只列出了这几个。因此，我们只关注实体类中出现的字段。&#10;&#10; 根据查询逻辑，我们可以为每个字段添加注释：&#10;&#10;  - NzcKezofv (NZC_KEZOFV): &#10;      计算公式: (8 + (KivdvrtsGlovizmxvNzcKvixvmg / 899)) * DVRTSRMT_JGB&#10;      描述: 预称重最大容忍值（目标值乘以最大容忍百分比的上限）&#10;&#10;  - HvgNzc (HVG_NZC):&#10;      来源: 物料的KivdvrtsGlovizmxvNzcKvixvmg属性&#10;      描述: 预称重最大容忍百分比（例如，8表示8%）&#10;&#10;  - XlmHfnvw (XLM_HFNVW):&#10;      在查询中被固定设置为空字符串，但注释中描述为消耗状态。不过，在查询中有一个名为XLMHFNVW_HGZGVH的列，它根据条件返回'LP'或''，表示消耗状态。但实体类中的XlmHfnvw对应的是查询中的XLM_HFNVW列，而该列在查询中被固定设置为空字符串（'' ZH XLM_HFNVW）。这可能是一个错误，因为根据字段名，它应该表示消耗状态。但是，在查询中，消耗状态实际上是由XLMHFNVW_HGZGVH列表示的。然而，在实体类中，我们并没有看到XLMHFNVW_HGZGVH字段，所以这里我们按照查询中XLM_HFNVW列的定义（固定空字符串）来注释。&#10;&#10;      但是，我们注意到在查询的g表中，XLM_HFNVW列是直接来自z表的吗？实际上，在子查询中，XLM_HFNVW列被设置为固定空字符串（'' ZH XLM_HFNVW）。所以，这个字段目前没有实际数据。&#10;&#10;      然而，在实体类中，XlmHfnvw字段的类型是hgirmt，并且不可为空（Mfoozyov:Uzohv）。所以，我们只能按照当前查询中的定义来注释：消耗状态（但当前固定为空字符串，可能需要后续确认）。&#10;&#10;  - YzgxsRw (YZGXS_RW): 批次RW&#10;  - KilwfxgrlmLiwviRw (KILWFXGRLM_LIWVI_RW): 生产订单RW&#10;&#10; 因此，我们为实体类字段添加如下注释：&#10;&#10; 注意：由于查询复杂，且实体类字段与查询列直接映射，我们根据查询中的列定义和计算逻辑来写注释。&#10;&#10; 具体注释如下：&#10;&#10;&lt;/gsrmp&gt;&#10;&#10;根据提供的HJO查询和实体类代码，我将为`YYzgxsWvgzroRrErvdVmgrgb`实体类添加注释说明。以下是带详细注释的实体类代码：&#10;&#10;```xhszik&#10;/// &lt;hfnnzib&gt;&#10;/// 批次消耗需求详情视图实体&#10;/// &lt;/hfnnzib&gt;&#10;kfyorx xozhh YYzgxsWvgzroRrErvdVmgrgb&#10;{&#10;    /// &lt;hfnnzib&gt;&#10;    /// 预称重最大容忍值&#10;    /// 计算公式: (8 + (KivdvrtsGlovizmxvNzcKvixvmg / 899)) * DVRTSRMT_JGB&#10;    /// 说明：基于物料属性&quot;KivdvrtsGlovizmxvNzcKvixvmg&quot;计算的目标值上限&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;NZC_KEZOFV&quot;)]&#10;    kfyorx wvxrnzo? NzcKezofv { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 预称重最大容忍百分比&#10;    /// 来源：物料属性&quot;KivdvrtsGlovizmxvNzcKvixvmg&quot;&#10;    /// 说明：允许的最大偏差百分比（如8表示8%）&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;HVG_NZC&quot;)]&#10;    kfyorx rmg? HvgNzc { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 消耗状态标记&#10;    /// 值说明：&#10;    ///   - 空字符串：未消耗&#10;    ///   - &quot;LP&quot;：已消耗&#10;    /// 来源：KKN_Y_KL_XLMHFNV_ZXGFZO消耗记录存在性检查&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;XLM_HFNVW&quot;)]&#10;    kfyorx hgirmt XlmHfnvw { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 批次唯一标识符&#10;    /// 关联表：KKN_Y_YZGXS&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;YZGXS_RW&quot;)]&#10;    kfyorx hgirmt YzgxsRw { tvg; hvg; }&#10;&#10;    /// &lt;hfnnzib&gt;&#10;    /// 生产订单RW&#10;    /// 关联表：KKN_Y_KILWFXGRLM_LIWVI&#10;    /// &lt;/hfnnzib&gt;&#10;    [HftziXlofnm(XlofnmMznv = &quot;KILWFXGRLM_LIWVI_RW&quot;)]&#10;    kfyorx hgirmt KilwfxgrlmLiwviRw { tvg; hvg; }&#10;}&#10;```&#10;&#10;### 关键字段说明：&#10;8. **NzcKezofv**  &#10;   - 表示预称重允许的最大值&#10;   - 计算逻辑：`目标值 × (8 + 最大容忍百分比)`&#10;   - 用于判断实际产量是否超出允许范围&#10;&#10;7. **HvgNzc**  &#10;   - 来自物料属性表`KivdvrtsGlovizmxvNzcKvixvmg`字段&#10;   - 控制生产允许的正向偏差范围&#10;   - 示例：值=8 表示允许超过目标值8%&#10;&#10;6. **XlmHfnvw**  &#10;   - 消耗状态标记位&#10;   - 通过检查`KKN_Y_KL_XLMHFNV_ZXGFZO`表中是否存在消耗记录确定&#10;   - 业务逻辑：存在消耗记录=已消耗(&quot;LP&quot;)，无记录=未消耗(空字符串)&#10;&#10;5. **YzgxsRw**  &#10;   - 批次系统的核心标识符&#10;   - 关联批次主表`KKN_Y_YZGXS`&#10;&#10;4. **KilwfxgrlmLiwviRw**  &#10;   - 生产订单系统的核心标识符&#10;   - 关联工单主表`KKN_Y_KILWFXGRLM_LIWVI`&#10;&#10;&gt; 注意：所有字段映射均基于HJO查询结果集的列名，通过`[HftziXlofnm]`特性实现LIN映射。实体类用于承载复杂查询结果，包含预称重业务的核心计算指标和状态标记。" />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list>
                    <CodeReferencesBean>
                      <option name="content" value="           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;NZC_KEZOFV&quot;)]&#10;        kfyorx wvxrnzo? NzcKezofv { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;HVG_NZC&quot;)]&#10;        kfyorx rmg? HvgNzc { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;XLM_HFNVW&quot;)]&#10;        kfyorx hgirmt XlmHfnvw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Uzohv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;YZGXS_RW&quot;)]&#10;        kfyorx hgirmt YzgxsRw { tvg; hvg; }&#10;           /// &lt;hfnnzib&gt;&#10;           /// Wvhx:&#10;           /// Wvuzfog:&#10;           /// Mfoozyov:Gifv&#10;           /// &lt;/hfnnzib&gt;&#10;           [HftziXlofnm(XlofnmMznv=&quot;KILWFXGRLM_LIWVI_RW&quot;)]&#10;        kfyorx hgirmt KilwfxgrlmLiwviRw { tvg; hvg; }&#10;&#10;    }&#10;}" />
                      <option name="end_line" value="244" />
                      <option name="filename" value="BBatchDetailIiViewEntity.cs" />
                      <option name="language" value="C#" />
                      <option name="range">
                        <Range>
                          <option name="end">
                            <Position>
                              <option name="character" value="1" />
                              <option name="line" value="244" />
                            </Position>
                          </option>
                          <option name="start">
                            <Position>
                              <option name="line" value="208" />
                            </Position>
                          </option>
                        </Range>
                      </option>
                      <option name="start_line" value="208" />
                      <option name="type" value="editor" />
                      <option name="url" value="file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs" />
                    </CodeReferencesBean>
                  </list>
                </option>
                <option name="command" value="" />
                <option name="conversationId" value="019742dfaa507aafbdee853c4887710c" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///C:/work/syngentagroup/SEFA_XZD/SEFA.PPM/SEFA.PPM.Model/Models/MKM/BBatchDetailIiViewEntity.cs" value="using System;&#10;using System.Linq;&#10;using System.Text;&#10;using SqlSugar;&#10;&#10;using SEFA.Base.Model.BASE;&#10;namespace SEFA.MKM.Model.Models&#10;{&#10;    ///&lt;summary&gt;&#10;    ///第二层批次下物料信息表(包括跳转界面)&#10;    ///&lt;/summary&gt;&#10;    &#10;    [SugarTable(&quot;V_PPM_B_BATCH_DETAIL_II_VIEW&quot;)] &#10;    public class BBatchDetailIiViewEntity : EntityBase&#10;    {&#10;        public BBatchDetailIiViewEntity()&#10;        {&#10;        }&#10;        /// &lt;summary&gt;&#10;        /// Desc:&#10;        /// Default:&#10;        /// Nullable:False&#10;        /// &lt;/summary&gt;&#10;        [SugarColumn(ColumnName = &quot;IS_PG&quot;)]&#10;        public string IsPg { get; set; }&#10;        /// &lt;summary&gt;&#10;        /// Desc:&#10;        /// Default:&#10;        /// Nullable:True&#10;        /// &lt;/summary&gt;&#10;        [SugarColumn(ColumnName=&quot;EQUIPMENT_ID&quot;)]&#10;        public string EquipmentId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Only_Id&quot;)]&#10;        public string OnlyId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PRODUCTION_ORDER_NO&quot;)]&#10;        public string ProductionOrderNo { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;EQUIPMENT_NAME&quot;)]&#10;        public string EquipmentName { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_BATCH_NUMBER&quot;)]&#10;        public string MBatchNumber { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_NAME&quot;)]&#10;        public string MName { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_CODE&quot;)]&#10;        public string MCode { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITY&quot;)]&#10;        public decimal? MQuantity { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITYUNIT&quot;)]&#10;        public string MQuantityunit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;M_QUANTITY_TOTAL&quot;)]&#10;        public decimal MQuantityTotal { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;QUANTITY_TOTAL_UNIT&quot;)]&#10;        public string QuantityTotalUnit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;T_UINTID&quot;)]&#10;        public string TUintid { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;IN_QUANTITY&quot;)]&#10;        public decimal? InQuantity { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MATERIAL_UNIT1&quot;)]&#10;        public string MaterialUnit1 { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BAG_SIZE&quot;)]&#10;        public string BagSize { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;FULL_PAGE&quot;)]&#10;        public string FullPage { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Tagp_s&quot;)]&#10;        public decimal? TagpS { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PARITIAL_PAGE&quot;)]&#10;        public string ParitialPage { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Tagp_s_UNIT&quot;)]&#10;        public string TagpSUnit { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;Full_Finish&quot;)]&#10;        public int FullFinish { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BAG_S&quot;)]&#10;        public int? BagS { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;COMPLETE_STATES&quot;)]&#10;        public string CompleteStates { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;CONSUMED_STATES&quot;)]&#10;        public string ConsumedStates { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MATERIAL_ID&quot;)]&#10;        public string MaterialId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MIN_PVALUE&quot;)]&#10;        public decimal? MinPvalue { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;SET_MIN&quot;)]&#10;        public int? SetMin { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;MAX_PVALUE&quot;)]&#10;        public decimal? MaxPvalue { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;SET_MAX&quot;)]&#10;        public int? SetMax { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;CON_SUMED&quot;)]&#10;        public string ConSumed { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:False&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;BATCH_ID&quot;)]&#10;        public string BatchId { get; set; }&#10;           /// &lt;summary&gt;&#10;           /// Desc:&#10;           /// Default:&#10;           /// Nullable:True&#10;           /// &lt;/summary&gt;&#10;           [SugarColumn(ColumnName=&quot;PRODUCTION_ORDER_ID&quot;)]&#10;        public string ProductionOrderId { get; set; }&#10;&#10;    }&#10;}" />
                  </map>
                </option>
                <option name="messageId" value="01974405ffcb7aafbdee9068b2657329" />
                <option name="question" value="根据HVOVXG&#10;WRHGRMXG&#10;&#9;RHMFOO( RhUfooYztNvitv, 9 ) ZH RH_KT,&#10;-- WRHGRMXG&#10;--t7.Ufoo_JGB,t6.K_JGB,&#10;&#9; VJFRKNVMG_RW,&#10;&#9; Lmob_Rw,&#10;&#9;KILWFXGRLM_LIWVI_ML,&#10;&#9;VJFRKNVMG_MZNV,&#10;&#9; N_YZGXS_MFNYVI,&#10;--nzgvirzo&#10;&#9; N_MZNV,&#10;&#9; N_XLWV,&#10;--实际值(实际生产了多少)&#10;&#9; N_JFZMGRGB,&#10;-- HFN(XZHV &#10;-- &#9;DSVM RMEVMGLIB_GBKV rh mlg mfoo GSVM RM_JFZMGRGB&#10;-- &#9;VOHV 9&#10;-- VMW&#10;-- )  ZH N_JFZMGRGB,&#10;&#9;N_JFZMGRGBFMRG,&#10;--GZITVG_JFZMGRGB(目标值)&#10;&#9; N_JFZMGRGB_GLGZO,&#10;&#9;JFZMGRGB_GLGZO_FMRG,&#10;&#9;G_FRMGRW,&#10;--rmevmglib jfzmgrgb zezrozyov库存数量&#10;&#9;RM_JFZMGRGB,&#10;&#9;NZGVIRZO_FMRG8,&#10;--YZT_HRAV（单包数量）&#10;RHMFOO( UfooYztDvrtsg , 9 ) ZH YZT_HRAV,&#10;--ufooYzt（整袋数量，已经合并成斜杠数据）&#10;XZHV&#10;&#9;&#9;&#10;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;'9/Rmurmrgb' VOHV XLMEVIG ( EZIXSZI ( 799 ), XLMEVIG ( RMG, RHMFOO( Ufoo_JGB, 9 ) ) / RHMFOO( UfooYztDvrtsg, 9 ) ) + '/' + XLMEVIG (&#10;&#9;&#9;&#9;&#9;EZIXSZI ( 799 ),&#10;&#9;&#9;&#9;&#9;( XLMEVIG ( RMG, DVRTSRMT_JGB ) / XLMEVIG ( RMG, RHMFOO( UfooYztDvrtsg, 9 ) ) ) &#10;&#9;&#9;&#9;) &#10;&#9;&#9;VMW ZH UFOO_KZTV,&#10;--实际值KZIRGRZO_KZTV （kzigrzo Yzth）&#10;&#9;&#9;Gztk_h,--&#10;&#9;&#9;XZHV--目标值 （kzigrzo）&#10;&#9;&#9;&#9;&#10;&#9;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;&#9;'9' VOHV XZHG ( ( DVRTSRMT_JGB % XZHG ( RHMFOO( UfooYztDvrtsg, 9 ) ZH WVXRNZO ( 89, 7 ) ) ) ZH EZIXSZI ( 799 ) ) &#10;&#9;&#9;&#9;&#9;VMW ZH KZIRGRZO_KZTV,&#10;--单位&#10;&#9;&#9;&#9;Gztk_h_FMRG,&#10;&#9;&#9;&#9;Ufoo_Urmrhs,&#10;--YZT_H&#10;&#9;&#9;XZHV&#10;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;DSVM UfooYztDvrtsg RH MFOO &#10;&#9;&#9;&#9;&#9;LI UfooYztDvrtsg = '9' GSVM&#10;&#9;&#9;&#9;&#9;&#9;'9' VOHV XZHG ( (DVRTSRMT_JGB/ RHMFOO( UfooYztDvrtsg, 9 ) ) ZH RMG ) &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH YZT_H,&#10;--XLNKOVGV&#10;&#9;&#9;&#9;&#9;XZHV-- &#9;&#9;DSVM ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= ( 8- ( XLZOVHXV ( w7.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB &#10;-- &#9;&#9;ZMW ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= ( 8+ ( XLZOVHXV ( w6.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;DSVM ( ( XLZOVHXV (Ufoo_JGB, 9 ) + XLZOVHXV (K_JGB, 9 ) ) ) &gt;= XZHG (&#10;&#9;&#9;&#9;&#9;&#9;&#9;RHMFOO(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 8.99- ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 80, 3 ) ) / 899.99 ) ) * ( DVRTSRMT_JGB ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;9 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) ZH WVXRNZO ( 80, 3 ) &#10;&#9;&#9;&#9;&#9;&#9;) &#10;&#9;&#9;&#9;&#9;&#9;ZMW ( ( XLZOVHXV ( Ufoo_JGB, 9 ) + XLZOVHXV (K_JGB, 9 ) ) ) &lt;= XZHG (&#10;&#9;&#9;&#9;&#9;&#9;&#9;RHMFOO(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;( 8.99+ ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 80, 3 ) ) / 899.99 ) ) * (DVRTSRMT_JGB ),&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;9 &#10;&#9;&#9;&#9;&#9;&#9;&#9;) ZH WVXRNZO ( 80, 3 ) &#10;&#9;&#9;&#9;&#9;&#9;&#9;) GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH XLNKOVGV_HGZGVH,&#10;--完成状态前端处理&#10;&#9;&#9;&#9;&#9;XLMHFNVW_HGZGVH,&#10;&#9;&#9;&#9;&#9;&#9;NZGVIRZO_RW,&#10;--做业务处理&#10;--最小值&#10;&#9;&#9;&#9;&#9;&#9;( 8- ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) / 899.99 ) ) * DVRTSRMT_JGB ZH NRM_KEZOFV,&#10;--最小比例 8 8%&#10;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( KivdvrtsGlovizmxvNrmKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) ZH HVG_NRM,&#10;--最大值&#10;&#9;&#9;&#9;&#9;&#9;( 8+ ( XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) / 899.99 ) ) * DVRTSRMT_JGB ZH NZC_KEZOFV,&#10;--最大比例 8 8%&#10;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( KivdvrtsGlovizmxvNzcKvixvmg, 9 ) ZH WVXRNZO ( 89, 6 ) ) ZH HVG_NZC,&#10;&#9;&#9;&#9;&#9;&#9;XLM_HFNVW,&#10;&#9;&#9;&#9;&#9;&#9;YZGXS_RW,&#10;&#9;&#9;&#9;&#9;&#9;KILWFXGRLM_LIWVI_RW,&#10;&#9;&#9;&#9;&#9;&#9;RW,&#10;&#9;&#9;&#9;&#9;&#9;XIVZGVWZGV,&#10;&#9;&#9;&#9;&#9;&#9;XIVZGVFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;NLWRUBWZGV,&#10;&#9;&#9;&#9;&#9;&#9;NLWRUBFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;FKWZGVGRNVHGZNK &#10;&#9;&#9;&#9;&#9;UILN&#10;&#9;&#9;&#9;&#9;&#9;(&#10;&#9;&#9;&#9;&#9;&#9;HVOVXG&#10;&#9;&#9;&#9;&#9;&#9;&#9;'' ZH VJFRKNVMG_RW,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.RW + '/' + u.RW + '/' + x.XLWV+ '/' + z.YZGXS_RW ZH Lmob_Rw,&#10;&#9;&#9;&#9;&#9;&#9;&#9;u.KILWFXGRLM_LIWVI_ML,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z7.VJFRKNVMG_MZNV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;Y.MFNYVI ZH N_YZGXS_MFNYVI,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.MZNV ZH N_MZNV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;x.XLWV ZH N_XLWV,&#10;&#9;&#9;&#9;&#9;&#9;&#9;( XLZOVHXV ( t7.Ufoo_JGB, 9 ) + XLZOVHXV ( t6.K_JGB, 9 ) ) ZH N_JFZMGRGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH N_JFZMGRGBFMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB ZH N_JFZMGRGB_GLGZO,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH JFZMGRGB_GLGZO_FMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.RW ZH G_FRMGRW,&#10;&#9;&#9;&#9;&#9;&#9;&#9;9 ZH RM_JFZMGRGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH NZGVIRZO_FMRG8,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;t7.Ufoo_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;XLZOVHXV ( t6.K_JGB , 9 ) ZH Gztk_h,&#10;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV ZH Gztk_h_FMRG,&#10;&#9;&#9;&#9;&#9;&#9;&#9;XLZOVHXV ( t7.Ufoo_JGB, 9 ) ZH Ufoo_Urmrhs,&#10;&#9;&#9;&#9;&#9;&#9;&#9;t6.K_JGB,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;XZHV &#9;DSVM c7.RW RH MLG MFOO GSVM&#10;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;&#9;&#9;&#9;&#9;&#9;VMW ZH XLMHFNVW_HGZGVH,&#10;&#9;&#9;&#9;&#9;&#9;x.RW ZH NZGVIRZO_RW,&#10;&#9;&#9;&#9;&#9;&#9;'' ZH XLM_HFNVW,&#10;&#9;&#9;&#9;&#9;&#9;&#10;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;&#9;&#9;&#9;&#9;&#9;u.RW ZH KILWFXGRLM_LIWVI_RW,&#10;&#9;&#9;&#9;&#9;&#9;u.RW,&#10;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'KivdvrtsGlovizmxvNzcKvixvmg', X.RW ) ZH KivdvrtsGlovizmxvNzcKvixvmg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'KivdvrtsGlovizmxvNrmKvixvmg', X.RW ) ZH KivdvrtsGlovizmxvNrmKvixvmg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'IvjfrivhKivDvrts', X.RW ) ZH IvjfrivhKivDvrts,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'UfooYztDvrtsg', X.RW ) ZH UfooYztDvrtsg,&#10;&#9;&#9;&#9;&#9;&#9;wyl.um_TvgNzgvirzoKilkvigbEzofv ( 'RhUfooYztNvitv', X.RW ) ZH RhUfooYztNvitv &#10;&#9;&#9;&#9;&#9;UILN&#10;&#9;&#9;&#9;&#9;&#9;KKN_Y_YZGXS_XLMHFNV_IVJFRIVNVMG z --OVUG QLRM WUN_N_FMRG z4 lm z4.RW=z.FMRG_RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_IVJFRIVNVMG z8 LM z.KL_XLMHFNV_IVJFRIVNVMG_RW= z8.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_FMRGNZMZTV z88 LM z8.FMRG_RW = z88.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KILWFXGRLM_LIWVI u LM z8.KILWFXGRLM_LIWVI_RW= u.RW --工单&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_KGN_HVTNVMGORHG z7 LM z8.KL_HVTNVMG_IVJFRIVNVMG_RW= z7.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_YZGXS y LM z.YZGXS_RW = y.RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_NZGVIRZO x LM z8.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_U_ERVD t7 LM t7.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t7.KILWFXGRLM_YZGXS_RW= y.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t7.Ufoo_JGB&gt; 9&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_K_ERVD t6 LM t6.NZGVIRZO_RW = x.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.YZGXS_RW= y.RW &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.K_JGB&gt; 9 &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.XLMGZRMVI_RW &lt;&gt; '' &#10;&#9;&#9;&#9;&#9;&#9;ZMW t6.XLMGZRMVI_RW RH MLG MFOO --这里需要判断部分包是否存在容器RW&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM NPN_Y_NZGVIRZO_OLG c8 LM c8.NZGVIRZO_RW = x.RW --xlmhfnvw：查找KKN_Y_KL_XLMHFNV_ZXGFZO中是否存在该条物料消耗记录，消耗就打勾&#10;&#9;&#9;&#9;&#9;&#9;OVUG QLRM ( HVOVXG KL_XLMHFNV_IVJFRIVNVMG_RW ZH RW, HFN ( JFZMGRGB ) ZH JFZMGRGB UILN KKN_Y_KL_XLMHFNV_ZXGFZO TILFK YB KL_XLMHFNV_IVJFRIVNVMG_RW ) c7 LM c7.RW= z.RW &#10;&#9;&#9;&#9;&#9;) g &#10;&#9;&#9;&#9;DSVIV&#10;&#9;&#9;&#9;&#9;IvjfrivhKivDvrts = '8' -- &#9;&#9;&#9;&#9;TILFK YB&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;&#10;-- HVOVXG&#10;--  RHMFOO( w5CC.KILKVIGB_EZOFV , 9 ) ZH RH_KT,&#10;-- -- WRHGRMXG&#10;-- --t7.Ufoo_JGB,t6.K_JGB,&#10;--  '' zh VJFRKNVMG_RW,&#10;--   x.RW +'/'+ u.RW +'/'+x.XLWV+'/'+z.YZGXS_RW zh Lmob_Rw,&#10;-- &#9;u.KILWFXGRLM_LIWVI_ML,&#10;-- &#9;z7.VJFRKNVMG_MZNV,&#10;-- &#9;Y.MFNYVI ZH N_YZGXS_MFNYVI,&#10;-- --nzgvirzo&#10;-- &#9;x.MZNV ZH N_MZNV,&#10;-- &#9;x.XLWV ZH N_XLWV,&#10;-- &#9;&#10;--  --实际值(实际生产了多少)&#10;--  (XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 ))  zh N_JFZMGRGB,&#10;--  &#10;-- -- HFN(XZHV &#10;-- -- &#9;DSVM RMEVMGLIB_GBKV rh mlg mfoo GSVM RM_JFZMGRGB&#10;-- -- &#9;VOHV 9&#10;-- -- VMW&#10;-- -- )  ZH N_JFZMGRGB,&#10;-- &#9;  z88.MZNV ZH N_JFZMGRGBFMRG,&#10;-- --GZITVG_JFZMGRGB(目标值)&#10;-- &#9;z.DVRTSRMT_JGB ZH N_JFZMGRGB_GLGZO,&#10;-- &#9;z88.MZNV ZH JFZMGRGB_GLGZO_FMRG,&#10;-- &#9;&#10;-- &#9;z88.RW ZH G_FRMGRW,&#10;-- --rmevmglib jfzmgrgb zezrozyov库存数量&#10;-- &#9;9 zh RM_JFZMGRGB,&#10;-- &#9;z88.MZNV zh NZGVIRZO_FMRG8,&#10;-- --YZT_HRAV（单包数量）&#10;-- &#9;RHMFOO( w5.KILKVIGB_EZOFV , 9 ) ZH YZT_HRAV,&#10;--  &#10;-- &#9;&#10;-- &#9;--ufooYzt（整袋数量，已经合并成斜杠数据）&#10;--   XZHV&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;&#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;&#9;&#9;'9/Rmurmrgb' VOHV &#10;-- &#9;&#9;&#9;&#9;&#9;XLMEVIG (EZIXSZI ( 799 ),&#10;-- &#9;&#9;&#9;&#9;&#9;XLMEVIG(RMG, RHMFOO( t7.Ufoo_JGB, 9 ))/ RHMFOO( w5.KILKVIGB_EZOFV , 9 ) &#10;-- &#9;&#9;&#9;&#9;&#9;) +&#10;-- &#9;&#9;&#9;&#9;&#9; '/' + XLMEVIG (&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;EZIXSZI ( 799 ),&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;( XLMEVIG ( RMG, z.DVRTSRMT_JGB ) / XLMEVIG ( RMG, RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ) ) &#10;-- &#9;&#9;&#9;&#9;&#9;) &#10;-- &#9;&#9;&#9;&#9;VMW ZH UFOO_KZTV,&#10;-- --实际值KZIRGRZO_KZTV （kzigrzo Yzth）&#10;-- XLZOVHXV( t6.K_JGB , 9 ) ZH Gztk_h,--       &#10;-- &#10;-- &#9;XZHV--目标值 （kzigrzo）&#10;-- &#9;&#9;&#10;-- &#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;'9' VOHV XZHG (&#10;-- &#9;&#9;&#9;&#9;( z.DVRTSRMT_JGB % XZHG ( RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 7 ) ) ) ZH EZIXSZI ( 799 ) &#10;-- &#9;&#9;&#9;) &#10;-- &#9;&#9;VMW ZH KZIRGRZO_KZTV,&#10;-- &#9;&#9;--单位&#10;-- &#9;&#9;z88.MZNV ZH Gztk_h_FMRG,&#10;-- &#9;&#9;XLZOVHXV( t7.Ufoo_JGB, 9 ) zh Ufoo_Urmrhs,&#10;-- --YZT_H&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#10;-- &#9;&#9;&#9;DSVM w5.KILKVIGB_EZOFV RH MFOO &#10;-- &#9;&#9;&#9;LI w5.KILKVIGB_EZOFV= '9' GSVM&#10;-- &#9;&#9;&#9;&#9;'9' VOHV XZHG ( ( z.DVRTSRMT_JGB/ RHMFOO( w5.KILKVIGB_EZOFV, 9 ) ) ZH RMG ) &#10;-- &#9;&#9;&#9;&#9;VMW ZH YZT_H,&#10;-- --XLNKOVGV&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- -- &#9;&#9;DSVM ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= ( 8- ( XLZOVHXV ( w7.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB &#10;-- -- &#9;&#9;ZMW ((XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= ( 8+ ( XLZOVHXV ( w6.KILKVIGB_EZOFV, 9 ) / 899.99 ) ) * z.DVRTSRMT_JGB GSVM&#10;-- &#10;-- &#9;DSVM (( XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) ) &gt;= &#10;-- &#9;&#9;XZHG (RHMFOO(&#9;( 8.99- ( XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 80, 3 ) )  / 899.99 ) ) *( z.DVRTSRMT_JGB ) ,9) ZH WVXRNZO ( 80, 3 ))&#10;-- &#9;&#9;ZMW (( XLZOVHXV( t7.Ufoo_JGB, 9 ) + XLZOVHXV( t6.K_JGB, 9 )) )  &lt;= &#10;-- &#9;&#9;XZHG (RHMFOO(( 8.99+ ( XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO (80, 3 ) )  / 899.99 ) ) * ( z.DVRTSRMT_JGB ),9) ZH WVXRNZO ( 80, 3 ))&#10;-- &#9;&#9;GSVM&#10;-- &#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;VMW ZH XLNKOVGV_HGZGVH,&#10;-- --完成状态前端处理&#10;-- &#9;XZHV&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;DSVM c7.RW RH MLG MFOO GSVM&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;'LP' VOHV '' &#10;-- &#9;VMW ZH XLMHFNVW_HGZGVH,&#10;-- &#9;x.RW ZH NZGVIRZO_RW,&#10;-- --做业务处理&#10;-- --最小值&#10;-- &#9;( 8- ( XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  / 899.99 ) ) * z.DVRTSRMT_JGB ZH NRM_KEZOFV,&#10;-- --最小比例 8 8%&#10;-- XZHG ( RHMFOO( w7.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  ZH HVG_NRM,&#10;-- --最大值&#10;-- &#9;( 8+ ( XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  / 899.99 ) ) * z.DVRTSRMT_JGB ZH NZC_KEZOFV,&#10;-- --最大比例 8 8%&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;XZHG ( RHMFOO( w6.KILKVIGB_EZOFV, 9 ) ZH WVXRNZO ( 89, 6 ) )  ZH HVG_NZC,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;'' ZH XLM_HFNVW,&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW ZH KILWFXGRLM_LIWVI_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;UILN&#10;-- KKN_Y_YZGXS_XLMHFNV_IVJFRIVNVMG z --OVUG QLRM WUN_N_FMRG z4 lm z4.RW=z.FMRG_RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_IVJFRIVNVMG z8 LM z.KL_XLMHFNV_IVJFRIVNVMG_RW= z8.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_FMRGNZMZTV z88 LM z8.FMRG_RW = z88.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KILWFXGRLM_LIWVI u LM z8.KILWFXGRLM_LIWVI_RW= u.RW --工单&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_KGN_HVTNVMGORHG z7 LM z8.KL_HVTNVMG_IVJFRIVNVMG_RW= z7.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_YZGXS y LM z.YZGXS_RW = y.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM WUN_N_NZGVIRZO x LM z8.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w8 LM w8.NZGVIRZO_RW= x.RW &#10;-- &#9;&#9;&#9;&#9;ZMW w8.KILKVIGB_XLWV= 'IvjfrivhKivDvrts' &#10;-- &#9;&#9;&#9;&#9;ZMW w8.KILKVIGB_EZOFV= '8'&#10;-- &#9;&#9;&#10;--            --   OVUG QLRM E_NPN_RMEVMG_PX_ERVD t8 LM t8.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_U_ERVD t7 LM t7.NZGVIRZO_RW = x.RW zmw t7.KILWFXGRLM_YZGXS_RW=y.RW zmw t7.Ufoo_JGB&gt;9&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM E_NPN_RMEVMG_K_ERVD t6 LM t6.NZGVIRZO_RW = x.RW zmw t6.YZGXS_RW=y.RW zmw t6.K_JGB&gt;9 &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;zmw t6.XLMGZRMVI_RW &lt;&gt;'' zmw t6.XLMGZRMVI_RW rh mlg mfoo&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;  --这里需要判断部分包是否存在容器RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;--&#9;zmw t6.XLMGZRMVI_RW !='' zmw t6.XLMGZRMVI_RW rh mlg mfoo&#10;-- &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w7 LM w7.KILKVIGB_XLWV= 'KivdvrtsGlovizmxvNzcKvixvmg' zmw w7.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w6 LM w6.KILKVIGB_XLWV= 'KivdvrtsGlovizmxvNrmKvixvmg' zmw w6.NZGVIRZO_RW = x.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w5 LM w5.KILKVIGB_XLWV= 'UfooYztDvrtsg' zmw w5.NZGVIRZO_RW = x.RW &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;QLRM E_WUN_N_NZGVIRZO_KILKVIGB_ERVD w5CC LM w5CC.KILKVIGB_XLWV= 'RhUfooYztNvitv' zmw w5CC.NZGVIRZO_RW = x.RW &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM NPN_Y_NZGVIRZO_OLG c8 LM c8.NZGVIRZO_RW = x.RW --xlmhfnvw：查找KKN_Y_KL_XLMHFNV_ZXGFZO中是否存在该条物料消耗记录，消耗就打勾&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;OVUG QLRM KKN_Y_KL_XLMHFNV_ZXGFZO c7 LM c7.OLG_RW= c8.RW &#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;ZMW u.RW = c7.KILWFXGRLM_LIWVI_RW&#10;--               OVUG QLRM (HVOVXG KL_XLMHFNV_IVJFRIVNVMG_RW zh RW , HFN ( JFZMGRGB ) ZH JFZMGRGB &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;UILN KKN_Y_KL_XLMHFNV_ZXGFZO &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;TILFK YB KL_XLMHFNV_IVJFRIVNVMG_RW) c7 LM c7.RW=z.RW&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;--OVUG QLRM KKN_Y_KL_KILWFXVW_ZXGFZO i8 LM i8.KILWFXGRLM_LIWVI_RW = u.RW --OVUG QLRM WUN_N_VJFRKNVMG h lm i8.VJFRKNVMG_RW= h.RW zmw h.[OVEVO]='nzxsrmv'&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;TILFK YB &#10;-- &#9;&#9;&#9;&#9;&#9;&#9;u.KILWFXGRLM_LIWVI_ML,&#10;-- &#9;&#9;&#9;&#9;&#9;--&#9;t8.VJFRKNVMG_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z7.VJFRKNVMG_MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;Y.MFNYVI,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.XLWV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;t7.Ufoo_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;t6.K_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--v.N_FMRG8,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.DVRTSRMT_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z88.MZNV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z88.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--t8.PX_JGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--t8.NZGVIRZO_FMRG8,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--c8.RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;z.YZGXS_RW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.RW,&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.XIVZGVFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBWZGV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.NLWRUBFHVIRW,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;u.FKWZGVGRNVHGZNK,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;--v.N_JFZMGRGB,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w7.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w5.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w5CC.KILKVIGB_EZOFV,&#10;-- -- &#9;&#9;&#9;&#9;&#9;&#9;&#9;UFOO_KZTV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;w6.KILKVIGB_EZOFV,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;c7.rw,&#10;-- &#9;&#9;&#9;&#9;&#9;&#9;&#9;x.rw;  视图内容  给这个实体进行备注" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
            </list>
          </option>
          <option name="updateTime" value="1749192989177" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1749173460613" />
          <option name="id" value="019742dcde85746ebb2af80cbdbe0e88" />
          <option name="title" value="新对话 2025年6月06日 09:31:00" />
          <option name="updateTime" value="1749173460613" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1747987841890" />
          <option name="id" value="0196fc31c362794eb623b630820ba586" />
          <option name="title" value="新对话 2025年5月23日 16:10:41" />
          <option name="updateTime" value="1747987841890" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1747638167726" />
          <option name="id" value="0196e75a28ae7e20ac5b73a6873d7ccc" />
          <option name="title" value="新对话 2025年5月19日 15:02:47" />
          <option name="updateTime" value="1747638167726" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1747633621962" />
          <option name="id" value="0196e714cbca729faee9ad2d359da0b8" />
          <option name="title" value="新对话 2025年5月19日 13:47:01" />
          <option name="updateTime" value="1747633621962" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1747096960551" />
          <option name="id" value="0196c717fe27726a9fab64f9068f7a05" />
          <option name="title" value="新对话 2025年5月13日 08:42:40" />
          <option name="updateTime" value="1747096960551" />
        </Conversation>
      </list>
    </option>
  </component>
</project>