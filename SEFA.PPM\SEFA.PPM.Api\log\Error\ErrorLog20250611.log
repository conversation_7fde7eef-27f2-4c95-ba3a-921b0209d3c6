2025-06-11 10:18:55.778 +08:00 [DBG] 登录超时退出
2025-06-11 10:18:55.778 +08:00 [DBG] --------------------------------
2025/6/11 10:18:55|
【当前操作用户】：admin 
【当前执行方法】：GetPageListByBatchDetailMaterial 
【携带的参数有】： SEFA.MKM.Model.ViewModels.BBatchDetailMaterialViewRequestModel, False, False 
【执行完成结果】：方法中出现异常：Object reference not set to an instance of an object.


2025-06-11 10:18:55.868 +08:00 [ERR] 
2025-06-11 10:18:55.868 +08:00 [ERR] 
2025-06-11 10:18:57.317 +08:00 [INF] --------------------------------
2025/6/11 10:18:57|
RequestID:1fe47b1f-8646-48c8-9d00-c78c87bbc949
Response Data:
{"status":401,"success":true,"msg":"很抱歉，您无权访问该接口，请确保已经登录!","msgDev":null,"response":null}

2025-06-11 10:18:57.317 +08:00 [ERR] 
2025-06-11 10:18:57.833 +08:00 [ERR] Object reference not set to an instance of an object.
【自定义错误】：Object reference not set to an instance of an object. 
【异常类型】：NullReferenceException 
【异常信息】：Object reference not set to an instance of an object. 
【堆栈调用】：   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListMaterialPreTop(BBatchDetailMaterialViewRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 159
   at lambda_method1246(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.NullReferenceException: Object reference not set to an instance of an object.
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListMaterialPreTop(BBatchDetailMaterialViewRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 159
   at lambda_method1246(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-11 10:18:57.859 +08:00 [ERR] Object reference not set to an instance of an object.
【自定义错误】：Object reference not set to an instance of an object. 
【异常类型】：NullReferenceException 
【异常信息】：Object reference not set to an instance of an object. 
【堆栈调用】：   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.ContainerSelectList(String proOrderID, String batchID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 246
   at lambda_method1253(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.NullReferenceException: Object reference not set to an instance of an object.
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.ContainerSelectList(String proOrderID, String batchID) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 246
   at lambda_method1253(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-11 10:18:57.834 +08:00 [ERR] Object reference not set to an instance of an object.
【自定义错误】：Object reference not set to an instance of an object. 
【异常类型】：NullReferenceException 
【异常信息】：Object reference not set to an instance of an object. 
【堆栈调用】：   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByBatchIDSII(BBatchDetailIIModel model) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 368
   at lambda_method1133(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.NullReferenceException: Object reference not set to an instance of an object.
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetPageListByBatchIDSII(BBatchDetailIIModel model) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 368
   at lambda_method1133(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
2025-06-11 10:18:57.885 +08:00 [ERR] Object reference not set to an instance of an object.
【自定义错误】：Object reference not set to an instance of an object. 
【异常类型】：NullReferenceException 
【异常信息】：Object reference not set to an instance of an object. 
【堆栈调用】：   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetNewDown(BBatchDetailMaterialViewRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 182
   at lambda_method1260(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
System.NullReferenceException: Object reference not set to an instance of an object.
   at tqiXw8qosNj3GEtmrgh.EK3VifqUeWPIoG9MHQ5.AwaitTaskWithPostActionAndFinallyAndGetResult[T](Task`1 actualReturnValue, Func`2 postAction, Action`1 finalAction)
   at SEFA.MKMApi.Controllers.MaterialPreparationViewController.GetNewDown(BBatchDetailMaterialViewRequestModel reqModel) in C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\MaterialPreparationViewController.cs:line 182
   at lambda_method1260(Closure , Object )
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Logged|12_1(ControllerActionInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextExceptionFilterAsync>g__Awaited|26_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
