{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\index.vue?vue&type=style&index=0&id=30ddf032&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\index.vue", "mtime": 1749177894582}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lbC1mb3JtLWl0ZW0tLXNtYWxsLmVsLWZvcm0taXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMHB4Owp9CgoubXQtOHAgewogIG1hcmdpbi10b3A6IDhweDsKfQoKLnBkLWxlZnQgewogIHBhZGRpbmctbGVmdDogNXB4Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoNA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/planManagement/weekSchedule", "sourcesContent": ["<!--\n * @Descripttion: (周计划/PPM_B_WEEK_SCHEDULE)\n * @version: (1.0)\n * @Author: (SECI)\n * @Date: (2025-04-10)\n * @LastEditors: (SECI)\n * @LastEditTime: (2025-04-10)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n            \t\t\t\t    \n        <el-form-item label=\"工厂\" class=\"mb-2\">\n          <!--el-select v-model=\"searchForm.factory\" placeholder=\"请选择工厂\">\n            <el-option v-for=\"item in  sys_classify_type \" :key=\"item.dictValue\" :label=\"item.dictLabel\" :value=\"item.dictValue\"></el-option>\n          </!--el-select-->\n          <el-input clearable v-model=\"searchForm.factory\" placeholder=\"请输入工厂\" />\n        </el-form-item>\n        <el-form-item label=\"车间\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.workshop\" placeholder=\"请输入车间\" />\n        </el-form-item>\n        <el-form-item label=\"产线\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.lineCode\" placeholder=\"请输入产线\" />\n        </el-form-item>\n        <el-form-item label=\"物料代码\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.materialCode\" placeholder=\"请输入物料代码\" />\n        </el-form-item>\n        <el-form-item label=\"计划编号\" class=\"mb-2\">\n          <el-input clearable v-model=\"searchForm.orderNo\" placeholder=\"请输入计划编号\" />\n        </el-form-item>\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n        <el-form-item>\n          <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">\n            {{ $t('GLOBAL._XZ') }}\n          </el-button>\n        </el-form-item>\n        <upload-button :option=\"buttonOption\" :searchForm=\"searchForm\" ref=\"uploadButton\"></upload-button>\n\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"bomDialog(scope.row)\">{{ $t('GLOBAL._BOM') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>          \n          </template>\n        </el-table-column>\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20,50,100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n    <BomDetail ref=\"bomDetail\" />\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\nimport FormDialog from './form-dialog'\nimport BomDetail from './bomDetail.vue'\nimport {delWeekSchedule, getWeekScheduleList} from \"@/api/planManagement/weekSchedule\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\nimport UploadButton from \"@/components/UploadButton.vue\";\n\nexport default {\n  name: 'index',\n  components: {\n    UploadButton,\n    FormDialog,\n    BomDetail\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('WeekSchedule.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n        {code: 'Factory', width: 100, align: 'center'},\n        {code: 'Workshop', width: 150, align: 'center'},\n        {code: 'Category', width: 150, align: 'center'},\n        {code: 'DesignCode', width: 150, align: 'center'},\n        {code: 'FinishWorkday', width: 180, align: 'center'},\n        {code: 'LineCode', width: 150, align: 'center'},\n        {code: 'FinishShift', width: 100, align: 'center'},\n        {code: 'MaterialCode', width: 100, align: 'center'},\n        {code: 'MaterialName', width: 180, align: 'center'},\n        {code: 'OrderNo', width: 150, align: 'center'},\n        {code: 'Output', width: 100, align: 'center'},\n        {code: 'PackSize', width: 150, align: 'center'},\n        {code: 'PlanQuantity', width: 100, align: 'center'},\n        {code: 'Remark', width: 150, align: 'center'},\n        {code: 'SapOrderNo', width: 150, align: 'center'},\n        {code: 'StartWorkday', width: 150, align: 'center'},\n        {code: 'StartShift', width: 100, align: 'center'},\n        {code: 'Status', width: 100, align: 'center'},\n        {code: 'Type', width: 100, align: 'center'},\n        {code: 'Unit', width: 100, align: 'center'},\n        {code: 'WorkCenter', width: 100, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'周计划',\n        serveIp:'baseURL_PPM',\n        uploadUrl:'/ppm/WeekSchedule/ImportData', //导入\n        //exportUrl:'/ppm/WeekSchedule/ExportData', //导出\n        DownLoadUrl:'/ppm/WeekSchedule/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      this.tableName = getTableHead(this.hansObj, this.tableOption)\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n    bomDialog(row) {\n      this.$refs.bomDetail.show(row)\n    },\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delWeekSchedule([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getWeekScheduleList(this.searchForm).then(res => {\n        console.log(res);\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}