<template>
    <div class="usemystyle MyPartialBag">
        <div class="tabinputbox">
            <div class="tabinputsinglebox">
                <el-input size="mini" ref="autoFocus" :placeholder="$t('Consume.SSCC')" @change="getSSCC()" v-model="sscc" :autofocus="true">
                    <template slot="append"><i class="el-icon-full-screen"></i></template>
                </el-input>
            </div>
            <div class="tabinputsinglebox">
                <el-select v-model="printer" disabled :placeholder="$t('MaterialPreparationBuild.ChooseScale')">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
            </div>
            <div class="tabinputsinglebox">
                <el-checkbox
                    :disabled="
                        (Math.floor(detailobj.MQuantity / Number(detailobj.BagSize)) == 0
                            ? detailobj.MQuantity >= detailobj.MaxPvalue
                            : Math.floor(detailobj.MQuantity % Number(detailobj.BagSize)) >= detailobj.MaxPvalue) && isGUnit
                    "
                    v-model="OverRide"
                    v-has="'INV_WEIGH_OVERRIDE'"
                    @change="
                        scale = 0;
                        percentage = 0;
                    "
                >
                    {{ $t('MaterialPreparationBuild.OverRide') }}
                </el-checkbox>
            </div>
            <div class="tabinputsinglebox">
                <el-input size="mini" :disabled="!OverRide" :placeholder="$t('MaterialPreparationBuild.scale')" v-model="scale" @input="scaleChange()"></el-input>
            </div>
            <div class="tabinputsinglebox">
                <div class="statusbox" ref="Remaining" :style="{ background: successFlag ? '#3DCD58' : '#FFA500' }">
                    <!-- {{ $t('MaterialPreparationBuild.Remaining') }} {{ OverRide ? (DataList[1].value - scale).toFixed(3) : DataList[1].value }} -->
                    {{ $t('MaterialPreparationBuild.Remaining') }} {{ (DataList[1].value - scale).toFixed(3) }}
                </div>
            </div>
            <div class="tabinputsinglebox">
                <div class="tabbtnsinglebox" style="width: 230px">
                    <el-button
                        style="margin-left: 5px"
                        :disabled="!(ssccFlag && sscc != '' && scale > 0 && progressStatus == 'success' && successFlag)"
                        size="small"
                        icon="el-icon-bottom"
                        @click="Transfer()"
                    >
                        {{ this.$t('MaterialPreparationBuild.POTransfer') }}
                    </el-button>
                    <el-button
                        style="margin-left: 0px"
                        size="small"
                        icon="el-icon-bottom"
                        :disabled="!(ssccFlag && sscc != '' && scale > 0 && progressStatus == 'success' && successFlag)"
                        @click="Merge()"
                    >
                        {{ this.$t('MaterialPreparationBuild.PartialBagMerge') }}
                    </el-button>
                </div>
            </div>
            <div class="tabinputsinglebox">
                <div class="statusbox" style="background: #777777">{{ DataList[0].label }}：{{ DataList[0].value }}</div>
            </div>
            <div class="tabinputsinglebox">
                <div class="statusbox" :style="{ background: successFlag ? '#3DCD58' : '#777777' }">{{ DataList[1].label }}：{{ DataList[1].value }}</div>
            </div>
            <div class="tabinputsinglebox">
                <div class="statusbox" style="background: #777777">{{ DataList[2].label }}：{{ DataList[2].value }}</div>
            </div>
        </div>
        <el-progress :status="progressStatus" :percentage="percentage"></el-progress>
    </div>
</template>
<script>
import '@/views/Inventory/mystyle.scss';
import { TransferPartialBag, PartialBagMerge, InventoryReadCallData, GetScaleSelect, MygetSSCC } from '@/api/Inventory/MaterialPreparation.js';
import { Message, MessageBox } from 'element-ui';

export default {
    data() {
        return {
            isGUnit: false,
            sscc: '',
            printer: '',
            InQuantity: 0,
            OverRide: false,
            progressStatus: 'success',
            percentage: 0,
            scale: 0,
            Remaining: null,
            detailobj: {
                TagpS: 0,
                ParitialPage: 0
            },
            ssccFlag: false,
            successFlag: false,
            DataList: [
                {
                    label: this.$t('MaterialPreparationBuild.Min'),
                    value: 14800
                },
                {
                    label: this.$t('MaterialPreparationBuild.Target'),
                    value: 15000
                },
                {
                    label: this.$t('MaterialPreparationBuild.Max'),
                    value: 15200
                }
            ],
            options: [],
            WeightInterval: null
        };
    },

    mounted() {
        this.detailobj = JSON.parse(this.$route.query.query);
        this.isGUnit = this.detailobj.isGUnit;
        this.room = window.sessionStorage.getItem('room');
        // this.Remaining = this.detailobj.InQuantity;
        this.DataList[1].value = (this.detailobj.ParitialPage - this.detailobj.TagpS).toFixed(3);
        this.DataList[0].value = this.detailobj.MinPvalue;
        this.DataList[2].value = this.detailobj.MaxPvalue;
        this.BagWeight = this.detailobj.BagSize;
        this.successFlag = Number(this.detailobj.TagpS) < Number(this.detailobj.MaxPvalue);
        setTimeout(() => {
            this.GetScaleSelect();
        }, 1000);
    },
    beforeDestroy() {
        clearInterval(this.WeightInterval);
    },
    watch: {
        OverRide(newVal, oldVal) {
            if (newVal == true) {
                clearInterval(this.WeightInterval);
            } else {
                if (this.printer != '') {
                    clearInterval(this.WeightInterval);
                    this.SetWeightInterval();
                    // this.WeightInterval = setInterval(() => {
                    //     this.SetWeightInterval();
                    // }, 1000);
                }
            }
        }
    },
    methods: {
        getbtnStatus() {
            return this.ssccFlag && this.sscc != '' && this.scale > 0 && this.progressStatus == 'success' && this.successFlag;
        },
        async getSSCC() {
            this.$emit('getRowBySscc', this.sscc);
        },
        async GetScaleSelect() {
            let equipmentID = {
                equipmentID: this.room
            };
            const res = await GetScaleSelect(equipmentID);
            if (res.response.length > 0) {
                res.response.forEach(item => {
                    item.label = item.ScaleName;
                    item.value = item.ScaleName + '|' + item.ScaleIp + '|' + item.ScalePort + '|' + item.Remark;
                });
                this.options = res.response;
                this.printer = window.sessionStorage.getItem('cz');
                if (this.printer != '') {
                    this.SetWeightInterval();
                }
            } else {
                this.options = [];
            }
        },
        async SetWeightInterval() {
            // this.WeightInterval = setInterval(async () => {
            let params = {
                printer: this.printer.split('|')[0],
                ip: this.printer.split('|')[1],
                port: this.printer.split('|')[2],
                isMTL: this.printer.split('|')[3]
            };
            let res = await InventoryReadCallData(params);
            let num = 0;
            if (res.response) {
                let data = res.response;
                if (data.Data) {
                    if (data.ValueType == '-') {
                        num = -Number(data.Data);
                    } else if (data.ValueType == '+') {
                        num = Number(data.Data);
                    } else {
                        num = this.scale;
                    }
                } else {
                    num = 0;
                }
                if (!this.OverRide) {
                    this.scale = num;
                    this.scaleChange();
                }
                if (this.$route.path == '/Inventory/buildpalletsStart' && this.$parent.$parent.$parent.activeName == 'PartialBag') {
                    this.WeightInterval = setTimeout(() => {
                        this.SetWeightInterval();
                    }, 1000);
                }
            } else {
                num = 0;
            }
            // if (data.ValueType == '+') {
            //     num = num + Number(data.Data);
            // } else {
            //     num = num - Number(data.Data);
            // }

            // }, 1000);
        },
        scaleChange() {
            this.detailobj = this.$parent.$parent.$parent.detailobj;
            let needs = this.detailobj.ParitialPage - this.detailobj.TagpS;
            if (needs == 0) {
                this.percentage = 0;
            } else {
                this.percentage = Number(this.scale / needs) * 100;
            }
            if ((Number(this.scale) + this.detailobj.MQuantity).toFixed(3) > this.detailobj.MaxPvalue) {
                if (this.percentage == 0) {
                    this.percentage == 100;
                }
                this.progressStatus = 'exception';
                return '满';
            } else {
                if (this.percentage == 0) {
                    this.percentage == 100;
                }
                this.progressStatus = 'success';
                return this.percentage;
            }
        },
        getDetailobj() {
            this.detailobj = this.$parent.$parent.$parent.detailobj;
            this.DataList[1].value = (this.detailobj.ParitialPage - this.detailobj.TagpS).toFixed(3);
            this.DataList[0].value = this.detailobj.MinPvalue;
            this.DataList[2].value = this.detailobj.MaxPvalue;
            this.successFlag = Number(this.detailobj.TagpS) < Number(this.detailobj.MaxPvalue);
            console.log(this.successFlag);
            this.BagWeight = this.detailobj.BagSize;
            this.isGUnit = this.detailobj.isGUnit;
            this.OverRide = this.detailobj.isGUnit;
        },
        async Merge() {
            this.detailobj = this.$parent.$parent.$parent.detailobj;
            MessageBox.prompt(`${this.$t('GLOBAL._COMFIRM_SSCC')}`, '', {
                confirmButtonText: `${this.$t('GLOBAL._QD')}`,
                cancelButtonText: `${this.$t('GLOBAL._GB')}`
            }).then(async ({ value }) => {
                let data = {
                    subID: this.SubId,
                    inputBagWeight: this.scale,
                    ssccCode: value
                };
                let res = await PartialBagMerge(data);
                Message({
                    message: res.msg,
                    type: 'success'
                });
                this.$emit('getRefresh');
            });
            // let data = {};
        },
        async Transfer() {
            if (this.$parent.$parent.$parent.isExpirationDate) {
                Message({
                    message: this.$t('MaterialPreparationBuild.OverExpirationDate'),
                    type: 'warning'
                });
                return;
            }
            if (this.progressStatus == 'exception') {
                return false;
            }
            if (this.detailobj.isGUnit) {
                if (Number(this.scale) > this.InQuantity * 1000) {
                    Message({
                        message: this.$t('MaterialPreparationBuild.InQuantityNotEnough'),
                        type: 'error'
                    });
                    return;
                }
            } else {
                if (Number(this.scale) > this.InQuantity) {
                    Message({
                        message: this.$t('MaterialPreparationBuild.InQuantityNotEnough'),
                        type: 'error'
                    });
                    return;
                }
            }

            this.detailobj = this.$parent.$parent.$parent.detailobj;
            this.BagWeight = this.detailobj.BagSize;
            let isType;
            //判断覆写是否选中
            if (this.OverRide == false) {
                isType = '自动录入';
            } else {
                isType = '手工录入';
            }
            // this.detailobj = this.$parent.detailobj;
            let data = {
                IS_FC: this.OverRide ? '是' : '否',
                ChangeUnit: this.detailobj.isGUnit ? this.detailobj.ChangeUnit : '',
                IsType: isType,
                PrintId: window.sessionStorage.getItem('PrintId'),
                equpmentID: window.sessionStorage.getItem('room'),
                subID: this.SubId,
                inputBagWeight: Number(this.scale),
                actualValue: this.detailobj.MQuantity.toString(),
                MaterialId: this.detailobj.MaterialId,
                targetWeight: this.detailobj.MQuantityTotal,
                actualWeight: Number(this.detailobj.ParitialPage),
                proOrderID: this.detailobj.ProductionOrderId,
                batchID: this.detailobj.BatchId,
                bagWeight: this.detailobj.BagSize,
                batchConsumeRequirementId: this.datailobj.ID
            };
            let res = await TransferPartialBag(data);
            this.OverRide = false;
            this.scale = 0;
            this.percentage = 0;
            Message({
                message: res.msg,
                type: 'success'
            });
            this.$nextTick(_ => {
                this.sscc = '';
                this.$refs.autoFocus.focus();
            });
            this.$emit('getRefresh');
        }
    }
};
</script>
<style lang="scss">
.MyPartialBag {
    padding: 10px;
    margin: 10px;
    height: 88px;
    width: 100%;
    border: 1px solid #ebeef5;
    .tabinputbox {
        height: 100%;
        width: 100%;
        display: flex;
    }
    .statusbox {
        padding: 0 10px;
        font-size: 12px;
        height: 30px;
        background: #ffa500;
    }
    .tabinputsinglebox {
        display: flex;
        flex-direction: column;
        justify-content: center;
        max-width: 250px;
        margin-right: 10px;
        .tabbtnsinglebox {
            height: 30px;
            margin-bottom: 4px;
        }
    }
}
</style>
