<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格自适应高度测试</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .control-panel {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .table-container {
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .info-panel {
            margin-top: 20px;
            padding: 15px;
            background: #e8f4fd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>可用库存表格自适应高度测试</h1>
            
            <div class="control-panel">
                <h3>控制面板</h3>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-button 
                            :type="useViewportHeight ? 'primary' : 'default'"
                            @click="toggleTableHeightMode()">
                            {{ useViewportHeight ? '视口模式' : '行数模式' }}
                        </el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="addTestData()">添加数据</el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="removeTestData()">删除数据</el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button @click="clearTestData()">清空数据</el-button>
                    </el-col>
                </el-row>
            </div>

            <div class="table-container">
                <h3>可用库存表格 (当前高度: {{ tableHeight }}px)</h3>
                <el-table 
                    :data="tableList" 
                    :height="tableHeight"
                    :key="tableHeightKey"
                    style="width: 100%"
                    border>
                    <el-table-column prop="id" label="ID" width="80"></el-table-column>
                    <el-table-column prop="material" label="物料编码" width="120"></el-table-column>
                    <el-table-column prop="batch" label="批次" width="100"></el-table-column>
                    <el-table-column prop="quantity" label="数量" width="100"></el-table-column>
                    <el-table-column prop="unit" label="单位" width="80"></el-table-column>
                    <el-table-column prop="status" label="状态" width="80"></el-table-column>
                    <el-table-column prop="location" label="位置"></el-table-column>
                </el-table>
            </div>

            <div class="info-panel">
                <h3>当前状态信息</h3>
                <p><strong>模式:</strong> {{ useViewportHeight ? '视口高度模式' : '数据行数模式' }}</p>
                <p><strong>数据行数:</strong> {{ tableList.length }}</p>
                <p><strong>窗口高度:</strong> {{ windowHeight }}px</p>
                <p><strong>表格高度:</strong> {{ tableHeight }}px</p>
                <p><strong>最小高度:</strong> {{ minTableHeight }}px</p>
                <p><strong>最大高度:</strong> {{ maxTableHeight }}px</p>
                <p><strong>行高:</strong> {{ rowHeight }}px</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    tableList: [],
                    useViewportHeight: false,
                    tableHeight: 200,
                    tableHeightKey: 0,
                    minTableHeight: 150,
                    maxTableHeight: 400,
                    rowHeight: 40,
                    windowHeight: window.innerHeight
                };
            },
            mounted() {
                // 初始化一些测试数据
                this.generateTestData(3);
                this.updateTableHeight();

                // 监听窗口大小变化
                this.handleResize = () => {
                    this.windowHeight = window.innerHeight;
                    if (this.useViewportHeight) {
                        this.updateTableHeight();
                    }
                };
                window.addEventListener('resize', this.handleResize);
            },
            beforeDestroy() {
                if (this.handleResize) {
                    window.removeEventListener('resize', this.handleResize);
                }
            },
            methods: {
                toggleTableHeightMode() {
                    console.log('切换前:', this.useViewportHeight);
                    this.useViewportHeight = !this.useViewportHeight;
                    console.log('切换后:', this.useViewportHeight);
                    this.updateTableHeight();
                },
                updateTableHeight() {
                    let newHeight;
                    
                    if (!this.tableList || this.tableList.length === 0) {
                        newHeight = this.minTableHeight;
                    } else if (this.useViewportHeight) {
                        // 基于视口高度的响应式计算
                        const availableHeight = this.windowHeight * 0.25;
                        newHeight = Math.min(Math.max(availableHeight, this.minTableHeight), this.maxTableHeight);
                    } else {
                        // 基于数据行数计算高度
                        const headerHeight = 40;
                        const dataHeight = headerHeight + (this.tableList.length * this.rowHeight);
                        newHeight = Math.min(Math.max(dataHeight, this.minTableHeight), this.maxTableHeight);
                    }
                    
                    console.log('新计算的高度:', newHeight);
                    this.tableHeight = newHeight;
                    this.tableHeightKey++;
                },
                generateTestData(count) {
                    const materials = ['MAT001', 'MAT002', 'MAT003', 'MAT004', 'MAT005'];
                    const statuses = ['可用', '锁定', '待检'];
                    const locations = ['A01-01', 'A01-02', 'B02-01', 'B02-02', 'C03-01'];
                    
                    for (let i = 0; i < count; i++) {
                        this.tableList.push({
                            id: this.tableList.length + 1,
                            material: materials[Math.floor(Math.random() * materials.length)],
                            batch: 'B' + (Math.floor(Math.random() * 1000) + 1000),
                            quantity: Math.floor(Math.random() * 1000) + 100,
                            unit: 'kg',
                            status: statuses[Math.floor(Math.random() * statuses.length)],
                            location: locations[Math.floor(Math.random() * locations.length)]
                        });
                    }
                },
                addTestData() {
                    this.generateTestData(1);
                    this.$nextTick(() => {
                        this.updateTableHeight();
                    });
                },
                removeTestData() {
                    if (this.tableList.length > 0) {
                        this.tableList.pop();
                        this.$nextTick(() => {
                            this.updateTableHeight();
                        });
                    }
                },
                clearTestData() {
                    this.tableList = [];
                    this.$nextTick(() => {
                        this.updateTableHeight();
                    });
                }
            }
        });
    </script>
</body>
</html>
