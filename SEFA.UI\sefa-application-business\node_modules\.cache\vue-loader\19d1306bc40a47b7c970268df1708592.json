{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetailForm.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetailForm.vue", "mtime": 1749177894578}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["bomDetailForm.vue"], "names": [], "mappings": ";AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "bomDetailForm.vue", "sourceRoot": "src/views/planManagement/weekPacking", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._TD') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n        <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\">              \n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"替代物料代码\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div>\n                {{dialogForm.InsteadMaterialCode}}&nbsp; &nbsp; {{dialogForm.InsteadMaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getWeekScheduleBomDetail,\n    changeMaterial\n  } from \"@/api/planManagement/weekSchedule\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n      },\n      \n      submit() {\n\n        changeMaterial(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getWeekScheduleBomDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)        \n        this.dialogForm.InsteadMaterialId = val.ID\n        this.dialogForm.InsteadMaterialCode = val.Code\n        this.dialogForm.InsteadMaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}