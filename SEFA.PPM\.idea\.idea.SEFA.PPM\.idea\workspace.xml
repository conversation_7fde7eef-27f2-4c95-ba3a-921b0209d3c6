<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoGeneratedRunConfigurationManager">
    <projectFile kind="Docker">SEFA.PPM.Api/SEFA.PPM.Api.csproj</projectFile>
    <projectFile profileName="IIS Express">SEFA.PPM.Api/SEFA.PPM.Api.csproj</projectFile>
    <projectFile profileName="SEFA.PPM">SEFA.PPM.Api/SEFA.PPM.Api.csproj</projectFile>
    <projectFile pubXmlPath="SEFA.PPM.Api/Properties/PublishProfiles/FolderProfile.pubxml">SEFA.PPM.Api/SEFA.PPM.Api.csproj</projectFile>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ccb1980a-77ae-4f25-b886-d1053a016367" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/322a95a6f87f4a6d82b2598696538a389c600/34/12ac3c2b/PropertyEntity.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/322a95a6f87f4a6d82b2598696538a389c600/5b/60b6ad20/MaterialEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/322a95a6f87f4a6d82b2598696538a389c600/c9/cd8a72bd/MaterialPropertyValueEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/SEFA.PPM.Model/Models/PPM/PoConsumeRequirementEntity.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/MaterialInventoryServices.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/MaterialPreparationViewServices.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/SEFA.PPM.Services/PPM/FormulascheduleServices.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="MetaFilesCheckinStateConfiguration" checkMetaFiles="true" />
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2vDDQV8wOufL73jD2AkI9rxzWaJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    ".NET 启动设置配置文件.SEFA.PPM.Api: SEFA.PPM.executor": "Debug",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "XThreadsFramesViewSplitterKey": "0.43145162",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected=".NET 启动设置配置文件.SEFA.PPM.Api: SEFA.PPM">
    <configuration name="SEFA.PPM.Api: FolderProfile" type="DotNetMsBuildPublish" factoryName="Publish to IIS">
      <riderPublish configuration="Release" platform="Any CPU" publish_profile="FolderProfile.pubxml" pubxml_path="$PROJECT_DIR$/SEFA.PPM.Api/Properties/PublishProfiles/FolderProfile.pubxml" uuid_high="8018558093272957986" uuid_low="-4939505218139161185" />
      <method v="2" />
    </configuration>
    <configuration name="SEFA.PPM.Api: IIS Express" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/SEFA.PPM.Api/SEFA.PPM.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="IIS Express" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration name="SEFA.PPM.Api: SEFA.PPM" type="LaunchSettings" factoryName=".NET Launch Settings Profile">
      <option name="LAUNCH_PROFILE_PROJECT_FILE_PATH" value="$PROJECT_DIR$/SEFA.PPM.Api/SEFA.PPM.Api.csproj" />
      <option name="LAUNCH_PROFILE_TFM" value="net6.0" />
      <option name="LAUNCH_PROFILE_NAME" value="SEFA.PPM" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <option name="USE_MONO" value="0" />
      <option name="RUNTIME_ARGUMENTS" value="" />
      <option name="GENERATE_APPLICATIONHOST_CONFIG" value="1" />
      <option name="SHOW_IIS_EXPRESS_OUTPUT" value="0" />
      <option name="SEND_DEBUG_REQUEST" value="1" />
      <option name="ADDITIONAL_IIS_EXPRESS_ARGUMENTS" value="" />
      <method v="2">
        <option name="Build" />
      </method>
    </configuration>
    <configuration default="true" type="docker-deploy" factoryName="dockerfile" temporary="true">
      <deployment type="dockerfile">
        <settings />
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
    <configuration name="SEFA.PPM.Api/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
      <deployment type="dockerfile">
        <settings>
          <option name="containerName" value="sefa.ppm.api" />
          <option name="contextFolderPath" value="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM" />
          <option name="publishAllPorts" value="true" />
          <option name="sourceFilePath" value="SEFA.PPM.Api/Dockerfile" />
        </settings>
      </deployment>
      <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="true" isSslEnabled="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="ccb1980a-77ae-4f25-b886-d1053a016367" name="更改" comment="" />
      <created>1743670737163</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743670737163</updated>
      <workItem from="1743670738156" duration="1206000" />
      <workItem from="1743671968738" duration="2478000" />
      <workItem from="1743679777266" duration="910000" />
      <workItem from="1744350815438" duration="3733000" />
      <workItem from="1745307941482" duration="575000" />
      <workItem from="1745309067338" duration="7989000" />
      <workItem from="1745371143517" duration="20844000" />
      <workItem from="1745485934836" duration="896000" />
      <workItem from="1745495116188" duration="669000" />
      <workItem from="1745549653924" duration="40350000" />
      <workItem from="1746493208968" duration="29017000" />
      <workItem from="1746774777032" duration="5719000" />
      <workItem from="1747014411013" duration="11081000" />
      <workItem from="1747096896707" duration="12822000" />
      <workItem from="1747295240603" duration="13269000" />
      <workItem from="1747362102178" duration="2199000" />
      <workItem from="1747623559029" duration="4002000" />
      <workItem from="1747633606219" duration="165000" />
      <workItem from="1747638156312" duration="10034000" />
      <workItem from="1747810471514" duration="19562000" />
      <workItem from="1747987828274" duration="2267000" />
      <workItem from="1747993137282" duration="204000" />
      <workItem from="1747993370471" duration="185000" />
      <workItem from="1747993575535" duration="134000" />
      <workItem from="1747993729222" duration="572000" />
      <workItem from="1747994692885" duration="293000" />
      <workItem from="1747995001657" duration="4539000" />
      <workItem from="1748225975548" duration="1502000" />
      <workItem from="1748227617073" duration="17599000" />
      <workItem from="1748403387593" duration="1108000" />
      <workItem from="1748481770968" duration="303000" />
      <workItem from="1748484282398" duration="1661000" />
      <workItem from="1748498472118" duration="4267000" />
      <workItem from="1748573163290" duration="12383000" />
      <workItem from="1748913882882" duration="828000" />
      <workItem from="1748917592782" duration="14250000" />
      <workItem from="1749173447756" duration="163000" />
      <workItem from="1749173636413" duration="12485000" />
      <workItem from="1749432296712" duration="31067000" />
      <workItem from="1749515884946" duration="27117000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityCheckinConfiguration" checkUnsavedScenes="true" />
  <component name="UnityProjectConfiguration" hasMinimizedUI="false" />
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="dotnet:Confluent.Kafka" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/InventorylistingViewServices.cs</url>
          <line>499</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\InventorylistingViewServices.cs" containingFunctionPresentation="方法 'GetPageList_YL'">
            <startOffsets>
              <option value="30103" />
            </startOffsets>
            <endOffsets>
              <option value="30134" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="30" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/InventorylistingViewServices.cs</url>
          <line>582</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\InventorylistingViewServices.cs" containingFunctionPresentation="方法 'GetPageList_YL'">
            <startOffsets>
              <option value="36169" />
            </startOffsets>
            <endOffsets>
              <option value="36203" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="31" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/InventorylistingViewServices.cs</url>
          <line>656</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\InventorylistingViewServices.cs" containingFunctionPresentation="方法 'GetPageList_YL'">
            <startOffsets>
              <option value="40807" />
            </startOffsets>
            <endOffsets>
              <option value="40943" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="32" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/InventorylistingViewServices.cs</url>
          <line>597</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\InventorylistingViewServices.cs" containingFunctionPresentation="方法 'GetPageList_YL'">
            <startOffsets>
              <option value="37106" />
            </startOffsets>
            <endOffsets>
              <option value="37210" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="33" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/InventorylistingViewServices.cs</url>
          <line>535</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\InventorylistingViewServices.cs" containingFunctionPresentation="方法 'GetPageList_YL'">
            <startOffsets>
              <option value="32786" />
            </startOffsets>
            <endOffsets>
              <option value="32890" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="34" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>181</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="7141" />
            </startOffsets>
            <endOffsets>
              <option value="7308" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="53" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>183</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="7320" />
            </startOffsets>
            <endOffsets>
              <option value="7360" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="54" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>184</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="7366" />
            </startOffsets>
            <endOffsets>
              <option value="7380" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="55" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>153</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="6073" />
            </startOffsets>
            <endOffsets>
              <option value="6152" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="56" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>159</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="6248" />
            </startOffsets>
            <endOffsets>
              <option value="6370" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="57" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>173</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="6912" />
            </startOffsets>
            <endOffsets>
              <option value="7050" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="58" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>248</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="9905" />
            </startOffsets>
            <endOffsets>
              <option value="9926" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="59" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>250</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="9956" />
            </startOffsets>
            <endOffsets>
              <option value="9980" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="60" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>226</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="8789" />
            </startOffsets>
            <endOffsets>
              <option value="9012" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="61" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/PTM/ConsumeViewServices.cs</url>
          <line>227</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\PTM\ConsumeViewServices.cs" containingFunctionPresentation="方法 'ScanSSCC'">
            <startOffsets>
              <option value="9016" />
            </startOffsets>
            <endOffsets>
              <option value="9901" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="62" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Api/Controllers/MKM/BBatchDetailIiViewController.cs</url>
          <line>44</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Api\Controllers\MKM\BBatchDetailIiViewController.cs" containingFunctionPresentation="方法 'GetListByBatchID'">
            <startOffsets>
              <option value="1514" />
            </startOffsets>
            <endOffsets>
              <option value="1598" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/MaterialPreparationViewServices.cs</url>
          <line>2200</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs" containingFunctionPresentation="方法 'GetPageListByBatchIDSII'">
            <startOffsets>
              <option value="97264" />
            </startOffsets>
            <endOffsets>
              <option value="97301" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="99" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/MaterialPreparationViewServices.cs</url>
          <line>2137</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs" containingFunctionPresentation="方法 'GetPageListByBatchIDSII'">
            <startOffsets>
              <option value="94640" />
            </startOffsets>
            <endOffsets>
              <option value="94829" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="100" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="DotNet Breakpoints">
          <url>file://$PROJECT_DIR$/SEFA.PPM.Services/MKM/MaterialPreparationViewServices.cs</url>
          <line>2141</line>
          <properties documentPath="C:\work\syngentagroup\SEFA_XZD\SEFA.PPM\SEFA.PPM.Services\MKM\MaterialPreparationViewServices.cs" containingFunctionPresentation="方法 'GetPageListByBatchIDSII'">
            <startOffsets>
              <option value="94874" />
            </startOffsets>
            <endOffsets>
              <option value="95201" />
            </endOffsets>
          </properties>
          <option name="timeStamp" value="101" />
        </line-breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.OperationCanceledException" breakIfHandledByOtherCode="false" displayValue="System.OperationCanceledException" />
          <option name="timeStamp" value="63" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.Tasks.TaskCanceledException" breakIfHandledByOtherCode="false" displayValue="System.Threading.Tasks.TaskCanceledException" />
          <option name="timeStamp" value="64" />
        </breakpoint>
        <breakpoint enabled="true" type="DotNet_Exception_Breakpoints">
          <properties exception="System.Threading.ThreadAbortException" breakIfHandledByOtherCode="false" displayValue="System.Threading.ThreadAbortException" />
          <option name="timeStamp" value="65" />
        </breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>