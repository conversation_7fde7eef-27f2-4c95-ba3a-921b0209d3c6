import { getRequestResources } from '@/api/fetch';
const baseURL = 'baseURL_ORDER'


/**
 * 周计划分页查询
 * @param {查询条件} data
 */
export function getWeekScheduleList(data) {
    const api = '/ppm/WeekSchedule/GetPageList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 周计划Bom查询
 * @param {查询条件} data
 */
export function getWeekScheduleBomList(data) {
    const api = '/ppm/WeekSchedule/GetBomList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 保存周计划
 * @param data
 */
export function saveWeekScheduleForm(data) {
    const api = '/ppm/WeekSchedule/SaveForm'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getWeekScheduleDetail(id) {
    const api = '/ppm/WeekSchedule/GetEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getWeekScheduleBomDetail(id) {
    const api = '/ppm/WeekSchedule/GetBomEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}
/**
 * 删除周计划
 * @param {主键} data
 */
export function delWeekSchedule(data) {
    const api = '/ppm/WeekSchedule/Delete'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * BOM物料替换
 * @param {主键} data
 */
export function changeMaterial(data) {
    const api = '/ppm/WeekSchedule/ChangeMaterial'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 导出入周计划
 */
export function importWeekSchedule(data) {
    const api = '/ppm/WeekSchedule/ImportData';
    return getRequestResources(baseURL, api, 'post', data);
}

/**
 * 导出周计划
 */
export function exportWeekSchedule(data) {
    const api = '/ppm/WeekSchedule/ExportData';
    return getRequestResources(baseURL, api, 'post', data);
}

// 产线
export function getLineList(data) {
    const api = `/ppm/StandardPeriodLot/GetLineList?areaCode=${data.areaCode}`
    return getRequestResources(baseURL, api, 'post', null);
}

/**
 * ProductionOrder查询
 * @param {查询条件} data
 */
export function getProductionOrderList(data) {
    const api = '/ppm/WeekSchedule/GetProductionOrderList'
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取周计划详情
 * @param {Id}
 */
export function getProductionOrderDetail(id) {
    const api = '/ppm/WeekSchedule/GetProductionOrderEntity/'+id;
    return getRequestResources(baseURL, api, 'get')
}

/**
 * 下发DCS
 * @param {data}
 */
export function downloadDCS(data) {
    const api = '/ppm/WeekSchedule/DownloadDCS';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取批次拆分信息
 * @param {data}
 */
export function getSplitBatchInfo(data) {
    const api = '/ppm/WeekSchedule/GetSplitBatchInfo';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 批次拆分
 * @param {data}
 */
export function splitBatch(data) {
    const api = '/ppm/WeekSchedule/SplitBatch';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 获取新增批次工单信息
 * @param {data}
 */
export function getAddBatchInfo(data) {
    const api = '/ppm/WeekSchedule/GetAddBatchInfo';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 新增批次工单
 * @param {data}
 */
export function addBatch(data) {
    const api = '/ppm/WeekSchedule/AddBatch';
    return getRequestResources(baseURL, api, 'post', data)
}

/**
 * 替代物料查询
 * @param {查询条件} data
 */
export function getInsteadMaterialList(data) {
    const api = '/ppm/WeekSchedule/GetInsteadMaterialList'
    return getRequestResources(baseURL, api, 'post', data)
}

