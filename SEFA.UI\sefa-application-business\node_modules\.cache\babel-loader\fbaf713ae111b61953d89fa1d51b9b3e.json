{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js??ref--14-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\planManagement\\batchDcs.js", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\api\\planManagement\\batchDcs.js", "mtime": 1749178050338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\eslint-loader\\index.js", "mtime": 1743379020994}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgZ2V0UmVxdWVzdFJlc291cmNlcyB9IGZyb20gJ0AvYXBpL2ZldGNoJzsKY29uc3QgYmFzZVVSTCA9ICdiYXNlVVJMX09SREVSJzsKLyoqCiAqIOaJueasoURDU+S4i+WPkeWIhumhteafpeivogogKiBAcGFyYW0ge+afpeivouadoeS7tn0gZGF0YQogKi8KCmV4cG9ydCBmdW5jdGlvbiBnZXRCYXRjaERjc0xpc3QoZGF0YSkgewogIGNvbnN0IGFwaSA9ICcvcHBtL0JhdGNoRGNzL0dldFBhZ2VMaXN0JzsKICByZXR1cm4gZ2V0UmVxdWVzdFJlc291cmNlcyhiYXNlVVJMLCBhcGksICdwb3N0JywgZGF0YSk7Cn0KLyoqCiAqIOS/neWtmOaJueasoURDU+S4i+WPkQogKiBAcGFyYW0gZGF0YQogKi8KCmV4cG9ydCBmdW5jdGlvbiBzYXZlQmF0Y2hEY3NGb3JtKGRhdGEpIHsKICBjb25zdCBhcGkgPSAnL3BwbS9CYXRjaERjcy9TYXZlRm9ybSc7CiAgcmV0dXJuIGdldFJlcXVlc3RSZXNvdXJjZXMoYmFzZVVSTCwgYXBpLCAncG9zdCcsIGRhdGEpOwp9Ci8qKgogKiDojrflj5bmibnmrKFEQ1PkuIvlj5Hor6bmg4UKICogQHBhcmFtIHtJZH0KICovCgpleHBvcnQgZnVuY3Rpb24gZ2V0QmF0Y2hEY3NEZXRhaWwoaWQpIHsKICBjb25zdCBhcGkgPSAnL3BwbS9CYXRjaERjcy9HZXRFbnRpdHkvJyArIGlkOwogIHJldHVybiBnZXRSZXF1ZXN0UmVzb3VyY2VzKGJhc2VVUkwsIGFwaSwgJ2dldCcpOwp9Ci8qKgogKiDliKDpmaTmibnmrKFEQ1PkuIvlj5EKICogQHBhcmFtIHvkuLvplK59IGRhdGEKICovCgpleHBvcnQgZnVuY3Rpb24gZGVsQmF0Y2hEY3MoZGF0YSkgewogIGNvbnN0IGFwaSA9ICcvcHBtL0JhdGNoRGNzL0RlbGV0ZSc7CiAgcmV0dXJuIGdldFJlcXVlc3RSZXNvdXJjZXMoYmFzZVVSTCwgYXBpLCAncG9zdCcsIGRhdGEpOwp9"}, {"version": 3, "names": ["getRequestResources", "baseURL", "getBatchDcsList", "data", "api", "saveBatchDcsForm", "getBatchDcsDetail", "id", "delBatchDcs"], "sources": ["C:/work/syngentagroup/SEFA_XZD/SEFA.UI/sefa-application-business/src/api/planManagement/batchDcs.js"], "sourcesContent": ["import { getRequestResources } from '@/api/fetch';\nconst baseURL = 'baseURL_ORDER'\n\n\n/**\n * 批次DCS下发分页查询\n * @param {查询条件} data\n */\nexport function getBatchDcsList(data) {\n    const api = '/ppm/BatchDcs/GetPageList'\n    return getRequestResources(baseURL, api, 'post', data)\n}\n\n\n/**\n * 保存批次DCS下发\n * @param data\n */\nexport function saveBatchDcsForm(data) {\n    const api = '/ppm/BatchDcs/SaveForm'\n    return getRequestResources(baseURL, api, 'post', data)\n}\n\n/**\n * 获取批次DCS下发详情\n * @param {Id}\n */\nexport function getBatchDcsDetail(id) {\n    const api = '/ppm/BatchDcs/GetEntity/'+id;\n    return getRequestResources(baseURL, api, 'get')\n}\n\n/**\n * 删除批次DCS下发\n * @param {主键} data\n */\nexport function delBatchDcs(data) {\n    const api = '/ppm/BatchDcs/Delete'\n    return getRequestResources(baseURL, api, 'post', data)\n}\n\n\n\n"], "mappings": "AAAA,SAASA,mBAAT,QAAoC,aAApC;AACA,MAAMC,OAAO,GAAG,eAAhB;AAGA;AACA;AACA;AACA;;AACA,OAAO,SAASC,eAAT,CAAyBC,IAAzB,EAA+B;EAClC,MAAMC,GAAG,GAAG,2BAAZ;EACA,OAAOJ,mBAAmB,CAACC,OAAD,EAAUG,GAAV,EAAe,MAAf,EAAuBD,IAAvB,CAA1B;AACH;AAGD;AACA;AACA;AACA;;AACA,OAAO,SAASE,gBAAT,CAA0BF,IAA1B,EAAgC;EACnC,MAAMC,GAAG,GAAG,wBAAZ;EACA,OAAOJ,mBAAmB,CAACC,OAAD,EAAUG,GAAV,EAAe,MAAf,EAAuBD,IAAvB,CAA1B;AACH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASG,iBAAT,CAA2BC,EAA3B,EAA+B;EAClC,MAAMH,GAAG,GAAG,6BAA2BG,EAAvC;EACA,OAAOP,mBAAmB,CAACC,OAAD,EAAUG,GAAV,EAAe,KAAf,CAA1B;AACH;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASI,WAAT,CAAqBL,IAArB,EAA2B;EAC9B,MAAMC,GAAG,GAAG,sBAAZ;EACA,OAAOJ,mBAAmB,CAACC,OAAD,EAAUG,GAAV,EAAe,MAAf,EAAuBD,IAAvB,CAA1B;AACH"}]}