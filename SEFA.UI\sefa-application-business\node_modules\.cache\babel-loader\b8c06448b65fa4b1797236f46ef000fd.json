{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetail.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\bomDetail.vue", "mtime": 1749177894577}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAkDA;AACA;AACA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,cADA;MAEAC,aAFA;MAGAC,aAHA;MAIAC,YACA;QAAAC;QAAAC;QAAAC;MAAA,CADA,EAEA;QAAAF;QAAAC;QAAAC;MAAA,CAFA,EAGA;QAAAF;QAAAC;QAAAC;MAAA,CAHA,EAIA;QAAAF;QAAAC;QAAAC;MAAA,CAJA,EAKA;QAAAF;QAAAC;QAAAC;MAAA,CALA,EAMA;QAAAF;QAAAC;QAAAC;MAAA,CANA,EAOA;QAAAF;QAAAC;QAAAC;MAAA,CAPA,EAQA;QAAAF;QAAAC;QAAAC;MAAA,CARA,EASA;QAAAF;QAAAC;QAAAC;MAAA,CATA,EAUA;QAAAF;QAAAC;QAAAC;MAAA,CAVA,EAWA;QAAAF;QAAAC;QAAAC;MAAA,CAXA,CAJA;MAiBAC,cAjBA;MAkBAC,gDAlBA;MAmBAC,uBAnBA;MAoBAC,cApBA;MAqBAC;QACA,cADA;QAEA,eAFA;QAGA;MAHA;IArBA;EA2BA,CAjCA;;EAkCAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA,CAPA;;IAQAC;MACA;IACA,CAVA;;IAWA;MACA;QAAAC;MAAA;QACAC,kCADA;QAEA;MAFA;MAIA;IACA,CAjBA;;IAkBAC;MACA;;MACA;QACA;UAAAC;UAAAC;QAAA;MACA;IACA,CAvBA;;IAwBAC;MACA;IACA;;EA1BA;AAlCA", "names": ["name", "components", "BomDetailForm", "data", "searchForm", "drawer", "tableData", "tableHead", "code", "width", "align", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sapSegmentMaterialId", "currentRow", "status", "methods", "show", "handleClose", "response", "WeekScheduleId", "initTableHead", "field", "label", "showDialog"], "sourceRoot": "src/views/planManagement/weekPacking", "sources": ["bomDetail.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-drawer class=\"drawer\" :visible.sync=\"drawer\" :direction=\"'rtl'\" :before-close=\"handleClose\"\r\n      :append-to-body=\"false\" size=\"80%\">\r\n      <div slot=\"title\" class=\"title-box\">\r\n        <span>{{ `${currentRow.LineCode}-${currentRow.Factory}：${currentRow.MaterialCode}-${currentRow.MaterialName}：${currentRow.OrderNo}` }}</span>\r\n      </div>\r\n      <div class=\"InventorySearchBox\">\r\n        <div class=\"searchbox pd5\">\r\n          <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\r\n            <el-form-item :label=\"$t('GLOBAL._SSL')\">\r\n              <el-input clearable v-model=\"searchForm.Key\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button icon=\"el-icon-search\" @click=\"getTableData\">{{ $t('GLOBAL._CX') }}</el-button>\r\n            </el-form-item>\r\n            <!-- <el-form-item>\r\n              <el-button size=\"small\" type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\">{{\r\n                $t('GLOBAL._XZ') }}\r\n              </el-button>\r\n            </el-form-item> -->\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"table-box\">\r\n        <el-table v-loading=\"loading\" :data=\"tableData\" element-loading-text=\"拼命加载中\"\r\n          element-loading-spinner=\"el-icon-loading\" style=\"width: 100%\" height=\"83vh\">\r\n          <el-table-column prop=\"operation\" width=\"100\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\" :disabled=\"scope.row.Status == 'NotSplit' ? true : false\">{{ $t('GLOBAL._TD') }}</el-button>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column v-for=\"(item, index) in tableHead\" :key=\"index\" :prop=\"item.field\" :label=\"item.label\">\r\n            <template slot-scope=\"scope\">\r\n              <span v-if=\"['PercentQuantity', 'AdjustPercentQuantity'].includes(item.field)\">\r\n                {{ scope.row[item.field] ? `${scope.row[item.field]}` : '-' }}\r\n              </span>\r\n              <span v-else-if=\"item.field === 'Status'\"> {{ status[scope.row[item.field]] }} </span>\r\n              <span v-else> {{ scope.row[item.field] }} </span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n        </el-table>\r\n      </div>\r\n    </el-drawer>\r\n    <BomDetailForm @saveForm=\"getTableData\" ref=\"text\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getWeekScheduleBomList } from '@/api/planManagement/weekSchedule'\r\nimport BomDetailForm from './bomDetailForm'\r\nexport default {\r\n  name: 'BomDetail',\r\n  components: {\r\n    BomDetailForm\r\n  },\r\n  data() {\r\n    return {\r\n      searchForm: {},\r\n      drawer: false,\r\n      tableData: [],\r\n      tableHead: [\r\n        {code: 'SegmentName', width: 100, align: 'center'},  \r\n        {code: 'Sort', width: 80, align: 'center'},\r\n        {code: 'MaterialCode', width: 150, align: 'left'},\r\n        {code: 'MaterialName', width: 180, align: 'left'},\r\n        {code: 'MaterialType', width: 100, align: 'left'},\r\n        {code: 'InsteadMaterialCode', width: 150, align: 'left'},\r\n        {code: 'InsteadMaterialName', width: 180, align: 'left'},\r\n        {code: 'Unit', width: 100, align: 'center'},\r\n        {code: 'StandardQuantity', width: 100, align: 'left'},\r\n        {code: 'PlanQuantity', width: 100, align: 'left'},\r\n        {code: 'Remark', width: 180, align: 'left'},\r\n      ],\r\n      loading: false,\r\n      hansObjDrawer: this.$t('WeekSchedule.bomDetail'),\r\n      sapSegmentMaterialId: 0,\r\n      currentRow: {},\r\n      status: {\r\n        '1': 'Disable',\r\n        '2': 'Released',\r\n        '3': 'Pending_Release'\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    show(val) {\r\n      this.currentRow = val\r\n      this.sapSegmentMaterialId = val.ID\r\n      this.drawer = true\r\n      this.initTableHead()\r\n      this.getTableData()\r\n    },\r\n    handleClose() {\r\n      this.drawer = false\r\n    },\r\n    async getTableData() {\r\n      const { response } = await getWeekScheduleBomList({\r\n        WeekScheduleId: this.currentRow.ID,\r\n        ...this.searchForm\r\n      })\r\n      this.tableData = response\r\n    },\r\n    initTableHead() {\r\n      this.tableHead = []\r\n      for (let key in this.hansObjDrawer) {\r\n        this.tableHead.push({ field: key, label: this.hansObjDrawer[key] })\r\n      }\r\n    },\r\n    showDialog(row) {\r\n      this.$refs.text.show(row,this.currentRow.MaterialVersionId)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.drawer {\r\n  :deep(.el-drawer__body) {\r\n    padding-top: 10px;\r\n    background-color: #FFFFFF;\r\n    overflow-y: hidden\r\n  }\r\n\r\n  :deep(.el-form--inline) {\r\n    height: 32px;\r\n  }\r\n\r\n  .title-box {\r\n    font-size: 18px;\r\n    color: #909399;\r\n  }\r\n\r\n  .pd5 {\r\n    padding: 5px;\r\n  }\r\n\r\n  .table-box {\r\n    padding: 0 10px;\r\n\r\n    :deep(.el-button.is-disabled) {\r\n      background-color: transparent !important;\r\n      border: 0 !important;\r\n    }\r\n\r\n    i {\r\n      margin-right: 5px;\r\n      font-size: 15px !important;\r\n      color: #67c23a;\r\n    }\r\n  }\r\n}\r\n</style>"]}]}