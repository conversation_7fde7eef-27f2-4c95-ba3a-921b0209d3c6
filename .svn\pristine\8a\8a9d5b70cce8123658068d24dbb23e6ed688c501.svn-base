﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.PPM.Controllers;
using System.Linq.Expressions;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [Authorize(Permissions.Name)]
    public class BBatchDetailIiViewController : BaseApiController
    {
        /// <summary>
        /// BBatchDetailIiView
        /// </summary>
        private readonly IBBatchDetailIiViewServices _bBatchDetailIiViewServices;

        public BBatchDetailIiViewController(IBBatchDetailIiViewServices BBatchDetailIiViewServices)
        {
            _bBatchDetailIiViewServices = BBatchDetailIiViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailIiViewEntity>>> GetList(
            [FromBody] BBatchDetailIiViewRequestModel reqModel)
        {
            var data = await _bBatchDetailIiViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据批次ID获取对应的物料信息
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailIiViewEntity>>> GetListByBatchID([FromBody] string batchID)
        {
            var data = await _bBatchDetailIiViewServices.GetListByBatchID(batchID);
            // var data = await _bBatchDetailIiViewServices.GetBatchPreparationDetailList(batchID);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并显示）
        /// </summary>
        /// <param name="batchIDS">批次ID组</param>
        /// <param name="pageIndex">当前界面</param>
        /// <param name="pageSize">每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailIiViewEntity>>> GetPageListByBatchIDS(string[] batchIDS,
            int pageIndex, int pageSize)
        {
            var data = await _bBatchDetailIiViewServices.GetPageListByBatchIDS(batchIDS, pageIndex, pageSize);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailIiViewEntity>>> GetPageList(
            [FromBody] BBatchDetailIiViewRequestModel reqModel)
        {
            Expression<Func<BBatchDetailIiViewEntity, bool>> whereExpression = a => true;
            var data = await _bBatchDetailIiViewServices.QueryPage(whereExpression, reqModel.pageIndex,
                reqModel.pageSize);
            return Success(data, "获取成功");
        }

        [HttpGet("{id}")]
        public async Task<MessageModel<BBatchDetailIiViewEntity>> GetEntity(string id)
        {
            var data = await _bBatchDetailIiViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] BBatchDetailIiViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _bBatchDetailIiViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }


        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _bBatchDetailIiViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }
    }
    //public class BBatchDetailIiViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}