﻿using SEFA.Base.IServices.BASE;
using SEFA.Base.Model;
using SEFA.DFM.Model.Models;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SEFA.PPM.IServices
{
    /// <summary>
    /// IStandardPeriodLotServices
    /// </summary>	
    public interface IStandardPeriodLotServices : IBaseServices<StandardPeriodLotEntity>
    {
        Task<PageModel<StandardPeriodLotEntity>> GetPageList(StandardPeriodLotRequestModel reqModel);

        Task<List<StandardPeriodLotEntity>> GetList(StandardPeriodLotRequestModel reqModel);

        Task<List<EquipmentEntity>> GetLine(string areaCode, string level);

        Task<bool> SaveForm(StandardPeriodLotEntity entity);
    }
}