﻿using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// WeekScheduleBomRepository
	/// </summary>
    public class WeekScheduleBomRepository : BaseRepository<WeekScheduleBomEntity>, IWeekScheduleBomRepository
    {
        public WeekScheduleBomRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}