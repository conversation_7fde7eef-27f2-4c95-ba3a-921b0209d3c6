{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekSchedule\\form-dialog.vue", "mtime": 1749177894580}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AA0IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/planManagement/weekPacking", "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"800px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"工厂\" prop=\"factory\">\n              <el-select v-model=\"dialogForm.Factory\" placeholder=\"请选择工厂\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in factoryOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"车间\" prop=\"Workshop\">\n              <el-select v-model=\"dialogForm.Workshop\" placeholder=\"请选择车间\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in workshopOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线代码\" prop=\"LineCode\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.LindCode\" placeholder=\"请选择产线\" @change=\"setFormLineName\">\n                    <el-option v-for=\"(item, index) in lineOptions\" :key=\"index\" :label=\"item.EquipmentName\" :value=\"item.EquipmentCode\">\n                    </el-option>\n                </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划分类\" prop=\"Category\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.Category\" placeholder=\"请选择计划分类\" >\n                <el-option v-for=\"item in categoryOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料代码\" prop=\"MaterialCode\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"包装规格\" prop=\"PackSize\">\n              <el-input v-model=\"dialogForm.PackSize\" placeholder=\"请输入包装规格\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"设计代码\" prop=\"DesignCode\">\n              <el-input v-model=\"dialogForm.DesignCode\" placeholder=\"请输入设计代码\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"输出\" prop=\"Output\">\n              <el-input v-model=\"dialogForm.Output\" placeholder=\"请输入输出\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-date-picker v-model=\"dialogForm.StartWorkday\" type=\"datetime\" placeholder=\"选择日期时间\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"开始班次\" prop=\"StartShift\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.StartShift\" placeholder=\"请选择开始班次\">\n                <el-option v-for=\"item in shiftOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-date-picker v-model=\"dialogForm.FinishWorkday\" type=\"datetime\" placeholder=\"选择日期时间\"></el-date-picker>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"结束班次\" prop=\"FinishShift\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.FinishShift\" placeholder=\"请选择结束班次\">\n                <el-option v-for=\"item in shiftOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入计划数量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划编号\" prop=\"OrderNo\">\n              <el-input v-model=\"dialogForm.OrderNo\" placeholder=\"请输入计划编号\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"计划类型\" prop=\"Type\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.Type\" placeholder=\"请选择计划类型\">\n                <el-option v-for=\"item in typeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"备注\" prop=\"remark\">\n              <el-input v-model=\"dialogForm.Remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">确定</el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n  \n\n<script>\n  import {\n    getWeekScheduleDetail,\n    saveWeekScheduleForm,\n    getLineList\n  } from \"@/api/planManagement/weekSchedule\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        factoryOptions: [],\n        workshopOptions: [],\n        lineOptions: [],        \n        categoryOptions: [],\n        shiftOptions: [],\n        typeOptions: [],\n        currentRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.factoryOptions = await this.$getDataDictionary('WeekScheduleFactory');\n        this.workshopOptions = await this.$getDataDictionary('WeekScheduleWorkshop');\n        this.categoryOptions = await this.$getDataDictionary('WeekScheduleCategory');\n        this.shiftOptions = await this.$getDataDictionary('WeekScheduleShift');\n        this.typeOptions = await this.$getDataDictionary('WeekScheduleType');\n      },\n      async getLineList() {\n        const { response } = await getLineList({\n         //areaCode: 'PackingArea'\n         areaCode: 'Formulation'\n        })\n        console.log(response)\n        this.lineOptions = response\n      },\n      submit() {\n        saveWeekScheduleForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n      },\n      getDialogDetail(id){\n        getWeekScheduleDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      setFormLineName(EquipmentCode) {\n        console.log(EquipmentCode);\n        console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n        console.log(this.dialogForm);\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)        \n        this.dialogForm.MaterialId = val.ID\n        this.dialogForm.MaterialCode = val.Code\n        this.dialogForm.MaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}