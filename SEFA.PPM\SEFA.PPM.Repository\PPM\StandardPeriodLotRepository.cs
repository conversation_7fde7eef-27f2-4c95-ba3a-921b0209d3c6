﻿using SEFA.PPM.IRepository;
using SEFA.Base.IRepository.UnitOfWork;
using SEFA.PPM.Model.Models;
using SEFA.Base.Repository.Base;

namespace SEFA.PPM.Repository
{
	/// <summary>
	/// StandardPeriodLotRepository
	/// </summary>
    public class StandardPeriodLotRepository : BaseRepository<StandardPeriodLotEntity>, IStandardPeriodLotRepository
    {
        public StandardPeriodLotRepository(IUnitOfWork unitOfWork) : base(unitOfWork)
        {
        }
    }
}