{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue?vue&type=style&index=0&id=58f3b69f&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue", "mtime": 1749179659142}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci8vIOaWsOWinuagt+W8jwo6ZGVlcCgucGFyZW50LWRpYWxvZykgewogIHotaW5kZXg6IDIwMDAgIWltcG9ydGFudDsKICAKICAudi1tb2RhbCB7CiAgICB6LWluZGV4OiAxOTk5ICFpbXBvcnRhbnQ7CiAgfQp9Cg=="}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AA4PA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\n    <el-dialog :title=\"$t('GLOBAL._PCGDCF')\" :visible.sync=\"dialogVisible\" width=\"1200px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" \n      custom-class=\"parent-dialog\"\n      :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"产线\" prop=\"LineCode\">\n              <div>\n                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"物料\" prop=\"MaterialCode\">\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输计划数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-input v-model=\"dialogForm.StartWorkday\" placeholder=\"请输入开始工作日\" disabled />\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"8\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-input v-model=\"dialogForm.FinishWorkday\" placeholder=\"请输入结束工作日\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"首批时长\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入首批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"中间批时长\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"末批时长\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入末批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"配方版本\" prop=\"BomVersion\">\n              <el-input v-model=\"dialogForm.BomVersion\" placeholder=\"请输入配方版本\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"每批数量\" prop=\"LotQuantity\">\n              <el-input v-model=\"dialogForm.LotQuantity\" placeholder=\"请输入每批数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"批次总数\" prop=\"LotCount\">\n              <el-input v-model=\"dialogForm.LotCount\" placeholder=\"请输入批次总数\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"对应关系\" prop=\"StandardLotType\">\n              <el-select v-model=\"dialogForm.StandardLotType\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in standardPeriodTypeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"从第几批返工\" prop=\"ReworkLot\">\n              <el-input v-model=\"dialogForm.ReworkLot\" placeholder=\"请输入从第几批返工\" />\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"24\">\n            <el-form-item label=\"\" > -->\n              <el-table class=\"mt-3\"\n                :height=\"200\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n                <el-table-column prop=\"operation\" width=\"100\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"combination\">\n                      <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._TD') }}</el-button>\n                    </div>                        \n                  </template>\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                :default-sort=\"{prop: 'date', order: 'descending'}\"\n                                :key=\"item.ID\"\n                                :prop=\"item.field\"\n                                :label=\"item.label\"\n                                :width=\"item.width\"\n                                :align=\"item.alignType\"\n                                sortable\n                                show-overflow-tooltip\n                >\n                  <template slot-scope=\"scope\">\n                    {{ scope.row[item.field] }}\n                  </template>\n                </el-table-column>\n              </el-table>\n            <!-- </el-form-item>\n          </el-col> -->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">拆分</el-button>\n      </div>      \n      <BomDetailForm @saveForm=\"setBomRowData\" ref=\"formDialog\" />\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    //getLineList,\n    getSplitBatchInfo,\n    splitBatch\n  } from \"@/api/planManagement/weekSchedule\";\n  import BomDetailForm from './bomDetailForm'\n  import {getTableHead} from \"@/util/dataDictionary.js\";\n  export default {\n    components:{\n      BomDetailForm\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        // factoryOptions: [],\n        // workshopOptions: [],\n        // lineOptions: [],        \n        // categoryOptions: [],\n        // shiftOptions: [],\n        // typeOptions: [],\n        standardPeriodTypeOptions: [],\n        tableName : [],\n        hansObjDrawer: this.$t('WeekFormulation.bomDetail'),\n        tableOption: [\n          {code: 'SegmentName', width: 100, align: 'center'},  \n          {code: 'Sort', width: 80, align: 'center'},\n          {code: 'MaterialCode', width: 150, align: 'left'},\n          {code: 'MaterialName', width: 180, align: 'left'},\n          {code: 'MaterialType', width: 100, align: 'left'},\n          {code: 'InsteadMaterialCode', width: 150, align: 'left'},\n          {code: 'InsteadMaterialName', width: 180, align: 'left'},\n          {code: 'Unit', width: 100, align: 'center'},\n          {code: 'StandardQuantity', width: 100, align: 'left'},\n          {code: 'PlanQuantity', width: 100, align: 'left'},\n          {code: 'Remark', width: 180, align: 'left'},\n        ],\n        tableData : [],\n        currentRow: {},\n        currentBomRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      //this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType');\n      },\n      // async getLineList() {\n      //   const { response } = await getLineList({\n      //    //areaCode: 'PackingArea'\n      //    areaCode: 'Formulation'\n      //   })\n      //   console.log(response)\n      //   this.lineOptions = response\n      // },\n      submit() {\n        splitBatch(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.initTableHead()\n        this.$nextTick(_ => {\n          if(data.ID){\n            //console.log(\"show\")\n            this.getDialogInfo(data)\n          }\n        })\n      },\n      initTableHead() {\n        this.tableName = getTableHead(this.hansObjDrawer, this.tableOption)\n      },\n      getDialogInfo(data){\n        //console.log(\"getDialogInfo\")\n        getSplitBatchInfo(data).then(res => {\n          this.dialogForm = res.response\n          this.tableData = res.response.wsBomModels\n        })\n      },    \n      showDialog(row) {\n        this.currentBomRow = row\n        this.$refs.formDialog.show(row)\n      },\n      async setBomRowData(val) {\n        console.log(\"setBomRowData\")\n        console.log(val)\n        this.currentBomRow.MaterialId = val.MaterialId\n        this.currentBomRow.MaterialCode = val.MaterialCode\n        this.currentBomRow.MaterialName = val.MaterialCode\n        this.$forceUpdate()\n        // const { response } = await getWeekScheduleBomList({\n        //   WeekScheduleId: this.currentRow.ID,\n        //   ...this.searchForm\n        // })\n        // this.tableData = response\n      },\n    }\n  }\n  </script>\n\n<style lang=\"scss\" scoped>\n// 新增样式\n:deep(.parent-dialog) {\n  z-index: 2000 !important;\n  \n  .v-modal {\n    z-index: 1999 !important;\n  }\n}\n</style>"]}]}