{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue?vue&type=style&index=0&id=5e9d0936&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\batchDcs\\index.vue", "mtime": 1749177894365}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1743379022465}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1743379015507}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1743379017016}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1743379015023}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5lbC1mb3JtLWl0ZW0tLXNtYWxsLmVsLWZvcm0taXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMHB4Owp9CgoubXQtOHAgewogIG1hcmdpbi10b3A6IDhweDsKfQoKLnBkLWxlZnQgewogIHBhZGRpbmctbGVmdDogNXB4Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAoNA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/planManagement/batchDcs", "sourcesContent": ["<!--\n * @Descripttion: (批次DCS下发/PPM_B_BATCH_DCS)\n * @version: (1.0)\n * @Author: (admin)\n * @Date: (2025-05-14)\n * @LastEditors: (admin)\n * @LastEditTime: (2025-05-14)\n-->\n\n<template>\n  <div class=\"root\">\n    <div class=\"root-head\">\n      <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n      \n      <el-form-item label=\"产线\" prop=\"LineName\">\n        <el-input v-model=\"searchForm.LineName\" placeholder=\"请输入产线名称\" />\n      </el-form-item>\n\n      <el-form-item label=\"工单号\" prop=\"PoNo\">\n        <el-input v-model=\"searchForm.PoNo\" placeholder=\"请输入工单号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"批次号\" prop=\"BatchtNo\">\n        <el-input v-model=\"searchForm.BatchtNo\" placeholder=\"请输入批次号\" />\n      </el-form-item>\n    \t\t\t\t    \n      <el-form-item label=\"物料代码\" prop=\"MaterialCode\">\n        <el-input v-model=\"searchForm.MaterialCode\" placeholder=\"请输入物料代码\" />\n      </el-form-item>\n\n        <el-form-item class=\"mb-2\">\n          <el-button icon=\"el-icon-search\" @click=\"getSearchBtn()\">{{ $t('GLOBAL._CX') }}</el-button>\n        </el-form-item>\n      </el-form>\n    </div>\n    <div class=\"root-main\">\n      <el-table class=\"mt-3\"\n                :height=\"mainH\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n        <el-table-column v-for=\"(item) in tableName\"\n                         :default-sort=\"{prop: 'date', order: 'descending'}\"\n                         :key=\"item.ID\"\n                         :prop=\"item.field\"\n                         :label=\"item.label\"\n                         :width=\"item.width\"\n                         :align=\"item.alignType\"\n                         sortable\n                         show-overflow-tooltip\n        >\n          <template slot-scope=\"scope\">\n            {{ scope.row[item.field] }}\n          </template>\n        </el-table-column>\n        <!-- <el-table-column prop=\"operation\" width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n          <template slot-scope=\"scope\">\n            <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._BJ') }}</el-button>\n            <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n          \n          </template>\n        </el-table-column> -->\n      </el-table>\n    </div>\n    <div class=\"root-footer\">\n      <el-pagination\n          class=\"mt-3\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"handleCurrentChange\"\n          :current-page=\"searchForm.pageIndex\"\n          :page-sizes=\"[10,20, 50, 100,500]\"\n          :page-size=\"searchForm.pageSize\"\n          layout=\"->,total, sizes, prev, pager, next, jumper\"\n          :total=\"total\"\n          background\n      ></el-pagination>\n    </div>\n    <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\"></form-dialog>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss';\n\nimport FormDialog from './form-dialog'\nimport {\n    delBatchDcs, getBatchDcsList\n} from \"@/api/planManagement/batchDcs\";\nimport {getTableHead} from \"@/util/dataDictionary.js\";\n\n//import { batchDcsColumn } from '@/columns/planManagement/batchDcs.js';\n\n\nexport default {\n  name: 'index.vue',\n  components: {\n    //UploadButton,\n    FormDialog,\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n      },\n      total: 0,\n      tableData: [{}],\n      hansObj: this.$t('BatchDcs.table'),\n      tableName: [],\n      loading: false,\n      tableOption: [\n      {code: 'LineName', width: 130, align: 'center'},\n      {code: 'PoNo', width: 180, align: 'center'},\n      {code: 'BatchtNo', width: 130, align: 'center'},\n      {code: 'MaterialCode', width: 180, align: 'center'},\n      {code: 'MaterialName', width: 180, align: 'center'},\n      {code: 'Unit', width: 130, align: 'center'},\n      {code: 'StandardQuantity', width: 130, align: 'center'},\n      {code: 'PlanQuantity', width: 130, align: 'center'},\n      {code: 'Status', width: 130, align: 'center'},\n      {code: 'SendData', width: 200, align: 'center'},\n      {code: 'ResponseData', width: 200, align: 'center'},\n      ],\n      mainH: 0,\n      buttonOption:{\n        name:'批次DCS下发',\n        serveIp:'baseURL_PPM',\n        // uploadUrl:'/api/BatchDcs/ImportData', //导入\n        // exportUrl:'/api/BatchDcs/ExportData', //导出\n        // DownLoadUrl:'/api/BatchDcs/DownLoadTemplate', //下载模板\n      }\n    }\n  },\n  mounted() {\n    this.getZHHans()\n    this.getTableData()\n    this.$nextTick(() => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    })\n    window.onresize = () => {\n      this.mainH = this.$webHeight(document.getElementsByClassName('root-head')[0].clientHeight, document.getElementsByClassName('root')[0].clientHeight)\n    }\n  },\n  methods: {\n    getZHHans() {\n      for (let key in this.hansObj) {\n        this.tableName = getTableHead(this.hansObj, this.tableOption)\n      }\n    },\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    delRow(row) {\n      this.$confirms({\n        title: this.$t('GLOBAL._TS'),\n        message: this.$t('GLOBAL._COMFIRM'),\n        confirmText: this.$t('GLOBAL._QD'),\n        cancelText: this.$t('GLOBAL._QX')\n      }).then(async () => {\n        delBatchDcs([row.ID]).then(res => {\n          this.$message.success(res.msg)\n          this.getTableData()\n        })\n      }).catch(err => {\n        console.log(err);\n      });\n    },\n\n    getTableData(data) {\n      getBatchDcsList(this.searchForm).then(res => {\n        this.tableData = res.response.data\n        this.total = res.response.dataCount\n      })\n    }\n  }\n}\n\n//<!-- 移到到src/local/en.json和zh-Hans.json -->\n//\"BatchDcs\": {\n//    \"table\": {\n//        \"poNo\": \"poNo\",\n//        \"batchtNo\": \"batchtNo\",\n//        \"materialCode\": \"materialCode\",\n//        \"unit\": \"unit\",\n//        \"standardQuantity\": \"standardQuantity\",\n//        \"planQuantity\": \"planQuantity\",\n//        \"status\": \"status\",\n//        \"sendData\": \"sendData\",\n//        \"responseData\": \"responseData\",\n//        \"remark\": \"remark\",\n//        \"createdate\": \"createdate\",\n//        \"createuserid\": \"createuserid\",\n//        \"modifydate\": \"modifydate\",\n//        \"modifyuserid\": \"modifyuserid\",\n//    }\n//},\n</script>\n\n<style lang=\"scss\" scoped>\n.el-form-item--small.el-form-item {\n  margin-bottom: 0px;\n}\n\n.mt-8p {\n  margin-top: 8px;\n}\n\n.pd-left {\n  padding-left: 5px\n}\n</style>"]}]}