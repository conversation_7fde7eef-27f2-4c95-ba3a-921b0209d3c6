{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\weekFormulation\\form-dialog.vue", "mtime": 1749180158972}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["form-dialog.vue"], "names": [], "mappings": ";AAyIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "form-dialog.vue", "sourceRoot": "src/views/planManagement/weekFormulation", "sourcesContent": ["<template>\n    <el-dialog :title=\"$t('GLOBAL._PCGDCF')\" :visible.sync=\"dialogVisible\" width=\"1200px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"产线\" prop=\"LineCode\">\n              <div>\n                {{dialogForm.LineCode}}&nbsp; &nbsp; {{dialogForm.LineName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"物料\" prop=\"MaterialCode\">\n              <div>\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"计划数量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输计划数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"单位\" prop=\"Unit\">\n              <el-input v-model=\"dialogForm.Unit\" placeholder=\"请输入单位\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"开始工作日\" prop=\"StartWorkday\">\n              <el-input v-model=\"dialogForm.StartWorkday\" placeholder=\"请输入开始工作日\" disabled />\n            </el-form-item>\n          </el-col>\n          \n          <el-col :lg=\"8\">\n            <el-form-item label=\"结束工作日\" prop=\"FinishWorkday\">\n              <el-input v-model=\"dialogForm.FinishWorkday\" placeholder=\"请输入结束工作日\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"首批时长\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入首批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"中间批时长\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"末批时长\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入末批时长\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"配方版本\" prop=\"BomVersion\">\n              <el-input v-model=\"dialogForm.BomVersion\" placeholder=\"请输入配方版本\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"每批数量\" prop=\"LotQuantity\">\n              <el-input v-model=\"dialogForm.LotQuantity\" placeholder=\"请输入每批数量\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"批次总数\" prop=\"LotCount\">\n              <el-input v-model=\"dialogForm.LotCount\" placeholder=\"请输入批次总数\" disabled />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"对应关系\" prop=\"StandardLotType\">\n              <el-select v-model=\"dialogForm.StandardLotType\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in standardPeriodTypeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"8\">\n            <el-form-item label=\"从第几批返工\" prop=\"ReworkLot\">\n              <el-input v-model=\"dialogForm.ReworkLot\" placeholder=\"请输入从第几批返工\" />\n            </el-form-item>\n          </el-col>\n\n          <!-- <el-col :lg=\"24\">\n            <el-form-item label=\"\" > -->\n              <el-table class=\"mt-3\"\n                :height=\"200\"\n                border\n                :data=\"tableData\"\n                style=\"width: 100%\">\n                <el-table-column prop=\"operation\" width=\"100\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"combination\">\n                      <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._TD') }}</el-button>\n                    </div>                        \n                  </template>\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                :default-sort=\"{prop: 'date', order: 'descending'}\"\n                                :key=\"item.ID\"\n                                :prop=\"item.field\"\n                                :label=\"item.label\"\n                                :width=\"item.width\"\n                                :align=\"item.alignType\"\n                                sortable\n                                show-overflow-tooltip\n                >\n                  <template slot-scope=\"scope\">\n                    {{ scope.row[item.field] }}\n                  </template>\n                </el-table-column>\n              </el-table>\n            <!-- </el-form-item>\n          </el-col> -->\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\" @click=\"submit()\">拆分</el-button>\n      </div>      \n      <BomDetailForm @saveForm=\"setBomRowData\" ref=\"formDialog\" />\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    //getLineList,\n    getSplitBatchInfo,\n    splitBatch\n  } from \"@/api/planManagement/weekSchedule\";\n  import BomDetailForm from './bomDetailForm'\n  import {getTableHead} from \"@/util/dataDictionary.js\";\n  export default {\n    components:{\n      BomDetailForm\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        // factoryOptions: [],\n        // workshopOptions: [],\n        // lineOptions: [],        \n        // categoryOptions: [],\n        // shiftOptions: [],\n        // typeOptions: [],\n        standardPeriodTypeOptions: [],\n        tableName : [],\n        hansObjDrawer: this.$t('WeekFormulation.bomDetail'),\n        tableOption: [\n          {code: 'SegmentName', width: 100, align: 'center'},  \n          {code: 'Sort', width: 80, align: 'center'},\n          {code: 'MaterialCode', width: 150, align: 'left'},\n          {code: 'MaterialName', width: 180, align: 'left'},\n          {code: 'MaterialType', width: 100, align: 'left'},\n          {code: 'InsteadMaterialCode', width: 150, align: 'left'},\n          {code: 'InsteadMaterialName', width: 180, align: 'left'},\n          {code: 'Unit', width: 100, align: 'center'},\n          {code: 'StandardQuantity', width: 100, align: 'left'},\n          {code: 'PlanQuantity', width: 100, align: 'left'},\n          {code: 'Remark', width: 180, align: 'left'},\n        ],\n        tableData : [],\n        currentRow: {},\n        currentBomRow: {},\n        matInfo:{}\n      }\n    },\n    created() {\n      this.initDictList();\n      //this.getLineList();\n    },\n    mounted() {\n    },\n    methods: {\n      async initDictList(){\n        this.standardPeriodTypeOptions = await this.$getDataDictionary('StandardPeriodType');\n      },\n      // async getLineList() {\n      //   const { response } = await getLineList({\n      //    //areaCode: 'PackingArea'\n      //    areaCode: 'Formulation'\n      //   })\n      //   console.log(response)\n      //   this.lineOptions = response\n      // },\n      submit() {\n        splitBatch(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.initTableHead()\n        this.$nextTick(_ => {\n          if(data.ID){\n            //console.log(\"show\")\n            this.getDialogInfo(data)\n          }\n        })\n      },\n      initTableHead() {\n        this.tableName = getTableHead(this.hansObjDrawer, this.tableOption)\n      },\n      getDialogInfo(data){\n        //console.log(\"getDialogInfo\")\n        getSplitBatchInfo(data).then(res => {\n          this.dialogForm = res.response\n          this.tableData = res.response.wsBomModels\n        })\n      },    \n      showDialog(row) {\n        this.currentBomRow = row\n        this.$refs.formDialog.show(row)\n      },\n      async setBomRowData(val) {\n        console.log(\"setBomRowData\")\n        console.log(val)\n        this.currentBomRow.MaterialId = val.MaterialId\n        this.currentBomRow.MaterialCode = val.MaterialCode\n        this.currentBomRow.MaterialName = val.MaterialCode\n        this.$forceUpdate()\n        // const { response } = await getWeekScheduleBomList({\n        //   WeekScheduleId: this.currentRow.ID,\n        //   ...this.searchForm\n        // })\n        // this.tableData = response\n      },\n    }\n  }\n  </script>"]}]}