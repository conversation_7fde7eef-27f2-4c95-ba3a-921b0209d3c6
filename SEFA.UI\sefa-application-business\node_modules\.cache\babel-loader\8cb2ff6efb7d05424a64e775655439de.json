{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\form-dialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\planManagement\\standardPeriodLot\\form-dialog.vue", "mtime": 1749177894387}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAyFA,SACAA,0BADA,EAEAC,yBAFA,EAGAC,WAHA,QAIA,wCAJA;AAKA;AACA;EACAC;IACAC;EADA,CADA;;EAIAC;IACA;MACAC,cADA;MAEAC,oBAFA;MAGAC,kBAHA;MAIAC,eAJA;MAKAC;IALA;EAOA,CAZA;;EAaAC,WACA,CAdA;;EAeAC;IACA;IACA;EACA,CAlBA;;EAmBAC;IACAC;MACAb;QACA;QACA;QACA;MACA,CAJA;IAKA,CAPA;;IAQA;MACA;IACA,CAVA;;IAWAc;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA,CAJA;IAMA,CArBA;;IAsBAC;MACAhB;QACA;MACA,CAFA;IAGA,CA1BA;;IA2BA;MACA;QAAAiB;MAAA;QACA;QACAC;MAFA;MAIAC;MACA;IACA,CAlCA;;IAmCAC;MACA;MACA;MACA,0FAHA,CAIA;;MACA,uGALA,CAMA;IACA,CA1CA;;IA2CAC;MACA;MACA;MACA;MACA;MACA;MACA,oBANA,CAOA;MACA;MACA;IACA,CArDA;;IAsDAC;MACA;IACA;;EAxDA;AAnBA", "names": ["getStandardPeriodLotDetail", "saveStandardPeriodLotForm", "getLineList", "components", "MaterialTable", "data", "dialogForm", "dialogVisible", "formLoading", "lineOptions", "currentRow", "mounted", "created", "methods", "submit", "show", "getDialogDetail", "response", "areaCode", "console", "setFormLineName", "setMaterial", "openMaterialTable"], "sourceRoot": "src/views/planManagement/standardPeriodLot", "sources": ["form-dialog.vue"], "sourcesContent": ["<template>\n    <el-dialog :title=\"dialogForm.ID ? $t('GLOBAL._BJ') : $t('GLOBAL._XZ')\" :visible.sync=\"dialogVisible\" width=\"700px\"\n      :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :close-on-press-escape=\"false\"\n      @close=\"dialogVisible = false\">\n      <el-form ref=\"dialogForm\" :model=\"dialogForm\" label-width=\"130px\">\n       \n          <el-col :lg=\"12\">\n            <el-form-item label=\"产线\" prop=\"LindCode\">\n              <el-select filterable clearable style=\"width: 100%\" v-model=\"dialogForm.LindCode\" placeholder=\"请选择\" @change=\"setFormLineName\">\n                    <el-option v-for=\"(item, index) in lineOptions\" :key=\"index\" :label=\"item.EquipmentName\"\n                        :value=\"item.EquipmentCode\">\n                    </el-option>\n                </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"物料\">\n              <div>\n                <el-button icon=\"el-icon-plus\" type=\"text\" @click=\"openMaterialTable\">{{ $t('GLOBAL._CX') }}</el-button>\n              </div>\n              <div v-if=\"dialogForm&&dialogForm.MaterialCode\">\n                {{dialogForm.MaterialCode}}&nbsp; &nbsp; {{dialogForm.MaterialName}}\n              </div>\n            </el-form-item>\n          </el-col>          \n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"对应关系\" prop=\"Type\">\n              <el-select v-model=\"dialogForm.Type\" placeholder=\"请选择对应关系\" clearable style=\"width:100%\">\n                <el-option v-for=\"item in typeOptions\" :key=\"item.ItemValue\" :label=\"item.ItemName\" :value=\"item.ItemValue\" />\n              </el-select>\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"第一批用时\" prop=\"FirstLotPeriod\">\n              <el-input v-model=\"dialogForm.FirstLotPeriod\" placeholder=\"请输入第一批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"中间批用时\" prop=\"MiddleLotPeriod\">\n              <el-input v-model=\"dialogForm.MiddleLotPeriod\" placeholder=\"请输入中间批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最后一批用时\" prop=\"LastLotPeriod\">\n              <el-input v-model=\"dialogForm.LastLotPeriod\" placeholder=\"请输入最后一批用时\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"批次量\" prop=\"PlanQuantity\">\n              <el-input v-model=\"dialogForm.PlanQuantity\" placeholder=\"请输入批次量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最小成批量\" prop=\"MinLotQuantity\">\n              <el-input v-model=\"dialogForm.MinLotQuantity\" placeholder=\"请输入最小成批量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"最大成批量\" prop=\"MaxLotQuantity\">\n              <el-input v-model=\"dialogForm.MaxLotQuantity\" placeholder=\"请输入最大成批量\" />\n            </el-form-item>\n          </el-col>\n\n          <el-col :lg=\"12\">\n            <el-form-item label=\"备注\" prop=\"Remark\">\n              <el-input v-model=\"dialogForm.Remark\" placeholder=\"请输入备注\" />\n            </el-form-item>\n          </el-col>\n\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button size=\"small\" @click=\"dialogVisible = false\">取 消</el-button>\n        <el-button v-loading=\"formLoading\" :disabled=\"formLoading\" element-loading-spinner=\"el-icon-loading\" size=\"small\"\n          @click=\"submit()\">确定\n        </el-button>\n      </div>\n      <material-table :is-id=\"false\" ref=\"materialTable\" @saveForm=\"setMaterial\"></material-table>\n    </el-dialog>\n  </template>\n\n<script>\n  import {\n    getStandardPeriodLotDetail,\n    saveStandardPeriodLotForm,\n    getLineList\n  } from \"@/api/planManagement/standardPeriodLot\";\n  import MaterialTable from '@/components/MaterialTable.vue';\n  export default {\n    components:{\n      MaterialTable\n    },\n    data() {\n      return {\n        dialogForm: {},\n        dialogVisible: false,\n        formLoading: false,\n        lineOptions: [],\n        currentRow: {},\n      }\n    },\n    mounted() {\n    },\n    created() {\n      this.initDictList();\n      this.getLineList();\n    },\n    methods: {\n      submit() {\n        saveStandardPeriodLotForm(this.dialogForm).then(res=>{\n          this.$message.success(res.msg)\n          this.$emit('saveForm')\n          this.dialogVisible = false\n        })\n      },\n      async initDictList(){\n        this.typeOptions = await this.$getDataDictionary('StandardPeriodType')    \n      },\n      show(data) {\n        this.dialogForm = {}\n        this.currentRow = data\n        this.dialogVisible = true\n        this.$nextTick(_ => {\n          if(data.ID){\n            this.getDialogDetail(data.ID)\n          }\n        })\n\n      },\n      getDialogDetail(id){\n        getStandardPeriodLotDetail(id).then(res => {\n          this.dialogForm = res.response\n        })\n      },\n      async getLineList() {\n        const { response } = await getLineList({\n         //areaCode: 'PackingArea'\n         areaCode: 'Formulation'\n        })\n        console.log(response)\n        this.lineOptions = response\n      },\n      setFormLineName(EquipmentCode) {\n        //console.log(EquipmentCode);\n        //console.log(this.lineOptions.find(e => e.EquipmentCode === EquipmentCode));\n        this.dialogForm.LineId = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).ID\n        //this.dialogForm.LineName = this.lineOptions.find(e => e.ID === id).EquipmentName\n        this.dialogForm.LineCode = this.lineOptions.find(e => e.EquipmentCode === EquipmentCode).EquipmentCode\n        //console.log(this.dialogForm);\n      },\n      setMaterial(val){\n        // console.log(\"setMaterial\")\n        // console.log(val)\n        this.dialogForm.MaterialId = val.ID\n        this.dialogForm.MaterialCode = val.Code\n        this.dialogForm.MaterialName = val.NAME\n        this.$forceUpdate()\n        // this.matInfo = val        \n        // console.log(this.dialogForm.MaterialCode)\n        // console.log(this.dialogForm.MaterialName)\n      },\n      openMaterialTable(){\n        this.$refs['materialTable'].show()\n      }\n    }\n  }\n  </script>"]}]}