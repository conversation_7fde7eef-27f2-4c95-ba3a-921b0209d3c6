﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
using SEFA.Base.Model;

namespace SEFA.PPM.Model.ViewModels
{
    public class StandardPeriodLotRequestModel : RequestPageModelBase
    {
        public StandardPeriodLotRequestModel()
        {
        }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线代码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string LineCode { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料代码
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料名称
           /// Default:
           /// Nullable:False
           /// </summary>
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:物料版本ID
           /// Default:
           /// Nullable:True
           /// </summary>
        public string MaterialVer { get; set; }
           /// <summary>
           /// Desc:对应关系
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Type { get; set; }
           /// <summary>
           /// Desc:第一批用时
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? FirstLotPeriod { get; set; }
           /// <summary>
           /// Desc:中间批用时
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? MiddleLotPeriod { get; set; }
           /// <summary>
           /// Desc:最后一批用时
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? LastLotPeriod { get; set; }
           /// <summary>
           /// Desc:批次量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? PlanQuantity { get; set; }
           /// <summary>
           /// Desc:最小成批量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? MinLotQuantity { get; set; }
           /// <summary>
           /// Desc:最大成批量
           /// Default:
           /// Nullable:True
           /// </summary>
        public decimal? MaxLotQuantity { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
        public string Remark { get; set; }

    }
}