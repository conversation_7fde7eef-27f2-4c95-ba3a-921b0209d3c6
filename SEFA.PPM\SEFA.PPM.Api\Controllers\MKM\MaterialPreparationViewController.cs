﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SEFA.Base;
using SEFA.Base.Model;
using SEFA.MKM.IServices;
using SEFA.MKM.Model.Models;
using SEFA.MKM.Model.ViewModels;
using SEFA.MKM.Model.ViewModels.View;
using SEFA.PPM.Controllers;
using SEFA.PPM.Model.Models;
using SEFA.PPM.Model.ViewModels;
using SEFA.PPM.Model.ViewModels.MKM.View;
using SEFA.PPM.Model.ViewModels.View;
using SqlSugar;

namespace SEFA.MKMApi.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    [AllowAnonymous]
    public class MaterialPreparationViewController : BaseApiController
    {
        /// <summary>
        /// MaterialPreparationView
        /// </summary>
        private readonly IMaterialPreparationViewServices _materialPreparationViewServices;

        private readonly IBBatchDetailIiViewServices _bBatchDetailIiViewServices;

        public MaterialPreparationViewController(IMaterialPreparationViewServices MaterialPreparationViewServices,
            IBBatchDetailIiViewServices bBatchDetailIiViewServices)
        {
            _materialPreparationViewServices = MaterialPreparationViewServices;
            _bBatchDetailIiViewServices = bBatchDetailIiViewServices;
        }

        [HttpPost]
        public async Task<MessageModel<List<MaterialPreparationViewEntity>>> GetList(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetList(reqModel);
            return Success(data, "获取成功");
        }

        #region 界面操作

        #region 查询

        #region 工单工序备料（生产批次备料）

        /// <summary>
        /// ROOM下拉框数据查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MEquipmentroomViewEntity>>> GetRoomSelectList()
        {
            var data = await _materialPreparationViewServices.GetRoomSelectList();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// ROOM下拉框数据查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MEquipmentroomViewEntity>>> GetRoomSelectListCLBL()
        {
            var data = await _materialPreparationViewServices.GetRoomSelectListCLBL();
            return Success(data, "获取成功");
        }


        /// <summary>
        /// ROOM下拉框数据查询
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MEquipmentroomViewEntity>>> GetRoomCLBLByEquipmentID(
            MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetRoomSelectListByName(reqModel.EquipmentCode);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 原料标签物料标签
        /// </summary>
        /// <param name="reqModel">工单id和ROOM数据</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<SelectDataModel>>> Get_SelectMaterial(
            MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.Get_SelectMaterial(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 原料标签工单号下拉框
        /// </summary>
        /// <param name="reqModel">传入ROOMID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<SelectDataModel>>> Get_SelectPro(
            MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.Get_SelectPro(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据EquipmentName查询对应的数据()
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MEquipmentroomViewEntity>>> GetRoomSelectListByPG(CallModel name)
        {
            var data = await _materialPreparationViewServices.GetRoomSelectListByName(name.Data);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据EquipmentName查询对应的数据()
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MEquipmentroomViewEntity>>> GetRoomSelectListBySC(CallModel name)
        {
            var data = await _materialPreparationViewServices.GetRoomSelectListByName(name.Data);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 工单工序批次备料查询 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> GetPageList(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取最后界面物料明细(根据物料id)(Top)
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMaterialViewEntity>>> GetPageListMaterialPreTop(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchDetailMaterial(reqModel, false, false);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取最后界面物料明细(根据物料id)(Down)（拼锅）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMaterialViewEntity>>> GetDown(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchDetailMaterialPG(reqModel, true, true);
            return Success(data, "获取成功");
        }


        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMaterialViewEntity>>> GetNewDown(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageListNewMaterial(reqModel, true, true, true);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取物料的耗用量(不传参数默认查询糖的)
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ViewEntity>>> GetMateriaConsumlList(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            if (string.IsNullOrEmpty(reqModel.mId) && string.IsNullOrEmpty(reqModel.MCode))
            {
                reqModel.MCode = "7300030001"; //白糖
            }

            var data = await _materialPreparationViewServices.GetMateriaConsumlList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 拼锅首页打开传参  Pids(list)  MCode "7300030001"
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ViewEntity>>> GetMateriaConsumlListPG(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            if (string.IsNullOrEmpty(reqModel.mId) && string.IsNullOrEmpty(reqModel.MCode))
            {
                reqModel.MCode = "7300030001"; //白糖
            }

            var data = await _materialPreparationViewServices.GetMateriaConsumlListPG(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 容器下拉框
        /// </summary>
        /// <param name="proOrderID"></param>
        /// <param name="batchID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ContainerEntity>>> GetConSelectList(string proOrderID, string batchID)
        {
            var data = await _materialPreparationViewServices.GetContainerSelectList(proOrderID, batchID);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询最后一页容器下拉框
        /// </summary>
        /// <param name="proOrderID"></param>
        /// <param name="batchID"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MContainerViewEntity>>> ContainerSelectList(string proOrderID,
            string batchID)
        {
            var data = await _materialPreparationViewServices.GetConSelectList(proOrderID, batchID);

            var distinctItems = data
                .GroupBy(item => new { item.ID, item.ContainerName, item.ContainerState })
                .Select(group => group.First()).OrderByDescending(p => p.ContainerState).ThenBy(p => p.ModifyDate)
                .ToList();

            return Success(distinctItems, "获取成功");
        }
        //Task<List<MContainerViewEntity>> (string proOrderID, string batchID);

        #region 跳转界面第一层级显示（工序等信息）

        /// <summary>
        /// 工序等信息(标题)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MBatchriiViewEntity>>> GetSegmentList(MBatchriiViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetSegmentList(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 备料跳转第一界面根据proOrderID订单号查询批次(这里需要根据batch表批次排序)
        /// </summary>
        /// <param name="proOrderID">订单ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailViewEntity>>> GetByList(string proOrderID)
        {
            var data = await _materialPreparationViewServices.GetByProOrderID(proOrderID);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)
        /// </summary>
        /// <param name="proOrderID">订单ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailViewEntity>>> GetByProOrderIDOLD(string proOrderID,
            string EquipmentId)
        {
            var data = await _materialPreparationViewServices.GetByList(proOrderID, EquipmentId);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)（拼锅）
        /// </summary>
        /// <param name="proOrderID">订单ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailViewEntity>>> GetByProOrderID(string proOrderID,
            string EquipmentId)
        {
            var data = await _materialPreparationViewServices.GetByList(proOrderID, EquipmentId);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 根据批次ID获取对应的物料信息(混料机下面的一层的一层数据)
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailIiViewEntity>>> GetListByBatchID(string batchID,
            string eqpmentID)
        {
            var data = await _bBatchDetailIiViewServices.GetBatchPreparationDetailList(batchID, eqpmentID);
            // var data = await _materialPreparationViewServices.GetPrepareationListByBatchID(batchID, eqpmentID);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 拼锅是否完成
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <param name="eqpmentID">roomID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> IsFinish_PG(BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.IsFinish(model.batchID, model.eqpmentID);
            if (data == true)
            {
                return Success("", "成功");
            }
            else
            {
                return Success("", "失败");
            }
        }


        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailIiViewEntity>>> GetPageListByBatchIDS(
            BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchIDS(model);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBdetailIiViewEntity>>> GetPageListByBatchIDSII(
            BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchIDSII(model);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailIiViewEntity>>> GetPageListByMaterial(BBatchDModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByMaterial(model);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBdetailIiViewEntity>>> GetPageListByMaterialII(BBatchDModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByMaterialII(model);
            return Success(data, "获取成功");
        }

        #endregion

        #endregion

        #endregion

        #region 活动

        #endregion

        #endregion

        #region 工单工序批次备料查询 （按照生产批次）

        /// <summary>
        /// 创建托盘
        /// </summary>
        /// <param name="plateAddModel">TareWeight 目标重量来自于本页最上面的 Target   UomID(Target的单位id,来自于)  ProBatchID   EquipMentID  MaterialId  ProRequestID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> AddPallet(MPPallentAddModel plateAddModel)
        {
            var data = await _materialPreparationViewServices.AddPallet(plateAddModel);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 创建托盘，部分数据源来自上一个跳转界面PRODUCTION_BATCH_ID、PRODUCTION_REQUEST_ID、EQUIPMENT_ID
        /// </summary>
        /// <param name="tareWeight">所有重量换算kg相加</param>
        /// <param name="uomID">单位</param>
        /// <param name="proBatchID">生产批次ID（参数重要用来判断是否添加了托盘信息）</param>
        /// <param name="proRequestID">工单ID</param>
        /// <param name="equipMentID">存储区ID</param>
        /// <param name="materialId">物料ID</param>
        /// <param name="lotID">物料批次</param>
        /// <param name="subLotID">物料子批次</param>
        /// <returns></returns>
        //public async Task<MessageModel<string>> AddPallet(int tareWeight, string uomID, string proBatchID, string proRequestID, string equipMentID, string materialId, string lotID, string subLotID)

        /// <summary>
        /// 根据批次号获取对应的容器（批次）
        /// </summary>
        /// <param name="batchID">生产批次ID</param>
        /// <param name="stateClass"(默认传入"BatchPallet")></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ContainerEntity>>> GetContainerBatchPalletList(string batchID)
        {
            var data = await _materialPreparationViewServices.GetContainerBatchPalletList(batchID, "BatchPallet");
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 获取备料主页右上角下拉框里面的EquipmentId需要传入到后面的界面使用
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<MessageModel<List<DicMaterialPreselectViewEntity>>> GetMarialPreSelect()
        {
            var data = await _materialPreparationViewServices.GetMarialPreSelect();
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 备料界面最下面的容器下拉框
        /// </summary>
        /// <param name="batchID">根据批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_MATERIAL_VIEW）</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<DicBatchPalletselectViewEntity>>> GetContainerSelect(string batchID)
        {
            var data = await _materialPreparationViewServices.GetContainerSelect(batchID);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询最下面batch pallets
        /// </summary>
        /// <param name="reqModel">只需要传入批次表的LotId 和分页参数</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMcontainerViewEntity>>> GetBatchPalletsPageList(
            [FromBody] BBatchDetailMcontainerViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageBatchDetailList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询实际耗用量（所有容器的）
        /// </summary>
        /// <param name="batchID">根据生产批次来查询容器（这里来自于上层数据V_PPM_B_BATCH_DETAIL_II_VIEW）</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetContainerSelectQuantity(string batchID)
        {
            var data = new MessageModel<string>();
            var dataS = await _materialPreparationViewServices.GetContainerSelectQuantity(batchID);
            return Success(dataS.ToString(), "获取成功");
        }

        /// <summary>
        /// 拼锅-转移-选可用库存
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>   
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_FullBag(FullBagModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_FullBag(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 备料转移PartialBag（只用于按照物料分）
        /// </summary> 
        /// <param name="subID">子批次</param>
        /// <param name="inputBagWeight">包数</param>
        /// <param name="bagsWeight">单包重量</param>
        /// <param name="targetWeight">目标重量在视图最上面</param>
        /// <param name="actualWeight">目标重量在视图最上面之前容器量</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_PartialBag(PartialBagModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_PartialBag(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 备料转移PartialBag_Merge（前端校验数量）
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="inputBagWeight">输入拆分数量</param>       
        /// <param name="ssccCode">子批次code</param>      
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_Merge(MergeModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_Merge(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 补打小标签
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> Preparation_PrintMinLable(MergeModel reqModel)
        {
            var data = await _materialPreparationViewServices.PrintMINLable(reqModel);
            if (data.success)
            {
                return Success("", "补打成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 新备料转移NewFullAmount（前端校验数量）
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>      
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_NewFullAmount(FullAmountModel reqModel)
        {
            //return Success("", "MPreparationTransfer_NewFullAmount");
            var data = await _materialPreparationViewServices.MPreparationTransfer_NewFullAmount(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 备料转移FullAmount（前端校验数量）
        /// </summary>      
        /// <param name="subID">子批次</param>
        /// <param name="containerID">选中的容器ID</param>
        /// <param name="proOrderID">产线工单ID</param>
        /// <param name="batchID">批次ID</param>      
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_FullAmount(FullAmountModel reqModel)
        {
            //return Success("", "MPreparationTransfer_FullAmount");
            var data = await _materialPreparationViewServices.MPreparationTransfer_FullAmount(reqModel);
            if (data.success)
            {
                return Success("", "转移成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 备料 Transfer_Remove（前端校验数量）
        /// </summary>      
        /// <param name="subIDs">子批次（最下面一个批次选择的ID）</param>
        /// <param name="proOrderID">产线工单ID(来自于主界面)</param>
        /// <param name="batchID">批次ID来自于主界</param>      
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_Remove(RemoveBagModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_Remove(reqModel);
            if (data.success)
            {
                return Success("", "移除成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 新备料 NewTransfer_Remove（前端校验数量）
        /// </summary>      
        /// <param name="subIDs">子批次（最下面一个批次选择的ID）</param>
        /// <param name="proOrderID">产线工单ID(来自于主界面)</param>
        /// <param name="batchID">批次ID来自于主界</param>      
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationNewTransfer_Remove(RemoveBagModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationNewTransfer_Remove(reqModel);
            if (data.success)
            {
                return Success("", "移除袋子成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 容器完成(CompletePalle)
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_CompletePallet(CompletePalletModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_CompletePallet(reqModel);
            if (data.success)
            {
                return Success("", "容器完成");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 容器打开(OpenPalle)
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_OpenPallet(OpenPalletModel reqModel)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_OpenPallet(reqModel);
            if (data.success)
            {
                return Success("", "容器打开成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 删除容器(DeletePallet)
        /// </summary>
        /// <param name="containerID">最下面选中参数</param>
        /// <param name="actualWeight">最上面的实际数量</param>
        /// <param name="uomId">单位ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_DeletePallet(DeletePalletModel model)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_DeletePallet(model);

            if (data.success)
            {
                return Success("", "删除成功！");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        #endregion


        #region 工单工序批次备料查询(按照物料批次)

        /// <summary>
        /// 工单工序批次备料查询(按照物料批次)备料查询 
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> GetPageList_NewMaterial(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageList(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 备料NewMaterial跳转第一界面根据proOrderID查询批次(支持多订单)(按照物料批次)
        /// </summary>
        /// <param name="proOrderID">订单ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailbymaterialViewEntity>>> GetByProOrderID_NewMaterial(
            string[] proOrderID)
        {
            var data = await _materialPreparationViewServices.GetByProOrderID_NewMaterial(proOrderID);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 根据批次ID获取对应的物料信息(按照物料批次)
        /// </summary>
        /// <param name="batchID">物料ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailIiViewEntity>>> GetList_NewByMaterialID(string batchID)
        {
            var data = await _materialPreparationViewServices.GetList_NewByMaterialID(batchID);
            return Success(data, "获取成功");
        }

        #endregion

        [HttpGet("{id}")]
        public async Task<MessageModel<MaterialPreparationViewEntity>> GetEntity(string id)
        {
            var data = await _materialPreparationViewServices.QueryById(id);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> SaveForm([FromBody] MaterialPreparationViewEntity request)
        {
            var data = new MessageModel<string>();
            data.success = await _materialPreparationViewServices.SaveForm(request);
            if (data.success)
            {
                return Success("", "添加成功");
            }
            else
            {
                return Failed("添加失败");
            }
        }

        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MPreparationViewEntity>>> GetMPreparationII(PreparationIIModel model)
        {
            var result = await _materialPreparationViewServices.GetMPreparationII(model);
            return Success(result, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<string>> Delete([FromBody] string[] ids)
        {
            var data = new MessageModel<string>();
            data.success = await _materialPreparationViewServices.DeleteByIds(ids);
            if (data.success)
            {
                return Success("", "删除成功");
            }
            else
            {
                return Failed("删除失败");
            }
        }


        #region 称量备料

        /// <summary>
        /// 称量备料界面查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> Get_CLBLNew(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.Get_CLBLNew(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 原料标签查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> Get_YLLabelNew(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.Get_YLLabelNew(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 称量备料界面查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> GetPageList_CLBL(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageList_CLBL(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 称量备料界面查询
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> GetCLBL_ByList(
            [FromBody] MaterialPreparationViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageList_CLBL(reqModel);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 获取工单界面跳转获取物料列表
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MPreparationViewEntity>>> GetMPreparationII_CLBL(
            PreparationIIModel model)
        {
            var result = await _materialPreparationViewServices.GetMPreparationII_CLBL(model);
            return Success(result, "获取成功");
        }

        /// <summary>
        /// 查询选中的物料（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ClblDiiViewEntity>>> GetPageListByMaterial_CLBL(BBatchDModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByMaterial_CLBL(model);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMaterialViewEntity>>> GetNewDown_CLBL(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageListNewByBatchDetailMaterial_CLBL(reqModel, true,
                true, true);
            return Success(data, "获取成功");
        }

        //New 界面
        [HttpPost]
        public async Task<MessageModel<PageModel<BBatchDetailMaterialViewEntity>>> GetPageListNewMaterialPreTop(
            [FromBody] BBatchDetailMaterialViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetPageListNewByBatchDetailMaterial_CLBLTop(reqModel,
                false, false, false);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 工序等信息(标题)
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<MBatchriiViewEntity>>> GetSegmentList_CLBL(
            MBatchriiViewRequestModel reqModel)
        {
            var data = await _materialPreparationViewServices.GetSegmentList_CLBL(reqModel);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 备料跳转第一界面根据proOrderID、EquipmentId订单号、设备号查询批次(这里需要根据batch表批次排序)(称量界面独享)
        /// </summary>
        /// <param name="proOrderID">订单ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BclblDetailViewEntity>>> GetByProOrderID_CLBL(string proOrderID,
            string EquipmentId)
        {
            if (EquipmentId == "null")
            {
                EquipmentId = null;
            }

            var data = await _materialPreparationViewServices.GetByList_CLBL(proOrderID, EquipmentId);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 根据批次ID获取对应的物料信息(混料机下面的一层的一层数据)
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<ClblDiiViewEntity>>> GetListByBatchID_CLBL(string batchID, string eqpmentID)
        {
            // var data = await _materialPreparationViewServices.GetListByBatchID_CLBL(batchID, eqpmentID);
            var data = await _materialPreparationViewServices.GetPrepareationListByBatchID(batchID, eqpmentID);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 查询选中的批次（函数用来选择多批次的物料信息并条状显示）build pallets
        /// </summary>
        /// <param name="model">batchIDS  批次ID组 pageIndex 当前界面  pageSize 每页数量</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<ClblDiiViewEntity>>> GetPageListByBatchIDS_CLBL(
            BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchIDS_CLBL(model);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<InventObj>>> GetInventState(BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetInventState(model);
            return Success(data, "获取成功");
        }

        [HttpPost]
        public async Task<MessageModel<PageModel<ClblDiiViewEntity>>> GetPageListByBatchIDS_CLBLBYID(
            BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetPageListByBatchIDS_CLBLBYID(model);
            return Success(data, "获取成功");
        }

        #endregion


        /// <summary>
        /// 拼锅最后一个界面打印
        /// </summary>
        /// <param name="plateAddModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReprintSavePG(MPPallentAddModel plateAddModel)
        {
            var data = await _materialPreparationViewServices.ReprintSavePG(plateAddModel);
            if (data.success)
            {
                return Success("", "创建托盘成功!");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 拼锅最后一个界面打印
        /// </summary>
        /// <param name="plateAddModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> ReprintSave(MPPallentAddModel plateAddModel)
        {
            var data = await _materialPreparationViewServices.ReprintSave(plateAddModel);
            if (data.success)
            {
                return Success("", "打印成功");
            }
            else
            {
                return Failed(data.msg);
            }
        }


        #region 拼锅

        /// <summary>
        /// 根据条码查询对应的容器编码是否存在
        /// batchID;proID;equpmentID;batchData.Number;当前批次号;括号内当前第几批;容器id
        /// </summary>
        /// <param name="sscc"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetContainerPDA(string sscc)
        {
            var data = new MessageModel<string>();
            data = await _materialPreparationViewServices.GetContainerPDA(sscc);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 根据工单号查询表头数据(获取第一数据)（缸号，返回的数据取第1行数据 缸号：Sequence/Sequencetotal ）
        /// 工单号:ProOrder+(GetContainerPDA最后个数据)+配方 FormulaNo +缸号：Sequence 批次产量：来自于接口GetListByBatchIDPDA 汇总
        /// 生产线：EquipmentCode 生产日期：PlanStartTime
        /// </summary>
        /// <param name="ProID">工单号</param>
        /// <param name="RoomID">拼锅room的ID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<PageModel<MaterialPreparationViewEntity>>> GetPGTopPDA(
            MaterialPreparationViewRequestModel model)
        {
            var result = await _materialPreparationViewServices.GetPGTopPDA(model);
            return Success(result, "获取成功");
        }


        /// <summary>
        /// 根据批次ID和eqpmentID获取数据源（底部数据源）（物料 ：MCode +MName  数量：MQuantity  /MQuantityTotal MQuantityunit 批次产量： returnData[0].Total）
        /// 单位ID returnData[0].UnitID
        /// </summary>
        /// <param name="batchID">批次id</param>
        /// <param name="eqpmentID">拼锅返回数据</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<List<BBatchDetailIiViewEntityModel>>> GetListByBatchIDPDA(string batchID,
            string eqpmentID)
        {
            var data = await _materialPreparationViewServices.GetListByBatchIDPDA(batchID, eqpmentID);
            return Success(data, "获取成功");
        }


        /// <summary>
        /// 用来查询可用库存的数据(实体)：sscc:  SSCC 追溯码 Bag 包数  Size 单包重量  MUnit 单位 TargetWeight 目标重量
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<InVentPDAModel>> GetInventPDA(BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.GetInventPDA(model);
            return Success(data, "获取成功");
        }

        /// <summary>
        /// 判断当前数据所处位置（托盘/备料小标签+ invent.SubLotId;"/可用库存++ invent.SubLotId;）分号
        /// </summary>
        /// <param name="cName">容器名称首页第一界面缓存</param>
        /// <param name="sscc">扫码的二维码</param>
        /// <param name="batchID">批次ID</param>
        /// <param name="proID">工单ID</param>
        /// <param name="equpmentID">ROOMID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> GetCodeTypeByPDA(string cName, string sscc, string batchID,
            string proID, string equpmentID)
        {
            var data = new MessageModel<string>();
            data = await _materialPreparationViewServices.GetCodeTypeByPDA(cName, sscc, batchID, proID, equpmentID);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 工单库存转移（传参和网页一致）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_FullAmountPDA(FullAmountModel reqModel)
        {
            var data = new MessageModel<string>();
            data = await _materialPreparationViewServices.MPreparationTransfer_FullAmountPDA(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }

        /// <summary>
        /// 可用库存转移（传参和网页一致）
        /// </summary>
        /// <param name="reqModel"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> MPreparationTransfer_FullBagPDA(FullBagModel reqModel)
        {
            var data = new MessageModel<string>();
            data = await _materialPreparationViewServices.MPreparationTransfer_FullBagPDA(reqModel);
            if (data.success)
            {
                return Success("", data.msg);
            }
            else
            {
                return Failed(data.msg);
            }
        }


        /// <summary>
        /// 拼锅是否完成
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <param name="eqpmentID">roomID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> IsFinish_PDA(BBatchDetailIIModel model)
        {
            var data = await _materialPreparationViewServices.IsFinish(model.batchID, model.eqpmentID);
            if (data == true)
            {
                return Success("", "成功");
            }
            else
            {
                return Success("", "失败");
            }
        }


        /// <summary>
        /// 拼锅是否完成
        /// </summary>
        /// <param name="batchID">批次ID</param>
        /// <param name="eqpmentID">roomID</param>
        /// <returns></returns>
        [HttpPost]
        public async Task<MessageModel<string>> CompletePalletPDA(CompletePalletModel model)
        {
            var data = await _materialPreparationViewServices.MPreparationTransfer_CompletePallet(model);
            if (data.success)
            {
                return Success("", "容器完成");
            }
            else
            {
                return Failed(data.msg);
            }
        }

        #endregion
    }
    //public class MaterialPreparationViewRequestModel : RequestPageModelBase
    //{
    //    public string key { get; set; }
    //}
}