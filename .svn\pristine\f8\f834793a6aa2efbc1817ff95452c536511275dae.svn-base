﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

using SEFA.Base.Model.BASE;
namespace SEFA.PPM.Model.Models
{
    ///<summary>
    ///周计划
    ///</summary>
    
    [SugarTable("PPM_B_WEEK_SCHEDULE")] 
    public class WeekScheduleEntity : EntityBase
    {
        public WeekScheduleEntity()
        {
        }
           /// <summary>
           /// Desc:工厂
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FACTORY")]
        public string Factory { get; set; }
           /// <summary>
           /// Desc:车间
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="WORKSHOP")]
        public string Workshop { get; set; }
           /// <summary>
           /// Desc:产线ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_ID")]
        public string LineId { get; set; }
           /// <summary>
           /// Desc:产线代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="LINE_CODE")]
        public string LineCode { get; set; }
           /// <summary>
           /// Desc:计划分类
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="CATEGORY")]
        public string Category { get; set; }
           /// <summary>
           /// Desc:物料ID
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_ID")]
        public string MaterialId { get; set; }
           /// <summary>
           /// Desc:物料代码
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_CODE")]
        public string MaterialCode { get; set; }
           /// <summary>
           /// Desc:物料名称
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_NAME")]
        public string MaterialName { get; set; }
           /// <summary>
           /// Desc:物料版本
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="MATERIAL_VER")]
        public string MaterialVer { get; set; }
           /// <summary>
           /// Desc:包装规格ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PACK_SIZE_ID")]
        public string PackSizeId { get; set; }
           /// <summary>
           /// Desc:包装规格
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PACK_SIZE")]
        public string PackSize { get; set; }
           /// <summary>
           /// Desc:设计代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESIGN_CODE")]
        public string DesignCode { get; set; }
           /// <summary>
           /// Desc:配方ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESIGN_ID")]
        public string DesignId { get; set; }
           /// <summary>
           /// Desc:配方版本
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="DESIGN_VER")]
        public string DesignVer { get; set; }
           /// <summary>
           /// Desc:单位
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT")]
        public string Unit { get; set; }
           /// <summary>
           /// Desc:单位ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="UNIT_ID")]
        public string UnitId { get; set; }
           /// <summary>
           /// Desc:输出
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="OUTPUT")]
        public string Output { get; set; }
           /// <summary>
           /// Desc:开始工作日
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="START_WORKDAY")]
        public DateTime StartWorkday { get; set; }
           /// <summary>
           /// Desc:开始工作日历ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="START_CALENDAR_ID")]
        public string StartCalendarId { get; set; }
           /// <summary>
           /// Desc:开始班次
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="START_SHIFT")]
        public string StartShift { get; set; }
           /// <summary>
           /// Desc:开始班次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="START_SHIFT_ID")]
        public string StartShiftId { get; set; }
           /// <summary>
           /// Desc:结束工作日
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FINISH_WORKDAY")]
        public DateTime FinishWorkday { get; set; }
           /// <summary>
           /// Desc:结束工作日历ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FINISH_CALENDAR_ID")]
        public string FinishCalendarId { get; set; }
           /// <summary>
           /// Desc:结束班次
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="FINISH_SHIFT")]
        public string FinishShift { get; set; }
           /// <summary>
           /// Desc:结束班次ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="FINISH_SHIFT_ID")]
        public string FinishShiftId { get; set; }
           /// <summary>
           /// Desc:计划数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="PLAN_QUANTITY")]
        public decimal? PlanQuantity { get; set; }
           /// <summary>
           /// Desc:返工物料ID
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REWORK_MATERIAL_ID")]
        public string ReworkMaterialId { get; set; }
           /// <summary>
           /// Desc:返工物料代码
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REWORK_MATERIAL_CODE")]
        public string ReworkMaterialCode { get; set; }
           /// <summary>
           /// Desc:返工物料名称
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REWORK_MATERIAL_NAME")]
        public string ReworkMaterialName { get; set; }
           /// <summary>
           /// Desc:返工物料版本
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REWORK_MATERIAL_VER")]
        public string ReworkMaterialVer { get; set; }
           /// <summary>
           /// Desc:返工物料数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REWORK_QUANTITY")]
        public decimal? ReworkQuantity { get; set; }
           /// <summary>
           /// Desc:计划编号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ORDER_NO")]
        public string OrderNo { get; set; }
           /// <summary>
           /// Desc:计划类型
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="TYPE")]
        public string Type { get; set; }
           /// <summary>
           /// Desc:计划状态
           /// Default:
           /// Nullable:False
           /// </summary>
           [SugarColumn(ColumnName="STATUS")]
        public string Status { get; set; }
           /// <summary>
           /// Desc:SAP工单号
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAP_ORDER_NO")]
        public string SapOrderNo { get; set; }
           /// <summary>
           /// Desc:SAP标识
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAP_FLAG")]
        public string SapFlag { get; set; }
           /// <summary>
           /// Desc:SAP状态
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAP_STATUS")]
        public string SapStatus { get; set; }
           /// <summary>
           /// Desc:SAP报工数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="SAP_FEEDBACK_QUANTITY")]
        public decimal? SapFeedbackQuantity { get; set; }
           /// <summary>
           /// Desc:工作中心
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="WORK_CENTER")]
        public string WorkCenter { get; set; }
           /// <summary>
           /// Desc:实际开始工作日
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_START_WORKDAY")]
        public DateTime? ActualStartWorkday { get; set; }
           /// <summary>
           /// Desc:实际开始时间
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_START_TIME")]
        public DateTime? ActualStartTime { get; set; }
           /// <summary>
           /// Desc:实际开始班次
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_START_SHIFT")]
        public string ActualStartShift { get; set; }
           /// <summary>
           /// Desc:实际结束工作日
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_FINISH_WORKDAY")]
        public DateTime? ActualFinishWorkday { get; set; }
           /// <summary>
           /// Desc:实际结束时间
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_FINISH_TIME")]
        public DateTime? ActualFinishTime { get; set; }
           /// <summary>
           /// Desc:实际结束班次
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_FINISH_SHIFT")]
        public string ActualFinishShift { get; set; }
           /// <summary>
           /// Desc:实际数量
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="ACTUAL_QUANTITY")]
        public decimal? ActualQuantity { get; set; }
           /// <summary>
           /// Desc:备注
           /// Default:
           /// Nullable:True
           /// </summary>
           [SugarColumn(ColumnName="REMARK")]
        public string Remark { get; set; }

    }
}