﻿using SEFA.Base.Model;
using System;

namespace SEFA.PPM.Model.ViewModels
{
    public class ProductionOrderRequestModel : RequestPageModelBase
    {
        public ProductionOrderRequestModel()
        {
        }
        /// <summary>
        /// Desc:ID
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ID { get; set; }
        /// <summary>
        /// Desc:订单号
        /// Default:
        /// Nullable:True
        /// </summary>
        public string ProductionOrderNo { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialVersionId { get; set; }
        /// <summary>
        /// Desc:物料ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialId { get; set; }
        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialCode { get; set; }

        /// <summary>
        /// Desc:物料版本ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string MaterialName { get; set; }

        /// <summary>
        /// Desc:类型：Batch，WorkOrder，PlanOrder
        /// Default:
        /// Nullable:False
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Desc:父级ID
        /// Default:
        /// Nullable:False
        /// </summary>
        public string ParentId { get; set; }
        /// <summary>
        /// Desc:计划数量
        /// Default:
        /// Nullable:False
        /// </summary>
        public decimal PlanQty { get; set; }
        /// <summary>
        /// Desc:设定速度
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Speed { get; set; }
        /// <summary>
        /// Desc:设定速度单位
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SpeedUom { get; set; }
        /// <summary>
        /// Desc:BOM版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string BomVersion { get; set; }
        /// <summary>
        /// Desc:长文本版本
        /// Default:
        /// Nullable:True
        /// </summary>
        public string TextVersion { get; set; }
        /// <summary>
        /// Desc:计划日期
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanDate { get; set; }
        /// <summary>
		/// Desc:备料班次
		/// Default: 
		/// Nullable:True
		/// </summary>
        public string PrepareShiftid { get; set; }
        /// <summary>
        /// Desc:计划开始时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanStartTime { get; set; }
        /// <summary>
        /// Desc:计划结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? PlanEndTime { get; set; }
        /// <summary>
		/// Desc:开始时间
		/// Default:
		/// Nullable:True
		/// </summary>
        public DateTime? StartTime { get; set; }
        /// <summary>
        /// Desc:结束时间
        /// Default:
        /// Nullable:True
        /// </summary>
        public DateTime? EndTime { get; set; }
        /// <summary>
        /// Desc:订单状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? PoStatus { get; set; }
        /// <summary>
        /// Desc:下发状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? ReleaseStatus { get; set; }
        /// <summary>
        /// Desc:下发状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? ProduceStatus { get; set; }

        /// <summary>
        /// Desc:完结状态
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? FinalStatus { get; set; }
        /// <summary>
        /// Desc:容量
        /// Default:
        /// Nullable:True
        /// </summary>
        public int? Capicity { get; set; }
        /// <summary>
        /// Desc:期望效率
        /// Default:
        /// Nullable:True
        /// </summary>
        public decimal? ExpectedEfficiency { get; set; }
        /// <summary>
        /// Desc:备注
        /// Default:
        /// Nullable:True
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// Desc:删除标识
        /// Default:
        /// Nullable:False
        /// </summary>
        public int Deleted { get; set; }

        /// <summary>
        /// Desc:工段编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string SegmentCode { get; set; }

        /// <summary>
        /// Desc:产线编码
        /// Default:
        /// Nullable:True
        /// </summary>
        public string LineCode { get; set; }

        /// <summary>
        /// Desc:SAP工单类型  ZXH9 包装车间返工工单   ZXH1 包装   ZXH2 制造   ZXH4  开盖取料(制造车间返工工单)
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string SapOrderType { get; set; }
        /// <summary>
        /// Desc:MES工单号 'C'+4为年+2位月+2位日+3位顺序号
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string MesOrderCode { get; set; }
        /// <summary>
        /// Desc:SAP同步状态
        /// Default:0 无需同步，1 待同步，2 已上传 3 同步状态 4 同步失败 
        /// Nullable:False
        /// </summary>
        public int SapFlag { get; set; }

        /// <summary>
        /// Desc:标准批次类型
        /// Default: 
        /// Nullable:True
        /// </summary>
        public string StandardLotType { get; set; }


    }

    public class OrderTextRequest
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        public string MaterialId { get; set; }

        /// <summary>
        /// 日期
        /// </summary>
        public string Date { get; set; }

        /// <summary>
        /// 工单号
        /// </summary>
        public string ProductionOrderNo { get; set; }

    }

    public class OrderTextResponse
    {
        public string ProductionOrderId { get; set; }

        public string ProductionOrderNo { get; set; }

        public DateTime? PlanStartTime { get; set; }

        public string MaterialCode { get; set; }

        public string MaterialVersionNumber { get; set; }

        public string ProcessData { get; set; }

        public string Token { get; set; }
    }
}